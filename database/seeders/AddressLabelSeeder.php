<?php

namespace Database\Seeders;

use App\Models\AddressLabel;
use Illuminate\Database\Seeder;

class AddressLabelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $labels = [
            ['label' => 'Home', 'icon' => 'heroicon-c-home'],
            ['label' => 'Work', 'icon' => 'heroicon-c-building-office'],
            ['label' => 'School', 'icon' => 'heroicon-c-academic-cap'],
            ['label' => 'Gym', 'icon' => 'mdi-dumbbell'],
        ];

        foreach ($labels as $label) {
            AddressLabel::create($label);
        }
    }
}
