<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class TripRatingsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('trip_ratings')->delete();

        \DB::table('trip_ratings')->insert([
            0 => [
                'trip_id' => 84,
                'rider_id' => 15,
                'driver_id' => 55,
                'rider_to_driver_rating' => 4,
                'rider_to_car_rating' => 3,
                'rider_review' => null,
                'driver_review' => null,
                'driver_to_rider_rating' => 1,
                'created_at' => '2025-04-04 13:47:06',
                'updated_at' => '2025-04-04 13:47:06',
            ],
            1 => [
                'trip_id' => 85,
                'rider_id' => 10,
                'driver_id' => 26,
                'rider_to_driver_rating' => 2,
                'rider_to_car_rating' => 5,
                'rider_review' => 'The ride was uncomfortable and not enjoyable.',
                'driver_review' => null,
                'driver_to_rider_rating' => 3,
                'created_at' => '2025-04-04 13:47:06',
                'updated_at' => '2025-04-04 13:47:06',
            ],
            2 => [
                'trip_id' => 86,
                'rider_id' => 51,
                'driver_id' => 71,
                'rider_to_driver_rating' => 3,
                'rider_to_car_rating' => 5,
                'rider_review' => 'The trip was fine, but could use some improvements.',
                'driver_review' => 'The rider was okay, but a bit difficult to manage.',
                'driver_to_rider_rating' => 1,
                'created_at' => '2025-04-04 13:47:06',
                'updated_at' => '2025-04-04 13:47:06',
            ],
            3 => [
                'trip_id' => 87,
                'rider_id' => 1,
                'driver_id' => 56,
                'rider_to_driver_rating' => 3,
                'rider_to_car_rating' => 3,
                'rider_review' => 'It was okay, nothing special.',
                'driver_review' => 'The rider caused issues during the trip.',
                'driver_to_rider_rating' => 1,
                'created_at' => '2025-04-04 13:47:06',
                'updated_at' => '2025-04-04 13:47:06',
            ],
            4 => [
                'trip_id' => 88,
                'rider_id' => 21,
                'driver_id' => 12,
                'rider_to_driver_rating' => 1,
                'rider_to_car_rating' => 5,
                'rider_review' => 'The trip was not great, had some issues.',
                'driver_review' => 'The rider caused issues during the trip.',
                'driver_to_rider_rating' => 2,
                'created_at' => '2025-04-04 13:47:06',
                'updated_at' => '2025-04-04 13:47:06',
            ],
            5 => [
                'trip_id' => 89,
                'rider_id' => 34,
                'driver_id' => 66,
                'rider_to_driver_rating' => 4,
                'rider_to_car_rating' => 1,
                'rider_review' => null,
                'driver_review' => 'The rider was polite and easy to communicate with.',
                'driver_to_rider_rating' => 4,
                'created_at' => '2025-04-04 13:47:07',
                'updated_at' => '2025-04-04 13:47:07',
            ],
            6 => [
                'trip_id' => 90,
                'rider_id' => 31,
                'driver_id' => 58,
                'rider_to_driver_rating' => 5,
                'rider_to_car_rating' => 5,
                'rider_review' => 'The trip was smooth and enjoyable.',
                'driver_review' => 'The rider was okay, but a bit difficult to manage.',
                'driver_to_rider_rating' => 5,
                'created_at' => '2025-04-04 13:47:07',
                'updated_at' => '2025-04-04 13:47:07',
            ],
            7 => [
                'trip_id' => 91,
                'rider_id' => 11,
                'driver_id' => 6,
                'rider_to_driver_rating' => 3,
                'rider_to_car_rating' => 2,
                'rider_review' => 'It was okay, nothing special.',
                'driver_review' => 'The rider was polite and easy to communicate with.',
                'driver_to_rider_rating' => 3,
                'created_at' => '2025-04-04 13:47:07',
                'updated_at' => '2025-04-04 13:47:07',
            ],
            8 => [
                'trip_id' => 92,
                'rider_id' => 44,
                'driver_id' => 34,
                'rider_to_driver_rating' => 1,
                'rider_to_car_rating' => 4,
                'rider_review' => 'The ride was uncomfortable and not enjoyable.',
                'driver_review' => null,
                'driver_to_rider_rating' => 3,
                'created_at' => '2025-04-04 13:47:07',
                'updated_at' => '2025-04-04 13:47:07',
            ],
            9 => [
                'trip_id' => 93,
                'rider_id' => 63,
                'driver_id' => 22,
                'rider_to_driver_rating' => 2,
                'rider_to_car_rating' => 4,
                'rider_review' => 'The ride was uncomfortable and not enjoyable.',
                'driver_review' => null,
                'driver_to_rider_rating' => 4,
                'created_at' => '2025-04-04 13:47:07',
                'updated_at' => '2025-04-04 13:47:07',
            ],
        ]);

    }
}
