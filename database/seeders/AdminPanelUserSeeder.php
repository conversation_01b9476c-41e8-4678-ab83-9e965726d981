<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminPanelUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {

        User::factory()->create([
            'name' => 'satoripop',
            'last_name' => 'satoripop',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin'),
            'type' => 'admin',
            'phone_number' => fake()->unique()->phoneNumber(),
        ]);
    }
}
