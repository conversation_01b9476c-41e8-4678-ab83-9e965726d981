<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PricingRuleSeatNumbersTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('pricing_rule_seat_numbers')->truncate();

        \DB::table('pricing_rule_seat_numbers')->insert([
            0 => [
                'seats_number' => '2',
                'base_fare' => '0.00',
                'distance_fare' => '0.00',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
                'created_at' => '2025-01-26 15:04:35',
                'updated_at' => '2025-01-26 15:35:44',
            ],
            1 => [
                'seats_number' => '4',
                'base_fare' => '0.00',
                'distance_fare' => '0.00',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
                'created_at' => '2025-01-26 15:04:35',
                'updated_at' => '2025-01-26 15:36:07',
            ],
            2 => [
                'seats_number' => '6',
                'base_fare' => '0.00',
                'distance_fare' => '0.00',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
                'created_at' => '2025-01-26 15:04:35',
                'updated_at' => '2025-01-26 15:36:20',
            ],
        ]);

    }
}
