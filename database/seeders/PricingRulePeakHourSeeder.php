<?php

namespace Database\Seeders;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRulePeakHour;
use Illuminate\Database\Seeder;

class PricingRulePeakHourSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing day charges instead of creating new ones
        $dayCharges = PricingRuleAdditionalDayCharge::all();

        if ($dayCharges->count() > 0) {
            foreach ($dayCharges->take(3) as $dayCharge) {
                PricingRulePeakHour::create([
                    'day_charge_id' => $dayCharge->id,
                    'peak_start_at' => '17:00',
                    'peak_end_at' => '19:00',
                    'base_fare_adjustment_type' => 'percentage',
                    'base_fare_fixed' => 0,
                    'base_fare_percentage' => 15.0,
                    'distance_fare_adjustment_type' => 'percentage',
                    'distance_fare_fixed' => 0,
                    'distance_fare_percentage' => 10.0,
                ]);
            }
        }
    }
}
