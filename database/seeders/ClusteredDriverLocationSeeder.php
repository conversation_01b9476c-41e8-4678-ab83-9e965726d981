<?php

namespace Database\Seeders;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ClusteredDriverLocationSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚗 Creating clustered test drivers to demonstrate clustering...');
        
        // Define cluster centers around Tripoli
        $clusterCenters = [
            ['name' => 'Tripoli Center', 'lat' => 32.8872, 'lng' => 13.1913, 'count' => 15],
            ['name' => 'Tripoli Airport', 'lat' => 32.6635, 'lng' => 13.1590, 'count' => 8],
            ['name' => 'Tripoli Port', 'lat' => 32.9057, 'lng' => 13.1867, 'count' => 12],
            ['name' => 'Tajoura', 'lat' => 32.8869, 'lng' => 13.3506, 'count' => 6],
            ['name' => 'Janzour', 'lat' => 32.8333, 'lng' => 12.9667, 'count' => 9],
        ];
        
        $driverCounter = 1;
        
        foreach ($clusterCenters as $cluster) {
            $this->command->info("📍 Creating {$cluster['count']} drivers around {$cluster['name']}...");
            
            for ($i = 1; $i <= $cluster['count']; $i++) {
                // Generate random coordinates within 500m radius of cluster center
                $distance = fake()->numberBetween(10, 500); // 10m to 500m for tight clustering
                $angle = fake()->numberBetween(0, 360);
                
                // Calculate new coordinates
                $lat = $cluster['lat'] + ($distance / 111320) * cos(deg2rad($angle));
                $lng = $cluster['lng'] + ($distance / 111320) * sin(deg2rad($angle)) / cos(deg2rad($cluster['lat']));
                
                // Create user for driver
                $user = User::factory()->create([
                    'type' => 'driver',
                    'name' => "Cluster Driver {$driverCounter}",
                    'last_name' => $cluster['name'],
                    'gender' => fake()->randomElement(['male', 'female']),
                    'email' => "cluster-driver-{$driverCounter}@example.com",
                ]);
                
                // Create driver with different statuses
                $driverStatus = fake()->randomElement([
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::active, // More active drivers
                    DriverGlobalStatus::blocked,
                ]);
                
                $driver = Driver::factory()->create([
                    'user_id' => $user->id,
                    'rider_gender' => fake()->randomElement(['female', 'male', 'both']),
                    'average_driver_rating' => fake()->numberBetween(3, 5),
                    'global_status' => $driverStatus,
                ]);
                
                // Set driver location using PostGIS
                DB::statement("
                    UPDATE drivers 
                    SET location = ST_SetSRID(ST_MakePoint($lng, $lat), 4326) 
                    WHERE id = {$driver->id}
                ");
                
                // Create vehicle for the driver
                $vehicleStatus = match($driverStatus) {
                    DriverGlobalStatus::active => VehicleStatus::active,
                    DriverGlobalStatus::blocked => VehicleStatus::blocked,
                    default => VehicleStatus::pending,
                };
                
                $vehicle = Vehicle::factory()->create([
                    'year' => fake()->numberBetween(2018, 2024),
                    'license_plate_number' => "CLU-{$driverCounter}-ليبيا",
                    'average_vehicle_rating' => fake()->numberBetween(3, 5),
                    'seat_number' => fake()->randomElement([2, 4, 6]),
                    'is_female_only' => fake()->boolean(20),
                    'global_status' => $vehicleStatus,
                ]);
                
                // Attach driver to vehicle
                $vehicle->drivers()->attach($driver);
                
                $driverCounter++;
            }
            
            $this->command->info("✅ Created {$cluster['count']} drivers around {$cluster['name']}");
        }
        
        $this->command->info('🎯 Clustered test drivers created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info('- 50 drivers created in 5 clusters around Tripoli');
        $this->command->info('- Tight clustering (within 500m of each center)');
        $this->command->info('- Mix of active and blocked statuses');
        $this->command->info('- Perfect for testing marker clustering');
        $this->command->info('');
        $this->command->info('🗺️  You can now test the clustering functionality!');
        $this->command->info('Zoom in/out to see clusters merge and separate.');
    }
}
