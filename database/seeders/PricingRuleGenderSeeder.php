<?php

namespace Database\Seeders;

use App\Models\PricingRuleGender;
use Illuminate\Database\Seeder;

class PricingRuleGenderSeeder extends Seeder
{
    public function run(): void
    {
        // Create the entry for male
        PricingRuleGender::create([
            'gender' => 'male',
            'base_fare_adjustment_type' => 'fixed',
            'base_fare_fixed' => 20.00,
            'base_fare_percentage' => 0,
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare_fixed' => 2.50,
            'distance_fare_percentage' => 0,
        ]);

        // Create the entry for female
        PricingRuleGender::create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0,
            'base_fare_percentage' => 10.00,
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_fixed' => 0,
            'distance_fare_percentage' => 5.00,
        ]);
    }
}
