<?php

namespace Database\Seeders;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Trips\TripStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DriverLocationSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚗 Creating drivers with locations and test scenarios...');

        // Clear existing test data
        $this->clearExistingData();

        // Libya major cities coordinates for realistic testing
        $locations = [
            ['name' => 'Tripoli', 'lat' => 32.8872, 'lng' => 13.1913],
            ['name' => 'Benghazi', 'lat' => 32.1167, 'lng' => 20.0667],
            ['name' => 'Misrata', 'lat' => 32.3745, 'lng' => 15.0919],
            ['name' => 'Zawiya', 'lat' => 32.7569, 'lng' => 12.7278],
            ['name' => 'Sabha', 'lat' => 27.0377, 'lng' => 14.4283],
        ];

        foreach ($locations as $location) {
            $this->createDriversInCity($location, 20); // 20 drivers per city
        }

        $this->command->info('✅ Created 100 drivers across 5 Libyan cities');
        $this->command->info('🎯 Creating test trips and scenarios...');

        // Create specific test scenarios
        $this->createTestScenarios();

        $this->command->info('✅ Test scenarios created successfully!');
    }

    private function clearExistingData(): void
    {
        $this->command->info('🧹 Clearing existing test data...');

        // Clear in proper order to avoid foreign key constraints
        try {
            DB::table('trip_ratings')->truncate();
        } catch (\Exception $e) {
            $this->command->warn('trip_ratings table not found or empty');
        }

        try {
            DB::table('trip_locations')->truncate();
        } catch (\Exception $e) {
            $this->command->warn('trip_locations table not found or empty');
        }

        try {
            DB::table('trips')->truncate();
        } catch (\Exception $e) {
            $this->command->warn('trips table not found or empty');
        }

        try {
            DB::table('driver_vehicle')->truncate();
        } catch (\Exception $e) {
            $this->command->warn('driver_vehicle table not found or empty');
        }

        // Delete test data but keep original seeded data
        DB::table('vehicles')->where('id', '>', 10)->delete(); // Keep seeded vehicles
        DB::table('drivers')->where('id', '>', 10)->delete(); // Keep seeded drivers
        DB::table('users')->where('type', 'driver')->where('id', '>', 20)->delete();
        DB::table('users')->where('type', 'passenger')->where('id', '>', 20)->delete();
    }

    private function createDriversInCity(array $location, int $count): void
    {
        $this->command->info("📍 Creating {$count} drivers in {$location['name']}...");

        for ($i = 0; $i < $count; $i++) {
            // Generate random coordinates within 10km radius of city center
            $distance = fake()->numberBetween(100, 10000); // 100m to 10km
            $angle = fake()->numberBetween(0, 360);

            // Convert to radians
            $angleRad = deg2rad($angle);

            // Earth's radius in meters
            $earthRadius = 6371000;

            // Calculate new coordinates
            $lat = $location['lat'] + ($distance / $earthRadius) * (180 / M_PI);
            $lng = $location['lng'] + ($distance / $earthRadius) * (180 / M_PI) / cos(deg2rad($location['lat']));

            // Create user for driver
            $user = User::factory()->create([
                'type' => 'driver',
                'name' => fake('ar_SA')->firstName(),
                'last_name' => fake('ar_SA')->lastName(),
                'gender' => fake()->randomElement(['male', 'female']),
            ]);

            // Create driver
            $driver = Driver::factory()->create([
                'user_id' => $user->id,
                'rider_gender' => fake()->randomElement(['female', 'male', 'both']),
                'average_driver_rating' => fake()->numberBetween(3, 5),
                'global_status' => fake()->randomElement([
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::active,
                    DriverGlobalStatus::active, // More active drivers
                    DriverGlobalStatus::blocked,
                ]),
            ]);

            // Set driver location using PostGIS
            DB::statement("
                UPDATE drivers
                SET location = ST_SetSRID(ST_MakePoint($lng, $lat), 4326)
                WHERE id = {$driver->id}
            ");

            // Create vehicle for the driver
            $vehicleStatus = match ($driver->global_status) {
                DriverGlobalStatus::active => VehicleStatus::active,
                DriverGlobalStatus::blocked => VehicleStatus::blocked,
                default => VehicleStatus::pending,
            };

            $vehicle = Vehicle::factory()->create([
                'year' => fake()->numberBetween(2018, 2024),
                'license_plate_number' => fake()->unique()->numerify('##-####-ليبيا'),
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
                'seat_number' => fake()->randomElement([2, 4, 6]),
                'is_female_only' => fake()->boolean(20), // 20% chance of being female-only
                'global_status' => $vehicleStatus,
            ]);

            // Attach driver to vehicle
            $vehicle->drivers()->attach($driver);
        }
    }

    private function createTestScenarios(): void
    {
        // Scenario 1: Active trips in Tripoli
        $this->createActiveTripsScenario();

        // Scenario 2: Available drivers waiting for trips
        $this->createAvailableDriversScenario();

        // Scenario 3: Completed trips for testing history
        $this->createCompletedTripsScenario();
    }

    private function createActiveTripsScenario(): void
    {
        $this->command->info('🚗 Creating active trip scenarios...');

        // Get some active drivers for trips
        $activeDrivers = Driver::where('global_status', DriverGlobalStatus::active)->take(5)->get();

        foreach ($activeDrivers as $driver) {
            // Create a rider
            $riderUser = User::factory()->create([
                'type' => 'passenger',
                'gender' => fake()->randomElement(['male', 'female']),
            ]);

            $rider = Rider::factory()->create([
                'user_id' => $riderUser->id,
                'average_rider_rating' => fake()->numberBetween(3, 5),
            ]);

            // Create an active trip
            $trip = Trip::factory()->create([
                'driver_id' => $driver->id,
                'rider_id' => $rider->id,
                'status' => fake()->randomElement([
                    TripStatus::assigned,
                    TripStatus::driver_arriving,
                    TripStatus::driver_arrived,
                    TripStatus::on_trip,
                ]),
                'estimated_departure_time' => now()->subMinutes(fake()->numberBetween(5, 30)),
                'estimated_arrival_time' => now()->addMinutes(fake()->numberBetween(10, 45)),
                'distance' => fake()->numberBetween(5, 25),
                'rider_notes' => fake()->optional(0.3)->sentence(),
            ]);

            // Create trip location
            TripLocation::factory()->create([
                'trip_id' => $trip->id,
                'departure_address' => fake()->address(),
                'arrival_address' => fake()->address(),
            ]);
        }
    }

    private function createAvailableDriversScenario(): void
    {
        $this->command->info('✅ Setting up available drivers...');

        // Ensure we have some available drivers
        Driver::where('global_status', DriverGlobalStatus::active)->take(10)->get()->each(function ($driver) {
            // Update their vehicles to be available too
            $driver->vehicles->each(function ($vehicle) {
                $vehicle->update(['global_status' => VehicleStatus::active->value]);
            });
        });
    }

    private function createCompletedTripsScenario(): void
    {
        $this->command->info('📋 Creating completed trip history...');

        // Get some blocked drivers for completed trips
        $blockedDrivers = Driver::where('global_status', DriverGlobalStatus::blocked)->take(3)->get();

        foreach ($blockedDrivers as $driver) {
            // Create a rider
            $riderUser = User::factory()->create([
                'type' => 'passenger',
                'gender' => fake()->randomElement(['male', 'female']),
            ]);

            $rider = Rider::factory()->create([
                'user_id' => $riderUser->id,
                'average_rider_rating' => fake()->numberBetween(3, 5),
            ]);

            // Create a completed trip
            $trip = Trip::factory()->create([
                'driver_id' => $driver->id,
                'rider_id' => $rider->id,
                'status' => TripStatus::completed,
                'estimated_departure_time' => now()->subHours(fake()->numberBetween(1, 24)),
                'estimated_arrival_time' => now()->subHours(fake()->numberBetween(1, 23)),
                'actual_departure_time' => now()->subHours(fake()->numberBetween(1, 24)),
                'actual_arrival_time' => now()->subHours(fake()->numberBetween(1, 23)),
                'distance' => fake()->numberBetween(5, 50),
                'final_price' => fake()->numberBetween(10, 100),
            ]);

            // Create trip location
            TripLocation::factory()->create([
                'trip_id' => $trip->id,
                'departure_address' => fake()->address(),
                'arrival_address' => fake()->address(),
            ]);
        }
    }
}
