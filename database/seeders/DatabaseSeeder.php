<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            // Core data first
            AdminPanelUserSeeder::class,
            VehicleBrandsTableSeeder::class,
            VehicleModelsTableSeeder::class,
            VehicleTypesTableSeeder::class,
            VehicleEquipmentTableSeeder::class,

            // Areas and addresses
            AreaSeeder::class,
            AreasTableSeeder::class,
            AddressSeeder::class,
            AddressLabelSeeder::class,

            // Pricing rules
            PricingRulesSeeder::class,
            PricingRulePeakHourSeeder::class,
            PricingRuleAdditionalDayChargesTableSeeder::class,
            PricingRuleSeatNumbersTableSeeder::class,
            PricingRuleGenderSeeder::class,

            // Users and drivers (this creates drivers with vehicles)
            RiderSeeder::class,
            DriverSeeder::class,
            DriverAvailabilitySeeder::class,

            // Additional test data (only if needed)
            // VehiclesTableSeeder::class, // Commented out to avoid conflicts
            // DriverVehicleTableSeeder::class, // Commented out to avoid conflicts

            // Other data
            PaymentSeeder::class,
            TripSeeder::class,
            TripRatingSeeder::class,
            ShieldSeeder::class,
        ]);
    }
}
