<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VehiclesTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('vehicles')->truncate();

        \DB::table('vehicles')->insert([
            0 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-26 00:00:00',
                'vehicle_model_id' => 10,
                'year' => 2020,
                'license_plate_number' => '5-67890-ليبيا',
                'average_vehicle_rating' => 1,
                'vehicle_type_id' => 3,
                'image' => 'https://example.com/images/car5.jpg',
                'seat_number' => 4,
                'is_female_only' => false,
            ],
            1 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:48:55',
                'vehicle_model_id' => 42,
                'year' => 2021,
                'license_plate_number' => '10-12345-ليبيا',
                'average_vehicle_rating' => 5,
                'vehicle_type_id' => 3,
                'image' => 'https://example.com/images/car10.jpg',
                'seat_number' => 4,
                'is_female_only' => true,
            ],
            2 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:50:03',
                'vehicle_model_id' => 45,
                'year' => 2020,
                'license_plate_number' => '9-01234-ليبيا',
                'average_vehicle_rating' => 2,
                'vehicle_type_id' => 3,
                'image' => 'https://example.com/images/car9.jpg',
                'seat_number' => 4,
                'is_female_only' => false,
            ],
            3 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:50:29',
                'vehicle_model_id' => 31,
                'year' => 2023,
                'average_vehicle_rating' => 1,
                'license_plate_number' => '8-90123-ليبيا',
                'vehicle_type_id' => 1,
                'image' => 'https://example.com/images/car8.jpg',
                'seat_number' => 5,
                'is_female_only' => true,
            ],
            4 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:51:13',
                'vehicle_model_id' => 28,
                'year' => 2022,
                'average_vehicle_rating' => 0,
                'license_plate_number' => '6-78901-ليبيا',
                'vehicle_type_id' => 3,
                'image' => 'https://example.com/images/car6.jpg',
                'seat_number' => 4,
                'is_female_only' => true,
            ],
            5 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:51:45',
                'vehicle_model_id' => 5,
                'year' => 2019,
                'average_vehicle_rating' => 1,
                'license_plate_number' => '4-56789-ليبيا',
                'vehicle_type_id' => 3,
                'image' => 'https://example.com/images/car4.jpg',
                'seat_number' => 6,
                'is_female_only' => true,
            ],
            6 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:52:03',
                'vehicle_model_id' => 34,
                'year' => 2023,
                'license_plate_number' => '3-45678-ليبيا',
                'average_vehicle_rating' => 4,
                'vehicle_type_id' => 2,
                'image' => 'https://example.com/images/car3.jpg',
                'seat_number' => 2,
                'is_female_only' => false,
            ],
            7 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:52:20',
                'vehicle_model_id' => 21,
                'year' => 2021,
                'average_vehicle_rating' => 4,
                'license_plate_number' => '2-34567-ليبيا',
                'vehicle_type_id' => 2,
                'image' => 'https://example.com/images/car2.jpg',
                'seat_number' => 5,
                'is_female_only' => true,
            ],
            8 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:52:32',
                'vehicle_model_id' => 11,
                'year' => 2022,
                'license_plate_number' => '1-23456-ليبيا',
                'average_vehicle_rating' => 3,
                'vehicle_type_id' => 1,
                'image' => 'https://example.com/images/car1.jpg',
                'seat_number' => 4,
                'is_female_only' => false,
            ],
            9 => [
                'created_at' => '2025-01-26 00:00:00',
                'updated_at' => '2025-01-27 10:52:49',
                'vehicle_model_id' => 37,
                'year' => 2021,
                'license_plate_number' => '7-89012-ليبيا',
                'average_vehicle_rating' => 1,
                'vehicle_type_id' => 4,
                'image' => 'https://example.com/images/car7.jpg',
                'seat_number' => 2,
                'is_female_only' => false,
            ],
        ]);

    }
}
