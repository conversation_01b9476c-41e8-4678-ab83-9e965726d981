<?php

namespace Database\Seeders;

use App\Models\Trip;
use Illuminate\Database\Seeder;

class TripSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a mix of trips with different statuses
        // Create 5 completed trips
        Trip::factory()
            ->count(5)
            ->create([
                'status' => 'completed',
            ]);

        // Create 3 in-progress trips
        Trip::factory()
            ->count(3)
            ->create([
                'status' => 'on_trip',
            ]);

        // Create 3 pending trips
        Trip::factory()
            ->count(3)
            ->create([
                'status' => 'pending',
            ]);

        // Create 2 canceled trips
        Trip::factory()
            ->count(2)
            ->create([
                'status' => 'canceled',
                'cancelled_by' => 'rider',
                'cancellation_stage' => 'afterDispatch',
            ]);

        // Create 2 more trips with random statuses
        Trip::factory()
            ->count(2)
            ->create();
    }
}
