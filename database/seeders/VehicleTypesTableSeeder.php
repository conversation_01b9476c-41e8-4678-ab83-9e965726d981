<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VehicleTypesTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('vehicle_types')->truncate();

        \DB::table('vehicle_types')->insert([
            0 => [
                'created_at' => '2025-01-26 22:20:20',
                'updated_at' => '2025-01-26 22:47:38',
                'name_en' => 'Luxury',
                'additional_base_fare' => '0.00',
                'additional_price_per_km' => '0.00',
                'name_ar' => 'فاخرة',
                'image' => 'vehicle_type_images/01JJJCS5K2614G3RE3NZK1QQVK.jpg',
                'description_ar' => 'راقية وفاخرة',
                'description_en' => 'Premium and elegant',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
            ],
            1 => [
                'created_at' => '2025-01-26 22:20:20',
                'updated_at' => '2025-01-26 22:58:46',
                'name_en' => 'Comfort',
                'additional_base_fare' => '0.00',
                'additional_price_per_km' => '0.00',
                'name_ar' => 'مريح',
                'image' => 'vehicle_type_images/01JJJDDHR8J6C7BMMZAW6DK2K1.png',
                'description_ar' => 'رحلة مريحة بمزايا إضافية',
                'description_en' => 'Spacious and relaxing',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'percentage',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
            ],
            2 => [
                'created_at' => '2025-01-26 22:20:20',
                'updated_at' => '2025-01-26 22:59:03',
                'name_en' => 'Economy',
                'additional_base_fare' => '0.00',
                'additional_price_per_km' => '0.00',
                'name_ar' => 'اقتصادي',
                'image' => 'vehicle_type_images/01JJJDE293GJNK8FD996JVGTH7.png',
                'description_ar' => 'خيار مناسب وبسعر اقتصادي',
                'description_en' => 'Affordable and efficient',
                'base_fare_adjustment_type' => 'percentage',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
            ],
            3 => [
                'created_at' => '2025-01-26 22:20:20',
                'updated_at' => '2025-01-26 22:59:14',
                'name_en' => 'Freight vehicle',
                'additional_base_fare' => '0.00',
                'additional_price_per_km' => '0.00',
                'name_ar' => 'نقل بضائع',
                'image' => 'vehicle_type_images/01JJJDED78E1KG0MX55HPNFRHG.png',
                'description_ar' => 'نقل آمن وموثوق للبضائع',
                'description_en' => 'Large cargo space',
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => '0.00',
                'distance_fare_adjustment' => '0.00',
            ],
        ]);

    }
}
