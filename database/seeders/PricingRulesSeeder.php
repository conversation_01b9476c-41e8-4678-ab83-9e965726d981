<?php

namespace Database\Seeders;

use App\Models\PricingRules;
use Illuminate\Database\Seeder;

class PricingRulesSeeder extends Seeder
{
    public function run(): void
    {
        // Create global pricing rules with realistic values for Libya
        PricingRules::create([
            'id' => 1,
            'global_base_price' => 15.0, // Base price in Libyan Dinar
            'global_price_per_km' => 2.5, // Price per km in Libyan Dinar
            'time_threshold_percentage' => 20.0, // Threshold for time overcharge
        ]);
    }
}
