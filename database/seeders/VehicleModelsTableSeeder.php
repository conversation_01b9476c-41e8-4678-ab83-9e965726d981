<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VehicleModelsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('vehicle_models')->truncate();

        \DB::table('vehicle_models')->insert([
            0 => [
                'name_en' => 'Elantra',
                'vehicle_brand_id' => 6,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'إلنترا',
            ],
            1 => [
                'name_en' => 'Santa Fe',
                'vehicle_brand_id' => 6,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'سانتا في',
            ],
            2 => [
                'name_en' => 'Tucson',
                'vehicle_brand_id' => 6,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'توسان',
            ],
            3 => [
                'name_en' => 'Sonata',
                'vehicle_brand_id' => 6,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'سوناتا',
            ],
            4 => [
                'name_en' => 'Kona',
                'vehicle_brand_id' => 6,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'كونا',
            ],
            5 => [
                'name_en' => 'Corolla',
                'vehicle_brand_id' => 11,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'كورولا',
            ],
            6 => [
                'name_en' => 'Camry',
                'vehicle_brand_id' => 11,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'كامري',
            ],
            7 => [
                'name_en' => 'RAV4',
                'vehicle_brand_id' => 11,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'راف فور',
            ],
            8 => [
                'name_en' => 'Highlander',
                'vehicle_brand_id' => 11,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'هايلاندر',
            ],
            9 => [
                'name_en' => 'Land Cruiser',
                'vehicle_brand_id' => 11,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'لاند كروزر',
            ],
            10 => [
                'name_en' => 'C-Class',
                'vehicle_brand_id' => 8,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'سي-كلاس',
            ],
            11 => [
                'name_en' => 'E-Class',
                'vehicle_brand_id' => 8,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'إي-كلاس',
            ],
            12 => [
                'name_en' => 'S-Class',
                'vehicle_brand_id' => 8,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'إس-كلاس',
            ],
            13 => [
                'name_en' => 'GLE',
                'vehicle_brand_id' => 8,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'جي إل إي',
            ],
            14 => [
                'name_en' => 'GLA',
                'vehicle_brand_id' => 8,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'جي إل إيه',
            ],
            15 => [
                'name_en' => 'Challenger',
                'vehicle_brand_id' => 4,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-26 21:56:40',
                'name_ar' => 'تشالنجر',
            ],
            16 => [
                'name_en' => 'Q5',
                'vehicle_brand_id' => 1,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:38:06',
                'name_ar' => 'كيو 5',
            ],
            17 => [
                'name_en' => 'A4',
                'vehicle_brand_id' => 1,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:38:23',
                'name_ar' => 'إيه 4',
            ],
            18 => [
                'name_en' => 'A6',
                'vehicle_brand_id' => 1,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:38:31',
                'name_ar' => 'إيه 6',
            ],
            19 => [
                'name_en' => 'A8',
                'vehicle_brand_id' => 1,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:38:39',
                'name_ar' => 'إيه 8',
            ],
            20 => [
                'name_en' => 'Q7',
                'vehicle_brand_id' => 1,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:38:46',
                'name_ar' => 'كيو 7',
            ],
            21 => [
                'name_en' => 'Carnival',
                'vehicle_brand_id' => 7,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:44:18',
                'name_ar' => 'كارنيفال',
            ],
            22 => [
                'name_en' => '508',
                'vehicle_brand_id' => 9,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:39:41',
                'name_ar' => '508',
            ],
            23 => [
                'name_en' => '2008',
                'vehicle_brand_id' => 9,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:39:49',
                'name_ar' => '2008',
            ],
            24 => [
                'name_en' => 'Megane',
                'vehicle_brand_id' => 10,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:39:57',
                'name_ar' => 'ميغان',
            ],
            25 => [
                'name_en' => 'Clio',
                'vehicle_brand_id' => 10,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:07',
                'name_ar' => 'كليو',
            ],
            26 => [
                'name_en' => '5008',
                'vehicle_brand_id' => 9,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:17',
                'name_ar' => '5008',
            ],
            27 => [
                'name_en' => 'Civic',
                'vehicle_brand_id' => 5,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:29',
                'name_ar' => 'سيفيك',
            ],
            28 => [
                'name_en' => 'Accord',
                'vehicle_brand_id' => 5,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:37',
                'name_ar' => 'أكورد',
            ],
            29 => [
                'name_en' => 'Golf',
                'vehicle_brand_id' => 12,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:47',
                'name_ar' => 'جولف',
            ],
            30 => [
                'name_en' => '7 Series',
                'vehicle_brand_id' => 2,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:40:59',
                'name_ar' => 'السلسلة 7',
            ],
            31 => [
                'name_en' => 'X5',
                'vehicle_brand_id' => 2,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:10',
                'name_ar' => 'إكس 5',
            ],
            32 => [
                'name_en' => '3 Series',
                'vehicle_brand_id' => 2,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:16',
                'name_ar' => 'السلسلة 3',
            ],
            33 => [
                'name_en' => 'Passat',
                'vehicle_brand_id' => 12,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:26',
                'name_ar' => 'باسات',
            ],
            34 => [
                'name_en' => '5 Series',
                'vehicle_brand_id' => 2,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:33',
                'name_ar' => 'السلسلة 5',
            ],
            35 => [
                'name_en' => 'X3',
                'vehicle_brand_id' => 2,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:39',
                'name_ar' => 'إكس 3',
            ],
            36 => [
                'name_en' => 'Ram 1500',
                'vehicle_brand_id' => 4,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:41:55',
                'name_ar' => 'رام 1500',
            ],
            37 => [
                'name_en' => 'Jetta',
                'vehicle_brand_id' => 12,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:42:13',
                'name_ar' => 'جيتا',
            ],
            38 => [
                'name_en' => '208',
                'vehicle_brand_id' => 9,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:42:52',
                'name_ar' => '208',
            ],
            39 => [
                'name_en' => 'Sportage',
                'vehicle_brand_id' => 7,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:43:04',
                'name_ar' => 'سبورتاج',
            ],
            40 => [
                'name_en' => 'Optima',
                'vehicle_brand_id' => 7,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:43:24',
                'name_ar' => 'أوبتيما',
            ],
            41 => [
                'name_en' => 'Seltos',
                'vehicle_brand_id' => 7,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:43:57',
                'name_ar' => 'سيلتوس',
            ],
            42 => [
                'name_en' => 'Telluride',
                'vehicle_brand_id' => 7,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:44:37',
                'name_ar' => 'تيلوريد',
            ],
            43 => [
                'name_en' => 'Malibu',
                'vehicle_brand_id' => 3,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:45:06',
                'name_ar' => 'ماليبوا',
            ],
            44 => [
                'name_en' => 'Cruze',
                'vehicle_brand_id' => 3,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:45:21',
                'name_ar' => 'كروز',
            ],
            45 => [
                'name_en' => 'Equinox',
                'vehicle_brand_id' => 3,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:45:34',
                'name_ar' => 'إكوينكس',
            ],
            46 => [
                'name_en' => 'Traverse',
                'vehicle_brand_id' => 3,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:45:47',
                'name_ar' => 'ترافيرس',
            ],
            47 => [
                'name_en' => 'Tahoe',
                'vehicle_brand_id' => 3,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:45:58',
                'name_ar' => 'تاهو',
            ],
            48 => [
                'name_en' => 'Durango',
                'vehicle_brand_id' => 4,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:46:33',
                'name_ar' => 'دورانغو',
            ],
            49 => [
                'name_en' => 'Charger',
                'vehicle_brand_id' => 4,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:46:49',
                'name_ar' => 'تشاجر',
            ],
            50 => [
                'name_en' => 'Journey',
                'vehicle_brand_id' => 4,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:47:00',
                'name_ar' => 'جورني',
            ],
            51 => [
                'name_en' => 'CR-V',
                'vehicle_brand_id' => 5,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:47:54',
                'name_ar' => 'سي آر في',
            ],
            52 => [
                'name_en' => 'Pilot',
                'vehicle_brand_id' => 5,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:48:22',
                'name_ar' => 'بايلوت',
            ],
            53 => [
                'name_en' => 'Fit',
                'vehicle_brand_id' => 5,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:48:41',
                'name_ar' => 'فيت',
            ],
            54 => [
                'name_en' => '3008',
                'vehicle_brand_id' => 9,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:50:36',
                'name_ar' => '3008',
            ],
            55 => [
                'name_en' => 'Kadjar',
                'vehicle_brand_id' => 10,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:51:01',
                'name_ar' => 'كاجار',
            ],
            56 => [
                'name_en' => 'Captur',
                'vehicle_brand_id' => 10,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:51:13',
                'name_ar' => 'كابتور',
            ],
            57 => [
                'name_en' => 'Twingo',
                'vehicle_brand_id' => 10,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:51:26',
                'name_ar' => 'توينغو',
            ],
            58 => [
                'name_en' => 'Tiguan',
                'vehicle_brand_id' => 12,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:56:17',
                'name_ar' => 'تيغوان',
            ],
            59 => [
                'name_en' => 'Polo',
                'vehicle_brand_id' => 12,
                'created_at' => '2025-01-26 21:56:40',
                'updated_at' => '2025-01-30 15:56:40',
                'name_ar' => 'بولو',
            ],
        ]);

    }
}
