<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VehicleEquipmentTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('vehicle_equipment')->truncate();

        \DB::table('vehicle_equipment')->insert([
            0 => [
                'name_en' => 'Baby seat',
                'icon' => 'mdi-car-child-seat',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:22:51',
                'name_ar' => 'مقعد طفل',
                'additional_fare' => '12.00',
            ],
            1 => [
                'name_en' => 'Bluetooth connectivity',
                'icon' => 'mdi-bluetooth',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:23:19',
                'name_ar' => 'اتصال بلوتوث',
                'additional_fare' => '18.00',
            ],
            2 => [
                'name_en' => 'Dash cam',
                'icon' => 'heroicon-c-camera',
                'status' => true,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:24:29',
                'name_ar' => 'كاميرا لوحة القي',
                'additional_fare' => '46.00',
            ],
            3 => [
                'name_en' => 'Heated seats',
                'icon' => 'mdi-car-seat-heater',
                'status' => true,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:24:58',
                'name_ar' => 'مقاعد مدفأة',
                'additional_fare' => '40.00',
            ],
            4 => [
                'name_en' => 'Large luggage capacity',
                'icon' => 'mdi-bag-carry-on',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:26:40',
                'name_ar' => 'سعة أمتعة كبيرة',
                'additional_fare' => '24.00',
            ],
            5 => [
                'name_en' => 'Phone charger',
                'icon' => 'mdi-power-plug-battery-outline',
                'status' => true,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:29:09',
                'name_ar' => 'شاحن هاتف',
                'additional_fare' => '26.00',
            ],
            6 => [
                'name_en' => 'Roof rack',
                'icon' => 'mdi-car-convertible',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:29:52',
                'name_ar' => 'حامل سقف',
                'additional_fare' => '20.00',
            ],
            7 => [
                'name_en' => 'Wheelchair accessibility',
                'icon' => 'mdi-wheelchair',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:33:18',
                'name_ar' => 'مخصصة لذوي الاحتياجات الخاصة',
                'additional_fare' => '12.00',
            ],
            8 => [
                'name_en' => 'All-weather tires',
                'icon' => 'mdi-tire',
                'status' => true,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:36:21',
                'name_ar' => 'إطارات لجميع الأحوال الجوية',
                'additional_fare' => '46.00',
            ],
            9 => [
                'name_en' => 'Gps navigation',
                'icon' => 'mdi-crosshairs-gps',
                'status' => false,
                'created_at' => '2025-01-26 20:16:52',
                'updated_at' => '2025-01-26 20:37:23',
                'name_ar' => 'نظام ملاحة GPS',
                'additional_fare' => '49.00',
            ],
        ]);

    }
}
