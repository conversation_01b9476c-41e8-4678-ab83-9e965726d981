<?php

namespace Database\Seeders;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SimpleDriverLocationSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚗 Creating simple test drivers with locations...');
        
        // Tripoli coordinates
        $tripoliLat = 32.8872;
        $tripoliLng = 13.1913;
        
        // Create 10 test drivers around Tripoli
        for ($i = 1; $i <= 10; $i++) {
            // Generate random coordinates within 5km radius of Tripoli center
            $distance = fake()->numberBetween(100, 5000); // 100m to 5km
            $angle = fake()->numberBetween(0, 360);
            
            // Calculate new coordinates
            $lat = $tripoliLat + ($distance / 111320) * cos(deg2rad($angle));
            $lng = $tripoliLng + ($distance / 111320) * sin(deg2rad($angle)) / cos(deg2rad($tripoliLat));
            
            // Create user for driver
            $user = User::factory()->create([
                'type' => 'driver',
                'name' => "Test Driver {$i}",
                'last_name' => 'Seeded',
                'gender' => fake()->randomElement(['male', 'female']),
                'email' => "test-driver-{$i}@example.com",
            ]);
            
            // Create driver with different statuses
            $driverStatus = match($i % 3) {
                0 => DriverGlobalStatus::active,
                1 => DriverGlobalStatus::active,
                2 => DriverGlobalStatus::blocked,
            };
            
            $driver = Driver::factory()->create([
                'user_id' => $user->id,
                'rider_gender' => fake()->randomElement(['female', 'male', 'both']),
                'average_driver_rating' => fake()->numberBetween(3, 5),
                'global_status' => $driverStatus,
            ]);
            
            // Set driver location using PostGIS
            DB::statement("
                UPDATE drivers 
                SET location = ST_SetSRID(ST_MakePoint($lng, $lat), 4326) 
                WHERE id = {$driver->id}
            ");
            
            // Create vehicle for the driver
            $vehicleStatus = match($driverStatus) {
                DriverGlobalStatus::active => VehicleStatus::active,
                DriverGlobalStatus::blocked => VehicleStatus::blocked,
                default => VehicleStatus::pending,
            };
            
            $vehicle = Vehicle::factory()->create([
                'year' => fake()->numberBetween(2018, 2024),
                'license_plate_number' => "TEST-{$i}-ليبيا",
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
                'seat_number' => fake()->randomElement([2, 4, 6]),
                'is_female_only' => fake()->boolean(20),
                'global_status' => $vehicleStatus,
            ]);
            
            // Attach driver to vehicle
            $vehicle->drivers()->attach($driver);
            
            $this->command->info("✅ Created driver {$i} with vehicle at ({$lat}, {$lng})");
        }
        
        $this->command->info('🎯 Test drivers created successfully!');
        $this->command->info('📊 Summary:');
        $this->command->info('- 10 test drivers created around Tripoli');
        $this->command->info('- Mix of active and blocked statuses');
        $this->command->info('- Vehicles with matching statuses');
        $this->command->info('');
        $this->command->info('🗺️  You can now test the vehicle tracking widget!');
    }
}
