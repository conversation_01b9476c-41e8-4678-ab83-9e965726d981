<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class PricingRuleAdditionalDayChargesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        \DB::table('pricing_rule_additional_day_charges')->truncate();

        \DB::table('pricing_rule_additional_day_charges')->insert([
            [
                'pricing_rule_id' => 1,
                'day' => 'Monday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Tuesday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Wednesday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Thursday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Friday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Saturday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
            [
                'pricing_rule_id' => 1,
                'day' => 'Sunday',
                'day_start_at' => '08:00:00',
                'day_end_at' => '18:00:00',
                'day_charge_type' => 'fixed',
                'night_start_at' => '18:01:00',
                'night_end_at' => '23:59:00',
                'night_charge_type' => 'percentage',
                'created_at' => '2025-01-23 14:04:25',
                'updated_at' => '2025-01-23 14:04:25',
                'day_fixed_charge' => '10.00',
                'day_percentage_charge' => '0.00',
                'night_fixed_charge' => '0.00',
                'night_percentage_charge' => '15.00',

            ],
        ]);
    }
}
