<?php

namespace Database\Seeders;

use App\Models\Rider;
use App\Models\RiderPreferences;
use App\Models\User;
use App\Models\VehicleEquipment;
use App\Models\VehicleType;
use Illuminate\Database\Seeder;

class RiderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create regular riders
        Rider::factory()
            ->count(40)
            ->create([
                'average_rider_rating' => fake()->numberBetween(3, 5),
            ]);

        // Create female riders with preferences
        $femaleRiders = Rider::factory()
            ->count(25)
            ->create([
                'user_id' => User::factory()->create([
                    'gender' => 'female',
                ]),
                'average_rider_rating' => fake()->numberBetween(4, 5),
            ]);

        // Add preferences for female riders
        foreach ($femaleRiders as $rider) {
            $preferences = RiderPreferences::create([
                'user_id' => $rider->user_id,
                'driver_gender' => 'female',
                'seats_number' => [4, 6],
            ]);

            // Attach some vehicle types
            $vehicleTypes = VehicleType::inRandomOrder()->limit(2)->get();
            $preferences->carTypes()->attach($vehicleTypes);

            // Attach some equipment
            $equipment = VehicleEquipment::inRandomOrder()->limit(rand(1, 3))->get();
            $preferences->carEquipments()->attach($equipment);
        }

        // Create riders with specific preferences
        $specialRiders = Rider::factory()
            ->count(15)
            ->create([
                'average_rider_rating' => fake()->numberBetween(4, 5),
            ]);

        // Add varied preferences
        foreach ($specialRiders as $rider) {
            $preferences = RiderPreferences::create([
                'user_id' => $rider->user_id,
                'driver_gender' => fake()->randomElement(['male', 'female', 'both']),
                'seats_number' => fake()->randomElements([2, 4, 6], rand(1, 3)),
            ]);

            // Attach some vehicle types
            $vehicleTypes = VehicleType::inRandomOrder()->limit(rand(1, 3))->get();
            $preferences->carTypes()->attach($vehicleTypes);

            // Attach some equipment
            $equipment = VehicleEquipment::inRandomOrder()->limit(rand(2, 4))->get();
            $preferences->carEquipments()->attach($equipment);
        }
    }
}
