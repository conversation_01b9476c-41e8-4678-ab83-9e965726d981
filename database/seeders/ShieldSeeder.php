<?php

namespace Database\Seeders;

use <PERSON><PERSON>hanSalleh\FilamentShield\Support\Utils;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"super_admin","guard_name":"web","permissions":["view_panel::area","view_any_panel::area","create_panel::area","update_panel::area","restore_panel::area","restore_any_panel::area","replicate_panel::area","reorder_panel::area","delete_panel::area","delete_any_panel::area","force_delete_panel::area","force_delete_any_panel::area","view_panel::driver","view_any_panel::driver","create_panel::driver","update_panel::driver","restore_panel::driver","restore_any_panel::driver","replicate_panel::driver","reorder_panel::driver","delete_panel::driver","delete_any_panel::driver","force_delete_panel::driver","force_delete_any_panel::driver","view_panel::driver::availability","view_any_panel::driver::availability","create_panel::driver::availability","update_panel::driver::availability","restore_panel::driver::availability","restore_any_panel::driver::availability","replicate_panel::driver::availability","reorder_panel::driver::availability","delete_panel::driver::availability","delete_any_panel::driver::availability","force_delete_panel::driver::availability","force_delete_any_panel::driver::availability","view_panel::payment","view_any_panel::payment","create_panel::payment","update_panel::payment","restore_panel::payment","restore_any_panel::payment","replicate_panel::payment","reorder_panel::payment","delete_panel::payment","delete_any_panel::payment","force_delete_panel::payment","force_delete_any_panel::payment","view_panel::pricing::rules","view_any_panel::pricing::rules","create_panel::pricing::rules","update_panel::pricing::rules","restore_panel::pricing::rules","restore_any_panel::pricing::rules","replicate_panel::pricing::rules","reorder_panel::pricing::rules","delete_panel::pricing::rules","delete_any_panel::pricing::rules","force_delete_panel::pricing::rules","force_delete_any_panel::pricing::rules","view_panel::rider","view_any_panel::rider","create_panel::rider","update_panel::rider","restore_panel::rider","restore_any_panel::rider","replicate_panel::rider","reorder_panel::rider","delete_panel::rider","delete_any_panel::rider","force_delete_panel::rider","force_delete_any_panel::rider","view_panel::trip","view_any_panel::trip","create_panel::trip","update_panel::trip","restore_panel::trip","restore_any_panel::trip","replicate_panel::trip","reorder_panel::trip","delete_panel::trip","delete_any_panel::trip","force_delete_panel::trip","force_delete_any_panel::trip","view_panel::trip::rating","view_any_panel::trip::rating","create_panel::trip::rating","update_panel::trip::rating","restore_panel::trip::rating","restore_any_panel::trip::rating","replicate_panel::trip::rating","reorder_panel::trip::rating","delete_panel::trip::rating","delete_any_panel::trip::rating","force_delete_panel::trip::rating","force_delete_any_panel::trip::rating","view_panel::user","view_any_panel::user","create_panel::user","update_panel::user","restore_panel::user","restore_any_panel::user","replicate_panel::user","reorder_panel::user","delete_panel::user","delete_any_panel::user","force_delete_panel::user","force_delete_any_panel::user","view_panel::vehicle","view_any_panel::vehicle","create_panel::vehicle","update_panel::vehicle","restore_panel::vehicle","restore_any_panel::vehicle","replicate_panel::vehicle","reorder_panel::vehicle","delete_panel::vehicle","delete_any_panel::vehicle","force_delete_panel::vehicle","force_delete_any_panel::vehicle","view_panel::vehicle::brand","view_any_panel::vehicle::brand","create_panel::vehicle::brand","update_panel::vehicle::brand","restore_panel::vehicle::brand","restore_any_panel::vehicle::brand","replicate_panel::vehicle::brand","reorder_panel::vehicle::brand","delete_panel::vehicle::brand","delete_any_panel::vehicle::brand","force_delete_panel::vehicle::brand","force_delete_any_panel::vehicle::brand","view_panel::vehicle::equipment","view_any_panel::vehicle::equipment","create_panel::vehicle::equipment","update_panel::vehicle::equipment","restore_panel::vehicle::equipment","restore_any_panel::vehicle::equipment","replicate_panel::vehicle::equipment","reorder_panel::vehicle::equipment","delete_panel::vehicle::equipment","delete_any_panel::vehicle::equipment","force_delete_panel::vehicle::equipment","force_delete_any_panel::vehicle::equipment","view_panel::vehicle::model","view_any_panel::vehicle::model","create_panel::vehicle::model","update_panel::vehicle::model","restore_panel::vehicle::model","restore_any_panel::vehicle::model","replicate_panel::vehicle::model","reorder_panel::vehicle::model","delete_panel::vehicle::model","delete_any_panel::vehicle::model","force_delete_panel::vehicle::model","force_delete_any_panel::vehicle::model","view_panel::vehicle::type","view_any_panel::vehicle::type","create_panel::vehicle::type","update_panel::vehicle::type","restore_panel::vehicle::type","restore_any_panel::vehicle::type","replicate_panel::vehicle::type","reorder_panel::vehicle::type","delete_panel::vehicle::type","delete_any_panel::vehicle::type","force_delete_panel::vehicle::type","force_delete_any_panel::vehicle::type","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","widget_VehiclesRealTimeMapWidget","widget_VehiclesRealTimeMovement"]}]';
        $directPermissions = '[]';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $data = [
            [
                'role_id' => 1,
                'model_type' => 'App\Models\User',
                'model_id' => 1,
            ],
            [
                'role_id' => 1,
                'model_type' => 'App\Models\User',
                'model_id' => 2,
            ],
        ];

        DB::table('model_has_roles')->insert($data);

        $this->command->info('Admin Permission Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn ($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}
