<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class VehicleBrandsTableSeeder extends Seeder
{
    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {

        \DB::table('vehicle_brands')->truncate();

        \DB::table('vehicle_brands')->insert([
            0 => [
                'name_en' => 'Audi',
                'created_at' => '2025-01-23 14:04:21',
                'updated_at' => '2025-01-23 14:04:21',
                'name_ar' => 'أودي',
            ],
            1 => [
                'name_en' => 'BMW',
                'created_at' => '2025-01-23 14:04:15',
                'updated_at' => '2025-01-23 14:04:15',
                'name_ar' => 'بي إم دبليو',
            ],
            2 => [
                'name_en' => 'Chevrolet',
                'created_at' => '2025-01-23 14:04:21',
                'updated_at' => '2025-01-26 19:41:40',
                'name_ar' => 'شيفروليه',
            ],
            3 => [
                'name_en' => 'Dodge',
                'created_at' => '2025-01-26 19:43:15',
                'updated_at' => '2025-01-26 19:43:15',
                'name_ar' => 'دودج',
            ],
            4 => [
                'name_en' => 'Honda',
                'created_at' => '2025-01-23 14:04:15',
                'updated_at' => '2025-01-26 19:40:22',
                'name_ar' => 'هوندا',
            ],
            5 => [
                'name_en' => 'Hyundai',
                'created_at' => '2025-01-23 14:04:15',
                'updated_at' => '2025-01-23 14:04:15',
                'name_ar' => 'هيونداي',
            ],
            6 => [
                'name_en' => 'Kia',
                'created_at' => '2025-01-26 19:38:16',
                'updated_at' => '2025-01-26 19:38:16',
                'name_ar' => 'كيا',
            ],
            7 => [
                'name_en' => 'Mercedes-Benz',
                'created_at' => '2025-01-23 14:04:15',
                'updated_at' => '2025-01-23 14:04:15',
                'name_ar' => 'مرسيدس-بنز',
            ],
            8 => [
                'name_en' => 'Peugeot',
                'created_at' => '2025-01-23 14:04:21',
                'updated_at' => '2025-01-26 19:41:08',
                'name_ar' => 'بيجو',
            ],
            9 => [
                'name_en' => 'Renault',
                'created_at' => '2025-01-26 15:41:33',
                'updated_at' => '2025-01-26 19:39:31',
                'name_ar' => 'رينو',
            ],
            10 => [
                'name_en' => 'Toyota',
                'created_at' => '2025-01-23 14:04:15',
                'updated_at' => '2025-01-23 14:04:15',
                'name_ar' => 'تويوتا',
            ],
            11 => [
                'name_en' => 'Volkswagen',
                'created_at' => '2025-01-26 19:42:33',
                'updated_at' => '2025-01-26 19:42:33',
                'name_ar' => 'فولكس فاجن',
            ],
        ]);
    }
}
