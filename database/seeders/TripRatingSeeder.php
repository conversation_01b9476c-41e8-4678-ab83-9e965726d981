<?php

namespace Database\Seeders;

use App\Models\TripRating;
use Illuminate\Database\Seeder;

class TripRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create ratings for all completed trips
        $completedTrips = \App\Models\Trip::where('status', 'completed')->get();

        foreach ($completedTrips as $trip) {
            // Check if a rating already exists for this trip
            if (! TripRating::where('trip_id', $trip->id)->exists()) {
                TripRating::factory()->create([
                    'trip_id' => $trip->id,
                    'rider_id' => $trip->rider_id,
                    'driver_id' => $trip->driver_id,
                ]);
            }
        }
    }
}
