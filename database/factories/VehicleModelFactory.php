<?php

namespace Database\Factories;

use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleModelFactory extends Factory
{
    protected $model = VehicleModel::class;

    public function definition(): array
    {
        $brandsAndModels = [
            'Toyota' => ['Corolla', 'Camry', 'RAV4'],
            'Ford' => ['F-150', 'Explorer', 'Mustang'],
            'Mercedes-Benz' => ['C-Class', 'E-Class', 'S-Class'],
            'BMW' => ['3 Series', '5 Series', 'X5'],
            'Hyundai' => ['Elantra', 'Santa Fe', 'Tucson'],
        ];

        $brandName = $this->faker->randomElement(array_keys($brandsAndModels));
        $modelName = $this->faker->randomElement($brandsAndModels[$brandName]);

        $brand = VehicleBrand::firstOrCreate(
            ['name_en' => $brandName],
            ['name_ar' => $this->getArabicBrandName($brandName)]
        );

        return [
            'name_en' => $modelName,
            'name_ar' => $this->getArabicModelName($modelName),
            'vehicle_brand_id' => $brand->id,
        ];
    }

    private function getArabicBrandName(string $brandName): string
    {
        $translations = [
            'Toyota' => 'تويوتا',
            'Ford' => 'فورد',
            'Mercedes-Benz' => 'مرسيدس',
            'BMW' => 'بي إم دبليو',
            'Hyundai' => 'هيونداي',
        ];

        return $translations[$brandName] ?? $brandName;
    }

    private function getArabicModelName(string $modelName): string
    {
        $translations = [
            'Corolla' => 'كورولا',
            'Camry' => 'كامري',
            'RAV4' => 'راف فور',
            'F-150' => 'إف-150',
            'Explorer' => 'إكسبلورر',
            'Mustang' => 'موستانج',
            'C-Class' => 'سي-كلاس',
            'E-Class' => 'إي-كلاس',
            'S-Class' => 'إس-كلاس',
            '3 Series' => 'السلسلة 3',
            '5 Series' => 'السلسلة 5',
            'X5' => 'إكس 5',
            'Elantra' => 'إلنترا',
            'Santa Fe' => 'سانتا في',
            'Tucson' => 'توسان',
        ];

        return $translations[$modelName] ?? $modelName;
    }
}
