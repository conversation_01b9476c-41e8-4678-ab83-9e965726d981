<?php

namespace Database\Factories;

use App\Models\Payment;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Generate a random total amount
        $amount = fake()->randomFloat(2, 10, 500); // Adjusted to avoid very small amounts

        // Define driver payout, platform commission, and taxes while ensuring they total to amount
        $driverPayout = fake()->randomFloat(2, 0, $amount * 0.85); // Driver gets 85% at most
        $platformCommission = fake()->randomFloat(2, 0, $amount * 0.1); // Platform takes 10% at most
        $taxes = $amount - ($driverPayout + $platformCommission); // Remaining amount is taxes

        // Ensure taxes are non-negative
        if ($taxes < 0) {
            $platformCommission = $amount * 0.1; // Adjust commission to keep taxes non-negative
            $taxes = $amount - ($driverPayout + $platformCommission);
        }

        return [
            'amount' => $amount,
            'driver_payout' => $driverPayout,
            'platform_commission' => $platformCommission,
            'taxes' => max(0, $taxes), // Ensure taxes is non-negative
            'payment_method' => fake()->randomElement(['card', 'cash', 'wallet']),
            'payment_status' => fake()->randomElement(['pending', 'completed', 'failed', 'refunded']),
            'transaction_id' => fake()->unique()->uuid(),
            'created_at' => fake()->dateTimeThisYear(), // Add timestamps
            'updated_at' => fake()->dateTimeThisYear(),
        ];
    }
}
