<?php

namespace Database\Factories;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Models\Driver;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

class DriverFactory extends Factory
{
    protected $model = Driver::class;

    public function definition(): array
    {
        return [
            'id_number' => fake()->unique()->numerify('############'),
            'rider_gender' => fake()->randomElement(['female', 'male', 'both']),
            'average_driver_rating' => fake()->numberBetween(1, 5),
            'global_status' => fake()->randomElement(DriverGlobalStatus::cases()),
            'user_id' => \App\Models\User::factory(),
        ];
    }

    public function withVehicles(int $vehicleCount = 1): Factory
    {
        return $this->afterCreating(function (Driver $driver) use ($vehicleCount) {
            $vehicles = Vehicle::factory($vehicleCount)->create();
            $driver->vehicles()->attach($vehicles);
        });
    }

    public function inProgress(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::in_progress,
        ]);
    }

    public function active(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::active,
        ]);
    }

    public function pending(): Factory
    {
        return $this->state(fn () => [
            'global_status' => DriverGlobalStatus::pending,
        ]);
    }

    public function withActiveAndInProgressVehicles(): Factory
    {
        return $this->afterCreating(function (Driver $driver) {
            // Create one active vehicle
            $activeVehicle = Vehicle::factory()->create([
                'global_status' => VehicleStatus::active,
                'average_vehicle_rating' => fake()->numberBetween(4, 5),
            ]);
            $driver->vehicles()->attach($activeVehicle);

            // Create one in_progress vehicle
            $inProgressVehicle = Vehicle::factory()->create([
                'global_status' => VehicleStatus::in_progress,
                'average_vehicle_rating' => fake()->numberBetween(3, 5),
            ]);
            $driver->vehicles()->attach($inProgressVehicle);
        });
    }
}
