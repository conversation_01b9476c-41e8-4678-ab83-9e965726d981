<?php

namespace Database\Factories;

use App\Models\Area;
use Illuminate\Database\Eloquent\Factories\Factory;

class AreaFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Area::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // List of cities
        $libyanCities = [
            [
                'name_en' => 'Tripoli',
                'name_ar' => 'طرابلس',
                'lat' => 32.8752,
                'lng' => 13.1875,
            ],
            [
                'name_en' => 'Benghazi',
                'name_ar' => 'بنغازي',
                'lat' => 32.1167,
                'lng' => 20.0667,
            ],
            [
                'name_en' => 'Misrata',
                'name_ar' => 'مصراطة',
                'lat' => 32.3754,
                'lng' => 15.0925,
            ],
            [
                'name_en' => 'Zawiya',
                'name_ar' => 'زاوية',
                'lat' => 32.7624,
                'lng' => 12.7322,
            ],
            [
                'name_en' => 'Sirte',
                'name_ar' => 'سرت',
                'lat' => 31.2026,
                'lng' => 16.5880,
            ],
            [
                'name_en' => 'Sebha',
                'name_ar' => 'سبها',
                'lat' => 27.0377,
                'lng' => 14.4283,
            ],
            [
                'name_en' => 'Derna',
                'name_ar' => 'درنة',
                'lat' => 32.7648,
                'lng' => 22.6394,
            ],
            [
                'name_en' => 'Ajdabiya',
                'name_ar' => 'الاجدابية',
                'lat' => 30.7554,
                'lng' => 20.2263,
            ],
        ];

        // Get a random city and remove it from the array to ensure it's unique
        $city = $this->faker->randomElement($libyanCities);
        $key = array_search($city, $libyanCities);
        unset($libyanCities[$key]);

        // Generate polygon for the city
        $polygon = json_encode([
            ['lat' => $city['lat'], 'lng' => $city['lng']],
            ['lat' => $city['lat'] + 0.01, 'lng' => $city['lng'] + 0.01],
            ['lat' => $city['lat'] + 0.01, 'lng' => $city['lng'] - 0.01],
            ['lat' => $city['lat'] - 0.01, 'lng' => $city['lng'] - 0.01],
        ]);

        // Return the generated area data
        return [
            'name_en' => $city['name_en'],
            'name_ar' => $city['name_ar'],
            'polygon' => $polygon,
            'is_active' => $this->faker->boolean(),
            'base_fare' => $this->faker->randomFloat(2, 5, 20),
            'distance_fare' => $this->faker->randomFloat(2, 1, 10),
            'base_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'distance_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'base_fare_adjustment' => $this->faker->randomFloat(2, 0, 5),
            'distance_fare_adjustment' => $this->faker->randomFloat(2, 0, 5),
        ];
    }
}
