<?php

namespace Database\Factories;

use App\Models\DriverAvailability;
use Illuminate\Database\Eloquent\Factories\Factory;

class DriverAvailabilityFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = DriverAvailability::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startTime = fake()->dateTimeThisMonth();
        $endTime = (clone $startTime)->modify('+'.fake()->numberBetween(1, 8).' hours');

        return [
            'status' => fake()->randomElement(['available', 'busy', 'off-duty']),
            'start_time' => $startTime->format('H:i:s'),
            'end_time' => $endTime->format('H:i:s'),
            'notes' => fake()->sentence(10),
            'driver_id' => \App\Models\Driver::factory(),
        ];
    }
}
