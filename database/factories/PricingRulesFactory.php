<?php

namespace Database\Factories;

use App\Models\PricingRules;  // Use correct model namespace
use Illuminate\Database\Eloquent\Factories\Factory;

class PricingRulesFactory extends Factory
{
    protected $model = PricingRules::class;

    public function definition(): array
    {
        return [
            'global_base_price' => 5.00,
            'global_price_per_km' => 5.00,
            'time_threshold_percentage' => 20.00,
        ];
    }
}
