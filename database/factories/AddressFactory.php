<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->faker->numberBetween(1, 10),
            'address' => $this->faker->streetAddress,
            'full_address' => $this->faker->address,
            'postal_address' => json_encode([
                'city' => $this->faker->city,
                'state' => $this->faker->state,
                'postal_code' => $this->faker->postcode,
            ]),
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'is_favorite' => $this->faker->boolean(50),
        ];
    }
}
