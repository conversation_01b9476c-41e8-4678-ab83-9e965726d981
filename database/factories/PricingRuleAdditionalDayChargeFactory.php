<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class PricingRuleAdditionalDayChargeFactory extends Factory
{
    protected $model = \App\Models\PricingRuleAdditionalDayCharge::class;

    public function definition(): array
    {
        return [
            'pricing_rule_id' => \App\Models\PricingRules::factory(),
            'day' => $this->faker->dayOfWeek,
            'day_start_at' => '08:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'fixed',
            'day_fixed_charge' => $this->faker->randomFloat(2, 1, 20),
            'day_percentage_charge' => 0.00,
            'night_start_at' => '18:01',
            'night_end_at' => '23:59',
            'night_charge_type' => 'percentage',
            'night_fixed_charge' => 0.00,
            'night_percentage_charge' => $this->faker->randomFloat(2, 0, 100),
        ];
    }
}
