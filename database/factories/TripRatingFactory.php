<?php

namespace Database\Factories;

use App\Models\Trip;
use App\Models\TripRating;
use Illuminate\Database\Eloquent\Factories\Factory;

class TripRatingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = TripRating::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Get a completed trip to ensure we're only rating completed trips
        $trip = Trip::where('status', 'completed')->inRandomOrder()->first();

        // If no completed trips exist, create one
        if (! $trip) {
            $trip = Trip::factory()->create([
                'status' => 'completed',
            ]);
        }

        $riderToDriverRating = fake()->numberBetween(1, 5);
        $driverToRiderRating = fake()->numberBetween(1, 5);
        $riderToCarRating = fake()->numberBetween(1, 5);

        return [
            'trip_id' => $trip->id,
            'rider_id' => $trip->rider_id,
            'driver_id' => $trip->driver_id,

            'rider_to_driver_rating' => $riderToDriverRating,
            'rider_review' => $this->generateReview($riderToDriverRating),
            'rider_to_car_rating' => $riderToCarRating,

            'driver_to_rider_rating' => $driverToRiderRating,

            'driver_review' => fake()->randomElement([
                'The rider was polite and easy to communicate with.',
                'The rider was on time and respectful.',
                'Great passenger, would drive again!',
                'The rider was okay, but a bit difficult to manage.',
                'The rider caused issues during the trip.',
                null,
            ]),

            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    /**
     * Generate a review based on the rating.
     */
    private function generateReview(int $rating): ?string
    {
        $positiveReviews = [
            'Absolutely fantastic experience!',
            'The trip was smooth and enjoyable.',
            "Couldn't have asked for a better ride.",
            'The driver was excellent and very professional.',
            'I had a wonderful time, highly recommend!',
        ];

        $neutralReviews = [
            'It was okay, nothing special.',
            'The trip was fine, but could use some improvements.',
            'Average experience overall.',
            'The ride was satisfactory.',
        ];

        $negativeReviews = [
            'The trip was not great, had some issues.',
            'Disappointed with the service.',
            'The ride was uncomfortable and not enjoyable.',
            'There were delays and the driver was unprofessional.',
        ];

        return match ($rating) {
            5 => fake()->randomElement($positiveReviews),
            3 => fake()->randomElement($neutralReviews),
            1, 2 => fake()->randomElement($negativeReviews),
            default => null,
        };
    }
}
