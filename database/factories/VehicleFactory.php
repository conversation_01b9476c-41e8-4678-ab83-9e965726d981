<?php

namespace Database\Factories;

use App\Models\Driver;
use App\Models\Vehicle;
use App\Models\VehicleModel;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleFactory extends Factory
{
    protected $model = Vehicle::class;

    public function definition(): array
    {
        return [
            // (color)::text = ANY ((ARRAY['White'::character varying, 'Yellow'::character varying, 'Blue'::character varying, 'Gray'::character varying, 'Black'::character varying, 'Red'::character varying, 'Orange'::character varying, 'Brown'::character varying, 'Beige'::character varying])::text[])
            'year' => fake()->numberBetween(2000, now()->year),
            // 'color' => fake()->randomElement([
            //     'White', 'Yellow', 'Blue', 'Gray', 'Black', 'Red', 'Orange', 'Brown', 'Beige',
            // ]),
            'license_plate_number' => fake()->regexify('[1-9]-[0-9]{5}-ليبيا'),
            // 'insurance_company' => fake()->randomElement([
            //     'شركة ليبيا للتأمين', 'شركة الشمال للتأمين', 'شركة أفريقيا للتأمين',
            // ]),
            // 'insurance_policy_number' => fake()->bothify('POL###-####'),
            // 'insurance_expiry_date' => fake()->dateTimeBetween('+1 month', '+2 years')->format('Y-m-d'),
            // 'registration_expiry_date' => fake()->dateTimeBetween('+6 months', '+1 year')->format('Y-m-d'),
            'vehicle_model_id' => VehicleModel::inRandomOrder()->first()?->id ?? VehicleModel::factory(),
            // 'vehicle_type_id' => \App\Models\VehicleType::factory(),

            // 'description' => fake()->sentence(10),
            'image' => fake()->imageUrl(640, 480, 'car'),
            'seat_number' => fake()->numberBetween(2, 8),
            // 'luggage_capacity' => fake()->numberBetween(100, 1000),
            'is_female_only' => fake()->boolean(),
        ];
    }

    public function withDriver(): Factory
    {
        return $this->afterCreating(function (Vehicle $vehicle) {
            $driver = Driver::factory()->create();
            $vehicle->drivers()->attach($driver);
        });
    }

    public function active(): Factory
    {
        return $this->state(fn () => [
            'global_status' => \App\Enums\Vehicles\VehicleStatus::active,
        ]);
    }

    public function inProgress(): Factory
    {
        return $this->state(fn () => [
            'global_status' => \App\Enums\Vehicles\VehicleStatus::in_progress,
        ]);
    }

    public function pending(): Factory
    {
        return $this->state(fn () => [
            'global_status' => \App\Enums\Vehicles\VehicleStatus::pending,
        ]);
    }
}
