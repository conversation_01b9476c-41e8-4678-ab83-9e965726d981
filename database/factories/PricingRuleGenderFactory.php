<?php

namespace Database\Factories;

use App\Models\PricingRuleGender;
use Illuminate\Database\Eloquent\Factories\Factory;

class PricingRuleGenderFactory extends Factory
{
    protected $model = PricingRuleGender::class;

    public function definition(): array
    {
        return [
            'base_fare_adjustment_type' => $this->faker->randomElement(['fixed', 'percentage']),
            'base_fare_fixed' => $this->faker->randomFloat(2, 1, 100),
            'base_fare_percentage' => $this->faker->randomFloat(2, 0, 100),
            'distance_fare_adjustment_type' => $this->faker->randomElement(['fixed', 'percentage']),
            'distance_fare_fixed' => $this->faker->randomFloat(2, 0.5, 10),
            'distance_fare_percentage' => $this->faker->randomFloat(2, 0, 100),
        ];
    }
}
