<?php

namespace Database\Factories;

use App\Models\PricingRuleSeatNumber;
use Illuminate\Database\Eloquent\Factories\Factory;

class PricingRuleSeatNumberFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PricingRuleSeatNumber::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $seatData = [
            2 => [
                'base_fare' => 15.00,
                'distance_fare' => 3.00,
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'fixed',
                'base_fare_adjustment' => 5.00,
                'distance_fare_adjustment' => 1.00,
            ],
            4 => [
                'base_fare' => 20.00,
                'distance_fare' => 4.50,
                'base_fare_adjustment_type' => 'percentage',
                'distance_fare_adjustment_type' => 'percentage',
                'base_fare_adjustment' => 10.00,
                'distance_fare_adjustment' => 10.00,
            ],
            6 => [
                'base_fare' => 25.00,
                'distance_fare' => 5.00,
                'base_fare_adjustment_type' => 'fixed',
                'distance_fare_adjustment_type' => 'percentage',
                'base_fare_adjustment' => 7.50,
                'distance_fare_adjustment' => 15.00,
            ],
        ];

        $seatNumber = $this->faker->randomElement([2, 4, 6]);

        return $seatData[$seatNumber];
    }
}
