<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PricingRulePeakHour>
 */
class PricingRulePeakHourFactory extends Factory
{
    protected $model = \App\Models\PricingRulePeakHour::class;

    public function definition(): array
    {
        return [
            'day_charge_id' => \App\Models\PricingRuleAdditionalDayCharge::factory(),
            'peak_start_at' => $this->faker->time('H:i'),
            'peak_end_at' => $this->faker->time('H:i'),
            'base_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'base_fare_fixed' => $this->faker->randomFloat(2, 1, 10),
            'base_fare_percentage' => $this->faker->randomFloat(2, 1, 10),
            'distance_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'distance_fare_fixed' => $this->faker->randomFloat(2, 1, 10),
            'distance_fare_percentage' => $this->faker->randomFloat(2, 1, 10),
        ];
    }
}
