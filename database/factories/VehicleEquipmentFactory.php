<?php

namespace Database\Factories;

use App\Models\VehicleEquipment;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleEquipmentFactory extends Factory
{
    protected $model = VehicleEquipment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $equipments = [
            'Baby Seat' => 'مقعد طفل',
            'GPS Navigation' => 'نظام ملاحة GPS',
            'Wheelchair Accessibility' => 'إمكانية الوصول لذوي الكراسي المتحركة',
            'Roof Rack' => 'حامل سقف',
            'All-Weather Tires' => 'إطارات لجميع الأحوال الجوية',
            'Dash Cam' => 'كاميرا لوحة القيادة',
            'Heated Seats' => 'مقاعد مدفأة',
            'Bluetooth Connectivity' => 'اتصال بلوتوث',
            'Large Luggage Capacity' => 'سعة أمتعة كبيرة',
            'Phone Charger' => 'شاحن هاتف',
        ];

        $randomEquipment = $this->faker->unique()->randomElement(array_keys($equipments));

        return [
            'name_en' => substr($randomEquipment, 0, 30),
            'name_ar' => substr($equipments[$randomEquipment], 0, 30),
            'icon' => null,
            'status' => $this->faker->boolean(),
            'additional_fare' => $this->faker->numberBetween(10, 50),
        ];

    }
}
