<?php

namespace Database\Factories;

use App\Models\VehicleType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VehicleType>
 */
class VehicleTypeFactory extends Factory
{
    protected $model = VehicleType::class;

    public function definition(): array
    {
        return [
            'name_en' => $nameEn = $this->faker->randomElement([
                'Economy', 'Comfort', 'Luxury', 'Freight Vehicle',
            ]),
            'name_ar' => match ($nameEn) {
                'Economy' => 'الاقتصادي',
                'Comfort' => 'الراحة',
                'Luxury' => 'الفاخر',
                'Freight Vehicle' => 'شاحنة',
            },

            'image' => $this->faker->imageUrl(640, 480, 'transport'),
            'description_en' => match ($nameEn) {
                'Economy' => 'Affordable and efficient',
                'Comfort' => 'Spacious and relaxing',
                'Luxury' => 'Premium and elegant',
                'Freight Vehicle' => 'Large cargo space',
            },
            'description_ar' => match ($nameEn) {
                'Economy' => 'فعالة ومناسبة التكلفة',
                'Comfort' => 'واسعة ومريحة',
                'Luxury' => 'راقية وفاخرة',
                'Freight Vehicle' => 'مساحة شحن واسعة',
            },
            // pricing
            'base_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'distance_fare_adjustment_type' => $this->faker->randomElement(['percentage', 'fixed']),
            'base_fare_adjustment' => 0,
            'distance_fare_adjustment' => 0,
            'additional_base_fare' => 0,
            'additional_price_per_km' => 0,
        ];
    }

    public function passenger(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => \App\Enums\VehicleTypesCategories::Passenger,
                'name_en' => $this->faker->randomElement(['Economy', 'Comfort', 'Luxury']),
                'is_covered' => null,
                'weight_category' => null,
            ];
        });
    }

    public function freight(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => \App\Enums\VehicleTypesCategories::Freight,
                'name_en' => 'Freight Vehicle',
                'is_covered' => $this->faker->boolean(),
                'weight_category' => $this->faker->randomElement(\App\Enums\WeightCategoryEnum::cases()),
            ];
        });
    }
}
