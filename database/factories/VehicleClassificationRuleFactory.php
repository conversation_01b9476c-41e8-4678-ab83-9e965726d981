<?php

namespace Database\Factories;

use App\Enums\VehicleTypesCategories;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleType;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleClassificationRuleFactory extends Factory
{
    protected $model = VehicleClassificationRule::class;

    public function definition(): array
    {
        return [
            'vehicle_type_id' => VehicleType::factory(),
            'category' => $this->faker->randomElement(VehicleTypesCategories::cases()),
        ];
    }

    public function passenger(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => VehicleTypesCategories::Passenger,
                'vehicle_type_id' => VehicleType::factory()->passenger(),
            ];
        });
    }

    public function freight(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'category' => VehicleTypesCategories::Freight,
                'vehicle_type_id' => VehicleType::factory()->freight(),
            ];
        });
    }
}
