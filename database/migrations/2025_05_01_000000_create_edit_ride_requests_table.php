<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('edit_ride_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('trip_id')->constrained('trips')->onDelete('cascade')->unique();

            // Location details
            $table->text('polyline')->nullable();
            $table->decimal('departure_lat', 10, 8);
            $table->decimal('departure_lng', 10, 8);
            $table->decimal('arrival_lat', 10, 8);
            $table->decimal('arrival_lng', 10, 8);
            $table->string('departure_address')->nullable();
            $table->string('arrival_address')->nullable();

            // Area references
            $table->integer('departure_area_id')->nullable();
            $table->integer('arrival_area_id')->nullable();

            // Trip metrics
            $table->decimal('distance', 10, 2);
            $table->integer('estimated_duration')->nullable();

            $table->json('previous_pricing_data')->nullable();

            // New calculated values
            $table->json('new_pricing')->nullable();

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('edit_ride_requests');
    }
};
