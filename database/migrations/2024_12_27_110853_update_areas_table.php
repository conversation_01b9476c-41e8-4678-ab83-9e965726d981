<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            $table->renameColumn('name', 'name_en');
            $table->string('name_ar')->after('name_en');
            $table->decimal('base_fare', 8, 2)->nullable();

            // Remove unnecessary columns
            $table->dropColumn([
                'departure_base_fare',
                'departure_price_per_km',
                'departure_surge_multiplier',
                'departure_night_fare_multiplier',
                'arrival_base_fare',
                'arrival_price_per_km',
                'arrival_surge_multiplier',
                'arrival_night_fare_multiplier',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            // Drop new columns
            $table->dropColumn('name_ar');
            $table->dropColumn('polygon');
            $table->dropColumn('is_active');

            // Re-add removed columns
            $table->decimal('departure_base_fare', 8, 2)->nullable();
            $table->decimal('departure_price_per_km', 8, 2)->nullable();
            $table->decimal('departure_surge_multiplier', 8, 2)->nullable();
            $table->decimal('departure_night_fare_multiplier', 8, 2)->nullable();
            $table->decimal('arrival_base_fare', 8, 2)->nullable();
            $table->decimal('arrival_price_per_km', 8, 2)->nullable();
            $table->decimal('arrival_surge_multiplier', 8, 2)->nullable();
            $table->decimal('arrival_night_fare_multiplier', 8, 2)->nullable();
        });
    }
};
