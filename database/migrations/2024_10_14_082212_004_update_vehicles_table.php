<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table
                ->bigInteger('vehicle_model_id')
                ->unsigned()
                ->after('id');
            $table->year('year')->after('vehicle_model_id');
            $table
                ->enum('color', [
                    'White',
                    'Yellow',
                    'Blue',
                    'Gray',
                    'Black',
                    'Red',
                    'Orange',
                    'Brown',
                    'Beige',
                ])
                ->after('year');
            $table
                ->string('license_plate_number')
                ->unique()
                ->after('color');
            $table
                ->string('insurance_company')
                ->nullable()
                ->after('license_plate_number');
            $table
                ->string('insurance_policy_number')
                ->nullable()
                ->after('insurance_company');
            $table
                ->date('insurance_expiry_date')
                ->nullable()
                ->after('insurance_policy_number');
            $table
                ->date('registration_expiry_date')
                ->nullable()
                ->after('insurance_expiry_date');
            $table
                ->foreign('vehicle_model_id')
                ->references('id')
                ->on('vehicle_models')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('vehicle_model_id');
            $table->dropColumn('year');
            $table->dropColumn('color');
            $table->dropColumn('license_plate_number');
            $table->dropColumn('insurance_company');
            $table->dropColumn('insurance_policy_number');
            $table->dropColumn('insurance_expiry_date');
            $table->dropColumn('registration_expiry_date');
            $table->dropForeign('vehicles_vehicle_model_id_foreign');
        });
    }
};
