<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->timestamp('last_heartbeat')->nullable();
        });
        Schema::table('riders', function (Blueprint $table) {
            $table->timestamp('last_heartbeat')->nullable();
        });
    }

    public function down()
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn('last_heartbeat');
        });
        Schema::table('riders', function (Blueprint $table) {
            $table->dropColumn('last_heartbeat');
        });
    }
};
