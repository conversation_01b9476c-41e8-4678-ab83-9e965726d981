<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('zones', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->json('polygon');
            $table->bigInteger('city_id')->unsigned();
            $table->boolean('is_active');
            $table->decimal('departure_base_fare');
            $table->decimal('departure_price_per_km');
            $table->decimal('departure_surge_multiplier');
            $table->decimal('departure_night_fare_multiplier');
            $table->decimal('arrival_base_fare');
            $table->decimal('arrival_price_per_km');
            $table->decimal('arrival_surge_multiplier');
            $table->decimal('arrival_night_fare_multiplier');
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();

            $table
                ->foreign('city_id')
                ->references('id')
                ->on('cities')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('zones');
    }
};
