<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_rules', function (Blueprint $table) {
            $table->id();
            $table->decimal('global_base_price', 10, 2);
            $table->decimal('global_price_per_km', 10, 2);
            $table->timestamps();
        });

        Schema::create('pricing_rule_additional_day_charges', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pricing_rule_id')->constrained()->cascadeOnDelete();
            $table->enum('day', [
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
                'Sunday',
            ]);
            $table->time('day_start_at');
            $table->time('day_end_at');
            $table->enum('day_charge_type', [
                'percentage',
                'fixed',
            ]);
            $table->decimal('day_charge', 10, 2)->nullable();
            $table->time('night_start_at');
            $table->time('night_end_at');
            $table->enum('night_charge_type', [
                'percentage',
                'fixed',
            ]);
            $table->decimal('night_charge', 10, 2)->nullable();
            $table->timestamps();
        });

        Schema::create('pricing_rule_peak_hours', function (Blueprint $table) {
            $table->id();
            $table->foreignId('day_charge_id')->constrained('pricing_rule_additional_day_charges')->cascadeOnDelete();
            $table->time('peak_start_at');
            $table->time('peak_end_at');
            $table->enum('peak_charge_type', [
                'percentage',
                'fixed',
            ]);
            $table->decimal('peak_charge', 10, 2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_rule_peak_hours');
        Schema::dropIfExists('pricing_rule_additional_day_charges');
        Schema::dropIfExists('pricing_rules');
    }
};
