<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_locations', function (Blueprint $table) {
            $table->id();
            $table->string('address')->nullable();
            $table->decimal('departure_lat', 9, 6)->nullable();
            $table->decimal('departure_lng', 9, 6)->nullable();
            $table->decimal('arrival_lat', 9, 6)->nullable();
            $table->decimal('arrival_lng', 9, 6)->nullable();
            $table->foreignId('trip_id')->nullable()->constrained('trips');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('trip_locations');
    }
};
