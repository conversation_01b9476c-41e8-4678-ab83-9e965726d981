<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameZoneColumnsToAreaInTripsTable extends Migration
{
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->renameColumn('departure_zone_id', 'departure_area_id');
            $table->renameColumn('arrival_zone_id', 'arrival_area_id');
        });
    }

    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->renameColumn('departure_area_id', 'departure_zone_id');
            $table->renameColumn('arrival_area_id', 'arrival_zone_id');
        });
    }
}
