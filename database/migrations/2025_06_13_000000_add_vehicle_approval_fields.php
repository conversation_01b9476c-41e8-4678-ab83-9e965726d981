<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->text('rejection_reason')->nullable()->after('global_status');
            $table->json('rejection_reason_columns')->nullable()->after('rejection_reason');
            $table->string('previous_global_status')->nullable()->after('rejection_reason_columns');
            $table->text('notes')->nullable()->after('previous_global_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn([
                'rejection_reason',
                'rejection_reason_columns',
                'previous_global_status',
                'notes'
            ]);
        });
    }
};
