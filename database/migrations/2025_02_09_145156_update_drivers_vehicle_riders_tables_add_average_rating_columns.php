<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('riders', function (Blueprint $table) {
            $table->tinyInteger('average_given_rating')
                ->after('global_status')
                ->nullable();
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->tinyInteger('average_driver_rating')
                ->after('global_status')
                ->nullable();
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->tinyInteger('average_vehicle_rating')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('riders', function (Blueprint $table) {
            $table->dropColumn('average_given_rating');
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn('average_driver_rating');
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('average_vehicle_rating');
        });
    }
};
