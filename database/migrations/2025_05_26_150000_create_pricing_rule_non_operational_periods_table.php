<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_rule_non_operational_periods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('day_charge_id')->constrained('pricing_rule_additional_day_charges')->cascadeOnDelete();
            $table->time('start_at');
            $table->time('end_at');
            $table->timestamps();

            // Add index for better performance
            $table->index(['day_charge_id', 'start_at', 'end_at']);
        });

        // Drop columns from pricing_rule_additional_day_charges if they exist
        if (Schema::hasColumn('pricing_rule_additional_day_charges', 'non_operational_start_at')) {
            Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
                $table->dropColumn(['non_operational_start_at', 'non_operational_end_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add the dropped columns to pricing_rule_additional_day_charges
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            $table->time('non_operational_start_at')->nullable();
            $table->time('non_operational_end_at')->nullable();
        });

        Schema::dropIfExists('pricing_rule_non_operational_periods');
    }
};
