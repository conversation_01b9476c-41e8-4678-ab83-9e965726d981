<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Drop the 'reason' column to remove enum constraint
            $table->dropColumn('reason');
        });

        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Re-add it as a string
            $table->string('reason')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Drop the string version
            $table->dropColumn('reason');
        });

        Schema::table('trip_cancellations', function (Blueprint $table) {
            // Re-add it as enum
            $table->enum('reason', [
                'driver_delay',
                'info_mismatch',
                'other',
            ])->nullable();
        });
    }
};
