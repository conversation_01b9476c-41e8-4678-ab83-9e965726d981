<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('trips', function (Blueprint $table) {
            $table
                ->enum('status', [
                    'pending',
                    'accepted',
                    'in_progress',
                    'completed',
                    'canceled',
                ])
                ->after('id');
            $table
                ->bigInteger('vehicle_id')
                ->unsigned()
                ->after('driver_id');
            $table
                ->bigInteger('departure_zone_id')
                ->unsigned()
                ->after('vehicle_id');
            $table
                ->bigInteger('arrival_zone_id')
                ->unsigned()
                ->after('departure_zone_id');
            $table->decimal('departure_lat', 10)->after('arrival_zone_id');
            $table->decimal('departure_lng', 10)->after('departure_lat');
            $table->decimal('arrival_lat', 10)->after('departure_lng');
            $table->decimal('arrival_lng', 10)->after('arrival_lat');
            $table->decimal('distance', 8)->after('arrival_lng');
            $table
                ->dateTime('estimated_departure_time')
                ->nullable()
                ->after('distance');
            $table
                ->dateTime('actual_departure_time')
                ->nullable()
                ->after('estimated_departure_time');
            $table
                ->dateTime('estimated_arrival_time')
                ->nullable()
                ->after('actual_departure_time');
            $table
                ->dateTime('actual_arrival_time')
                ->nullable()
                ->after('estimated_arrival_time');
            $table
                ->json('pricing_breakdown')
                ->nullable()
                ->after('actual_arrival_time');
            $table
                ->foreign('vehicle_id')
                ->references('id')
                ->on('vehicles')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('departure_zone_id')
                ->references('id')
                ->on('zones')
                ->onDelete('cascade')
                ->onUpdate('cascade');
            $table
                ->foreign('arrival_zone_id')
                ->references('id')
                ->on('zones')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropColumn('status');
            $table->dropColumn('vehicle_id');
            $table->dropColumn('departure_zone_id');
            $table->dropColumn('arrival_zone_id');
            $table->dropColumn('departure_lat');
            $table->dropColumn('departure_lng');
            $table->dropColumn('arrival_lat');
            $table->dropColumn('arrival_lng');
            $table->dropColumn('distance');
            $table->dropColumn('estimated_departure_time');
            $table->dropColumn('actual_departure_time');
            $table->dropColumn('estimated_arrival_time');
            $table->dropColumn('actual_arrival_time');
            $table->dropColumn('pricing_breakdown');
            $table->dropForeign('trips_vehicle_id_foreign');
            $table->dropForeign('trips_departure_zone_id_foreign');
            $table->dropForeign('trips_arrival_zone_id_foreign');
        });
    }
};
