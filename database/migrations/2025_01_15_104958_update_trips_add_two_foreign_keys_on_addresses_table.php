<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->unsignedBigInteger('departure_address_id')->nullable();
            $table->unsignedBigInteger('arrival_address_id')->nullable();

            $table->foreign('departure_address_id')->references('id')
                ->on('addresses')->onDelete('cascade');
            $table->foreign('arrival_address_id')->references('id')
                ->on('addresses')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropForeign(['departure_address_id']);
            $table->dropForeign(['arrival_address_id']);

            $table->dropColumn(['departure_address_id', 'arrival_address_id']);
        });
    }
};
