<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_rule_peak_hours', function (Blueprint $table) {
            $table->dropColumn(['peak_charge_type', 'peak_charge']);
            $table->enum('base_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('base_fare_fixed', 10, 2)->default(0);
            $table->decimal('base_fare_percentage', 10, 2)->default(0);
            $table->enum('distance_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('distance_fare_fixed', 10, 2)->default(0);
            $table->decimal('distance_fare_percentage', 10, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_rule_peak_hours', function (Blueprint $table) {
            $table->dropColumn(['base_fare_adjustment_type', 'base_fare_fixed', 'base_fare_percentage', 'distance_fare_adjustment_type', 'distance_fare_fixed', 'distance_fare_percentage']);
            $table->enum('peak_charge_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('peak_charge', 10, 2)->default(0);
        });
    }
};
