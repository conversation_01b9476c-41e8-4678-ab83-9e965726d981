<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            // Add separate columns for day charges
            $table->decimal('day_fixed_charge', 10, 2)->default(0)->after('day_end_at');
            $table->decimal('day_percentage_charge', 10, 2)->default(0)->after('day_fixed_charge');

            // Add separate columns for night charges
            $table->decimal('night_fixed_charge', 10, 2)->default(0)->after('night_end_at');
            $table->decimal('night_percentage_charge', 10, 2)->default(0)->after('night_fixed_charge');

            // Add non-operational hours columns
            $table->time('non_operational_start_at')->nullable()->after('night_percentage_charge');
            $table->time('non_operational_end_at')->nullable()->after('non_operational_start_at');

            // Drop old unified charge columns
            $table->dropColumn(['day_charge', 'night_charge']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            // Restore old unified charge columns
            $table->decimal('day_charge', 10, 2)->nullable()->after('day_end_at');
            $table->decimal('night_charge', 10, 2)->nullable()->after('night_end_at');

            // Drop the newly added separate charge columns and non-operational hours
            $table->dropColumn([
                'day_fixed_charge',
                'day_percentage_charge',
                'night_fixed_charge',
                'night_percentage_charge',
                'non_operational_start_at',
                'non_operational_end_at',
            ]);
        });
    }
};
