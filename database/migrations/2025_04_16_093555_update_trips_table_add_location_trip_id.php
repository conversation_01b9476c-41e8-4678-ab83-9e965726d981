<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->foreignId('trip_location_id')->nullable()->constrained('trip_locations')->onDelete('set null');
            $table->dropForeign(['departure_address_id']);
            $table->dropForeign(['arrival_address_id']);
            $table->dropColumn(['departure_address_id', 'arrival_address_id']);
        });
    }

    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropForeign(['trip_location_id']);
            $table->dropColumn('trip_location_id');
            $table->foreignId('departure_address_id')->nullable()->constrained('addresses')->onDelete('set null');
            $table->foreignId('arrival_address_id')->nullable()->constrained('addresses')->onDelete('set null');
        });
    }
};
