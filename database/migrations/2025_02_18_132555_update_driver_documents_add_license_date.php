<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn('license_expiry');
        });

        Schema::table('driver_documents', function (Blueprint $table) {
            $table->date('license_expiry')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_documents', function (Blueprint $table) {
            $table->dropColumn('license_expiry');
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->date('license_expiry')->nullable();
        });
    }
};
