<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->decimal('amount');
            $table->decimal('driver_payout');
            $table->decimal('platform_commission');
            $table->decimal('taxes');
            $table->enum('payment_method', ['cash', 'card', 'wallet']);
            $table->enum('payment_status', [
                'pending',
                'completed',
                'failed',
                'refunded',
            ]);
            $table->string('transaction_id')->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payments');
    }
};
