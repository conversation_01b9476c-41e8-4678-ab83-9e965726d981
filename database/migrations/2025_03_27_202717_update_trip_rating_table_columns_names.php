<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('trip_ratings');

        Schema::create('trip_ratings', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('trip_id')->unsigned();
            $table->bigInteger('rider_id')->unsigned();
            $table->bigInteger('driver_id')->unsigned();

            $table->tinyInteger('rider_to_driver_rating');
            $table->tinyInteger('rider_to_car_rating');
            $table->text('rider_review')->nullable();
            $table->text('driver_review')->nullable();
            $table->tinyInteger('driver_to_rider_rating');

            $table->timestamps();

            $table->foreign('trip_id')->references('id')->on('trips')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('rider_id')->references('id')->on('riders')->onDelete('cascade')->onUpdate('cascade');
            $table->foreign('driver_id')->references('id')->on('drivers')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('trip_ratings');
    }
};
