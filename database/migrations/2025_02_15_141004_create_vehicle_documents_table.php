<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vehicle_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained('vehicles')->onDelete('cascade');

            $table->date('insurance_expiry')->nullable();
            $table->date('technical_inspection_expiry')->nullable();
            $table->date('roaming_permit_expiry')->nullable();

            $table->string('insurance')->nullable();
            $table->string('technical_inspection')->nullable();
            $table->string('roaming_permit')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicle_documents', function (Blueprint $table) {
            $table->dropForeign(['vehicle_id']);
        });

        Schema::dropIfExists('vehicle_documents');
    }
};
