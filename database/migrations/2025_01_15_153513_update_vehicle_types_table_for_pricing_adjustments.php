<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->decimal('additional_base_fare', 10, 2)->default(0)->change();
            $table->decimal('additional_price_per_km', 10, 2)->default(0)->change();
            $table->enum('base_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->enum('distance_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('base_fare_adjustment', 10, 2)->default(0);
            $table->decimal('distance_fare_adjustment', 10, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->dropColumn([
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type',
                'base_fare_adjustment',
                'distance_fare_adjustment',
            ]);
        });
    }
};
