<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->renameColumn('national_id_number', 'id_number');
            $table->dropColumn('id_card');
            $table->dropColumn('driver_license');
            $table->dropColumn('license_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->renameColumn('id_number', 'national_id_number');
            $table->string('id_card')->nullable();
            $table->string('driver_license')->nullable();
            $table->string('license_number')->nullable();
        });
    }
};
