<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            // Ensure base_fare is always present (not nullable)
            $table->decimal('base_fare', 10, 2)->default(0)->change();
            $table->decimal('distance_fare', 10, 2)->default(0);
            $table->enum('base_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->enum('distance_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('base_fare_adjustment', 10, 2)->default(0);
            $table->decimal('distance_fare_adjustment', 10, 2)->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('areas', function (Blueprint $table) {
            $table->dropColumn([
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type',
                'base_fare_adjustment',
                'distance_fare_adjustment',
            ]);
        });
    }
};
