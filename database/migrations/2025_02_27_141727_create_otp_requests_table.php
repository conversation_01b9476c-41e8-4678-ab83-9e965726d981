<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('otp_requests', function (Blueprint $table) {
            $table->id();
            $table->string('identifier'); // email or phone number
            $table->string('type'); // 'verify_email' or 'patch'
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('otp_requests');
    }
};
