<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_vehicle_type', function (Blueprint $table) {
            $table->string('vehicle_category')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_vehicle_type', function (Blueprint $table) {
            $table->dropColumn('vehicle_category');
        });
    }
};
