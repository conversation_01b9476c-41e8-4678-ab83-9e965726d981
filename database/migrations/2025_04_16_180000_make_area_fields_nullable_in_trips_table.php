<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Make departure_area_id and arrival_area_id nullable
            $table->unsignedBigInteger('departure_area_id')->nullable()->change();
            $table->unsignedBigInteger('arrival_area_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            // Make departure_area_id and arrival_area_id required again
            $table->unsignedBigInteger('departure_area_id')->nullable(false)->change();
            $table->unsignedBigInteger('arrival_area_id')->nullable(false)->change();
        });
    }
};
