<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('driver_documents', function (Blueprint $table) {
            $table->renameColumn('cin_front', 'document_front');
            $table->renameColumn('cin_back', 'document_back');
            $table->renameColumn('licence_front', 'license_front');
            $table->renameColumn('licence_back', 'license_back');
            $table->dropColumn('registration_card');
            $table->dropColumn('insurance');
            $table->string('document_type')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('driver_documents', function (Blueprint $table) {
            $table->renameColumn('document_front', 'cin_front');
            $table->renameColumn('document_back', 'cin_back');
            $table->string('registration_card')->nullable();
            $table->string('insurance')->nullable();
            $table->dropColumn('document_type');
        });
    }
};
