<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_ratings', function (Blueprint $table) {
            $table->boolean('traffic_lights_exceeded')->default(false);
            $table->boolean('speed_exceeded')->default(false);
            $table->enum('driver_behavior', ['excellent', 'good', 'poor'])->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_ratings', function (Blueprint $table) {
            $table->dropColumn('traffic_lights_exceeded');
            $table->dropColumn('speed_exceeded');
            $table->dropColumn('driver_behavior');
        });
    }
};
