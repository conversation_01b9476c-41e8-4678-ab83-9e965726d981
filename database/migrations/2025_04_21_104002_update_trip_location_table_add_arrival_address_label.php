<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_locations', function (Blueprint $table) {
            $table->renameColumn('address', 'departure_address');
            $table->string('arrival_address')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_locations', function (Blueprint $table) {
            $table->renameColumn('departure_address', 'address');
            $table->dropColumn('arrival_address');
        });
    }
};
