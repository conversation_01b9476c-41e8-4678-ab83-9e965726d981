<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('address');
        });

        Schema::table('trips', function (Blueprint $table) {
            $table->dropColumn(['departure_lat', 'departure_lng', 'arrival_lat', 'arrival_lng']);
        });
    }

    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('address')->nullable();
        });

        Schema::table('trips', function (Blueprint $table) {
            $table->decimal('departure_lat', 10, 8)->nullable();
            $table->decimal('departure_lng', 11, 8)->nullable();
            $table->decimal('arrival_lat', 10, 8)->nullable();
            $table->decimal('arrival_lng', 11, 8)->nullable();
        });
    }
};
