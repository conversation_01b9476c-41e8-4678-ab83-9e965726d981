<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateVehicleTable extends Migration
{
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->text('description')->nullable()->after('id');
            $table->string('image', 255)->nullable()->after('description');
            $table->unsignedInteger('seat_number')->default(0)->after('image');
            $table->integer('luggage_capacity')->nullable()->after('seat_number');
            $table->boolean('is_female_only')->default(false)->after('luggage_capacity');
        });
    }

    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn([
                'description',
                'image',
                'seat_number',
                'luggage_capacity',
                'is_female_only',
            ]);
        });
    }
}
