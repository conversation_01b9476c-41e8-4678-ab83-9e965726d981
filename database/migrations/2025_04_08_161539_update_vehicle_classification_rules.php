<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        // First modify the existing table to remove the fields that will be in the repeater
        Schema::table('vehicle_classification_rules', function (Blueprint $table) {
            $table->dropColumn([
                'brands',
                'models',
                'min_year',
                'max_year',
                'seat_numbers',
                'is_covered',
                'weight_category',
            ]);
        });

        // Create the new qualifications table
        Schema::create('vehicle_classification_qualifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rule_id')->constrained('vehicle_classification_rules')->cascadeOnDelete();
            $table->integer('min_year')->nullable();
            $table->integer('max_year')->nullable();
            $table->text('brands')->nullable(); // JSON array of brand IDs
            $table->text('models')->nullable(); // JSON array of model IDs

            // Passenger-specific fields
            $table->text('seat_numbers')->nullable(); // JSON array of allowed seat numbers

            // Freight-specific fields
            $table->boolean('is_covered')->nullable();
            $table->string('weight_category')->nullable(); // less than 1000kg, more than 1000kg

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('vehicle_classification_qualifications');

        // Add back the columns if rolling back
        Schema::table('vehicle_classification_rules', function (Blueprint $table) {
            $table->text('brands')->nullable();
            $table->text('models')->nullable();
            $table->integer('min_year')->nullable();
            $table->integer('max_year')->nullable();
            $table->text('seat_numbers')->nullable();
            $table->boolean('is_covered')->nullable();
            $table->string('weight_category')->nullable();
        });
    }
};
