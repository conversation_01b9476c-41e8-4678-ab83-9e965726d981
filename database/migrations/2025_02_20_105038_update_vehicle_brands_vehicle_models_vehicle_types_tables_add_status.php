<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicle_brands', function (Blueprint $table) {
            $table->boolean('status')->default(1)
                ->after('name_ar');
        });
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->boolean('status')->default(1);
        });
        Schema::table('vehicle_models', function (Blueprint $table) {
            $table->boolean('status')->default(1)
                ->after('name_ar');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_brands');
        Schema::dropIfExists('vehicle_types');
        Schema::dropIfExists('vehicle_models');
    }
};
