<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('edit_ride_requests', function (Blueprint $table) {
            $table->dropColumn('estimated_duration');
        });
    
        Schema::table('edit_ride_requests', function (Blueprint $table) {
            $table->timestamp('estimated_duration');
        });
    }
    
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
