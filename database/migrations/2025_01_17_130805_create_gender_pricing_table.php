<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_rule_genders', function (Blueprint $table) {
            $table->id();
            $table->enum('gender', ['male', 'female']);
            $table->enum('base_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('base_fare_fixed', 10, 2)->default(0);
            $table->decimal('base_fare_percentage', 5, 2)->default(0);
            $table->enum('distance_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('distance_fare_fixed', 10, 2)->default(0);
            $table->decimal('distance_fare_percentage', 5, 2)->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('gender_pricing');
    }
};
