<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('description');
            $table->dropColumn('luggage_capacity');
            $table->dropColumn('insurance_policy_number');
            $table->dropColumn('insurance_company');
            $table->dropColumn('insurance_expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->text('description')->nullable();
            $table->integer('luggage_capacity')->nullable();
            $table->string('insurance_policy_number', 255)->nullable();
            $table->string('insurance_company', 255)->nullable();
            $table->date('insurance_expiry_date')->nullable();
        });
    }
};
