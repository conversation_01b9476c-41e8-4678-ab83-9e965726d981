<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('preference_vehicle_equipments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('rider_preferences_id')->constrained('rider_preferences')->onDelete('cascade');
            $table->foreignId('vehicle_equipment_id')->constrained('vehicle_equipment')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('preference_vehicle_equipments');
    }
};
