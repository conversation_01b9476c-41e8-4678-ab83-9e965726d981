<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('color'); // Drop old column
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->enum('color', [
                'white', 'black', 'beige', 'blue', 'bronze', 'brown',
                'dark grey', 'gold', 'gray', 'green', 'light blue',
                'maroon', 'navy blue', 'orange', 'pearl', 'pearl white',
                'pink', 'purple', 'silver', 'sky blue', 'wine',
                'wine red', 'yellow',
            ])->nullable();
        });
    }

    public function down()
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('color');
            $table->string('color')->nullable(); // Restore as string if rolled back
        });
    }
};
