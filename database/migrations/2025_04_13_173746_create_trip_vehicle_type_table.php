<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('trip_vehicle_type', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('trip_id')->nullable();

            $table->smallInteger('seat_number')->nullable();
            $table->string('vehicle_equipments')->nullable();

            $table->boolean('is_covered')->nullable();
            $table->string('weight_category')->nullable();

            $table->foreign('trip_id')
                ->references('id')
                ->on('trips')
                ->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_vehicle_type', function (Blueprint $table) {
            $table->dropForeign(['trip_id']);
        });
        Schema::dropIfExists('trip_vehicle_type');
    }
};
