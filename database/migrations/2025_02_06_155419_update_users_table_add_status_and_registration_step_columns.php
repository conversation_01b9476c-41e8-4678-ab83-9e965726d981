<?php

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\RiderGlobalStatus;
use App\Enums\UserStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('riders', function (Blueprint $table) {
            $table->string('global_status')
                ->default(RiderGlobalStatus::pending->value)
                ->after('id');
        });
        Schema::table('drivers', function (Blueprint $table) {

            $table->string('global_status')
                ->default(DriverGlobalStatus::pending->value)
                ->after('id');
        });
        Schema::table('users', function (Blueprint $table) {
            $table->string('status')->default(UserStatus::ONLINE->value);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('riders', function (Blueprint $table) {
            $table->dropColumn('global_status');
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn(['global_status', 'registration_step']);
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
