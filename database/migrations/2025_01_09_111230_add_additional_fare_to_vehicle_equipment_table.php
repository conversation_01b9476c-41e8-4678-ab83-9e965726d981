<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicle_equipment', function (Blueprint $table) {
            $table->decimal('additional_fare', 10, 2)->nullable()->after('name_ar');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicle_equipment', function (Blueprint $table) {
            $table->dropColumn('additional_fare');
        });
    }
};
