<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('vehicle_classification_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_type_id')->constrained('vehicle_types');
            $table->string('category'); // passenger or freight from enum
            $table->text('brands')->nullable(); // JSON array of brand IDs
            $table->text('models')->nullable(); // JSON array of model IDs
            $table->integer('min_year')->nullable();
            $table->integer('max_year')->nullable();

            // Passenger-specific fields
            $table->text('seat_numbers')->nullable(); // JSON array of allowed seat numbers

            // Freight-specific fields
            $table->boolean('is_covered')->nullable();
            $table->string('weight_category')->nullable(); // less than 1000kg, more than 1000kg

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('vehicle_classification_rules');
    }
};
