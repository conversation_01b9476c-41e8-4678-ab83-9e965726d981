<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('driver_availabilities', function (Blueprint $table) {
            $table
                ->bigInteger('driver_id')
                ->unsigned()
                ->after('id');
            $table
                ->enum('status', ['available', 'busy', 'off-duty'])
                ->after('driver_id');
            $table->time('start_time')->after('status');
            $table->time('end_time')->after('start_time');
            $table
                ->string('notes')
                ->nullable()
                ->after('end_time');
            $table
                ->foreign('driver_id')
                ->references('id')
                ->on('drivers')
                ->onDelete('cascade')
                ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('driver_availabilities', function (Blueprint $table) {
            $table->dropColumn('driver_id');
            $table->dropColumn('status');
            $table->dropColumn('start_time');
            $table->dropColumn('end_time');
            $table->dropColumn('notes');
            $table->dropForeign('driver_availabilities_driver_id_foreign');
        });
    }
};
