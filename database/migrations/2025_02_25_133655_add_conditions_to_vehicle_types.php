<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->boolean('is_covered')->default(true); // true = Covered, false = Open
            $table->enum('weight_category', ['less_than_1000kg', 'more_than_1000kg'])->default('less_than_1000kg');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->dropColumn(['is_covered', 'weight_category']);
        });
    }
};
