<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // update the average rating columns in related tables
        Schema::table('riders', function (Blueprint $table) {
            $table->decimal('average_rider_rating', 3, 2)->nullable()->change();
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->decimal('average_driver_rating', 3, 2)->nullable()->change();
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->decimal('average_vehicle_rating', 3, 2)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // revert the average rating columns in related tables
        Schema::table('riders', function (Blueprint $table) {
            $table->tinyInteger('average_rider_rating')->nullable()->change();
        });

        Schema::table('drivers', function (Blueprint $table) {
            $table->tinyInteger('average_driver_rating')->nullable()->change();
        });

        Schema::table('vehicles', function (Blueprint $table) {
            $table->tinyInteger('average_vehicle_rating')->nullable()->change();
        });
    }
};
