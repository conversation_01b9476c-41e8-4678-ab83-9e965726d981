<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->unsignedBigInteger('address_label_id')->nullable();
            $table
                ->foreign('address_label_id')->nullable()
                ->references('id')
                ->on('address_label')
                ->onDelete('cascade')
                ->after('is_favorite');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropForeign(['address_label_id']);

            $table->dropColumn('address_label_id');
        });
    }
};
