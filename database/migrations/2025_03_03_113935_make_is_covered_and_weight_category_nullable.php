<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            // Drop existing columns
            $table->dropColumn(['is_covered', 'weight_category']);
        });

        Schema::table('vehicle_types', function (Blueprint $table) {
            // Recreate columns with nullable modifier
            $table->boolean('is_covered')->nullable()->default(true);
            $table->enum('weight_category', ['less_than_1000kg', 'more_than_1000kg'])->nullable()->default('less_than_1000kg');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            // Drop the nullable columns
            $table->dropColumn(['is_covered', 'weight_category']);
        });

        Schema::table('vehicle_types', function (Blueprint $table) {
            // Recreate the columns with non-nullable and default values
            $table->boolean('is_covered')->default(true);
            $table->enum('weight_category', ['less_than_1000kg', 'more_than_1000kg'])->default('less_than_1000kg');
        });
    }
};
