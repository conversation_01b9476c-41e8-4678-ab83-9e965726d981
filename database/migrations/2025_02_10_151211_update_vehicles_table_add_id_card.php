<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('drivers', function (Blueprint $table) {
            if (! Schema::hasColumn('drivers', 'id_card')) {
                $table->string('id_card')->nullable()->after('license_expiry');
            }
            if (! Schema::hasColumn('drivers', 'driver_license')) {
                $table->string('driver_license')->nullable()->after('id_card');
            }
        });
    }

    public function down()
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn(['id_card', 'driver_license']);
        });
    }
};
