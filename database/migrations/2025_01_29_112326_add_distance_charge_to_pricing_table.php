<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            $table->enum('day_distance_charge_type', ['fixed', 'percentage'])->default('fixed');
            $table->decimal('day_distance_fixed_charge', 8, 2)->default(0);
            $table->decimal('day_distance_percentage_charge', 5, 2)->default(0);
            $table->enum('night_distance_charge_type', ['fixed', 'percentage'])->default('fixed');
            $table->decimal('night_distance_fixed_charge', 8, 2)->default(0);
            $table->decimal('night_distance_percentage_charge', 5, 2)->default(0);
        });
    }

    public function down()
    {
        Schema::table('pricing', function (Blueprint $table) {
            $table->dropColumn(['day_distance_charge_type', 'day_distance_fixed_charge', 'day_distance_percentage_charge', 'night_distance_charge_type', 'night_distance_fixed_charge', 'night_distance_percentage_charge']);
        });
    }
};
