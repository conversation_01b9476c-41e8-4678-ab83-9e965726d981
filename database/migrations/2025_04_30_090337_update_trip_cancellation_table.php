<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            $table->string('reason')->change();
        });
    }

    public function down(): void
    {
        Schema::table('trip_cancellations', function (Blueprint $table) {
            $table->enum('reason', [
                'driver_delay',
                'info_mismatch',
                'other',
            ])->change();
        });
    }
};
