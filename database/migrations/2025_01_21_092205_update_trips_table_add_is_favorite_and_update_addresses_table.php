<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->boolean('is_favorite')->default(false);
        });

        Schema::table('addresses', callback: function (Blueprint $table) {
            $table->dropForeign(['address_label_id']);

            $table
                ->foreign('address_label_id')
                ->references('id')
                ->on('address_label')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropColumn('is_favorite');

        });

        Schema::table('addresses', function (Blueprint $table) {
            $table->dropForeign(['address_label_id']);

            $table
                ->foreign('address_label_id')
                ->references('id')
                ->on('address_label')
                ->onDelete('cascade');
        });
    }
};
