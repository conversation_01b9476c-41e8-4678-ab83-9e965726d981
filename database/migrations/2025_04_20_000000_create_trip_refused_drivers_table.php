<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('trip_refused_drivers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('trip_id');
            $table->unsignedBigInteger('driver_id');
            $table->string('reason')->nullable();
            $table->timestamps();

            // Explicit foreign key constraints
            $table->foreign('trip_id')
                ->references('id')
                ->on('trips')
                ->onDelete('cascade');

            $table->foreign('driver_id')
                ->references('id')
                ->on('drivers')
                ->onDelete('cascade');

            // Prevent duplicate refusals
            $table->unique(['trip_id', 'driver_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('trip_refused_drivers');
    }
};
