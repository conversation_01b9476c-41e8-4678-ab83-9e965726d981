<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->string('previous_global_status')->nullable()->after('global_status');
        });
        Schema::table('riders', function (Blueprint $table) {
            $table->string('previous_global_status')->nullable()->after('global_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drivers', function (Blueprint $table) {
            $table->dropColumn('previous_global_status');
        });

        Schema::table('riders', function (Blueprint $table) {
            $table->dropColumn('previous_global_status');
        });
    }
};
