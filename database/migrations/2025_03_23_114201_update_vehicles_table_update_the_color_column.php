<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropColumn('color');
            $table->string('color')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vehicles', function (Blueprint $table) {
            $table->enum('color', [
                'white', 'black', 'red', 'beige', 'blue', 'bronze', 'brown',
                'dark grey', 'gold', 'gray', 'green', 'light blue',
                'maroon', 'navy blue', 'orange', 'pearl', 'pearl white',
                'pink', 'purple', 'silver', 'sky blue', 'wine',
                'wine red', 'yellow',
            ])->change();
        });
    }
};
