<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_rule_seat_numbers', function (Blueprint $table) {
            $table->id();
            $table->enum('seats_number', [2, 4, 6]);
            $table->decimal('base_fare', 10, 2)->default(0);
            $table->decimal('distance_fare', 10, 2)->default(0);
            $table->enum('base_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->enum('distance_fare_adjustment_type', ['fixed', 'percentage'])->nullable();
            $table->decimal('base_fare_adjustment', 10, 2)->default(0);
            $table->decimal('distance_fare_adjustment', 10, 2)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_rule_seat_numbers');
    }
};
