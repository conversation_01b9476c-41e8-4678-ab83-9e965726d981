<?php

use App\Enums\UserStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('vehicles', 'global_status')) {
            Schema::table('vehicles', function (Blueprint $table) {
                $table->string('global_status')->default('pending')->change();
            });
        } else {
            Schema::table('vehicles', function (Blueprint $table) {
                $table->string('global_status')->default('pending');
            });
        }

        if (Schema::hasColumn('vehicles', 'status')) {
            Schema::table('vehicles', function (Blueprint $table) {
                $table->string('status')->default(UserStatus::ONLINE->value)->change();
            });
        } else {
            Schema::table('vehicles', function (Blueprint $table) {
                $table->string('status')->default(UserStatus::ONLINE->value);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Proper down method to revert changes
        // If you want to revert to original defaults:
        Schema::table('vehicles', function (Blueprint $table) {
            if (Schema::hasColumn('vehicles', 'global_status')) {
                $table->string('global_status')->default(null)->change();
            }

            if (Schema::hasColumn('vehicles', 'status')) {
                $table->string('status')->default(null)->change();
            }
        });
    }
};
