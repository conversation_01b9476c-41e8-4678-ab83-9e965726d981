<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            $table->time('night_start_at')->nullable()->change();
            $table->time('night_end_at')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('pricing_rule_additional_day_charges', function (Blueprint $table) {
            $table->time('night_start_at')->nullable(false)->change();
            $table->time('night_end_at')->nullable(false)->change();
        });
    }
};
