<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->string('name')->after('id');
            $table->text('description')->after('name');
            $table->string('image')->nullable()->after('description');
            $table->decimal('additional_base_fare')->after('image');
            $table->decimal('additional_price_per_km')->after('additional_base_fare');
            $table->unsignedInteger('seat_number')->after('additional_price_per_km')
                ->comment('Number of seats available in the vehicle');
            $table->boolean('is_female_only')->default(false)->after('seat_number')
                ->comment('Indicates if the vehicle is restricted to female passengers only');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('vehicle_types', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropColumn('description');
            $table->dropColumn('image');
            $table->dropColumn('additional_base_fare');
            $table->dropColumn('additional_price_per_km');
        });
    }
};
