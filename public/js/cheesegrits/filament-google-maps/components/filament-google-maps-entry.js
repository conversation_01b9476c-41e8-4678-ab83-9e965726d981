function filamentGoogleMapsField({state,defaultLocation,controls,layers,defaultZoom,gmaps,mapEl,drawingField,geoJson,geoJsonField,geoJsonProperty,geoJsonVisible}){return{state,map:null,geocoder:null,marker:null,markerLocation:null,layers:null,symbols:{"%n":["street_number"],"%z":["postal_code"],"%S":["street_address","route"],"%A1":["administrative_area_level_1"],"%A2":["administrative_area_level_2"],"%A3":["administrative_area_level_3"],"%A4":["administrative_area_level_4"],"%A5":["administrative_area_level_5"],"%a1":["administrative_area_level_1"],"%a2":["administrative_area_level_2"],"%a3":["administrative_area_level_3"],"%a4":["administrative_area_level_4"],"%a5":["administrative_area_level_5"],"%L":["locality","postal_town"],"%D":["sublocality"],"%C":["country"],"%c":["country"],"%p":["premise"],"%P":["premise"]},drawingManager:null,overlays:[],dataLayer:null,geoJsonDataLayer:null,loadGMaps:function(){if(document.getElementById("filament-google-maps-google-maps-entry-js")){let waitForGlobal=function(key,callback){window[key]?callback():setTimeout(function(){waitForGlobal(key,callback)},100)};waitForGlobal("filamentGoogleMapsAPILoaded",function(){this.createMap()}.bind(this))}else{let script=document.createElement("script");script.id="filament-google-maps-google-maps-entry-js",window.filamentGoogleMapsAsyncLoad=this.createMap.bind(this),script.src=gmaps+"&callback=filamentGoogleMapsAsyncLoad",document.head.appendChild(script)}},init:function(){this.loadGMaps()},createMap:function(){window.filamentGoogleMapsAPILoaded=!0,this.map=new google.maps.Map(mapEl,{center:this.getCoordinates(),zoom:defaultZoom,...controls}),this.marker=new google.maps.Marker({map:this.map}),this.marker.setPosition(this.getCoordinates()),layers&&(this.layers=layers.map(layerUrl=>{new google.maps.KmlLayer({url:layerUrl,map:this.map}).addListener("click",kmlEvent=>{let text=kmlEvent.featureData.description})})),geoJson&&(geoJsonVisible?this.geoJsonDataLayer=this.map.data:this.geoJsonDataLayer=new google.maps.Data,/^http/.test(geoJson)?this.geoJsonDataLayer.loadGeoJson(geoJson):this.geoJsonDataLayer.addGeoJson(JSON.parse(geoJson)))},getCoordinates:function(){return(this.state===null||!this.state.hasOwnProperty("lat"))&&(this.state={lat:defaultLocation.lat,lng:defaultLocation.lng}),this.state},instanceOverlay:function(feature){var instance=null;switch(feature.properties.type){case google.maps.drawing.OverlayType.MARKER:instance=new google.maps.Marker({id:feature.properties.id,type:feature.properties.type,position:new google.maps.LatLng(feature.geometry.coordinates[1],feature.geometry.coordinates[0]),draggable:!0});break;case google.maps.drawing.OverlayType.RECTANGLE:var NE=new google.maps.LatLng(feature.geometry.coordinates[0][2][1],feature.geometry.coordinates[0][2][0]),SW=new google.maps.LatLng(feature.geometry.coordinates[0][0][1],feature.geometry.coordinates[0][0][0]);instance=new google.maps.Rectangle(Object.assign({},this.polyOptions,{id:feature.properties.id,type:feature.properties.type,bounds:new google.maps.LatLngBounds(SW,NE),editable:!1}));break;case google.maps.drawing.OverlayType.POLYGON:instance=new google.maps.Polygon(Object.assign({},this.polyOptions,{id:feature.properties.id,type:feature.properties.type,paths:this.transformToMVCArray(feature.geometry.coordinates),editable:!1}));break;case google.maps.drawing.OverlayType.POLYLINE:instance=new google.maps.Polyline({id:feature.properties.id,type:feature.properties.type,path:this.transformToMVCArray([feature.geometry.coordinates]).getAt(0),draggable:!0,editable:!1});break;case google.maps.drawing.OverlayType.CIRCLE:instance=new google.maps.Circle(Object.assign({},this.polyOptions,{id:feature.properties.id,type:feature.properties.type,center:new google.maps.LatLng(feature.geometry.coordinates[1],feature.geometry.coordinates[0]),radius:feature.properties.radius,editable:!1}));break}return instance},instanceFeature:function(overlay){var calculatedOverlay=this.calculateGeometry(overlay);return this.dataLayer.add(new google.maps.Data.Feature({geometry:calculatedOverlay.geometry,properties:Object.assign({id:this.guid(),type:overlay.type},calculatedOverlay.hasOwnProperty("properties")?calculatedOverlay.properties:{})}))},calculateGeometry:function(overlay,geometryOnly){switch(overlay.type){case google.maps.drawing.OverlayType.MARKER:return geometryOnly?new google.maps.Data.Point(overlay.getPosition()):{geometry:new google.maps.Data.Point(overlay.getPosition())};case google.maps.drawing.OverlayType.RECTANGLE:let b=overlay.getBounds(),p=[b.getSouthWest(),{lat:b.getSouthWest().lat(),lng:b.getNorthEast().lng()},b.getNorthEast(),{lng:b.getSouthWest().lng(),lat:b.getNorthEast().lat()}];return geometryOnly?new google.maps.Data.Polygon([p]):{geometry:new google.maps.Data.Polygon([p])};case google.maps.drawing.OverlayType.POLYGON:return geometryOnly?new google.maps.Data.Polygon([overlay.getPath().getArray()]):{geometry:new google.maps.Data.Polygon([overlay.getPath().getArray()])};case google.maps.drawing.OverlayType.POLYLINE:return geometryOnly?new google.maps.Data.LineString(overlay.getPath().getArray()):{geometry:new google.maps.Data.LineString(overlay.getPath().getArray())};case google.maps.drawing.OverlayType.CIRCLE:return geometryOnly?new google.maps.Data.Point(overlay.getCenter()):{properties:{radius:overlay.getRadius()},geometry:new google.maps.Data.Point(overlay.getCenter())}}},transformToMVCArray:function(a){let clone=new google.maps.MVCArray;function transform($a,parent){$a.length==2&&!Array.isArray($a[0])&&!Array.isArray($a[1])&&parent.push(new google.maps.LatLng($a[1],$a[0]));for(let a2=0;a2<$a.length;a2++)!Array.isArray($a[a2])||transform($a[a2],parent?$a[a2].length==2&&!Array.isArray($a[a2][0])&&!Array.isArray($a[a2][1])?parent:parent.getAt(parent.push(new google.maps.MVCArray)-1):clone.getAt(clone.push(new google.maps.MVCArray)-1))}function isMVCArray(array){return array instanceof google.maps.MVCArray}return transform(a),clone},loadFeaturesCollection:function(geoJSON){if(Array.isArray(geoJSON.features)&&geoJSON.features.length>0){let bounds=new google.maps.LatLngBounds;for(let f=0;f<geoJSON.features.length;f++){let overlay=this.instanceOverlay(geoJSON.features[f]);overlay.feature=this.instanceFeature(overlay),this.addOverlayEvents(overlay),overlay.feature.getGeometry().forEachLatLng(function(latlng){bounds.extend(latlng)}),overlay.setMap(this.map),this.overlays.push(overlay)}this.map.fitBounds(bounds)}},guid:function(){function s4(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}return s4()+s4()+"-"+s4()+"-"+s4()+"-"+s4()+"-"+s4()+s4()+s4()}}}export{filamentGoogleMapsField as default};
