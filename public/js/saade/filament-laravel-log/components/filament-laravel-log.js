var Re=Object.create;var be=Object.defineProperty;var Te=Object.getOwnPropertyDescriptor;var Ee=Object.getOwnPropertyNames;var _e=Object.getPrototypeOf,Ie=Object.prototype.hasOwnProperty;var $e=(x,k)=>()=>(k||x((k={exports:{}}).exports,k),k.exports);var Oe=(x,k,H,R)=>{if(k&&typeof k=="object"||typeof k=="function")for(let M of Ee(k))!Ie.call(x,M)&&M!==H&&be(x,M,{get:()=>k[M],enumerable:!(R=Te(k,M))||R.enumerable});return x};var Ce=(x,k,H)=>(H=x!=null?Re(_e(x)):{},Oe(k||!x||!x.__esModule?be(H,"default",{value:x,enumerable:!0}):H,x));var Ae=$e((Se,ve)=>{(function(){var x="ace",k=function(){return this}();if(!k&&typeof window<"u"&&(k=window),!x&&typeof requirejs<"u")return;var H=function(t,n,r){if(typeof t!="string"){H.original?H.original.apply(this,arguments):(console.error("dropping module because define wasn't a string."),console.trace());return}arguments.length==2&&(r=n),H.modules[t]||(H.payloads[t]=r,H.modules[t]=null)};H.modules={},H.payloads={};var R=function(t,n,r){if(typeof n=="string"){var e=a(t,n);if(e!=null)return r&&r(),e}else if(Object.prototype.toString.call(n)==="[object Array]"){for(var i=[],o=0,s=n.length;o<s;++o){var c=a(t,n[o]);if(c==null&&M.original)return;i.push(c)}return r&&r.apply(null,i)||!0}},M=function(t,n){var r=R("",t,n);return r==null&&M.original?M.original.apply(this,arguments):r},S=function(t,n){if(n.indexOf("!")!==-1){var r=n.split("!");return S(t,r[0])+"!"+S(t,r[1])}if(n.charAt(0)=="."){var e=t.split("/").slice(0,-1).join("/");for(n=e+"/"+n;n.indexOf(".")!==-1&&i!=n;){var i=n;n=n.replace(/\/\.\//,"/").replace(/[^\/]+\/\.\.\//,"")}}return n},a=function(t,n){n=S(t,n);var r=H.modules[n];if(!r){if(r=H.payloads[n],typeof r=="function"){var e={},i={id:n,uri:"",exports:e,packaged:!0},o=function(c,g){return R(n,c,g)},s=r(o,e,i);e=s||i.exports,H.modules[n]=e,delete H.payloads[n]}r=H.modules[n]=e||r}return r};function l(t){var n=k;t&&(k[t]||(k[t]={}),n=k[t]),(!n.define||!n.define.packaged)&&(H.original=n.define,n.define=H,n.define.packaged=!0),(!n.require||!n.require.packaged)&&(M.original=n.require,n.require=M,n.require.packaged=!0)}l(x)})();ace.define("ace/lib/es6-shim",["require","exports","module"],function(x,k,H){function R(M,S,a){Object.defineProperty(M,S,{value:a,enumerable:!1,writable:!0,configurable:!0})}String.prototype.startsWith||R(String.prototype,"startsWith",function(M,S){return S=S||0,this.lastIndexOf(M,S)===S}),String.prototype.endsWith||R(String.prototype,"endsWith",function(M,S){var a=this;(S===void 0||S>a.length)&&(S=a.length),S-=M.length;var l=a.indexOf(M,S);return l!==-1&&l===S}),String.prototype.repeat||R(String.prototype,"repeat",function(M){for(var S="",a=this;M>0;)M&1&&(S+=a),(M>>=1)&&(a+=a);return S}),String.prototype.includes||R(String.prototype,"includes",function(M,S){return this.indexOf(M,S)!=-1}),Object.assign||(Object.assign=function(M){if(M==null)throw new TypeError("Cannot convert undefined or null to object");for(var S=Object(M),a=1;a<arguments.length;a++){var l=arguments[a];l!=null&&Object.keys(l).forEach(function(t){S[t]=l[t]})}return S}),Object.values||(Object.values=function(M){return Object.keys(M).map(function(S){return M[S]})}),Array.prototype.find||R(Array.prototype,"find",function(M){for(var S=this.length,a=arguments[1],l=0;l<S;l++){var t=this[l];if(M.call(a,t,l,this))return t}}),Array.prototype.findIndex||R(Array.prototype,"findIndex",function(M){for(var S=this.length,a=arguments[1],l=0;l<S;l++){var t=this[l];if(M.call(a,t,l,this))return l}}),Array.prototype.includes||R(Array.prototype,"includes",function(M,S){return this.indexOf(M,S)!=-1}),Array.prototype.fill||R(Array.prototype,"fill",function(M){for(var S=this,a=S.length>>>0,l=arguments[1],t=l>>0,n=t<0?Math.max(a+t,0):Math.min(t,a),r=arguments[2],e=r===void 0?a:r>>0,i=e<0?Math.max(a+e,0):Math.min(e,a);n<i;)S[n]=M,n++;return S}),Array.of||R(Array,"of",function(){return Array.prototype.slice.call(arguments)})});ace.define("ace/lib/fixoldbrowsers",["require","exports","module","ace/lib/es6-shim"],function(x,k,H){"use strict";x("./es6-shim")});ace.define("ace/lib/lang",["require","exports","module"],function(x,k,H){"use strict";k.last=function(S){return S[S.length-1]},k.stringReverse=function(S){return S.split("").reverse().join("")},k.stringRepeat=function(S,a){for(var l="";a>0;)a&1&&(l+=S),(a>>=1)&&(S+=S);return l};var R=/^\s\s*/,M=/\s\s*$/;k.stringTrimLeft=function(S){return S.replace(R,"")},k.stringTrimRight=function(S){return S.replace(M,"")},k.copyObject=function(S){var a={};for(var l in S)a[l]=S[l];return a},k.copyArray=function(S){for(var a=[],l=0,t=S.length;l<t;l++)S[l]&&typeof S[l]=="object"?a[l]=this.copyObject(S[l]):a[l]=S[l];return a},k.deepCopy=function S(a){if(typeof a!="object"||!a)return a;var l;if(Array.isArray(a)){l=[];for(var t=0;t<a.length;t++)l[t]=S(a[t]);return l}if(Object.prototype.toString.call(a)!=="[object Object]")return a;l={};for(var t in a)l[t]=S(a[t]);return l},k.arrayToMap=function(S){for(var a={},l=0;l<S.length;l++)a[S[l]]=1;return a},k.createMap=function(S){var a=Object.create(null);for(var l in S)a[l]=S[l];return a},k.arrayRemove=function(S,a){for(var l=0;l<=S.length;l++)a===S[l]&&S.splice(l,1)},k.escapeRegExp=function(S){return S.replace(/([.*+?^${}()|[\]\/\\])/g,"\\$1")},k.escapeHTML=function(S){return(""+S).replace(/&/g,"&#38;").replace(/"/g,"&#34;").replace(/'/g,"&#39;").replace(/</g,"&#60;")},k.getMatchOffsets=function(S,a){var l=[];return S.replace(a,function(t){l.push({offset:arguments[arguments.length-2],length:t.length})}),l},k.deferredCall=function(S){var a=null,l=function(){a=null,S()},t=function(n){return t.cancel(),a=setTimeout(l,n||0),t};return t.schedule=t,t.call=function(){return this.cancel(),S(),t},t.cancel=function(){return clearTimeout(a),a=null,t},t.isPending=function(){return a},t},k.delayedCall=function(S,a){var l=null,t=function(){l=null,S()},n=function(r){l==null&&(l=setTimeout(t,r||a))};return n.delay=function(r){l&&clearTimeout(l),l=setTimeout(t,r||a)},n.schedule=n,n.call=function(){this.cancel(),S()},n.cancel=function(){l&&clearTimeout(l),l=null},n.isPending=function(){return l},n},k.supportsLookbehind=function(){try{new RegExp("(?<=.)")}catch{return!1}return!0},k.supportsUnicodeFlag=function(){try{new RegExp("^.$","u")}catch{return!1}return!0}});ace.define("ace/lib/useragent",["require","exports","module"],function(x,k,H){"use strict";k.OS={LINUX:"LINUX",MAC:"MAC",WINDOWS:"WINDOWS"},k.getOS=function(){return k.isMac?k.OS.MAC:k.isLinux?k.OS.LINUX:k.OS.WINDOWS};var R=typeof navigator=="object"?navigator:{},M=(/mac|win|linux/i.exec(R.platform)||["other"])[0].toLowerCase(),S=R.userAgent||"",a=R.appName||"";k.isWin=M=="win",k.isMac=M=="mac",k.isLinux=M=="linux",k.isIE=a=="Microsoft Internet Explorer"||a.indexOf("MSAppHost")>=0?parseFloat((S.match(/(?:MSIE |Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/)||[])[1]):parseFloat((S.match(/(?:Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/)||[])[1]),k.isOldIE=k.isIE&&k.isIE<9,k.isGecko=k.isMozilla=S.match(/ Gecko\/\d+/),k.isOpera=typeof opera=="object"&&Object.prototype.toString.call(window.opera)=="[object Opera]",k.isWebKit=parseFloat(S.split("WebKit/")[1])||void 0,k.isChrome=parseFloat(S.split(" Chrome/")[1])||void 0,k.isEdge=parseFloat(S.split(" Edge/")[1])||void 0,k.isAIR=S.indexOf("AdobeAIR")>=0,k.isAndroid=S.indexOf("Android")>=0,k.isChromeOS=S.indexOf(" CrOS ")>=0,k.isIOS=/iPad|iPhone|iPod/.test(S)&&!window.MSStream,k.isIOS&&(k.isMac=!0),k.isMobile=k.isIOS||k.isAndroid});ace.define("ace/lib/dom",["require","exports","module","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("./useragent"),M="http://www.w3.org/1999/xhtml";k.buildDom=function r(e,i,o){if(typeof e=="string"&&e){var s=document.createTextNode(e);return i&&i.appendChild(s),s}if(!Array.isArray(e))return e&&e.appendChild&&i&&i.appendChild(e),e;if(typeof e[0]!="string"||!e[0]){for(var c=[],g=0;g<e.length;g++){var w=r(e[g],i,o);w&&c.push(w)}return c}var p=document.createElement(e[0]),b=e[1],y=1;b&&typeof b=="object"&&!Array.isArray(b)&&(y=2);for(var g=y;g<e.length;g++)r(e[g],p,o);return y==2&&Object.keys(b).forEach(function(d){var v=b[d];d==="class"?p.className=Array.isArray(v)?v.join(" "):v:typeof v=="function"||d=="value"||d[0]=="$"?p[d]=v:d==="ref"?o&&(o[v]=p):d==="style"?typeof v=="string"&&(p.style.cssText=v):v!=null&&p.setAttribute(d,v)}),i&&i.appendChild(p),p},k.getDocumentHead=function(r){return r||(r=document),r.head||r.getElementsByTagName("head")[0]||r.documentElement},k.createElement=function(r,e){return document.createElementNS?document.createElementNS(e||M,r):document.createElement(r)},k.removeChildren=function(r){r.innerHTML=""},k.createTextNode=function(r,e){var i=e?e.ownerDocument:document;return i.createTextNode(r)},k.createFragment=function(r){var e=r?r.ownerDocument:document;return e.createDocumentFragment()},k.hasCssClass=function(r,e){var i=(r.className+"").split(/\s+/g);return i.indexOf(e)!==-1},k.addCssClass=function(r,e){k.hasCssClass(r,e)||(r.className+=" "+e)},k.removeCssClass=function(r,e){for(var i=r.className.split(/\s+/g);;){var o=i.indexOf(e);if(o==-1)break;i.splice(o,1)}r.className=i.join(" ")},k.toggleCssClass=function(r,e){for(var i=r.className.split(/\s+/g),o=!0;;){var s=i.indexOf(e);if(s==-1)break;o=!1,i.splice(s,1)}return o&&i.push(e),r.className=i.join(" "),o},k.setCssClass=function(r,e,i){i?k.addCssClass(r,e):k.removeCssClass(r,e)},k.hasCssString=function(r,e){var i=0,o;if(e=e||document,o=e.querySelectorAll("style")){for(;i<o.length;)if(o[i++].id===r)return!0}},k.removeElementById=function(r,e){e=e||document,e.getElementById(r)&&e.getElementById(r).remove()};var S,a=[];k.useStrictCSP=function(r){S=r,r==!1?l():a||(a=[])};function l(){var r=a;a=null,r&&r.forEach(function(e){t(e[0],e[1])})}function t(r,e,i){if(!(typeof document>"u")){if(a){if(i)l();else if(i===!1)return a.push([r,e])}if(!S){var o=i;!i||!i.getRootNode?o=document:(o=i.getRootNode(),(!o||o==i)&&(o=document));var s=o.ownerDocument||o;if(e&&k.hasCssString(e,o))return null;e&&(r+=`
/*# sourceURL=ace/css/`+e+" */");var c=k.createElement("style");c.appendChild(s.createTextNode(r)),e&&(c.id=e),o==s&&(o=k.getDocumentHead(s)),o.insertBefore(c,o.firstChild)}}}if(k.importCssString=t,k.importCssStylsheet=function(r,e){k.buildDom(["link",{rel:"stylesheet",href:r}],k.getDocumentHead(e))},k.scrollbarWidth=function(r){var e=k.createElement("ace_inner");e.style.width="100%",e.style.minWidth="0px",e.style.height="200px",e.style.display="block";var i=k.createElement("ace_outer"),o=i.style;o.position="absolute",o.left="-10000px",o.overflow="hidden",o.width="200px",o.minWidth="0px",o.height="150px",o.display="block",i.appendChild(e);var s=r&&r.documentElement||document&&document.documentElement;if(!s)return 0;s.appendChild(i);var c=e.offsetWidth;o.overflow="scroll";var g=e.offsetWidth;return c===g&&(g=i.clientWidth),s.removeChild(i),c-g},k.computedStyle=function(r,e){return window.getComputedStyle(r,"")||{}},k.setStyle=function(r,e,i){r[e]!==i&&(r[e]=i)},k.HAS_CSS_ANIMATION=!1,k.HAS_CSS_TRANSFORMS=!1,k.HI_DPI=R.isWin?typeof window<"u"&&window.devicePixelRatio>=1.5:!0,R.isChromeOS&&(k.HI_DPI=!1),typeof document<"u"){var n=document.createElement("div");k.HI_DPI&&n.style.transform!==void 0&&(k.HAS_CSS_TRANSFORMS=!0),!R.isEdge&&typeof n.style.animationName<"u"&&(k.HAS_CSS_ANIMATION=!0),n=null}k.HAS_CSS_TRANSFORMS?k.translate=function(r,e,i){r.style.transform="translate("+Math.round(e)+"px, "+Math.round(i)+"px)"}:k.translate=function(r,e,i){r.style.top=Math.round(i)+"px",r.style.left=Math.round(e)+"px"}});ace.define("ace/lib/net",["require","exports","module","ace/lib/dom"],function(x,k,H){"use strict";var R=x("./dom");k.get=function(M,S){var a=new XMLHttpRequest;a.open("GET",M,!0),a.onreadystatechange=function(){a.readyState===4&&S(a.responseText)},a.send(null)},k.loadScript=function(M,S){var a=R.getDocumentHead(),l=document.createElement("script");l.src=M,a.appendChild(l),l.onload=l.onreadystatechange=function(t,n){(n||!l.readyState||l.readyState=="loaded"||l.readyState=="complete")&&(l=l.onload=l.onreadystatechange=null,n||S())}},k.qualifyURL=function(M){var S=document.createElement("a");return S.href=M,S.href}});ace.define("ace/lib/oop",["require","exports","module"],function(x,k,H){"use strict";k.inherits=function(R,M){R.super_=M,R.prototype=Object.create(M.prototype,{constructor:{value:R,enumerable:!1,writable:!0,configurable:!0}})},k.mixin=function(R,M){for(var S in M)R[S]=M[S];return R},k.implement=function(R,M){k.mixin(R,M)}});ace.define("ace/lib/event_emitter",["require","exports","module"],function(x,k,H){"use strict";var R={},M=function(){this.propagationStopped=!0},S=function(){this.defaultPrevented=!0};R._emit=R._dispatchEvent=function(a,l){this._eventRegistry||(this._eventRegistry={}),this._defaultHandlers||(this._defaultHandlers={});var t=this._eventRegistry[a]||[],n=this._defaultHandlers[a];if(!(!t.length&&!n)){(typeof l!="object"||!l)&&(l={}),l.type||(l.type=a),l.stopPropagation||(l.stopPropagation=M),l.preventDefault||(l.preventDefault=S),t=t.slice();for(var r=0;r<t.length&&(t[r](l,this),!l.propagationStopped);r++);if(n&&!l.defaultPrevented)return n(l,this)}},R._signal=function(a,l){var t=(this._eventRegistry||{})[a];if(t){t=t.slice();for(var n=0;n<t.length;n++)t[n](l,this)}},R.once=function(a,l){var t=this;if(this.on(a,function n(){t.off(a,n),l.apply(null,arguments)}),!l)return new Promise(function(n){l=n})},R.setDefaultHandler=function(a,l){var t=this._defaultHandlers;if(t||(t=this._defaultHandlers={_disabled_:{}}),t[a]){var n=t[a],r=t._disabled_[a];r||(t._disabled_[a]=r=[]),r.push(n);var e=r.indexOf(l);e!=-1&&r.splice(e,1)}t[a]=l},R.removeDefaultHandler=function(a,l){var t=this._defaultHandlers;if(t){var n=t._disabled_[a];if(t[a]==l)n&&this.setDefaultHandler(a,n.pop());else if(n){var r=n.indexOf(l);r!=-1&&n.splice(r,1)}}},R.on=R.addEventListener=function(a,l,t){this._eventRegistry=this._eventRegistry||{};var n=this._eventRegistry[a];return n||(n=this._eventRegistry[a]=[]),n.indexOf(l)==-1&&n[t?"unshift":"push"](l),l},R.off=R.removeListener=R.removeEventListener=function(a,l){this._eventRegistry=this._eventRegistry||{};var t=this._eventRegistry[a];if(t){var n=t.indexOf(l);n!==-1&&t.splice(n,1)}},R.removeAllListeners=function(a){a||(this._eventRegistry=this._defaultHandlers=void 0),this._eventRegistry&&(this._eventRegistry[a]=void 0),this._defaultHandlers&&(this._defaultHandlers[a]=void 0)},k.EventEmitter=R});ace.define("ace/lib/app_config",["require","exports","module","ace/lib/oop","ace/lib/event_emitter"],function(x,k,H){"no use strict";var R=x("./oop"),M=x("./event_emitter").EventEmitter,S={setOptions:function(r){Object.keys(r).forEach(function(e){this.setOption(e,r[e])},this)},getOptions:function(r){var e={};if(r)Array.isArray(r)||(e=r,r=Object.keys(e));else{var i=this.$options;r=Object.keys(i).filter(function(o){return!i[o].hidden})}return r.forEach(function(o){e[o]=this.getOption(o)},this),e},setOption:function(r,e){if(this["$"+r]!==e){var i=this.$options[r];if(!i)return a('misspelled option "'+r+'"');if(i.forwardTo)return this[i.forwardTo]&&this[i.forwardTo].setOption(r,e);i.handlesSet||(this["$"+r]=e),i&&i.set&&i.set.call(this,e)}},getOption:function(r){var e=this.$options[r];return e?e.forwardTo?this[e.forwardTo]&&this[e.forwardTo].getOption(r):e&&e.get?e.get.call(this):this["$"+r]:a('misspelled option "'+r+'"')}};function a(r){typeof console<"u"&&console.warn&&console.warn.apply(console,arguments)}function l(r,e){var i=new Error(r);i.data=e,typeof console=="object"&&console.error&&console.error(i),setTimeout(function(){throw i})}var t,n=function(){function r(){this.$defaultOptions={}}return r.prototype.defineOptions=function(e,i,o){return e.$options||(this.$defaultOptions[i]=e.$options={}),Object.keys(o).forEach(function(s){var c=o[s];typeof c=="string"&&(c={forwardTo:c}),c.name||(c.name=s),e.$options[c.name]=c,"initialValue"in c&&(e["$"+c.name]=c.initialValue)}),R.implement(e,S),this},r.prototype.resetOptions=function(e){Object.keys(e.$options).forEach(function(i){var o=e.$options[i];"value"in o&&e.setOption(i,o.value)})},r.prototype.setDefaultValue=function(e,i,o){if(!e){for(e in this.$defaultOptions)if(this.$defaultOptions[e][i])break;if(!this.$defaultOptions[e][i])return!1}var s=this.$defaultOptions[e]||(this.$defaultOptions[e]={});s[i]&&(s.forwardTo?this.setDefaultValue(s.forwardTo,i,o):s[i].value=o)},r.prototype.setDefaultValues=function(e,i){Object.keys(i).forEach(function(o){this.setDefaultValue(e,o,i[o])},this)},r.prototype.setMessages=function(e){t=e},r.prototype.nls=function(e,i){t&&!t[e]&&a("No message found for '"+e+"' in the provided messages, falling back to default English message.");var o=t&&t[e]||e;return i&&(o=o.replace(/\$(\$|[\d]+)/g,function(s,c){return c=="$"?"$":i[c]})),o},r}();n.prototype.warn=a,n.prototype.reportError=l,R.implement(n.prototype,M),k.AppConfig=n});ace.define("ace/theme/textmate-css",["require","exports","module"],function(x,k,H){H.exports=`.ace-tm .ace_gutter {
  background: #f0f0f0;
  color: #333;
}

.ace-tm .ace_print-margin {
  width: 1px;
  background: #e8e8e8;
}

.ace-tm .ace_fold {
    background-color: #6B72E6;
}

.ace-tm {
  background-color: #FFFFFF;
  color: black;
}

.ace-tm .ace_cursor {
  color: black;
}
        
.ace-tm .ace_invisible {
  color: rgb(191, 191, 191);
}

.ace-tm .ace_storage,
.ace-tm .ace_keyword {
  color: blue;
}

.ace-tm .ace_constant {
  color: rgb(197, 6, 11);
}

.ace-tm .ace_constant.ace_buildin {
  color: rgb(88, 72, 246);
}

.ace-tm .ace_constant.ace_language {
  color: rgb(88, 92, 246);
}

.ace-tm .ace_constant.ace_library {
  color: rgb(6, 150, 14);
}

.ace-tm .ace_invalid {
  background-color: rgba(255, 0, 0, 0.1);
  color: red;
}

.ace-tm .ace_support.ace_function {
  color: rgb(60, 76, 114);
}

.ace-tm .ace_support.ace_constant {
  color: rgb(6, 150, 14);
}

.ace-tm .ace_support.ace_type,
.ace-tm .ace_support.ace_class {
  color: rgb(109, 121, 222);
}

.ace-tm .ace_keyword.ace_operator {
  color: rgb(104, 118, 135);
}

.ace-tm .ace_string {
  color: rgb(3, 106, 7);
}

.ace-tm .ace_comment {
  color: rgb(76, 136, 107);
}

.ace-tm .ace_comment.ace_doc {
  color: rgb(0, 102, 255);
}

.ace-tm .ace_comment.ace_doc.ace_tag {
  color: rgb(128, 159, 191);
}

.ace-tm .ace_constant.ace_numeric {
  color: rgb(0, 0, 205);
}

.ace-tm .ace_variable {
  color: rgb(49, 132, 149);
}

.ace-tm .ace_xml-pe {
  color: rgb(104, 104, 91);
}

.ace-tm .ace_entity.ace_name.ace_function {
  color: #0000A2;
}


.ace-tm .ace_heading {
  color: rgb(12, 7, 255);
}

.ace-tm .ace_list {
  color:rgb(185, 6, 144);
}

.ace-tm .ace_meta.ace_tag {
  color:rgb(0, 22, 142);
}

.ace-tm .ace_string.ace_regex {
  color: rgb(255, 0, 0)
}

.ace-tm .ace_marker-layer .ace_selection {
  background: rgb(181, 213, 255);
}
.ace-tm.ace_multiselect .ace_selection.ace_start {
  box-shadow: 0 0 3px 0px white;
}
.ace-tm .ace_marker-layer .ace_step {
  background: rgb(252, 255, 0);
}

.ace-tm .ace_marker-layer .ace_stack {
  background: rgb(164, 229, 101);
}

.ace-tm .ace_marker-layer .ace_bracket {
  margin: -1px 0 0 -1px;
  border: 1px solid rgb(192, 192, 192);
}

.ace-tm .ace_marker-layer .ace_active-line {
  background: rgba(0, 0, 0, 0.07);
}

.ace-tm .ace_gutter-active-line {
    background-color : #dcdcdc;
}

.ace-tm .ace_marker-layer .ace_selected-word {
  background: rgb(250, 250, 255);
  border: 1px solid rgb(200, 200, 250);
}

.ace-tm .ace_indent-guide {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;
}

.ace-tm .ace_indent-guide-active {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAZSURBVHjaYvj///9/hivKyv8BAAAA//8DACLqBhbvk+/eAAAAAElFTkSuQmCC") right repeat-y;
}
`});ace.define("ace/theme/textmate",["require","exports","module","ace/theme/textmate-css","ace/lib/dom"],function(x,k,H){"use strict";k.isDark=!1,k.cssClass="ace-tm",k.cssText=x("./textmate-css"),k.$id="ace/theme/textmate";var R=x("../lib/dom");R.importCssString(k.cssText,k.cssClass,!1)});ace.define("ace/config",["require","exports","module","ace/lib/lang","ace/lib/net","ace/lib/dom","ace/lib/app_config","ace/theme/textmate"],function(x,k,H){"no use strict";var R=x("./lib/lang"),M=x("./lib/net"),S=x("./lib/dom"),a=x("./lib/app_config").AppConfig;H.exports=k=new a;var l={packaged:!1,workerPath:null,modePath:null,themePath:null,basePath:"",suffix:".js",$moduleUrls:{},loadWorkerFromBlob:!0,sharedPopups:!1,useStrictCSP:null};k.get=function(e){if(!l.hasOwnProperty(e))throw new Error("Unknown config key: "+e);return l[e]},k.set=function(e,i){if(l.hasOwnProperty(e))l[e]=i;else if(this.setDefaultValue("",e,i)==!1)throw new Error("Unknown config key: "+e);e=="useStrictCSP"&&S.useStrictCSP(i)},k.all=function(){return R.copyObject(l)},k.$modes={},k.moduleUrl=function(e,i){if(l.$moduleUrls[e])return l.$moduleUrls[e];var o=e.split("/");i=i||o[o.length-2]||"";var s=i=="snippets"?"/":"-",c=o[o.length-1];if(i=="worker"&&s=="-"){var g=new RegExp("^"+i+"[\\-_]|[\\-_]"+i+"$","g");c=c.replace(g,"")}(!c||c==i)&&o.length>1&&(c=o[o.length-2]);var w=l[i+"Path"];return w==null?w=l.basePath:s=="/"&&(i=s=""),w&&w.slice(-1)!="/"&&(w+="/"),w+i+s+c+this.get("suffix")},k.setModuleUrl=function(e,i){return l.$moduleUrls[e]=i};var t=function(e,i){if(e==="ace/theme/textmate"||e==="./theme/textmate")return i(null,x("./theme/textmate"));if(n)return n(e,i);console.error("loader is not configured")},n;k.setLoader=function(e){n=e},k.dynamicModules=Object.create(null),k.$loading={},k.$loaded={},k.loadModule=function(e,i){var o,s;Array.isArray(e)&&(s=e[0],e=e[1]);var c=function(g){if(g&&!k.$loading[e])return i&&i(g);if(k.$loading[e]||(k.$loading[e]=[]),k.$loading[e].push(i),!(k.$loading[e].length>1)){var w=function(){t(e,function(p,b){b&&(k.$loaded[e]=b),k._emit("load.module",{name:e,module:b});var y=k.$loading[e];k.$loading[e]=null,y.forEach(function(d){d&&d(b)})})};if(!k.get("packaged"))return w();M.loadScript(k.moduleUrl(e,s),w),r()}};if(k.dynamicModules[e])k.dynamicModules[e]().then(function(g){g.default?c(g.default):c(g)});else{try{o=this.$require(e)}catch{}c(o||k.$loaded[e])}},k.$require=function(e){if(typeof H.require=="function"){var i="require";return H[i](e)}},k.setModuleLoader=function(e,i){k.dynamicModules[e]=i};var r=function(){!l.basePath&&!l.workerPath&&!l.modePath&&!l.themePath&&!Object.keys(l.$moduleUrls).length&&(console.error("Unable to infer path to ace from script src,","use ace.config.set('basePath', 'path') to enable dynamic loading of modes and themes","or with webpack use ace/webpack-resolver"),r=function(){})};k.version="1.24.1"});ace.define("ace/loader_build",["require","exports","module","ace/lib/fixoldbrowsers","ace/config"],function(x,k,H){"use strict";x("./lib/fixoldbrowsers");var R=x("./config");R.setLoader(function(l,t){x([l],function(n){t(null,n)})});var M=function(){return this||typeof window<"u"&&window}();H.exports=function(l){R.init=S,R.$require=x,l.require=x,typeof define=="function"&&(l.define=define)},S(!0);function S(l){if(!(!M||!M.document)){R.set("packaged",l||x.packaged||H.packaged||M.define&&define.packaged);var t={},n="",r=document.currentScript||document._currentScript,e=r&&r.ownerDocument||document;r&&r.src&&(n=r.src.split(/[?#]/)[0].split("/").slice(0,-1).join("/")||"");for(var i=e.getElementsByTagName("script"),o=0;o<i.length;o++){var s=i[o],c=s.src||s.getAttribute("src");if(c){for(var g=s.attributes,w=0,p=g.length;w<p;w++){var b=g[w];b.name.indexOf("data-ace-")===0&&(t[a(b.name.replace(/^data-ace-/,""))]=b.value)}var y=c.match(/^(.*)\/ace([\-.]\w+)?\.js(\?|$)/);y&&(n=y[1])}}n&&(t.base=t.base||n,t.packaged=!0),t.basePath=t.base,t.workerPath=t.workerPath||t.base,t.modePath=t.modePath||t.base,t.themePath=t.themePath||t.base,delete t.base;for(var d in t)typeof t[d]<"u"&&R.set(d,t[d])}}function a(l){return l.replace(/-(.)/g,function(t,n){return n.toUpperCase()})}});ace.define("ace/range",["require","exports","module"],function(x,k,H){"use strict";var R=function(S,a){return S.row-a.row||S.column-a.column},M=function(){function S(a,l,t,n){this.start={row:a,column:l},this.end={row:t,column:n}}return S.prototype.isEqual=function(a){return this.start.row===a.start.row&&this.end.row===a.end.row&&this.start.column===a.start.column&&this.end.column===a.end.column},S.prototype.toString=function(){return"Range: ["+this.start.row+"/"+this.start.column+"] -> ["+this.end.row+"/"+this.end.column+"]"},S.prototype.contains=function(a,l){return this.compare(a,l)==0},S.prototype.compareRange=function(a){var l,t=a.end,n=a.start;return l=this.compare(t.row,t.column),l==1?(l=this.compare(n.row,n.column),l==1?2:l==0?1:0):l==-1?-2:(l=this.compare(n.row,n.column),l==-1?-1:l==1?42:0)},S.prototype.comparePoint=function(a){return this.compare(a.row,a.column)},S.prototype.containsRange=function(a){return this.comparePoint(a.start)==0&&this.comparePoint(a.end)==0},S.prototype.intersects=function(a){var l=this.compareRange(a);return l==-1||l==0||l==1},S.prototype.isEnd=function(a,l){return this.end.row==a&&this.end.column==l},S.prototype.isStart=function(a,l){return this.start.row==a&&this.start.column==l},S.prototype.setStart=function(a,l){typeof a=="object"?(this.start.column=a.column,this.start.row=a.row):(this.start.row=a,this.start.column=l)},S.prototype.setEnd=function(a,l){typeof a=="object"?(this.end.column=a.column,this.end.row=a.row):(this.end.row=a,this.end.column=l)},S.prototype.inside=function(a,l){return this.compare(a,l)==0?!(this.isEnd(a,l)||this.isStart(a,l)):!1},S.prototype.insideStart=function(a,l){return this.compare(a,l)==0?!this.isEnd(a,l):!1},S.prototype.insideEnd=function(a,l){return this.compare(a,l)==0?!this.isStart(a,l):!1},S.prototype.compare=function(a,l){return!this.isMultiLine()&&a===this.start.row?l<this.start.column?-1:l>this.end.column?1:0:a<this.start.row?-1:a>this.end.row?1:this.start.row===a?l>=this.start.column?0:-1:this.end.row===a?l<=this.end.column?0:1:0},S.prototype.compareStart=function(a,l){return this.start.row==a&&this.start.column==l?-1:this.compare(a,l)},S.prototype.compareEnd=function(a,l){return this.end.row==a&&this.end.column==l?1:this.compare(a,l)},S.prototype.compareInside=function(a,l){return this.end.row==a&&this.end.column==l?1:this.start.row==a&&this.start.column==l?-1:this.compare(a,l)},S.prototype.clipRows=function(a,l){if(this.end.row>l)var t={row:l+1,column:0};else if(this.end.row<a)var t={row:a,column:0};if(this.start.row>l)var n={row:l+1,column:0};else if(this.start.row<a)var n={row:a,column:0};return S.fromPoints(n||this.start,t||this.end)},S.prototype.extend=function(a,l){var t=this.compare(a,l);if(t==0)return this;if(t==-1)var n={row:a,column:l};else var r={row:a,column:l};return S.fromPoints(n||this.start,r||this.end)},S.prototype.isEmpty=function(){return this.start.row===this.end.row&&this.start.column===this.end.column},S.prototype.isMultiLine=function(){return this.start.row!==this.end.row},S.prototype.clone=function(){return S.fromPoints(this.start,this.end)},S.prototype.collapseRows=function(){return this.end.column==0?new S(this.start.row,0,Math.max(this.start.row,this.end.row-1),0):new S(this.start.row,0,this.end.row,0)},S.prototype.toScreenRange=function(a){var l=a.documentToScreenPosition(this.start),t=a.documentToScreenPosition(this.end);return new S(l.row,l.column,t.row,t.column)},S.prototype.moveBy=function(a,l){this.start.row+=a,this.start.column+=l,this.end.row+=a,this.end.column+=l},S}();M.fromPoints=function(S,a){return new M(S.row,S.column,a.row,a.column)},M.comparePoints=R,M.comparePoints=function(S,a){return S.row-a.row||S.column-a.column},k.Range=M});ace.define("ace/lib/keys",["require","exports","module","ace/lib/oop"],function(x,k,H){"use strict";var R=x("./oop"),M=function(){var S={MODIFIER_KEYS:{16:"Shift",17:"Ctrl",18:"Alt",224:"Meta",91:"MetaLeft",92:"MetaRight",93:"ContextMenu"},KEY_MODS:{ctrl:1,alt:2,option:2,shift:4,super:8,meta:8,command:8,cmd:8,control:1},FUNCTION_KEYS:{8:"Backspace",9:"Tab",13:"Return",19:"Pause",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"Print",45:"Insert",46:"Delete",96:"Numpad0",97:"Numpad1",98:"Numpad2",99:"Numpad3",100:"Numpad4",101:"Numpad5",102:"Numpad6",103:"Numpad7",104:"Numpad8",105:"Numpad9","-13":"NumpadEnter",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"Numlock",145:"Scrolllock"},PRINTABLE_KEYS:{32:" ",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",59:";",61:"=",65:"a",66:"b",67:"c",68:"d",69:"e",70:"f",71:"g",72:"h",73:"i",74:"j",75:"k",76:"l",77:"m",78:"n",79:"o",80:"p",81:"q",82:"r",83:"s",84:"t",85:"u",86:"v",87:"w",88:"x",89:"y",90:"z",107:"+",109:"-",110:".",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",111:"/",106:"*"}};S.PRINTABLE_KEYS[173]="-";var a,l;for(l in S.FUNCTION_KEYS)a=S.FUNCTION_KEYS[l].toLowerCase(),S[a]=parseInt(l,10);for(l in S.PRINTABLE_KEYS)a=S.PRINTABLE_KEYS[l].toLowerCase(),S[a]=parseInt(l,10);return R.mixin(S,S.MODIFIER_KEYS),R.mixin(S,S.PRINTABLE_KEYS),R.mixin(S,S.FUNCTION_KEYS),S.enter=S.return,S.escape=S.esc,S.del=S.delete,function(){for(var t=["cmd","ctrl","alt","shift"],n=Math.pow(2,t.length);n--;)S.KEY_MODS[n]=t.filter(function(r){return n&S.KEY_MODS[r]}).join("-")+"-"}(),S.KEY_MODS[0]="",S.KEY_MODS[-1]="input-",S}();R.mixin(k,M),k.default=k,k.keyCodeToString=function(S){var a=M[S];return typeof a!="string"&&(a=String.fromCharCode(S)),a.toLowerCase()}});ace.define("ace/lib/event",["require","exports","module","ace/lib/keys","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("./keys"),M=x("./useragent"),S=null,a=0,l;function t(){l=!1;try{document.createComment("").addEventListener("test",function(){},{get passive(){l={passive:!1}}})}catch{}}function n(){return l==null&&t(),l}function r(w,p,b){this.elem=w,this.type=p,this.callback=b}r.prototype.destroy=function(){i(this.elem,this.type,this.callback),this.elem=this.type=this.callback=void 0};var e=k.addListener=function(w,p,b,y){w.addEventListener(p,b,n()),y&&y.$toDestroy.push(new r(w,p,b))},i=k.removeListener=function(w,p,b){w.removeEventListener(p,b,n())};k.stopEvent=function(w){return k.stopPropagation(w),k.preventDefault(w),!1},k.stopPropagation=function(w){w.stopPropagation&&w.stopPropagation()},k.preventDefault=function(w){w.preventDefault&&w.preventDefault()},k.getButton=function(w){return w.type=="dblclick"?0:w.type=="contextmenu"||M.isMac&&w.ctrlKey&&!w.altKey&&!w.shiftKey?2:w.button},k.capture=function(w,p,b){var y=w&&w.ownerDocument||document;function d(v){p&&p(v),b&&b(v),i(y,"mousemove",p),i(y,"mouseup",d),i(y,"dragstart",d)}return e(y,"mousemove",p),e(y,"mouseup",d),e(y,"dragstart",d),d},k.addMouseWheelListener=function(w,p,b){e(w,"wheel",function(y){var d=.15,v=y.deltaX||0,$=y.deltaY||0;switch(y.deltaMode){case y.DOM_DELTA_PIXEL:y.wheelX=v*d,y.wheelY=$*d;break;case y.DOM_DELTA_LINE:var f=15;y.wheelX=v*f,y.wheelY=$*f;break;case y.DOM_DELTA_PAGE:var u=150;y.wheelX=v*u,y.wheelY=$*u;break}p(y)},b)},k.addMultiMouseDownListener=function(w,p,b,y,d){var v=0,$,f,u,A={2:"dblclick",3:"tripleclick",4:"quadclick"};function m(h){if(k.getButton(h)!==0?v=0:h.detail>1?(v++,v>4&&(v=1)):v=1,M.isIE){var C=Math.abs(h.clientX-$)>5||Math.abs(h.clientY-f)>5;(!u||C)&&(v=1),u&&clearTimeout(u),u=setTimeout(function(){u=null},p[v-1]||600),v==1&&($=h.clientX,f=h.clientY)}if(h._clicks=v,b[y]("mousedown",h),v>4)v=0;else if(v>1)return b[y](A[v],h)}Array.isArray(w)||(w=[w]),w.forEach(function(h){e(h,"mousedown",m,d)})};var o=function(w){return 0|(w.ctrlKey?1:0)|(w.altKey?2:0)|(w.shiftKey?4:0)|(w.metaKey?8:0)};k.getModifierString=function(w){return R.KEY_MODS[o(w)]};function s(w,p,b){var y=o(p);if(!M.isMac&&S){if(p.getModifierState&&(p.getModifierState("OS")||p.getModifierState("Win"))&&(y|=8),S.altGr)if((3&y)!=3)S.altGr=0;else return;if(b===18||b===17){var d="location"in p?p.location:p.keyLocation;if(b===17&&d===1)S[b]==1&&(a=p.timeStamp);else if(b===18&&y===3&&d===2){var v=p.timeStamp-a;v<50&&(S.altGr=!0)}}}if(b in R.MODIFIER_KEYS&&(b=-1),!y&&b===13){var d="location"in p?p.location:p.keyLocation;if(d===3&&(w(p,y,-b),p.defaultPrevented))return}if(M.isChromeOS&&y&8){if(w(p,y,b),p.defaultPrevented)return;y&=-9}return!y&&!(b in R.FUNCTION_KEYS)&&!(b in R.PRINTABLE_KEYS)?!1:w(p,y,b)}k.addCommandKeyListener=function(w,p,b){if(M.isOldGecko||M.isOpera&&!("KeyboardEvent"in window)){var y=null;e(w,"keydown",function(v){y=v.keyCode},b),e(w,"keypress",function(v){return s(p,v,y)},b)}else{var d=null;e(w,"keydown",function(v){S[v.keyCode]=(S[v.keyCode]||0)+1;var $=s(p,v,v.keyCode);return d=v.defaultPrevented,$},b),e(w,"keypress",function(v){d&&(v.ctrlKey||v.altKey||v.shiftKey||v.metaKey)&&(k.stopEvent(v),d=null)},b),e(w,"keyup",function(v){S[v.keyCode]=null},b),S||(c(),e(window,"focus",c))}};function c(){S=Object.create(null)}if(typeof window=="object"&&window.postMessage&&!M.isOldIE){var g=1;k.nextTick=function(w,p){p=p||window;var b="zero-timeout-message-"+g++,y=function(d){d.data==b&&(k.stopPropagation(d),i(p,"message",y),w())};e(p,"message",y),p.postMessage(b,"*")}}k.$idleBlocked=!1,k.onIdle=function(w,p){return setTimeout(function b(){k.$idleBlocked?setTimeout(b,100):w()},p)},k.$idleBlockId=null,k.blockIdle=function(w){k.$idleBlockId&&clearTimeout(k.$idleBlockId),k.$idleBlocked=!0,k.$idleBlockId=setTimeout(function(){k.$idleBlocked=!1},w||100)},k.nextFrame=typeof window=="object"&&(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame),k.nextFrame?k.nextFrame=k.nextFrame.bind(window):k.nextFrame=function(w){setTimeout(w,17)}});ace.define("ace/clipboard",["require","exports","module"],function(x,k,H){"use strict";var R;H.exports={lineMode:!1,pasteCancelled:function(){return R&&R>Date.now()-50?!0:R=!1},cancel:function(){R=Date.now()}}});ace.define("ace/keyboard/textinput",["require","exports","module","ace/lib/event","ace/config","ace/lib/useragent","ace/lib/dom","ace/lib/lang","ace/clipboard","ace/lib/keys"],function(x,k,H){"use strict";var R=x("../lib/event"),M=x("../config").nls,S=x("../lib/useragent"),a=x("../lib/dom"),l=x("../lib/lang"),t=x("../clipboard"),n=S.isChrome<18,r=S.isIE,e=S.isChrome>63,i=400,o=x("../lib/keys"),s=o.KEY_MODS,c=S.isIOS,g=c?/\s/:/\n/,w=S.isMobile,p=function(b,y){var d=a.createElement("textarea");d.className="ace_text-input",d.setAttribute("wrap","off"),d.setAttribute("autocorrect","off"),d.setAttribute("autocapitalize","off"),d.setAttribute("spellcheck",!1),d.style.opacity="0",b.insertBefore(d,b.firstChild);var v=!1,$=!1,f=!1,u=!1,A="";w||(d.style.fontSize="1px");var m=!1,h=!1,C="",L=0,T=0,E=0,_=Number.MAX_SAFE_INTEGER,O=Number.MIN_SAFE_INTEGER,I=0;try{var W=document.activeElement===d}catch{}this.setNumberOfExtraLines=function(N){if(_=Number.MAX_SAFE_INTEGER,O=Number.MIN_SAFE_INTEGER,N<0){I=0;return}I=N},this.setAriaOptions=function(N){if(N.activeDescendant?(d.setAttribute("aria-haspopup","true"),d.setAttribute("aria-autocomplete",N.inline?"both":"list"),d.setAttribute("aria-activedescendant",N.activeDescendant)):(d.setAttribute("aria-haspopup","false"),d.setAttribute("aria-autocomplete","both"),d.removeAttribute("aria-activedescendant")),N.role&&d.setAttribute("role",N.role),N.setLabel&&(d.setAttribute("aria-roledescription",M("editor")),y.session)){var j=y.session.selection.cursor.row;d.setAttribute("aria-label",M("Cursor at row $0",[j+1]))}},this.setAriaOptions({role:"textbox"}),R.addListener(d,"blur",function(N){h||(y.onBlur(N),W=!1)},y),R.addListener(d,"focus",function(N){if(!h){if(W=!0,S.isEdge)try{if(!document.hasFocus())return}catch{}y.onFocus(N),S.isEdge?setTimeout(D):D()}},y),this.$focusScroll=!1,this.focus=function(){if(this.setAriaOptions({setLabel:y.renderer.enableKeyboardAccessibility}),A||e||this.$focusScroll=="browser")return d.focus({preventScroll:!0});var N=d.style.top;d.style.position="fixed",d.style.top="0px";try{var j=d.getBoundingClientRect().top!=0}catch{return}var X=[];if(j)for(var Z=d.parentElement;Z&&Z.nodeType==1;)X.push(Z),Z.setAttribute("ace_nocontext",!0),!Z.parentElement&&Z.getRootNode?Z=Z.getRootNode().host:Z=Z.parentElement;d.focus({preventScroll:!0}),j&&X.forEach(function(te){te.removeAttribute("ace_nocontext")}),setTimeout(function(){d.style.position="",d.style.top=="0px"&&(d.style.top=N)},0)},this.blur=function(){d.blur()},this.isFocused=function(){return W},y.on("beforeEndOperation",function(){var N=y.curOp,j=N&&N.command&&N.command.name;if(j!="insertstring"){var X=j&&(N.docChanged||N.selectionChanged);f&&X&&(C=d.value="",de()),D()}});var F=function(N,j){for(var X=j,Z=1;Z<=N-_&&Z<2*I+1;Z++)X+=y.session.getLine(N-Z).length+1;return X},D=c?function(N){if(!(!W||v&&!N||u)){N||(N="");var j=`
 ab`+N+`cde fg
`;j!=d.value&&(d.value=C=j);var X=4,Z=4+(N.length||(y.selection.isEmpty()?0:1));(L!=X||T!=Z)&&d.setSelectionRange(X,Z),L=X,T=Z}}:function(){if(!(f||u)&&!(!W&&!G)){f=!0;var N=0,j=0,X="";if(y.session){var Z=y.selection,te=Z.getRange(),oe=Z.cursor.row;oe===O+1?(_=O+1,O=_+2*I):oe===_-1?(O=_-1,_=O-2*I):(oe<_-1||oe>O+1)&&(_=oe>I?oe-I:0,O=oe>I?oe+I:2*I);for(var le=[],se=_;se<=O;se++)le.push(y.session.getLine(se));if(X=le.join(`
`),N=F(te.start.row,te.start.column),j=F(te.end.row,te.end.column),te.start.row<_){var re=y.session.getLine(_-1);N=te.start.row<_-1?0:N,j+=re.length+1,X=re+`
`+X}else if(te.end.row>O){var ie=y.session.getLine(O+1);j=te.end.row>O+1?ie.length:te.end.column,j+=X.length+1,X=X+`
`+ie}else w&&oe>0&&(X=`
`+X,j+=1,N+=1);X.length>i&&(N<i&&j<i?X=X.slice(0,i):(X=`
`,N==j?N=j=0:(N=0,j=1)));var he=X+`

`;he!=C&&(d.value=C=he,L=T=he.length)}if(G&&(L=d.selectionStart,T=d.selectionEnd),T!=j||L!=N||d.selectionEnd!=T)try{d.setSelectionRange(N,j),L=N,T=j}catch{}f=!1}};this.resetSelection=D,W&&y.onFocus();var B=function(N){return N.selectionStart===0&&N.selectionEnd>=C.length&&N.value===C&&C&&N.selectionEnd!==T},U=function(N){f||(v?v=!1:B(d)?(y.selectAll(),D()):w&&d.selectionStart!=L&&D())},P=null;this.setInputHandler=function(N){P=N},this.getInputHandler=function(){return P};var G=!1,z=function(N,j){if(G&&(G=!1),$)return D(),N&&y.onPaste(N),$=!1,"";for(var X=d.selectionStart,Z=d.selectionEnd,te=L,oe=C.length-T,le=N,se=N.length-X,re=N.length-Z,ie=0;te>0&&C[ie]==N[ie];)ie++,te--;for(le=le.slice(ie),ie=1;oe>0&&C.length-ie>L-1&&C[C.length-ie]==N[N.length-ie];)ie++,oe--;se-=ie-1,re-=ie-1;var he=le.length-ie+1;if(he<0&&(te=-he,he=0),le=le.slice(0,he),!j&&!le&&!se&&!te&&!oe&&!re)return"";u=!0;var me=!1;return S.isAndroid&&le==". "&&(le="  ",me=!0),le&&!te&&!oe&&!se&&!re||m?y.onTextInput(le):y.onTextInput(le,{extendLeft:te,extendRight:oe,restoreStart:se,restoreEnd:re}),u=!1,C=N,L=X,T=Z,E=re,me?`
`:le},K=function(N){if(f)return ne();if(N&&N.inputType){if(N.inputType=="historyUndo")return y.execCommand("undo");if(N.inputType=="historyRedo")return y.execCommand("redo")}var j=d.value,X=z(j,!0);(j.length>i+100||g.test(X)||w&&L<1&&L==T)&&D()},Y=function(N,j,X){var Z=N.clipboardData||window.clipboardData;if(!(!Z||n)){var te=r||X?"Text":"text/plain";try{return j?Z.setData(te,j)!==!1:Z.getData(te)}catch(oe){if(!X)return Y(oe,j,!0)}}},V=function(N,j){var X=y.getCopyText();if(!X)return R.preventDefault(N);Y(N,X)?(c&&(D(X),v=X,setTimeout(function(){v=!1},10)),j?y.onCut():y.onCopy(),R.preventDefault(N)):(v=!0,d.value=X,d.select(),setTimeout(function(){v=!1,D(),j?y.onCut():y.onCopy()}))},J=function(N){V(N,!0)},q=function(N){V(N,!1)},ee=function(N){var j=Y(N);t.pasteCancelled()||(typeof j=="string"?(j&&y.onPaste(j,N),S.isIE&&setTimeout(D),R.preventDefault(N)):(d.value="",$=!0))};R.addCommandKeyListener(d,y.onCommandKey.bind(y),y),R.addListener(d,"select",U,y),R.addListener(d,"input",K,y),R.addListener(d,"cut",J,y),R.addListener(d,"copy",q,y),R.addListener(d,"paste",ee,y),(!("oncut"in d)||!("oncopy"in d)||!("onpaste"in d))&&R.addListener(b,"keydown",function(N){if(!(S.isMac&&!N.metaKey||!N.ctrlKey))switch(N.keyCode){case 67:q(N);break;case 86:ee(N);break;case 88:J(N);break}},y);var Q=function(N){if(!(f||!y.onCompositionStart||y.$readOnly)&&(f={},!m)){N.data&&(f.useTextareaForIME=!1),setTimeout(ne,0),y._signal("compositionStart"),y.on("mousedown",fe);var j=y.getSelectionRange();j.end.row=j.start.row,j.end.column=j.start.column,f.markerRange=j,f.selectionStart=L,y.onCompositionStart(f),f.useTextareaForIME?(C=d.value="",L=0,T=0):(d.msGetInputContext&&(f.context=d.msGetInputContext()),d.getInputContext&&(f.context=d.getInputContext()))}},ne=function(){if(!(!f||!y.onCompositionUpdate||y.$readOnly)){if(m)return fe();if(f.useTextareaForIME)y.onCompositionUpdate(d.value);else{var N=d.value;z(N),f.markerRange&&(f.context&&(f.markerRange.start.column=f.selectionStart=f.context.compositionStartOffset),f.markerRange.end.column=f.markerRange.start.column+T-f.selectionStart+E)}}},de=function(N){!y.onCompositionEnd||y.$readOnly||(f=!1,y.onCompositionEnd(),y.off("mousedown",fe),N&&K())};function fe(){h=!0,d.blur(),d.focus(),h=!1}var ue=l.delayedCall(ne,50).schedule.bind(null,null);function ae(N){N.keyCode==27&&d.value.length<d.selectionStart&&(f||(C=d.value),L=T=-1,D()),ue()}R.addListener(d,"compositionstart",Q,y),R.addListener(d,"compositionupdate",ne,y),R.addListener(d,"keyup",ae,y),R.addListener(d,"keydown",ue,y),R.addListener(d,"compositionend",de,y),this.getElement=function(){return d},this.setCommandMode=function(N){m=N,d.readOnly=!1},this.setReadOnly=function(N){m||(d.readOnly=N)},this.setCopyWithEmptySelection=function(N){},this.onContextMenu=function(N){G=!0,D(),y._emit("nativecontextmenu",{target:y,domEvent:N}),this.moveToMouse(N,!0)},this.moveToMouse=function(N,j){A||(A=d.style.cssText),d.style.cssText=(j?"z-index:100000;":"")+(S.isIE?"opacity:0.1;":"")+"text-indent: -"+(L+T)*y.renderer.characterWidth*.5+"px;";var X=y.container.getBoundingClientRect(),Z=a.computedStyle(y.container),te=X.top+(parseInt(Z.borderTopWidth)||0),oe=X.left+(parseInt(X.borderLeftWidth)||0),le=X.bottom-te-d.clientHeight-2,se=function(re){a.translate(d,re.clientX-oe-2,Math.min(re.clientY-te-2,le))};se(N),N.type=="mousedown"&&(y.renderer.$isMousePressed=!0,clearTimeout(ce),S.isWin&&R.capture(y.container,se,pe))},this.onContextMenuClose=pe;var ce;function pe(){clearTimeout(ce),ce=setTimeout(function(){A&&(d.style.cssText=A,A=""),y.renderer.$isMousePressed=!1,y.renderer.$keepTextAreaAtCursor&&y.renderer.$moveTextAreaToCursor()},0)}var ge=function(N){y.textInput.onContextMenu(N),pe()};R.addListener(d,"mouseup",ge,y),R.addListener(d,"mousedown",function(N){N.preventDefault(),pe()},y),R.addListener(y.renderer.scroller,"contextmenu",ge,y),R.addListener(d,"contextmenu",ge,y),c&&xe(b,y,d);function xe(N,j,X){var Z=null,te=!1;X.addEventListener("keydown",function(le){Z&&clearTimeout(Z),te=!0},!0),X.addEventListener("keyup",function(le){Z=setTimeout(function(){te=!1},100)},!0);var oe=function(le){if(document.activeElement===X&&!(te||f||j.$mouseHandler.isMousePressed)&&!v){var se=X.selectionStart,re=X.selectionEnd,ie=null,he=0;if(se==0?ie=o.up:se==1?ie=o.home:re>T&&C[re]==`
`?ie=o.end:se<L&&C[se-1]==" "?(ie=o.left,he=s.option):se<L||se==L&&T!=L&&se==re?ie=o.left:re>T&&C.slice(0,re).split(`
`).length>2?ie=o.down:re>T&&C[re-1]==" "?(ie=o.right,he=s.option):(re>T||re==T&&T!=L&&se==re)&&(ie=o.right),se!==re&&(he|=s.shift),ie){var me=j.onCommandKey({},he,ie);if(!me&&j.commands){ie=o.keyCodeToString(ie);var we=j.commands.findKeyCommand(he,ie);we&&j.execCommand(we)}L=se,T=re,D("")}}};document.addEventListener("selectionchange",oe),j.on("destroy",function(){document.removeEventListener("selectionchange",oe)})}this.destroy=function(){d.parentElement&&d.parentElement.removeChild(d)}};k.TextInput=p,k.$setUserAgentForTests=function(b,y){w=b,c=y}});ace.define("ace/mouse/default_handlers",["require","exports","module","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("../lib/useragent"),M=0,S=550,a=function(){function n(r){r.$clickSelection=null;var e=r.editor;e.setDefaultHandler("mousedown",this.onMouseDown.bind(r)),e.setDefaultHandler("dblclick",this.onDoubleClick.bind(r)),e.setDefaultHandler("tripleclick",this.onTripleClick.bind(r)),e.setDefaultHandler("quadclick",this.onQuadClick.bind(r)),e.setDefaultHandler("mousewheel",this.onMouseWheel.bind(r));var i=["select","startSelect","selectEnd","selectAllEnd","selectByWordsEnd","selectByLinesEnd","dragWait","dragWaitEnd","focusWait"];i.forEach(function(o){r[o]=this[o]},this),r.selectByLines=this.extendSelectionBy.bind(r,"getLineRange"),r.selectByWords=this.extendSelectionBy.bind(r,"getWordRange")}return n.prototype.onMouseDown=function(r){var e=r.inSelection(),i=r.getDocumentPosition();this.mousedownEvent=r;var o=this.editor,s=r.getButton();if(s!==0){var c=o.getSelectionRange(),g=c.isEmpty();(g||s==1)&&o.selection.moveToPosition(i),s==2&&(o.textInput.onContextMenu(r.domEvent),R.isMozilla||r.preventDefault());return}if(this.mousedownEvent.time=Date.now(),e&&!o.isFocused()&&(o.focus(),this.$focusTimeout&&!this.$clickSelection&&!o.inMultiSelectMode)){this.setState("focusWait"),this.captureMouse(r);return}return this.captureMouse(r),this.startSelect(i,r.domEvent._clicks>1),r.preventDefault()},n.prototype.startSelect=function(r,e){r=r||this.editor.renderer.screenToTextCoordinates(this.x,this.y);var i=this.editor;this.mousedownEvent&&(this.mousedownEvent.getShiftKey()?i.selection.selectToPosition(r):e||i.selection.moveToPosition(r),e||this.select(),i.setStyle("ace_selecting"),this.setState("select"))},n.prototype.select=function(){var r,e=this.editor,i=e.renderer.screenToTextCoordinates(this.x,this.y);if(this.$clickSelection){var o=this.$clickSelection.comparePoint(i);if(o==-1)r=this.$clickSelection.end;else if(o==1)r=this.$clickSelection.start;else{var s=t(this.$clickSelection,i);i=s.cursor,r=s.anchor}e.selection.setSelectionAnchor(r.row,r.column)}e.selection.selectToPosition(i),e.renderer.scrollCursorIntoView()},n.prototype.extendSelectionBy=function(r){var e,i=this.editor,o=i.renderer.screenToTextCoordinates(this.x,this.y),s=i.selection[r](o.row,o.column);if(this.$clickSelection){var c=this.$clickSelection.comparePoint(s.start),g=this.$clickSelection.comparePoint(s.end);if(c==-1&&g<=0)e=this.$clickSelection.end,(s.end.row!=o.row||s.end.column!=o.column)&&(o=s.start);else if(g==1&&c>=0)e=this.$clickSelection.start,(s.start.row!=o.row||s.start.column!=o.column)&&(o=s.end);else if(c==-1&&g==1)o=s.end,e=s.start;else{var w=t(this.$clickSelection,o);o=w.cursor,e=w.anchor}i.selection.setSelectionAnchor(e.row,e.column)}i.selection.selectToPosition(o),i.renderer.scrollCursorIntoView()},n.prototype.selectByLinesEnd=function(){this.$clickSelection=null,this.editor.unsetStyle("ace_selecting")},n.prototype.focusWait=function(){var r=l(this.mousedownEvent.x,this.mousedownEvent.y,this.x,this.y),e=Date.now();(r>M||e-this.mousedownEvent.time>this.$focusTimeout)&&this.startSelect(this.mousedownEvent.getDocumentPosition())},n.prototype.onDoubleClick=function(r){var e=r.getDocumentPosition(),i=this.editor,o=i.session,s=o.getBracketRange(e);s?(s.isEmpty()&&(s.start.column--,s.end.column++),this.setState("select")):(s=i.selection.getWordRange(e.row,e.column),this.setState("selectByWords")),this.$clickSelection=s,this.select()},n.prototype.onTripleClick=function(r){var e=r.getDocumentPosition(),i=this.editor;this.setState("selectByLines");var o=i.getSelectionRange();o.isMultiLine()&&o.contains(e.row,e.column)?(this.$clickSelection=i.selection.getLineRange(o.start.row),this.$clickSelection.end=i.selection.getLineRange(o.end.row).end):this.$clickSelection=i.selection.getLineRange(e.row),this.select()},n.prototype.onQuadClick=function(r){var e=this.editor;e.selectAll(),this.$clickSelection=e.getSelectionRange(),this.setState("selectAll")},n.prototype.onMouseWheel=function(r){if(!r.getAccelKey()){r.getShiftKey()&&r.wheelY&&!r.wheelX&&(r.wheelX=r.wheelY,r.wheelY=0);var e=this.editor;this.$lastScroll||(this.$lastScroll={t:0,vx:0,vy:0,allowed:0});var i=this.$lastScroll,o=r.domEvent.timeStamp,s=o-i.t,c=s?r.wheelX/s:i.vx,g=s?r.wheelY/s:i.vy;s<S&&(c=(c+i.vx)/2,g=(g+i.vy)/2);var w=Math.abs(c/g),p=!1;if(w>=1&&e.renderer.isScrollableBy(r.wheelX*r.speed,0)&&(p=!0),w<=1&&e.renderer.isScrollableBy(0,r.wheelY*r.speed)&&(p=!0),p)i.allowed=o;else if(o-i.allowed<S){var b=Math.abs(c)<=1.5*Math.abs(i.vx)&&Math.abs(g)<=1.5*Math.abs(i.vy);b?(p=!0,i.allowed=o):i.allowed=0}if(i.t=o,i.vx=c,i.vy=g,p)return e.renderer.scrollBy(r.wheelX*r.speed,r.wheelY*r.speed),r.stop()}},n}();a.prototype.selectEnd=a.prototype.selectByLinesEnd,a.prototype.selectAllEnd=a.prototype.selectByLinesEnd,a.prototype.selectByWordsEnd=a.prototype.selectByLinesEnd,k.DefaultHandlers=a;function l(n,r,e,i){return Math.sqrt(Math.pow(e-n,2)+Math.pow(i-r,2))}function t(n,r){if(n.start.row==n.end.row)var e=2*r.column-n.start.column-n.end.column;else if(n.start.row==n.end.row-1&&!n.start.column&&!n.end.column)var e=r.column-4;else var e=2*r.row-n.start.row-n.end.row;return e<0?{cursor:n.start,anchor:n.end}:{cursor:n.end,anchor:n.start}}});ace.define("ace/tooltip",["require","exports","module","ace/lib/dom","ace/range"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var i=function(o,s){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,g){c.__proto__=g}||function(c,g){for(var w in g)Object.prototype.hasOwnProperty.call(g,w)&&(c[w]=g[w])},i(o,s)};return function(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");i(o,s);function c(){this.constructor=o}o.prototype=s===null?Object.create(s):(c.prototype=s.prototype,new c)}}(),M=this&&this.__values||function(i){var o=typeof Symbol=="function"&&Symbol.iterator,s=o&&i[o],c=0;if(s)return s.call(i);if(i&&typeof i.length=="number")return{next:function(){return i&&c>=i.length&&(i=void 0),{value:i&&i[c++],done:!i}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")},S=x("./lib/dom"),a=x("./range").Range,l="ace_tooltip",t=function(){function i(o){this.isOpen=!1,this.$element=null,this.$parentNode=o}return i.prototype.$init=function(){return this.$element=S.createElement("div"),this.$element.className=l,this.$element.style.display="none",this.$parentNode.appendChild(this.$element),this.$element},i.prototype.getElement=function(){return this.$element||this.$init()},i.prototype.setText=function(o){this.getElement().textContent=o},i.prototype.setHtml=function(o){this.getElement().innerHTML=o},i.prototype.setPosition=function(o,s){this.getElement().style.left=o+"px",this.getElement().style.top=s+"px"},i.prototype.setClassName=function(o){S.addCssClass(this.getElement(),o)},i.prototype.setTheme=function(o){this.$element.className=l+" "+(o.isDark?"ace_dark ":"")+(o.cssClass||"")},i.prototype.show=function(o,s,c){o!=null&&this.setText(o),s!=null&&c!=null&&this.setPosition(s,c),this.isOpen||(this.getElement().style.display="block",this.isOpen=!0)},i.prototype.hide=function(){this.isOpen&&(this.getElement().style.display="none",this.getElement().className=l,this.isOpen=!1)},i.prototype.getHeight=function(){return this.getElement().offsetHeight},i.prototype.getWidth=function(){return this.getElement().offsetWidth},i.prototype.destroy=function(){this.isOpen=!1,this.$element&&this.$element.parentNode&&this.$element.parentNode.removeChild(this.$element)},i}(),n=function(){function i(){this.popups=[]}return i.prototype.addPopup=function(o){this.popups.push(o),this.updatePopups()},i.prototype.removePopup=function(o){var s=this.popups.indexOf(o);s!==-1&&(this.popups.splice(s,1),this.updatePopups())},i.prototype.updatePopups=function(){var o,s,c,g;this.popups.sort(function(u,A){return A.priority-u.priority});var w=[];try{for(var p=M(this.popups),b=p.next();!b.done;b=p.next()){var y=b.value,d=!0;try{for(var v=(c=void 0,M(w)),$=v.next();!$.done;$=v.next()){var f=$.value;if(this.doPopupsOverlap(f,y)){d=!1;break}}}catch(u){c={error:u}}finally{try{$&&!$.done&&(g=v.return)&&g.call(v)}finally{if(c)throw c.error}}d?w.push(y):y.hide()}}catch(u){o={error:u}}finally{try{b&&!b.done&&(s=p.return)&&s.call(p)}finally{if(o)throw o.error}}},i.prototype.doPopupsOverlap=function(o,s){var c=o.getElement().getBoundingClientRect(),g=s.getElement().getBoundingClientRect();return c.left<g.right&&c.right>g.left&&c.top<g.bottom&&c.bottom>g.top},i}(),r=new n;k.popupManager=r,k.Tooltip=t;var e=function(i){R(o,i);function o(s){s===void 0&&(s=document.body);var c=i.call(this,s)||this;c.timeout=void 0,c.lastT=0,c.idleTime=350,c.lastEvent=void 0,c.onMouseOut=c.onMouseOut.bind(c),c.onMouseMove=c.onMouseMove.bind(c),c.waitForHover=c.waitForHover.bind(c),c.hide=c.hide.bind(c);var g=c.getElement();return g.style.whiteSpace="pre-wrap",g.style.pointerEvents="auto",g.addEventListener("mouseout",c.onMouseOut),g.tabIndex=-1,g.addEventListener("blur",function(){g.contains(document.activeElement)||this.hide()}.bind(c)),c}return o.prototype.addToEditor=function(s){s.on("mousemove",this.onMouseMove),s.on("mousedown",this.hide),s.renderer.getMouseEventTarget().addEventListener("mouseout",this.onMouseOut,!0)},o.prototype.removeFromEditor=function(s){s.off("mousemove",this.onMouseMove),s.off("mousedown",this.hide),s.renderer.getMouseEventTarget().removeEventListener("mouseout",this.onMouseOut,!0),this.timeout&&(clearTimeout(this.timeout),this.timeout=null)},o.prototype.onMouseMove=function(s,c){this.lastEvent=s,this.lastT=Date.now();var g=c.$mouseHandler.isMousePressed;if(this.isOpen){var w=this.lastEvent&&this.lastEvent.getDocumentPosition();(!this.range||!this.range.contains(w.row,w.column)||g||this.isOutsideOfText(this.lastEvent))&&this.hide()}this.timeout||g||(this.lastEvent=s,this.timeout=setTimeout(this.waitForHover,this.idleTime))},o.prototype.waitForHover=function(){this.timeout&&clearTimeout(this.timeout);var s=Date.now()-this.lastT;if(this.idleTime-s>10){this.timeout=setTimeout(this.waitForHover,this.idleTime-s);return}this.timeout=null,this.lastEvent&&!this.isOutsideOfText(this.lastEvent)&&this.$gatherData(this.lastEvent,this.lastEvent.editor)},o.prototype.isOutsideOfText=function(s){var c=s.editor,g=s.getDocumentPosition(),w=c.session.getLine(g.row);if(g.column==w.length){var p=c.renderer.pixelToScreenCoordinates(s.clientX,s.clientY),b=c.session.documentToScreenPosition(g.row,g.column);if(b.column!=p.column||b.row!=p.row)return!0}return!1},o.prototype.setDataProvider=function(s){this.$gatherData=s},o.prototype.showForRange=function(s,c,g,w){if(!(w&&w!=this.lastEvent)&&!(this.isOpen&&document.activeElement==this.getElement())){var p=s.renderer;this.isOpen||(r.addPopup(this),this.$registerCloseEvents(),this.setTheme(p.theme)),this.isOpen=!0,this.addMarker(c,s.session),this.range=a.fromPoints(c.start,c.end);var b=this.getElement();b.innerHTML="",b.appendChild(g),b.style.display="block";var y=p.textToScreenCoordinates(c.start.row,c.start.column),d=s.getCursorPosition(),v=b.clientHeight,$=p.scroller.getBoundingClientRect(),f=!0;this.row>d.row?f=!0:this.row<d.row&&(f=!1),y.pageY-v+p.lineHeight<$.top?f=!0:y.pageY+v>$.bottom&&(f=!1),f?y.pageY+=p.lineHeight:y.pageY-=v,b.style.maxWidth=$.width-(y.pageX-$.left)+"px",this.setPosition(y.pageX,y.pageY)}},o.prototype.addMarker=function(s,c){this.marker&&this.$markerSession.removeMarker(this.marker),this.$markerSession=c,this.marker=c&&c.addMarker(s,"ace_highlight-marker","text")},o.prototype.hide=function(s){!s&&document.activeElement==this.getElement()||s&&s.target&&(s.type!="keydown"||s.ctrlKey||s.metaKey)&&this.$element.contains(s.target)||(this.lastEvent=null,this.timeout&&clearTimeout(this.timeout),this.timeout=null,this.addMarker(null),this.isOpen&&(this.$removeCloseEvents(),this.getElement().style.display="none",this.isOpen=!1,r.removePopup(this)))},o.prototype.$registerCloseEvents=function(){window.addEventListener("keydown",this.hide,!0),window.addEventListener("mousewheel",this.hide,!0),window.addEventListener("mousedown",this.hide,!0)},o.prototype.$removeCloseEvents=function(){window.removeEventListener("keydown",this.hide,!0),window.removeEventListener("mousewheel",this.hide,!0),window.removeEventListener("mousedown",this.hide,!0)},o.prototype.onMouseOut=function(s){this.timeout&&(clearTimeout(this.timeout),this.timeout=null),this.lastEvent=null,this.isOpen&&(!s.relatedTarget||s.relatedTarget==this.getElement()||s&&s.currentTarget.contains(s.relatedTarget)||s.relatedTarget.classList.contains("ace_content")||this.hide())},o}(t);k.HoverTooltip=e});ace.define("ace/mouse/default_gutter_handler",["require","exports","module","ace/lib/dom","ace/lib/event","ace/tooltip","ace/config"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var e=function(i,o){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,c){s.__proto__=c}||function(s,c){for(var g in c)Object.prototype.hasOwnProperty.call(c,g)&&(s[g]=c[g])},e(i,o)};return function(i,o){if(typeof o!="function"&&o!==null)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");e(i,o);function s(){this.constructor=i}i.prototype=o===null?Object.create(o):(s.prototype=o.prototype,new s)}}(),M=this&&this.__values||function(e){var i=typeof Symbol=="function"&&Symbol.iterator,o=i&&e[i],s=0;if(o)return o.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&s>=e.length&&(e=void 0),{value:e&&e[s++],done:!e}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")},S=x("../lib/dom"),a=x("../lib/event"),l=x("../tooltip").Tooltip,t=x("../config").nls;function n(e){var i=e.editor,o=i.renderer.$gutterLayer,s=new r(i);e.editor.setDefaultHandler("guttermousedown",function(y){if(!(!i.isFocused()||y.getButton()!=0)){var d=o.getRegion(y);if(d!="foldWidgets"){var v=y.getDocumentPosition().row,$=i.session.selection;if(y.getShiftKey())$.selectTo(v,0);else{if(y.domEvent.detail==2)return i.selectAll(),y.preventDefault();e.$clickSelection=i.selection.getLineRange(v)}return e.setState("selectByLines"),e.captureMouse(y),y.preventDefault()}}});var c,g;function w(){var y=g.getDocumentPosition().row,d=i.session.getLength();if(y==d){var v=i.renderer.pixelToScreenCoordinates(0,g.y).row,$=g.$pos;if(v>i.session.documentToScreenRow($.row,$.column))return p()}if(s.showTooltip(y),!!s.isOpen)if(i.on("mousewheel",p),e.$tooltipFollowsMouse)b(g);else{var f=g.getGutterRow(),u=o.$lines.get(f);if(u){var A=u.element.querySelector(".ace_gutter_annotation"),m=A.getBoundingClientRect(),h=s.getElement().style;h.left=m.right+"px",h.top=m.bottom+"px"}else b(g)}}function p(){c&&(c=clearTimeout(c)),s.isOpen&&(s.hideTooltip(),i.off("mousewheel",p))}function b(y){s.setPosition(y.x,y.y)}e.editor.setDefaultHandler("guttermousemove",function(y){var d=y.domEvent.target||y.domEvent.srcElement;if(S.hasCssClass(d,"ace_fold-widget"))return p();s.isOpen&&e.$tooltipFollowsMouse&&b(y),g=y,!c&&(c=setTimeout(function(){c=null,g&&!e.isMousePressed?w():p()},50))}),a.addListener(i.renderer.$gutter,"mouseout",function(y){g=null,!(!s.isOpen||c)&&(c=setTimeout(function(){c=null,p()},50))},i),i.on("changeSession",p),i.on("input",p)}k.GutterHandler=n;var r=function(e){R(i,e);function i(o){var s=e.call(this,o.container)||this;return s.editor=o,s}return i.prototype.setPosition=function(o,s){var c=window.innerWidth||document.documentElement.clientWidth,g=window.innerHeight||document.documentElement.clientHeight,w=this.getWidth(),p=this.getHeight();o+=15,s+=15,o+w>c&&(o-=o+w-c),s+p>g&&(s-=20+p),l.prototype.setPosition.call(this,o,s)},Object.defineProperty(i,"annotationLabels",{get:function(){return{error:{singular:t("error"),plural:t("errors")},warning:{singular:t("warning"),plural:t("warnings")},info:{singular:t("information message"),plural:t("information messages")}}},enumerable:!1,configurable:!0}),i.prototype.showTooltip=function(o){var s=this.editor.renderer.$gutterLayer,c=s.$annotations[o],g;c?g={text:Array.from(c.text),type:Array.from(c.type)}:g={text:[],type:[]};var w=s.session.getFoldLine(o);if(w&&s.$showFoldedAnnotations){for(var p={error:[],warning:[],info:[]},b,y=o+1;y<=w.end.row;y++)if(s.$annotations[y])for(var d=0;d<s.$annotations[y].text.length;d++){var v=s.$annotations[y].type[d];if(p[v].push(s.$annotations[y].text[d]),v==="error"){b="error_fold";continue}if(v==="warning"){b="warning_fold";continue}}if(b==="error_fold"||b==="warning_fold"){var $="".concat(i.annotationsToSummaryString(p)," in folded code.");g.text.push($),g.type.push(b)}}if(g.text.length===0)return this.hide();for(var f={error:[],warning:[],info:[]},u=s.$useSvgGutterIcons?"ace_icon_svg":"ace_icon",y=0;y<g.text.length;y++){var A="<span class='ace_".concat(g.type[y]," ").concat(u,"' aria-label='").concat(i.annotationLabels[g.type[y].replace("_fold","")].singular,"' role=img> </span> ").concat(g.text[y]);f[g.type[y].replace("_fold","")].push(A)}var m=[].concat(f.error,f.warning,f.info).join("<br>");this.setHtml(m),this.$element.setAttribute("aria-live","polite"),this.isOpen||(this.setTheme(this.editor.renderer.theme),this.setClassName("ace_gutter-tooltip")),this.show(),this.editor._signal("showGutterTooltip",this)},i.prototype.hideTooltip=function(){this.$element.removeAttribute("aria-live"),this.hide(),this.editor._signal("hideGutterTooltip",this)},i.annotationsToSummaryString=function(o){var s,c,g=[],w=["error","warning","info"];try{for(var p=M(w),b=p.next();!b.done;b=p.next()){var y=b.value;if(o[y].length){var d=o[y].length===1?i.annotationLabels[y].singular:i.annotationLabels[y].plural;g.push("".concat(o[y].length," ").concat(d))}}}catch(v){s={error:v}}finally{try{b&&!b.done&&(c=p.return)&&c.call(p)}finally{if(s)throw s.error}}return g.join(", ")},i}(l);k.GutterTooltip=r});ace.define("ace/mouse/mouse_event",["require","exports","module","ace/lib/event","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("../lib/event"),M=x("../lib/useragent"),S=function(){function a(l,t){this.domEvent=l,this.editor=t,this.x=this.clientX=l.clientX,this.y=this.clientY=l.clientY,this.$pos=null,this.$inSelection=null,this.propagationStopped=!1,this.defaultPrevented=!1}return a.prototype.stopPropagation=function(){R.stopPropagation(this.domEvent),this.propagationStopped=!0},a.prototype.preventDefault=function(){R.preventDefault(this.domEvent),this.defaultPrevented=!0},a.prototype.stop=function(){this.stopPropagation(),this.preventDefault()},a.prototype.getDocumentPosition=function(){return this.$pos?this.$pos:(this.$pos=this.editor.renderer.screenToTextCoordinates(this.clientX,this.clientY),this.$pos)},a.prototype.getGutterRow=function(){var l=this.getDocumentPosition().row,t=this.editor.session.documentToScreenRow(l,0),n=this.editor.session.documentToScreenRow(this.editor.renderer.$gutterLayer.$lines.get(0).row,0);return t-n},a.prototype.inSelection=function(){if(this.$inSelection!==null)return this.$inSelection;var l=this.editor,t=l.getSelectionRange();if(t.isEmpty())this.$inSelection=!1;else{var n=this.getDocumentPosition();this.$inSelection=t.contains(n.row,n.column)}return this.$inSelection},a.prototype.getButton=function(){return R.getButton(this.domEvent)},a.prototype.getShiftKey=function(){return this.domEvent.shiftKey},a.prototype.getAccelKey=function(){return M.isMac?this.domEvent.metaKey:this.domEvent.ctrlKey},a}();k.MouseEvent=S});ace.define("ace/mouse/dragdrop_handler",["require","exports","module","ace/lib/dom","ace/lib/event","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=x("../lib/event"),S=x("../lib/useragent"),a=200,l=200,t=5;function n(e){var i=e.editor,o=R.createElement("div");o.style.cssText="top:-100px;position:absolute;z-index:2147483647;opacity:0.5",o.textContent="\xA0";var s=["dragWait","dragWaitEnd","startDrag","dragReadyEnd","onMouseDrag"];s.forEach(function(F){e[F]=this[F]},this),i.on("mousedown",this.onMouseDown.bind(e));var c=i.container,g,w,p,b,y,d,v=0,$,f,u,A,m;this.onDragStart=function(F){if(this.cancelDrag||!c.draggable){var D=this;return setTimeout(function(){D.startSelect(),D.captureMouse(F)},0),F.preventDefault()}y=i.getSelectionRange();var B=F.dataTransfer;B.effectAllowed=i.getReadOnly()?"copy":"copyMove",i.container.appendChild(o),B.setDragImage&&B.setDragImage(o,0,0),setTimeout(function(){i.container.removeChild(o)}),B.clearData(),B.setData("Text",i.session.getTextRange()),f=!0,this.setState("drag")},this.onDragEnd=function(F){if(c.draggable=!1,f=!1,this.setState(null),!i.getReadOnly()){var D=F.dataTransfer.dropEffect;!$&&D=="move"&&i.session.remove(i.getSelectionRange()),i.$resetCursorStyle()}this.editor.unsetStyle("ace_dragging"),this.editor.renderer.setCursorStyle("")},this.onDragEnter=function(F){if(!(i.getReadOnly()||!I(F.dataTransfer)))return w=F.clientX,p=F.clientY,g||T(),v++,F.dataTransfer.dropEffect=$=W(F),M.preventDefault(F)},this.onDragOver=function(F){if(!(i.getReadOnly()||!I(F.dataTransfer)))return w=F.clientX,p=F.clientY,g||(T(),v++),_!==null&&(_=null),F.dataTransfer.dropEffect=$=W(F),M.preventDefault(F)},this.onDragLeave=function(F){if(v--,v<=0&&g)return E(),$=null,M.preventDefault(F)},this.onDrop=function(F){if(d){var D=F.dataTransfer;if(f)switch($){case"move":y.contains(d.row,d.column)?y={start:d,end:d}:y=i.moveText(y,d);break;case"copy":y=i.moveText(y,d,!0);break}else{var B=D.getData("Text");y={start:d,end:i.session.insert(d,B)},i.focus(),$=null}return E(),M.preventDefault(F)}},M.addListener(c,"dragstart",this.onDragStart.bind(e),i),M.addListener(c,"dragend",this.onDragEnd.bind(e),i),M.addListener(c,"dragenter",this.onDragEnter.bind(e),i),M.addListener(c,"dragover",this.onDragOver.bind(e),i),M.addListener(c,"dragleave",this.onDragLeave.bind(e),i),M.addListener(c,"drop",this.onDrop.bind(e),i);function h(F,D){var B=Date.now(),U=!D||F.row!=D.row,P=!D||F.column!=D.column;if(!A||U||P)i.moveCursorToPosition(F),A=B,m={x:w,y:p};else{var G=r(m.x,m.y,w,p);G>t?A=null:B-A>=l&&(i.renderer.scrollCursorIntoView(),A=null)}}function C(F,D){var B=Date.now(),U=i.renderer.layerConfig.lineHeight,P=i.renderer.layerConfig.characterWidth,G=i.renderer.scroller.getBoundingClientRect(),z={x:{left:w-G.left,right:G.right-w},y:{top:p-G.top,bottom:G.bottom-p}},K=Math.min(z.x.left,z.x.right),Y=Math.min(z.y.top,z.y.bottom),V={row:F.row,column:F.column};K/P<=2&&(V.column+=z.x.left<z.x.right?-3:2),Y/U<=1&&(V.row+=z.y.top<z.y.bottom?-1:1);var J=F.row!=V.row,q=F.column!=V.column,ee=!D||F.row!=D.row;J||q&&!ee?u?B-u>=a&&i.renderer.scrollCursorIntoView(V):u=B:u=null}function L(){var F=d;d=i.renderer.screenToTextCoordinates(w,p),h(d,F),C(d,F)}function T(){y=i.selection.toOrientedRange(),g=i.session.addMarker(y,"ace_selection",i.getSelectionStyle()),i.clearSelection(),i.isFocused()&&i.renderer.$cursorLayer.setBlinking(!1),clearInterval(b),L(),b=setInterval(L,20),v=0,M.addListener(document,"mousemove",O)}function E(){clearInterval(b),i.session.removeMarker(g),g=null,i.selection.fromOrientedRange(y),i.isFocused()&&!f&&i.$resetCursorStyle(),y=null,d=null,v=0,u=null,A=null,M.removeListener(document,"mousemove",O)}var _=null;function O(){_==null&&(_=setTimeout(function(){_!=null&&g&&E()},20))}function I(F){var D=F.types;return!D||Array.prototype.some.call(D,function(B){return B=="text/plain"||B=="Text"})}function W(F){var D=["copy","copymove","all","uninitialized"],B=["move","copymove","linkmove","all","uninitialized"],U=S.isMac?F.altKey:F.ctrlKey,P="uninitialized";try{P=F.dataTransfer.effectAllowed.toLowerCase()}catch{}var G="none";return U&&D.indexOf(P)>=0?G="copy":B.indexOf(P)>=0?G="move":D.indexOf(P)>=0&&(G="copy"),G}}(function(){this.dragWait=function(){var e=Date.now()-this.mousedownEvent.time;e>this.editor.getDragDelay()&&this.startDrag()},this.dragWaitEnd=function(){var e=this.editor.container;e.draggable=!1,this.startSelect(this.mousedownEvent.getDocumentPosition()),this.selectEnd()},this.dragReadyEnd=function(e){this.editor.$resetCursorStyle(),this.editor.unsetStyle("ace_dragging"),this.editor.renderer.setCursorStyle(""),this.dragWaitEnd()},this.startDrag=function(){this.cancelDrag=!1;var e=this.editor,i=e.container;i.draggable=!0,e.renderer.$cursorLayer.setBlinking(!1),e.setStyle("ace_dragging");var o=S.isWin?"default":"move";e.renderer.setCursorStyle(o),this.setState("dragReady")},this.onMouseDrag=function(e){var i=this.editor.container;if(S.isIE&&this.state=="dragReady"){var o=r(this.mousedownEvent.x,this.mousedownEvent.y,this.x,this.y);o>3&&i.dragDrop()}if(this.state==="dragWait"){var o=r(this.mousedownEvent.x,this.mousedownEvent.y,this.x,this.y);o>0&&(i.draggable=!1,this.startSelect(this.mousedownEvent.getDocumentPosition()))}},this.onMouseDown=function(e){if(this.$dragEnabled){this.mousedownEvent=e;var i=this.editor,o=e.inSelection(),s=e.getButton(),c=e.domEvent.detail||1;if(c===1&&s===0&&o){if(e.editor.inMultiSelectMode&&(e.getAccelKey()||e.getShiftKey()))return;this.mousedownEvent.time=Date.now();var g=e.domEvent.target||e.domEvent.srcElement;if("unselectable"in g&&(g.unselectable="on"),i.getDragDelay()){if(S.isWebKit){this.cancelDrag=!0;var w=i.container;w.draggable=!0}this.setState("dragWait")}else this.startDrag();this.captureMouse(e,this.onMouseDrag.bind(this)),e.defaultPrevented=!0}}}}).call(n.prototype);function r(e,i,o,s){return Math.sqrt(Math.pow(o-e,2)+Math.pow(s-i,2))}k.DragdropHandler=n});ace.define("ace/mouse/touch_handler",["require","exports","module","ace/mouse/mouse_event","ace/lib/event","ace/lib/dom"],function(x,k,H){"use strict";var R=x("./mouse_event").MouseEvent,M=x("../lib/event"),S=x("../lib/dom");k.addTouchListeners=function(a,l){var t="scroll",n,r,e,i,o,s,c=0,g,w=0,p=0,b=0,y,d;function v(){var h=window.navigator&&window.navigator.clipboard,C=!1,L=function(){var E=l.getCopyText(),_=l.session.getUndoManager().hasUndo();d.replaceChild(S.buildDom(C?["span",!E&&["span",{class:"ace_mobile-button",action:"selectall"},"Select All"],E&&["span",{class:"ace_mobile-button",action:"copy"},"Copy"],E&&["span",{class:"ace_mobile-button",action:"cut"},"Cut"],h&&["span",{class:"ace_mobile-button",action:"paste"},"Paste"],_&&["span",{class:"ace_mobile-button",action:"undo"},"Undo"],["span",{class:"ace_mobile-button",action:"find"},"Find"],["span",{class:"ace_mobile-button",action:"openCommandPallete"},"Palette"]]:["span"]),d.firstChild)},T=function(E){var _=E.target.getAttribute("action");if(_=="more"||!C)return C=!C,L();_=="paste"?h.readText().then(function(O){l.execCommand(_,O)}):_&&((_=="cut"||_=="copy")&&(h?h.writeText(l.getCopyText()):document.execCommand("copy")),l.execCommand(_)),d.firstChild.style.display="none",C=!1,_!="openCommandPallete"&&l.focus()};d=S.buildDom(["div",{class:"ace_mobile-menu",ontouchstart:function(E){t="menu",E.stopPropagation(),E.preventDefault(),l.textInput.focus()},ontouchend:function(E){E.stopPropagation(),E.preventDefault(),T(E)},onclick:T},["span"],["span",{class:"ace_mobile-button",action:"more"},"..."]],l.container)}function $(){d||v();var h=l.selection.cursor,C=l.renderer.textToScreenCoordinates(h.row,h.column),L=l.renderer.textToScreenCoordinates(0,0).pageX,T=l.renderer.scrollLeft,E=l.container.getBoundingClientRect();d.style.top=C.pageY-E.top-3+"px",C.pageX-E.left<E.width-70?(d.style.left="",d.style.right="10px"):(d.style.right="",d.style.left=L+T-E.left+"px"),d.style.display="",d.firstChild.style.display="none",l.on("input",f)}function f(h){d&&(d.style.display="none"),l.off("input",f)}function u(){o=null,clearTimeout(o);var h=l.selection.getRange(),C=h.contains(g.row,g.column);(h.isEmpty()||!C)&&(l.selection.moveToPosition(g),l.selection.selectWord()),t="wait",$()}function A(){o=null,clearTimeout(o),l.selection.moveToPosition(g);var h=w>=2?l.selection.getLineRange(g.row):l.session.getBracketRange(g);h&&!h.isEmpty()?l.selection.setRange(h):l.selection.selectWord(),t="wait"}M.addListener(a,"contextmenu",function(h){if(y){var C=l.textInput.getElement();C.focus()}},l),M.addListener(a,"touchstart",function(h){var C=h.touches;if(o||C.length>1){clearTimeout(o),o=null,e=-1,t="zoom";return}y=l.$mouseHandler.isMousePressed=!0;var L=l.renderer.layerConfig.lineHeight,T=l.renderer.layerConfig.lineHeight,E=h.timeStamp;i=E;var _=C[0],O=_.clientX,I=_.clientY;Math.abs(n-O)+Math.abs(r-I)>L&&(e=-1),n=h.clientX=O,r=h.clientY=I,p=b=0;var W=new R(h,l);if(g=W.getDocumentPosition(),E-e<500&&C.length==1&&!c)w++,h.preventDefault(),h.button=0,A();else{w=0;var F=l.selection.cursor,D=l.selection.isEmpty()?F:l.selection.anchor,B=l.renderer.$cursorLayer.getPixelPosition(F,!0),U=l.renderer.$cursorLayer.getPixelPosition(D,!0),P=l.renderer.scroller.getBoundingClientRect(),G=l.renderer.layerConfig.offset,z=l.renderer.scrollLeft,K=function(J,q){return J=J/T,q=q/L-.75,J*J+q*q};if(h.clientX<P.left){t="zoom";return}var Y=K(h.clientX-P.left-B.left+z,h.clientY-P.top-B.top+G),V=K(h.clientX-P.left-U.left+z,h.clientY-P.top-U.top+G);Y<3.5&&V<3.5&&(t=Y>V?"cursor":"anchor"),V<3.5?t="anchor":Y<3.5?t="cursor":t="scroll",o=setTimeout(u,450)}e=E},l),M.addListener(a,"touchend",function(h){y=l.$mouseHandler.isMousePressed=!1,s&&clearInterval(s),t=="zoom"?(t="",c=0):o?(l.selection.moveToPosition(g),c=0,$()):t=="scroll"?(m(),f()):$(),clearTimeout(o),o=null},l),M.addListener(a,"touchmove",function(h){o&&(clearTimeout(o),o=null);var C=h.touches;if(!(C.length>1||t=="zoom")){var L=C[0],T=n-L.clientX,E=r-L.clientY;if(t=="wait")if(T*T+E*E>4)t="cursor";else return h.preventDefault();n=L.clientX,r=L.clientY,h.clientX=L.clientX,h.clientY=L.clientY;var _=h.timeStamp,O=_-i;if(i=_,t=="scroll"){var I=new R(h,l);I.speed=1,I.wheelX=T,I.wheelY=E,10*Math.abs(T)<Math.abs(E)&&(T=0),10*Math.abs(E)<Math.abs(T)&&(E=0),O!=0&&(p=T/O,b=E/O),l._emit("mousewheel",I),I.propagationStopped||(p=b=0)}else{var W=new R(h,l),F=W.getDocumentPosition();t=="cursor"?l.selection.moveCursorToPosition(F):t=="anchor"&&l.selection.setSelectionAnchor(F.row,F.column),l.renderer.scrollCursorIntoView(F),h.preventDefault()}}},l);function m(){c+=60,s=setInterval(function(){c--<=0&&(clearInterval(s),s=null),Math.abs(p)<.01&&(p=0),Math.abs(b)<.01&&(b=0),c<20&&(p=.9*p),c<20&&(b=.9*b);var h=l.session.getScrollTop();l.renderer.scrollBy(10*p,10*b),h==l.session.getScrollTop()&&(c=0)},10)}}});ace.define("ace/mouse/mouse_handler",["require","exports","module","ace/lib/event","ace/lib/useragent","ace/mouse/default_handlers","ace/mouse/default_gutter_handler","ace/mouse/mouse_event","ace/mouse/dragdrop_handler","ace/mouse/touch_handler","ace/config"],function(x,k,H){"use strict";var R=x("../lib/event"),M=x("../lib/useragent"),S=x("./default_handlers").DefaultHandlers,a=x("./default_gutter_handler").GutterHandler,l=x("./mouse_event").MouseEvent,t=x("./dragdrop_handler").DragdropHandler,n=x("./touch_handler").addTouchListeners,r=x("../config"),e=function(){function i(o){var s=this;this.editor=o,new S(this),new a(this),new t(this);var c=function(p){var b=!document.hasFocus||!document.hasFocus()||!o.isFocused()&&document.activeElement==(o.textInput&&o.textInput.getElement());b&&window.focus(),o.focus(),setTimeout(function(){o.isFocused()||o.focus()})},g=o.renderer.getMouseEventTarget();R.addListener(g,"click",this.onMouseEvent.bind(this,"click"),o),R.addListener(g,"mousemove",this.onMouseMove.bind(this,"mousemove"),o),R.addMultiMouseDownListener([g,o.renderer.scrollBarV&&o.renderer.scrollBarV.inner,o.renderer.scrollBarH&&o.renderer.scrollBarH.inner,o.textInput&&o.textInput.getElement()].filter(Boolean),[400,300,250],this,"onMouseEvent",o),R.addMouseWheelListener(o.container,this.onMouseWheel.bind(this,"mousewheel"),o),n(o.container,o);var w=o.renderer.$gutter;R.addListener(w,"mousedown",this.onMouseEvent.bind(this,"guttermousedown"),o),R.addListener(w,"click",this.onMouseEvent.bind(this,"gutterclick"),o),R.addListener(w,"dblclick",this.onMouseEvent.bind(this,"gutterdblclick"),o),R.addListener(w,"mousemove",this.onMouseEvent.bind(this,"guttermousemove"),o),R.addListener(g,"mousedown",c,o),R.addListener(w,"mousedown",c,o),M.isIE&&o.renderer.scrollBarV&&(R.addListener(o.renderer.scrollBarV.element,"mousedown",c,o),R.addListener(o.renderer.scrollBarH.element,"mousedown",c,o)),o.on("mousemove",function(p){if(!(s.state||s.$dragDelay||!s.$dragEnabled)){var b=o.renderer.screenToTextCoordinates(p.x,p.y),y=o.session.selection.getRange(),d=o.renderer;!y.isEmpty()&&y.insideStart(b.row,b.column)?d.setCursorStyle("default"):d.setCursorStyle("")}},o)}return i.prototype.onMouseEvent=function(o,s){this.editor.session&&this.editor._emit(o,new l(s,this.editor))},i.prototype.onMouseMove=function(o,s){var c=this.editor._eventRegistry&&this.editor._eventRegistry.mousemove;!c||!c.length||this.editor._emit(o,new l(s,this.editor))},i.prototype.onMouseWheel=function(o,s){var c=new l(s,this.editor);c.speed=this.$scrollSpeed*2,c.wheelX=s.wheelX,c.wheelY=s.wheelY,this.editor._emit(o,c)},i.prototype.setState=function(o){this.state=o},i.prototype.captureMouse=function(o,s){this.x=o.x,this.y=o.y,this.isMousePressed=!0;var c=this.editor,g=this.editor.renderer;g.$isMousePressed=!0;var w=this,p=function($){if($){if(M.isWebKit&&!$.which&&w.releaseMouse)return w.releaseMouse();w.x=$.clientX,w.y=$.clientY,s&&s($),w.mouseEvent=new l($,w.editor),w.$mouseMoved=!0}},b=function($){c.off("beforeEndOperation",d),clearInterval(v),c.session&&y(),w[w.state+"End"]&&w[w.state+"End"]($),w.state="",w.isMousePressed=g.$isMousePressed=!1,g.$keepTextAreaAtCursor&&g.$moveTextAreaToCursor(),w.$onCaptureMouseMove=w.releaseMouse=null,$&&w.onMouseEvent("mouseup",$),c.endOperation()},y=function(){w[w.state]&&w[w.state](),w.$mouseMoved=!1};if(M.isOldIE&&o.domEvent.type=="dblclick")return setTimeout(function(){b(o)});var d=function($){w.releaseMouse&&c.curOp.command.name&&c.curOp.selectionChanged&&(w[w.state+"End"]&&w[w.state+"End"](),w.state="",w.releaseMouse())};c.on("beforeEndOperation",d),c.startOperation({command:{name:"mouse"}}),w.$onCaptureMouseMove=p,w.releaseMouse=R.capture(this.editor.container,p,b);var v=setInterval(y,20)},i.prototype.cancelContextMenu=function(){var o=function(s){s&&s.domEvent&&s.domEvent.type!="contextmenu"||(this.editor.off("nativecontextmenu",o),s&&s.domEvent&&R.stopEvent(s.domEvent))}.bind(this);setTimeout(o,10),this.editor.on("nativecontextmenu",o)},i.prototype.destroy=function(){this.releaseMouse&&this.releaseMouse()},i}();e.prototype.releaseMouse=null,r.defineOptions(e.prototype,"mouseHandler",{scrollSpeed:{initialValue:2},dragDelay:{initialValue:M.isMac?150:0},dragEnabled:{initialValue:!0},focusTimeout:{initialValue:0},tooltipFollowsMouse:{initialValue:!0}}),k.MouseHandler=e});ace.define("ace/mouse/fold_handler",["require","exports","module","ace/lib/dom"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=function(){function S(a){a.on("click",function(l){var t=l.getDocumentPosition(),n=a.session,r=n.getFoldAt(t.row,t.column,1);r&&(l.getAccelKey()?n.removeFold(r):n.expandFold(r),l.stop());var e=l.domEvent&&l.domEvent.target;e&&R.hasCssClass(e,"ace_inline_button")&&R.hasCssClass(e,"ace_toggle_wrap")&&(n.setOption("wrap",!n.getUseWrapMode()),a.renderer.scrollCursorIntoView())}),a.on("gutterclick",function(l){var t=a.renderer.$gutterLayer.getRegion(l);if(t=="foldWidgets"){var n=l.getDocumentPosition().row,r=a.session;r.foldWidgets&&r.foldWidgets[n]&&a.session.onFoldWidgetClick(n,l),a.isFocused()||a.focus(),l.stop()}}),a.on("gutterdblclick",function(l){var t=a.renderer.$gutterLayer.getRegion(l);if(t=="foldWidgets"){var n=l.getDocumentPosition().row,r=a.session,e=r.getParentFoldRangeData(n,!0),i=e.range||e.firstRange;if(i){n=i.start.row;var o=r.getFoldAt(n,r.getLine(n).length,1);o?r.removeFold(o):(r.addFold("...",i),a.renderer.scrollCursorIntoView({row:i.start.row,column:0}))}l.stop()}})}return S}();k.FoldHandler=M});ace.define("ace/keyboard/keybinding",["require","exports","module","ace/lib/keys","ace/lib/event"],function(x,k,H){"use strict";var R=x("../lib/keys"),M=x("../lib/event"),S=function(){function a(l){this.$editor=l,this.$data={editor:l},this.$handlers=[],this.setDefaultHandler(l.commands)}return a.prototype.setDefaultHandler=function(l){this.removeKeyboardHandler(this.$defaultHandler),this.$defaultHandler=l,this.addKeyboardHandler(l,0)},a.prototype.setKeyboardHandler=function(l){var t=this.$handlers;if(t[t.length-1]!=l){for(;t[t.length-1]&&t[t.length-1]!=this.$defaultHandler;)this.removeKeyboardHandler(t[t.length-1]);this.addKeyboardHandler(l,1)}},a.prototype.addKeyboardHandler=function(l,t){if(l){typeof l=="function"&&!l.handleKeyboard&&(l.handleKeyboard=l);var n=this.$handlers.indexOf(l);n!=-1&&this.$handlers.splice(n,1),t==null?this.$handlers.push(l):this.$handlers.splice(t,0,l),n==-1&&l.attach&&l.attach(this.$editor)}},a.prototype.removeKeyboardHandler=function(l){var t=this.$handlers.indexOf(l);return t==-1?!1:(this.$handlers.splice(t,1),l.detach&&l.detach(this.$editor),!0)},a.prototype.getKeyboardHandler=function(){return this.$handlers[this.$handlers.length-1]},a.prototype.getStatusText=function(){var l=this.$data,t=l.editor;return this.$handlers.map(function(n){return n.getStatusText&&n.getStatusText(t,l)||""}).filter(Boolean).join(" ")},a.prototype.$callKeyboardHandlers=function(l,t,n,r){for(var e,i=!1,o=this.$editor.commands,s=this.$handlers.length;s--&&(e=this.$handlers[s].handleKeyboard(this.$data,l,t,n,r),!(!(!e||!e.command)&&(e.command=="null"?i=!0:i=o.exec(e.command,this.$editor,e.args,r),i&&r&&l!=-1&&e.passEvent!=!0&&e.command.passEvent!=!0&&M.stopEvent(r),i))););return!i&&l==-1&&(e={command:"insertstring"},i=o.exec("insertstring",this.$editor,t)),i&&this.$editor._signal&&this.$editor._signal("keyboardActivity",e),i},a.prototype.onCommandKey=function(l,t,n){var r=R.keyCodeToString(n);return this.$callKeyboardHandlers(t,r,n,l)},a.prototype.onTextInput=function(l){return this.$callKeyboardHandlers(-1,l)},a}();k.KeyBinding=S});ace.define("ace/lib/bidiutil",["require","exports","module"],function(x,k,H){"use strict";var R=["\u0621","\u0641"],M=["\u063A","\u064A"],S=0,a=0,l=!1,t=!1,n=!1,r=!1,e=!1,i=!1,o=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],s=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],c=0,g=1,w=0,p=1,b=2,y=3,d=4,v=5,$=6,f=7,u=8,A=9,m=10,h=11,C=12,L=13,T=14,E=15,_=16,O=17,I=18,W=[I,I,I,I,I,I,I,I,I,$,v,$,u,v,I,I,I,I,I,I,I,I,I,I,I,I,I,I,v,v,v,$,u,d,d,h,h,h,d,d,d,d,d,m,A,m,A,A,b,b,b,b,b,b,b,b,b,b,A,d,d,d,d,d,d,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,d,d,d,d,d,d,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,w,d,d,d,d,I,I,I,I,I,I,v,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,I,A,d,h,h,h,h,d,d,d,d,w,d,d,I,d,d,h,h,b,b,d,w,d,d,d,b,w,d,d,d,d,d],F=[u,u,u,u,u,u,u,u,u,u,u,I,I,I,w,p,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,u,v,L,T,E,_,O,A,h,h,h,h,h,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,A,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,d,u];function D(z,K,Y,V){var J=S?s:o,q=null,ee=null,Q=null,ne=0,de=null,fe=null,ue=-1,ae=null,ce=null,pe=[];if(!V)for(ae=0,V=[];ae<Y;ae++)V[ae]=P(z[ae]);for(a=S,l=!1,t=!1,n=!1,r=!1,ce=0;ce<Y;ce++){if(q=ne,pe[ce]=ee=U(z,V,pe,ce),ne=J[q][ee],de=ne&240,ne&=15,K[ce]=Q=J[ne][5],de>0)if(de==16){for(ae=ue;ae<ce;ae++)K[ae]=1;ue=-1}else ue=-1;if(fe=J[ne][6],fe)ue==-1&&(ue=ce);else if(ue>-1){for(ae=ue;ae<ce;ae++)K[ae]=Q;ue=-1}V[ce]==v&&(K[ce]=0),a|=Q}if(r){for(ae=0;ae<Y;ae++)if(V[ae]==$){K[ae]=S;for(var ge=ae-1;ge>=0&&V[ge]==u;ge--)K[ge]=S}}}function B(z,K,Y){if(!(a<z)){if(z==1&&S==g&&!n){Y.reverse();return}for(var V=Y.length,J=0,q,ee,Q,ne;J<V;){if(K[J]>=z){for(q=J+1;q<V&&K[q]>=z;)q++;for(ee=J,Q=q-1;ee<Q;ee++,Q--)ne=Y[ee],Y[ee]=Y[Q],Y[Q]=ne;J=q}J++}}}function U(z,K,Y,V){var J=K[V],q,ee,Q,ne;switch(J){case w:case p:l=!1;case d:case y:return J;case b:return l?y:b;case f:return l=!0,t=!0,p;case u:return d;case A:return V<1||V+1>=K.length||(q=Y[V-1])!=b&&q!=y||(ee=K[V+1])!=b&&ee!=y?d:(l&&(ee=y),ee==q?ee:d);case m:return q=V>0?Y[V-1]:v,q==b&&V+1<K.length&&K[V+1]==b?b:d;case h:if(V>0&&Y[V-1]==b)return b;if(l)return d;for(ne=V+1,Q=K.length;ne<Q&&K[ne]==h;)ne++;return ne<Q&&K[ne]==b?b:d;case C:for(Q=K.length,ne=V+1;ne<Q&&K[ne]==C;)ne++;if(ne<Q){var de=z[V],fe=de>=1425&&de<=2303||de==64286;if(q=K[ne],fe&&(q==p||q==f))return p}return V<1||(q=K[V-1])==v?d:Y[V-1];case v:return l=!1,n=!0,S;case $:return r=!0,d;case L:case T:case _:case O:case E:l=!1;case I:return d}}function P(z){var K=z.charCodeAt(0),Y=K>>8;return Y==0?K>191?w:W[K]:Y==5?/[\u0591-\u05f4]/.test(z)?p:w:Y==6?/[\u0610-\u061a\u064b-\u065f\u06d6-\u06e4\u06e7-\u06ed]/.test(z)?C:/[\u0660-\u0669\u066b-\u066c]/.test(z)?y:K==1642?h:/[\u06f0-\u06f9]/.test(z)?b:f:Y==32&&K<=8287?F[K&255]:Y==254&&K>=65136?f:d}function G(z){return z>="\u064B"&&z<="\u0655"}k.L=w,k.R=p,k.EN=b,k.ON_R=3,k.AN=4,k.R_H=5,k.B=6,k.RLE=7,k.DOT="\xB7",k.doBidiReorder=function(z,K,Y){if(z.length<2)return{};var V=z.split(""),J=new Array(V.length),q=new Array(V.length),ee=[];S=Y?g:c,D(V,ee,V.length,K);for(var Q=0;Q<J.length;J[Q]=Q,Q++);B(2,ee,J),B(1,ee,J);for(var Q=0;Q<J.length-1;Q++)K[Q]===y?ee[Q]=k.AN:ee[Q]===p&&(K[Q]>f&&K[Q]<L||K[Q]===d||K[Q]===I)?ee[Q]=k.ON_R:Q>0&&V[Q-1]==="\u0644"&&/\u0622|\u0623|\u0625|\u0627/.test(V[Q])&&(ee[Q-1]=ee[Q]=k.R_H,Q++);V[V.length-1]===k.DOT&&(ee[V.length-1]=k.B),V[0]==="\u202B"&&(ee[0]=k.RLE);for(var Q=0;Q<J.length;Q++)q[Q]=ee[J[Q]];return{logicalFromVisual:J,bidiLevels:q}},k.hasBidiCharacters=function(z,K){for(var Y=!1,V=0;V<z.length;V++)K[V]=P(z.charAt(V)),!Y&&(K[V]==p||K[V]==f||K[V]==y)&&(Y=!0);return Y},k.getVisualFromLogicalIdx=function(z,K){for(var Y=0;Y<K.logicalFromVisual.length;Y++)if(K.logicalFromVisual[Y]==z)return Y;return 0}});ace.define("ace/bidihandler",["require","exports","module","ace/lib/bidiutil","ace/lib/lang"],function(x,k,H){"use strict";var R=x("./lib/bidiutil"),M=x("./lib/lang"),S=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\u202B]/,a=function(){function l(t){this.session=t,this.bidiMap={},this.currentRow=null,this.bidiUtil=R,this.charWidths=[],this.EOL="\xAC",this.showInvisibles=!0,this.isRtlDir=!1,this.$isRtl=!1,this.line="",this.wrapIndent=0,this.EOF="\xB6",this.RLE="\u202B",this.contentWidth=0,this.fontMetrics=null,this.rtlLineOffset=0,this.wrapOffset=0,this.isMoveLeftOperation=!1,this.seenBidi=S.test(t.getValue())}return l.prototype.isBidiRow=function(t,n,r){return this.seenBidi?(t!==this.currentRow&&(this.currentRow=t,this.updateRowLine(n,r),this.updateBidiMap()),this.bidiMap.bidiLevels):!1},l.prototype.onChange=function(t){this.seenBidi?this.currentRow=null:t.action=="insert"&&S.test(t.lines.join(`
`))&&(this.seenBidi=!0,this.currentRow=null)},l.prototype.getDocumentRow=function(){var t=0,n=this.session.$screenRowCache;if(n.length){var r=this.session.$getRowCacheIndex(n,this.currentRow);r>=0&&(t=this.session.$docRowCache[r])}return t},l.prototype.getSplitIndex=function(){var t=0,n=this.session.$screenRowCache;if(n.length)for(var r,e=this.session.$getRowCacheIndex(n,this.currentRow);this.currentRow-t>0&&(r=this.session.$getRowCacheIndex(n,this.currentRow-t-1),r===e);)e=r,t++;else t=this.currentRow;return t},l.prototype.updateRowLine=function(t,n){t===void 0&&(t=this.getDocumentRow());var r=t===this.session.getLength()-1,e=r?this.EOF:this.EOL;if(this.wrapIndent=0,this.line=this.session.getLine(t),this.isRtlDir=this.$isRtl||this.line.charAt(0)===this.RLE,this.session.$useWrapMode){var i=this.session.$wrapData[t];i&&(n===void 0&&(n=this.getSplitIndex()),n>0&&i.length?(this.wrapIndent=i.indent,this.wrapOffset=this.wrapIndent*this.charWidths[R.L],this.line=n<i.length?this.line.substring(i[n-1],i[n]):this.line.substring(i[i.length-1])):this.line=this.line.substring(0,i[n]),n==i.length&&(this.line+=this.showInvisibles?e:R.DOT))}else this.line+=this.showInvisibles?e:R.DOT;var o=this.session,s=0,c;this.line=this.line.replace(/\t|[\u1100-\u2029, \u202F-\uFFE6]/g,function(g,w){return g==="	"||o.isFullWidth(g.charCodeAt(0))?(c=g==="	"?o.getScreenTabSize(w+s):2,s+=c-1,M.stringRepeat(R.DOT,c)):g}),this.isRtlDir&&(this.fontMetrics.$main.textContent=this.line.charAt(this.line.length-1)==R.DOT?this.line.substr(0,this.line.length-1):this.line,this.rtlLineOffset=this.contentWidth-this.fontMetrics.$main.getBoundingClientRect().width)},l.prototype.updateBidiMap=function(){var t=[];R.hasBidiCharacters(this.line,t)||this.isRtlDir?this.bidiMap=R.doBidiReorder(this.line,t,this.isRtlDir):this.bidiMap={}},l.prototype.markAsDirty=function(){this.currentRow=null},l.prototype.updateCharacterWidths=function(t){if(this.characterWidth!==t.$characterSize.width){this.fontMetrics=t;var n=this.characterWidth=t.$characterSize.width,r=t.$measureCharWidth("\u05D4");this.charWidths[R.L]=this.charWidths[R.EN]=this.charWidths[R.ON_R]=n,this.charWidths[R.R]=this.charWidths[R.AN]=r,this.charWidths[R.R_H]=r*.45,this.charWidths[R.B]=this.charWidths[R.RLE]=0,this.currentRow=null}},l.prototype.setShowInvisibles=function(t){this.showInvisibles=t,this.currentRow=null},l.prototype.setEolChar=function(t){this.EOL=t},l.prototype.setContentWidth=function(t){this.contentWidth=t},l.prototype.isRtlLine=function(t){return this.$isRtl?!0:t!=null?this.session.getLine(t).charAt(0)==this.RLE:this.isRtlDir},l.prototype.setRtlDirection=function(t,n){for(var r=t.getCursorPosition(),e=t.selection.getSelectionAnchor().row;e<=r.row;e++)!n&&t.session.getLine(e).charAt(0)===t.session.$bidiHandler.RLE?t.session.doc.removeInLine(e,0,1):n&&t.session.getLine(e).charAt(0)!==t.session.$bidiHandler.RLE&&t.session.doc.insert({column:0,row:e},t.session.$bidiHandler.RLE)},l.prototype.getPosLeft=function(t){t-=this.wrapIndent;var n=this.line.charAt(0)===this.RLE?1:0,r=t>n?this.session.getOverwrite()?t:t-1:n,e=R.getVisualFromLogicalIdx(r,this.bidiMap),i=this.bidiMap.bidiLevels,o=0;!this.session.getOverwrite()&&t<=n&&i[e]%2!==0&&e++;for(var s=0;s<e;s++)o+=this.charWidths[i[s]];return!this.session.getOverwrite()&&t>n&&i[e]%2===0&&(o+=this.charWidths[i[e]]),this.wrapIndent&&(o+=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset),this.isRtlDir&&(o+=this.rtlLineOffset),o},l.prototype.getSelections=function(t,n){var r=this.bidiMap,e=r.bidiLevels,i,o=[],s=0,c=Math.min(t,n)-this.wrapIndent,g=Math.max(t,n)-this.wrapIndent,w=!1,p=!1,b=0;this.wrapIndent&&(s+=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset);for(var y,d=0;d<e.length;d++)y=r.logicalFromVisual[d],i=e[d],w=y>=c&&y<g,w&&!p?b=s:!w&&p&&o.push({left:b,width:s-b}),s+=this.charWidths[i],p=w;if(w&&d===e.length&&o.push({left:b,width:s-b}),this.isRtlDir)for(var v=0;v<o.length;v++)o[v].left+=this.rtlLineOffset;return o},l.prototype.offsetToCol=function(r){this.isRtlDir&&(r-=this.rtlLineOffset);var n=0,r=Math.max(r,0),e=0,i=0,o=this.bidiMap.bidiLevels,s=this.charWidths[o[i]];for(this.wrapIndent&&(r-=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset);r>e+s/2;){if(e+=s,i===o.length-1){s=0;break}s=this.charWidths[o[++i]]}return i>0&&o[i-1]%2!==0&&o[i]%2===0?(r<e&&i--,n=this.bidiMap.logicalFromVisual[i]):i>0&&o[i-1]%2===0&&o[i]%2!==0?n=1+(r>e?this.bidiMap.logicalFromVisual[i]:this.bidiMap.logicalFromVisual[i-1]):this.isRtlDir&&i===o.length-1&&s===0&&o[i-1]%2===0||!this.isRtlDir&&i===0&&o[i]%2!==0?n=1+this.bidiMap.logicalFromVisual[i]:(i>0&&o[i-1]%2!==0&&s!==0&&i--,n=this.bidiMap.logicalFromVisual[i]),n===0&&this.isRtlDir&&n++,n+this.wrapIndent},l}();k.BidiHandler=a});ace.define("ace/selection",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/range"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./lib/lang"),S=x("./lib/event_emitter").EventEmitter,a=x("./range").Range,l=function(t){this.session=t,this.doc=t.getDocument(),this.clearSelection(),this.cursor=this.lead=this.doc.createAnchor(0,0),this.anchor=this.doc.createAnchor(0,0),this.$silent=!1;var n=this;this.cursor.on("change",function(r){n.$cursorChanged=!0,n.$silent||n._emit("changeCursor"),!n.$isEmpty&&!n.$silent&&n._emit("changeSelection"),!n.$keepDesiredColumnOnChange&&r.old.column!=r.value.column&&(n.$desiredColumn=null)}),this.anchor.on("change",function(){n.$anchorChanged=!0,!n.$isEmpty&&!n.$silent&&n._emit("changeSelection")})};(function(){R.implement(this,S),this.isEmpty=function(){return this.$isEmpty||this.anchor.row==this.lead.row&&this.anchor.column==this.lead.column},this.isMultiLine=function(){return!this.$isEmpty&&this.anchor.row!=this.cursor.row},this.getCursor=function(){return this.lead.getPosition()},this.setAnchor=function(t,n){this.$isEmpty=!1,this.anchor.setPosition(t,n)},this.setSelectionAnchor=this.setAnchor,this.getAnchor=function(){return this.$isEmpty?this.getSelectionLead():this.anchor.getPosition()},this.getSelectionAnchor=this.getAnchor,this.getSelectionLead=function(){return this.lead.getPosition()},this.isBackwards=function(){var t=this.anchor,n=this.lead;return t.row>n.row||t.row==n.row&&t.column>n.column},this.getRange=function(){var t=this.anchor,n=this.lead;return this.$isEmpty?a.fromPoints(n,n):this.isBackwards()?a.fromPoints(n,t):a.fromPoints(t,n)},this.clearSelection=function(){this.$isEmpty||(this.$isEmpty=!0,this._emit("changeSelection"))},this.selectAll=function(){this.$setSelection(0,0,Number.MAX_VALUE,Number.MAX_VALUE)},this.setRange=this.setSelectionRange=function(t,n){var r=n?t.end:t.start,e=n?t.start:t.end;this.$setSelection(r.row,r.column,e.row,e.column)},this.$setSelection=function(t,n,r,e){if(!this.$silent){var i=this.$isEmpty,o=this.inMultiSelectMode;this.$silent=!0,this.$cursorChanged=this.$anchorChanged=!1,this.anchor.setPosition(t,n),this.cursor.setPosition(r,e),this.$isEmpty=!a.comparePoints(this.anchor,this.cursor),this.$silent=!1,this.$cursorChanged&&this._emit("changeCursor"),(this.$cursorChanged||this.$anchorChanged||i!=this.$isEmpty||o)&&this._emit("changeSelection")}},this.$moveSelection=function(t){var n=this.lead;this.$isEmpty&&this.setSelectionAnchor(n.row,n.column),t.call(this)},this.selectTo=function(t,n){this.$moveSelection(function(){this.moveCursorTo(t,n)})},this.selectToPosition=function(t){this.$moveSelection(function(){this.moveCursorToPosition(t)})},this.moveTo=function(t,n){this.clearSelection(),this.moveCursorTo(t,n)},this.moveToPosition=function(t){this.clearSelection(),this.moveCursorToPosition(t)},this.selectUp=function(){this.$moveSelection(this.moveCursorUp)},this.selectDown=function(){this.$moveSelection(this.moveCursorDown)},this.selectRight=function(){this.$moveSelection(this.moveCursorRight)},this.selectLeft=function(){this.$moveSelection(this.moveCursorLeft)},this.selectLineStart=function(){this.$moveSelection(this.moveCursorLineStart)},this.selectLineEnd=function(){this.$moveSelection(this.moveCursorLineEnd)},this.selectFileEnd=function(){this.$moveSelection(this.moveCursorFileEnd)},this.selectFileStart=function(){this.$moveSelection(this.moveCursorFileStart)},this.selectWordRight=function(){this.$moveSelection(this.moveCursorWordRight)},this.selectWordLeft=function(){this.$moveSelection(this.moveCursorWordLeft)},this.getWordRange=function(t,n){if(typeof n>"u"){var r=t||this.lead;t=r.row,n=r.column}return this.session.getWordRange(t,n)},this.selectWord=function(){this.setSelectionRange(this.getWordRange())},this.selectAWord=function(){var t=this.getCursor(),n=this.session.getAWordRange(t.row,t.column);this.setSelectionRange(n)},this.getLineRange=function(t,n){var r=typeof t=="number"?t:this.lead.row,e,i=this.session.getFoldLine(r);return i?(r=i.start.row,e=i.end.row):e=r,n===!0?new a(r,0,e,this.session.getLine(e).length):new a(r,0,e+1,0)},this.selectLine=function(){this.setSelectionRange(this.getLineRange())},this.moveCursorUp=function(){this.moveCursorBy(-1,0)},this.moveCursorDown=function(){this.moveCursorBy(1,0)},this.wouldMoveIntoSoftTab=function(t,n,r){var e=t.column,i=t.column+n;return r<0&&(e=t.column-n,i=t.column),this.session.isTabStop(t)&&this.doc.getLine(t.row).slice(e,i).split(" ").length-1==n},this.moveCursorLeft=function(){var t=this.lead.getPosition(),n;if(n=this.session.getFoldAt(t.row,t.column,-1))this.moveCursorTo(n.start.row,n.start.column);else if(t.column===0)t.row>0&&this.moveCursorTo(t.row-1,this.doc.getLine(t.row-1).length);else{var r=this.session.getTabSize();this.wouldMoveIntoSoftTab(t,r,-1)&&!this.session.getNavigateWithinSoftTabs()?this.moveCursorBy(0,-r):this.moveCursorBy(0,-1)}},this.moveCursorRight=function(){var t=this.lead.getPosition(),n;if(n=this.session.getFoldAt(t.row,t.column,1))this.moveCursorTo(n.end.row,n.end.column);else if(this.lead.column==this.doc.getLine(this.lead.row).length)this.lead.row<this.doc.getLength()-1&&this.moveCursorTo(this.lead.row+1,0);else{var r=this.session.getTabSize(),t=this.lead;this.wouldMoveIntoSoftTab(t,r,1)&&!this.session.getNavigateWithinSoftTabs()?this.moveCursorBy(0,r):this.moveCursorBy(0,1)}},this.moveCursorLineStart=function(){var t=this.lead.row,n=this.lead.column,r=this.session.documentToScreenRow(t,n),e=this.session.screenToDocumentPosition(r,0),i=this.session.getDisplayLine(t,null,e.row,e.column),o=i.match(/^\s*/);o[0].length!=n&&!this.session.$useEmacsStyleLineStart&&(e.column+=o[0].length),this.moveCursorToPosition(e)},this.moveCursorLineEnd=function(){var t=this.lead,n=this.session.getDocumentLastRowColumnPosition(t.row,t.column);if(this.lead.column==n.column){var r=this.session.getLine(n.row);if(n.column==r.length){var e=r.search(/\s+$/);e>0&&(n.column=e)}}this.moveCursorTo(n.row,n.column)},this.moveCursorFileEnd=function(){var t=this.doc.getLength()-1,n=this.doc.getLine(t).length;this.moveCursorTo(t,n)},this.moveCursorFileStart=function(){this.moveCursorTo(0,0)},this.moveCursorLongWordRight=function(){var t=this.lead.row,n=this.lead.column,r=this.doc.getLine(t),e=r.substring(n);this.session.nonTokenRe.lastIndex=0,this.session.tokenRe.lastIndex=0;var i=this.session.getFoldAt(t,n,1);if(i){this.moveCursorTo(i.end.row,i.end.column);return}if(this.session.nonTokenRe.exec(e)&&(n+=this.session.nonTokenRe.lastIndex,this.session.nonTokenRe.lastIndex=0,e=r.substring(n)),n>=r.length){this.moveCursorTo(t,r.length),this.moveCursorRight(),t<this.doc.getLength()-1&&this.moveCursorWordRight();return}this.session.tokenRe.exec(e)&&(n+=this.session.tokenRe.lastIndex,this.session.tokenRe.lastIndex=0),this.moveCursorTo(t,n)},this.moveCursorLongWordLeft=function(){var t=this.lead.row,n=this.lead.column,r;if(r=this.session.getFoldAt(t,n,-1)){this.moveCursorTo(r.start.row,r.start.column);return}var e=this.session.getFoldStringAt(t,n,-1);e==null&&(e=this.doc.getLine(t).substring(0,n));var i=M.stringReverse(e);if(this.session.nonTokenRe.lastIndex=0,this.session.tokenRe.lastIndex=0,this.session.nonTokenRe.exec(i)&&(n-=this.session.nonTokenRe.lastIndex,i=i.slice(this.session.nonTokenRe.lastIndex),this.session.nonTokenRe.lastIndex=0),n<=0){this.moveCursorTo(t,0),this.moveCursorLeft(),t>0&&this.moveCursorWordLeft();return}this.session.tokenRe.exec(i)&&(n-=this.session.tokenRe.lastIndex,this.session.tokenRe.lastIndex=0),this.moveCursorTo(t,n)},this.$shortWordEndIndex=function(t){var n=0,r,e=/\s/,i=this.session.tokenRe;if(i.lastIndex=0,this.session.tokenRe.exec(t))n=this.session.tokenRe.lastIndex;else{for(;(r=t[n])&&e.test(r);)n++;if(n<1){for(i.lastIndex=0;(r=t[n])&&!i.test(r);)if(i.lastIndex=0,n++,e.test(r))if(n>2){n--;break}else{for(;(r=t[n])&&e.test(r);)n++;if(n>2)break}}}return i.lastIndex=0,n},this.moveCursorShortWordRight=function(){var t=this.lead.row,n=this.lead.column,r=this.doc.getLine(t),e=r.substring(n),i=this.session.getFoldAt(t,n,1);if(i)return this.moveCursorTo(i.end.row,i.end.column);if(n==r.length){var o=this.doc.getLength();do t++,e=this.doc.getLine(t);while(t<o&&/^\s*$/.test(e));/^\s+/.test(e)||(e=""),n=0}var s=this.$shortWordEndIndex(e);this.moveCursorTo(t,n+s)},this.moveCursorShortWordLeft=function(){var t=this.lead.row,n=this.lead.column,r;if(r=this.session.getFoldAt(t,n,-1))return this.moveCursorTo(r.start.row,r.start.column);var e=this.session.getLine(t).substring(0,n);if(n===0){do t--,e=this.doc.getLine(t);while(t>0&&/^\s*$/.test(e));n=e.length,/\s+$/.test(e)||(e="")}var i=M.stringReverse(e),o=this.$shortWordEndIndex(i);return this.moveCursorTo(t,n-o)},this.moveCursorWordRight=function(){this.session.$selectLongWords?this.moveCursorLongWordRight():this.moveCursorShortWordRight()},this.moveCursorWordLeft=function(){this.session.$selectLongWords?this.moveCursorLongWordLeft():this.moveCursorShortWordLeft()},this.moveCursorBy=function(t,n){var r=this.session.documentToScreenPosition(this.lead.row,this.lead.column),e;if(n===0&&(t!==0&&(this.session.$bidiHandler.isBidiRow(r.row,this.lead.row)?(e=this.session.$bidiHandler.getPosLeft(r.column),r.column=Math.round(e/this.session.$bidiHandler.charWidths[0])):e=r.column*this.session.$bidiHandler.charWidths[0]),this.$desiredColumn?r.column=this.$desiredColumn:this.$desiredColumn=r.column),t!=0&&this.session.lineWidgets&&this.session.lineWidgets[this.lead.row]){var i=this.session.lineWidgets[this.lead.row];t<0?t-=i.rowsAbove||0:t>0&&(t+=i.rowCount-(i.rowsAbove||0))}var o=this.session.screenToDocumentPosition(r.row+t,r.column,e);t!==0&&n===0&&o.row===this.lead.row&&(o.column,this.lead.column),this.moveCursorTo(o.row,o.column+n,n===0)},this.moveCursorToPosition=function(t){this.moveCursorTo(t.row,t.column)},this.moveCursorTo=function(t,n,r){var e=this.session.getFoldAt(t,n,1);e&&(t=e.start.row,n=e.start.column),this.$keepDesiredColumnOnChange=!0;var i=this.session.getLine(t);/[\uDC00-\uDFFF]/.test(i.charAt(n))&&i.charAt(n-1)&&(this.lead.row==t&&this.lead.column==n+1?n=n-1:n=n+1),this.lead.setPosition(t,n),this.$keepDesiredColumnOnChange=!1,r||(this.$desiredColumn=null)},this.moveCursorToScreen=function(t,n,r){var e=this.session.screenToDocumentPosition(t,n);this.moveCursorTo(e.row,e.column,r)},this.detach=function(){this.lead.detach(),this.anchor.detach()},this.fromOrientedRange=function(t){this.setSelectionRange(t,t.cursor==t.start),this.$desiredColumn=t.desiredColumn||this.$desiredColumn},this.toOrientedRange=function(t){var n=this.getRange();return t?(t.start.column=n.start.column,t.start.row=n.start.row,t.end.column=n.end.column,t.end.row=n.end.row):t=n,t.cursor=this.isBackwards()?t.start:t.end,t.desiredColumn=this.$desiredColumn,t},this.getRangeOfMovements=function(t){var n=this.getCursor();try{t(this);var r=this.getCursor();return a.fromPoints(n,r)}catch{return a.fromPoints(n,n)}finally{this.moveCursorToPosition(n)}},this.toJSON=function(){if(this.rangeCount)var t=this.ranges.map(function(n){var r=n.clone();return r.isBackwards=n.cursor==n.start,r});else{var t=this.getRange();t.isBackwards=this.isBackwards()}return t},this.fromJSON=function(t){if(t.start==null)if(this.rangeList&&t.length>1){this.toSingleRange(t[0]);for(var n=t.length;n--;){var r=a.fromPoints(t[n].start,t[n].end);t[n].isBackwards&&(r.cursor=r.start),this.addRange(r,!0)}return}else t=t[0];this.rangeList&&this.toSingleRange(t),this.setSelectionRange(t,t.isBackwards)},this.isEqual=function(t){if((t.length||this.rangeCount)&&t.length!=this.rangeCount)return!1;if(!t.length||!this.ranges)return this.getRange().isEqual(t);for(var n=this.ranges.length;n--;)if(!this.ranges[n].isEqual(t[n]))return!1;return!0}}).call(l.prototype),k.Selection=l});ace.define("ace/tokenizer",["require","exports","module","ace/config"],function(x,k,H){"use strict";var R=x("./config"),M=2e3,S=function(){function a(l){this.states=l,this.regExps={},this.matchMappings={};for(var t in this.states){for(var n=this.states[t],r=[],e=0,i=this.matchMappings[t]={defaultToken:"text"},o="g",s=[],c=0;c<n.length;c++){var g=n[c];if(g.defaultToken&&(i.defaultToken=g.defaultToken),g.caseInsensitive&&o.indexOf("i")===-1&&(o+="i"),g.unicode&&o.indexOf("u")===-1&&(o+="u"),g.regex!=null){g.regex instanceof RegExp&&(g.regex=g.regex.toString().slice(1,-1));var w=g.regex,p=new RegExp("(?:("+w+")|(.))").exec("a").length-2;Array.isArray(g.token)?g.token.length==1||p==1?g.token=g.token[0]:p-1!=g.token.length?(this.reportError("number of classes and regexp groups doesn't match",{rule:g,groupCount:p-1}),g.token=g.token[0]):(g.tokenArray=g.token,g.token=null,g.onMatch=this.$arrayTokens):typeof g.token=="function"&&!g.onMatch&&(p>1?g.onMatch=this.$applyToken:g.onMatch=g.token),p>1&&(/\\\d/.test(g.regex)?w=g.regex.replace(/\\([0-9]+)/g,function(b,y){return"\\"+(parseInt(y,10)+e+1)}):(p=1,w=this.removeCapturingGroups(g.regex)),!g.splitRegex&&typeof g.token!="string"&&s.push(g)),i[e]=c,e+=p,r.push(w),g.onMatch||(g.onMatch=null)}}r.length||(i[0]=0,r.push("$")),s.forEach(function(b){b.splitRegex=this.createSplitterRegexp(b.regex,o)},this),this.regExps[t]=new RegExp("("+r.join(")|(")+")|($)",o)}}return a.prototype.$setMaxTokenCount=function(l){M=l|0},a.prototype.$applyToken=function(l){var t=this.splitRegex.exec(l).slice(1),n=this.token.apply(this,t);if(typeof n=="string")return[{type:n,value:l}];for(var r=[],e=0,i=n.length;e<i;e++)t[e]&&(r[r.length]={type:n[e],value:t[e]});return r},a.prototype.$arrayTokens=function(l){if(!l)return[];var t=this.splitRegex.exec(l);if(!t)return"text";for(var n=[],r=this.tokenArray,e=0,i=r.length;e<i;e++)t[e+1]&&(n[n.length]={type:r[e],value:t[e+1]});return n},a.prototype.removeCapturingGroups=function(l){var t=l.replace(/\\.|\[(?:\\.|[^\\\]])*|\(\?[:=!<]|(\()/g,function(n,r){return r?"(?:":n});return t},a.prototype.createSplitterRegexp=function(l,t){if(l.indexOf("(?=")!=-1){var n=0,r=!1,e={};l.replace(/(\\.)|(\((?:\?[=!])?)|(\))|([\[\]])/g,function(i,o,s,c,g,w){return r?r=g!="]":g?r=!0:c?(n==e.stack&&(e.end=w+1,e.stack=-1),n--):s&&(n++,s.length!=1&&(e.stack=n,e.start=w)),i}),e.end!=null&&/^\)*$/.test(l.substr(e.end))&&(l=l.substring(0,e.start)+l.substr(e.end))}return l.charAt(0)!="^"&&(l="^"+l),l.charAt(l.length-1)!="$"&&(l+="$"),new RegExp(l,(t||"").replace("g",""))},a.prototype.getLineTokens=function(l,t){if(t&&typeof t!="string"){var n=t.slice(0);t=n[0],t==="#tmp"&&(n.shift(),t=n.shift())}else var n=[];var r=t||"start",e=this.states[r];e||(r="start",e=this.states[r]);var i=this.matchMappings[r],o=this.regExps[r];o.lastIndex=0;for(var s,c=[],g=0,w=0,p={type:null,value:""};s=o.exec(l);){var b=i.defaultToken,y=null,d=s[0],v=o.lastIndex;if(v-d.length>g){var $=l.substring(g,v-d.length);p.type==b?p.value+=$:(p.type&&c.push(p),p={type:b,value:$})}for(var f=0;f<s.length-2;f++)if(s[f+1]!==void 0){y=e[i[f]],y.onMatch?b=y.onMatch(d,r,n,l):b=y.token,y.next&&(typeof y.next=="string"?r=y.next:r=y.next(r,n),e=this.states[r],e||(this.reportError("state doesn't exist",r),r="start",e=this.states[r]),i=this.matchMappings[r],g=v,o=this.regExps[r],o.lastIndex=v),y.consumeLineEnd&&(g=v);break}if(d){if(typeof b=="string")(!y||y.merge!==!1)&&p.type===b?p.value+=d:(p.type&&c.push(p),p={type:b,value:d});else if(b){p.type&&c.push(p),p={type:null,value:""};for(var f=0;f<b.length;f++)c.push(b[f])}}if(g==l.length)break;if(g=v,w++>M){for(w>2*l.length&&this.reportError("infinite loop with in ace tokenizer",{startState:t,line:l});g<l.length;)p.type&&c.push(p),p={value:l.substring(g,g+=500),type:"overflow"};r="start",n=[];break}}return p.type&&c.push(p),n.length>1&&n[0]!==r&&n.unshift("#tmp",r),{tokens:c,state:n.length?n:r}},a}();S.prototype.reportError=R.reportError,k.Tokenizer=S});ace.define("ace/mode/text_highlight_rules",["require","exports","module","ace/lib/lang"],function(x,k,H){"use strict";var R=x("../lib/lang"),M=function(){this.$rules={start:[{token:"empty_line",regex:"^$"},{defaultToken:"text"}]}};(function(){this.addRules=function(l,t){if(!t){for(var n in l)this.$rules[n]=l[n];return}for(var n in l){for(var r=l[n],e=0;e<r.length;e++){var i=r[e];(i.next||i.onMatch)&&(typeof i.next=="string"&&i.next.indexOf(t)!==0&&(i.next=t+i.next),i.nextState&&i.nextState.indexOf(t)!==0&&(i.nextState=t+i.nextState))}this.$rules[t+n]=r}},this.getRules=function(){return this.$rules},this.embedRules=function(l,t,n,r,e){var i=typeof l=="function"?new l().getRules():l;if(r)for(var o=0;o<r.length;o++)r[o]=t+r[o];else{r=[];for(var s in i)r.push(t+s)}if(this.addRules(i,t),n)for(var c=Array.prototype[e?"push":"unshift"],o=0;o<r.length;o++)c.apply(this.$rules[r[o]],R.deepCopy(n));this.$embeds||(this.$embeds=[]),this.$embeds.push(t)},this.getEmbeds=function(){return this.$embeds};var S=function(l,t){return(l!="start"||t.length)&&t.unshift(this.nextState,l),this.nextState},a=function(l,t){return t.shift(),t.shift()||"start"};this.normalizeRules=function(){var l=0,t=this.$rules;function n(r){var e=t[r];e.processed=!0;for(var i=0;i<e.length;i++){var o=e[i],s=null;Array.isArray(o)&&(s=o,o={}),!o.regex&&o.start&&(o.regex=o.start,o.next||(o.next=[]),o.next.push({defaultToken:o.token},{token:o.token+".end",regex:o.end||o.start,next:"pop"}),o.token=o.token+".start",o.push=!0);var c=o.next||o.push;if(c&&Array.isArray(c)){var g=o.stateName;g||(g=o.token,typeof g!="string"&&(g=g[0]||""),t[g]&&(g+=l++)),t[g]=c,o.next=g,n(g)}else c=="pop"&&(o.next=a);if(o.push&&(o.nextState=o.next||o.push,o.next=S,delete o.push),o.rules)for(var w in o.rules)t[w]?t[w].push&&t[w].push.apply(t[w],o.rules[w]):t[w]=o.rules[w];var p=typeof o=="string"?o:o.include;if(p&&(p==="$self"&&(p="start"),Array.isArray(p)?s=p.map(function(y){return t[y]}):s=t[p]),s){var b=[i,1].concat(s);o.noEscape&&(b=b.filter(function(y){return!y.next})),e.splice.apply(e,b),i--}o.keywordMap&&(o.token=this.createKeywordMapper(o.keywordMap,o.defaultToken||"text",o.caseInsensitive),delete o.defaultToken)}}Object.keys(t).forEach(n,this)},this.createKeywordMapper=function(l,t,n,r){var e=Object.create(null);return this.$keywordList=[],Object.keys(l).forEach(function(i){for(var o=l[i],s=o.split(r||"|"),c=s.length;c--;){var g=s[c];this.$keywordList.push(g),n&&(g=g.toLowerCase()),e[g]=i}},this),l=null,n?function(i){return e[i.toLowerCase()]||t}:function(i){return e[i]||t}},this.getKeywords=function(){return this.$keywords}}).call(M.prototype),k.TextHighlightRules=M});ace.define("ace/mode/behaviour",["require","exports","module"],function(x,k,H){"use strict";var R=function(){this.$behaviours={}};(function(){this.add=function(M,S,a){switch(void 0){case this.$behaviours:this.$behaviours={};case this.$behaviours[M]:this.$behaviours[M]={}}this.$behaviours[M][S]=a},this.addBehaviours=function(M){for(var S in M)for(var a in M[S])this.add(S,a,M[S][a])},this.remove=function(M){this.$behaviours&&this.$behaviours[M]&&delete this.$behaviours[M]},this.inherit=function(M,S){if(typeof M=="function")var a=new M().getBehaviours(S);else var a=M.getBehaviours(S);this.addBehaviours(a)},this.getBehaviours=function(M){if(M){for(var S={},a=0;a<M.length;a++)this.$behaviours[M[a]]&&(S[M[a]]=this.$behaviours[M[a]]);return S}else return this.$behaviours}}).call(R.prototype),k.Behaviour=R});ace.define("ace/token_iterator",["require","exports","module","ace/range"],function(x,k,H){"use strict";var R=x("./range").Range,M=function(){function S(a,l,t){this.$session=a,this.$row=l,this.$rowTokens=a.getTokens(l);var n=a.getTokenAt(l,t);this.$tokenIndex=n?n.index:-1}return S.prototype.stepBackward=function(){for(this.$tokenIndex-=1;this.$tokenIndex<0;){if(this.$row-=1,this.$row<0)return this.$row=0,null;this.$rowTokens=this.$session.getTokens(this.$row),this.$tokenIndex=this.$rowTokens.length-1}return this.$rowTokens[this.$tokenIndex]},S.prototype.stepForward=function(){this.$tokenIndex+=1;for(var a;this.$tokenIndex>=this.$rowTokens.length;){if(this.$row+=1,a||(a=this.$session.getLength()),this.$row>=a)return this.$row=a-1,null;this.$rowTokens=this.$session.getTokens(this.$row),this.$tokenIndex=0}return this.$rowTokens[this.$tokenIndex]},S.prototype.getCurrentToken=function(){return this.$rowTokens[this.$tokenIndex]},S.prototype.getCurrentTokenRow=function(){return this.$row},S.prototype.getCurrentTokenColumn=function(){var a=this.$rowTokens,l=this.$tokenIndex,t=a[l].start;if(t!==void 0)return t;for(t=0;l>0;)l-=1,t+=a[l].value.length;return t},S.prototype.getCurrentTokenPosition=function(){return{row:this.$row,column:this.getCurrentTokenColumn()}},S.prototype.getCurrentTokenRange=function(){var a=this.$rowTokens[this.$tokenIndex],l=this.getCurrentTokenColumn();return new R(this.$row,l,this.$row,l+a.value.length)},S}();k.TokenIterator=M});ace.define("ace/mode/behaviour/cstyle",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/token_iterator","ace/lib/lang"],function(x,k,H){"use strict";var R=x("../../lib/oop"),M=x("../behaviour").Behaviour,S=x("../../token_iterator").TokenIterator,a=x("../../lib/lang"),l=["text","paren.rparen","rparen","paren","punctuation.operator"],t=["text","paren.rparen","rparen","paren","punctuation.operator","comment"],n,r={},e={'"':'"',"'":"'"},i=function(c){var g=-1;if(c.multiSelect&&(g=c.selection.index,r.rangeCount!=c.multiSelect.rangeCount&&(r={rangeCount:c.multiSelect.rangeCount})),r[g])return n=r[g];n=r[g]={autoInsertedBrackets:0,autoInsertedRow:-1,autoInsertedLineEnd:"",maybeInsertedBrackets:0,maybeInsertedRow:-1,maybeInsertedLineStart:"",maybeInsertedLineEnd:""}},o=function(c,g,w,p){var b=c.end.row-c.start.row;return{text:w+g+p,selection:[0,c.start.column+1,b,c.end.column+(b?0:1)]}},s=function(c){c=c||{},this.add("braces","insertion",function(g,w,p,b,y){var d=p.getCursorPosition(),v=b.doc.getLine(d.row);if(y=="{"){i(p);var $=p.getSelectionRange(),f=b.doc.getTextRange($);if(f!==""&&f!=="{"&&p.getWrapBehavioursEnabled())return o($,f,"{","}");if(s.isSaneInsertion(p,b))return/[\]\}\)]/.test(v[d.column])||p.inMultiSelectMode||c.braces?(s.recordAutoInsert(p,b,"}"),{text:"{}",selection:[1,1]}):(s.recordMaybeInsert(p,b,"{"),{text:"{",selection:[1,1]})}else if(y=="}"){i(p);var u=v.substring(d.column,d.column+1);if(u=="}"){var A=b.$findOpeningBracket("}",{column:d.column+1,row:d.row});if(A!==null&&s.isAutoInsertedClosing(d,v,y))return s.popAutoInsertedClosing(),{text:"",selection:[1,1]}}}else if(y==`
`||y==`\r
`){i(p);var m="";s.isMaybeInsertedClosing(d,v)&&(m=a.stringRepeat("}",n.maybeInsertedBrackets),s.clearMaybeInsertedClosing());var u=v.substring(d.column,d.column+1);if(u==="}"){var h=b.findMatchingBracket({row:d.row,column:d.column+1},"}");if(!h)return null;var C=this.$getIndent(b.getLine(h.row))}else if(m)var C=this.$getIndent(v);else{s.clearMaybeInsertedClosing();return}var L=C+b.getTabString();return{text:`
`+L+`
`+C+m,selection:[1,L.length,1,L.length]}}else s.clearMaybeInsertedClosing()}),this.add("braces","deletion",function(g,w,p,b,y){var d=b.doc.getTextRange(y);if(!y.isMultiLine()&&d=="{"){i(p);var v=b.doc.getLine(y.start.row),$=v.substring(y.end.column,y.end.column+1);if($=="}")return y.end.column++,y;n.maybeInsertedBrackets--}}),this.add("parens","insertion",function(g,w,p,b,y){if(y=="("){i(p);var d=p.getSelectionRange(),v=b.doc.getTextRange(d);if(v!==""&&p.getWrapBehavioursEnabled())return o(d,v,"(",")");if(s.isSaneInsertion(p,b))return s.recordAutoInsert(p,b,")"),{text:"()",selection:[1,1]}}else if(y==")"){i(p);var $=p.getCursorPosition(),f=b.doc.getLine($.row),u=f.substring($.column,$.column+1);if(u==")"){var A=b.$findOpeningBracket(")",{column:$.column+1,row:$.row});if(A!==null&&s.isAutoInsertedClosing($,f,y))return s.popAutoInsertedClosing(),{text:"",selection:[1,1]}}}}),this.add("parens","deletion",function(g,w,p,b,y){var d=b.doc.getTextRange(y);if(!y.isMultiLine()&&d=="("){i(p);var v=b.doc.getLine(y.start.row),$=v.substring(y.start.column+1,y.start.column+2);if($==")")return y.end.column++,y}}),this.add("brackets","insertion",function(g,w,p,b,y){if(y=="["){i(p);var d=p.getSelectionRange(),v=b.doc.getTextRange(d);if(v!==""&&p.getWrapBehavioursEnabled())return o(d,v,"[","]");if(s.isSaneInsertion(p,b))return s.recordAutoInsert(p,b,"]"),{text:"[]",selection:[1,1]}}else if(y=="]"){i(p);var $=p.getCursorPosition(),f=b.doc.getLine($.row),u=f.substring($.column,$.column+1);if(u=="]"){var A=b.$findOpeningBracket("]",{column:$.column+1,row:$.row});if(A!==null&&s.isAutoInsertedClosing($,f,y))return s.popAutoInsertedClosing(),{text:"",selection:[1,1]}}}}),this.add("brackets","deletion",function(g,w,p,b,y){var d=b.doc.getTextRange(y);if(!y.isMultiLine()&&d=="["){i(p);var v=b.doc.getLine(y.start.row),$=v.substring(y.start.column+1,y.start.column+2);if($=="]")return y.end.column++,y}}),this.add("string_dquotes","insertion",function(g,w,p,b,y){var d=b.$mode.$quotes||e;if(y.length==1&&d[y]){if(this.lineCommentStart&&this.lineCommentStart.indexOf(y)!=-1)return;i(p);var v=y,$=p.getSelectionRange(),f=b.doc.getTextRange($);if(f!==""&&(f.length!=1||!d[f])&&p.getWrapBehavioursEnabled())return o($,f,v,v);if(!f){var u=p.getCursorPosition(),A=b.doc.getLine(u.row),m=A.substring(u.column-1,u.column),h=A.substring(u.column,u.column+1),C=b.getTokenAt(u.row,u.column),L=b.getTokenAt(u.row,u.column+1);if(m=="\\"&&C&&/escape/.test(C.type))return null;var T=C&&/string|escape/.test(C.type),E=!L||/string|escape/.test(L.type),_;if(h==v)_=T!==E,_&&/string\.end/.test(L.type)&&(_=!1);else{if(T&&!E||T&&E)return null;var O=b.$mode.tokenRe;O.lastIndex=0;var I=O.test(m);O.lastIndex=0;var W=O.test(h),F=b.$mode.$pairQuotesAfter,D=F&&F[v]&&F[v].test(m);if(!D&&I||W||h&&!/[\s;,.})\]\\]/.test(h))return null;var B=A[u.column-2];if(m==v&&(B==v||O.test(B)))return null;_=!0}return{text:_?v+v:"",selection:[1,1]}}}}),this.add("string_dquotes","deletion",function(g,w,p,b,y){var d=b.$mode.$quotes||e,v=b.doc.getTextRange(y);if(!y.isMultiLine()&&d.hasOwnProperty(v)){i(p);var $=b.doc.getLine(y.start.row),f=$.substring(y.start.column+1,y.start.column+2);if(f==v)return y.end.column++,y}}),c.closeDocComment!==!1&&this.add("doc comment end","insertion",function(g,w,p,b,y){if(g==="doc-start"&&(y===`
`||y===`\r
`)&&p.selection.isEmpty()){var d=p.getCursorPosition(),v=b.doc.getLine(d.row),$=b.doc.getLine(d.row+1),f=this.$getIndent(v);if(/\s*\*/.test($))return/^\s*\*/.test(v)?{text:y+f+"* ",selection:[1,3+f.length,1,3+f.length]}:{text:y+f+" * ",selection:[1,3+f.length,1,3+f.length]};if(/\/\*\*/.test(v.substring(0,d.column)))return{text:y+f+" * "+y+" "+f+"*/",selection:[1,4+f.length,1,4+f.length]}}})};s.isSaneInsertion=function(c,g){var w=c.getCursorPosition(),p=new S(g,w.row,w.column);if(!this.$matchTokenType(p.getCurrentToken()||"text",l)){if(/[)}\]]/.test(c.session.getLine(w.row)[w.column]))return!0;var b=new S(g,w.row,w.column+1);if(!this.$matchTokenType(b.getCurrentToken()||"text",l))return!1}return p.stepForward(),p.getCurrentTokenRow()!==w.row||this.$matchTokenType(p.getCurrentToken()||"text",t)},s.$matchTokenType=function(c,g){return g.indexOf(c.type||c)>-1},s.recordAutoInsert=function(c,g,w){var p=c.getCursorPosition(),b=g.doc.getLine(p.row);this.isAutoInsertedClosing(p,b,n.autoInsertedLineEnd[0])||(n.autoInsertedBrackets=0),n.autoInsertedRow=p.row,n.autoInsertedLineEnd=w+b.substr(p.column),n.autoInsertedBrackets++},s.recordMaybeInsert=function(c,g,w){var p=c.getCursorPosition(),b=g.doc.getLine(p.row);this.isMaybeInsertedClosing(p,b)||(n.maybeInsertedBrackets=0),n.maybeInsertedRow=p.row,n.maybeInsertedLineStart=b.substr(0,p.column)+w,n.maybeInsertedLineEnd=b.substr(p.column),n.maybeInsertedBrackets++},s.isAutoInsertedClosing=function(c,g,w){return n.autoInsertedBrackets>0&&c.row===n.autoInsertedRow&&w===n.autoInsertedLineEnd[0]&&g.substr(c.column)===n.autoInsertedLineEnd},s.isMaybeInsertedClosing=function(c,g){return n.maybeInsertedBrackets>0&&c.row===n.maybeInsertedRow&&g.substr(c.column)===n.maybeInsertedLineEnd&&g.substr(0,c.column)==n.maybeInsertedLineStart},s.popAutoInsertedClosing=function(){n.autoInsertedLineEnd=n.autoInsertedLineEnd.substr(1),n.autoInsertedBrackets--},s.clearMaybeInsertedClosing=function(){n&&(n.maybeInsertedBrackets=0,n.maybeInsertedRow=-1)},R.inherits(s,M),k.CstyleBehaviour=s});ace.define("ace/unicode",["require","exports","module"],function(x,k,H){"use strict";for(var R=[48,9,8,25,5,0,2,25,48,0,11,0,5,0,6,22,2,30,2,457,5,11,15,4,8,0,2,0,18,116,2,1,3,3,9,0,2,2,2,0,2,19,2,82,2,138,2,4,3,155,12,37,3,0,8,38,10,44,2,0,2,1,2,1,2,0,9,26,6,2,30,10,7,61,2,9,5,101,2,7,3,9,2,18,3,0,17,58,3,100,15,53,5,0,6,45,211,57,3,18,2,5,3,11,3,9,2,1,7,6,2,2,2,7,3,1,3,21,2,6,2,0,4,3,3,8,3,1,3,3,9,0,5,1,2,4,3,11,16,2,2,5,5,1,3,21,2,6,2,1,2,1,2,1,3,0,2,4,5,1,3,2,4,0,8,3,2,0,8,15,12,2,2,8,2,2,2,21,2,6,2,1,2,4,3,9,2,2,2,2,3,0,16,3,3,9,18,2,2,7,3,1,3,21,2,6,2,1,2,4,3,8,3,1,3,2,9,1,5,1,2,4,3,9,2,0,17,1,2,5,4,2,2,3,4,1,2,0,2,1,4,1,4,2,4,11,5,4,4,2,2,3,3,0,7,0,15,9,18,2,2,7,2,2,2,22,2,9,2,4,4,7,2,2,2,3,8,1,2,1,7,3,3,9,19,1,2,7,2,2,2,22,2,9,2,4,3,8,2,2,2,3,8,1,8,0,2,3,3,9,19,1,2,7,2,2,2,22,2,15,4,7,2,2,2,3,10,0,9,3,3,9,11,5,3,1,2,17,4,23,2,8,2,0,3,6,4,0,5,5,2,0,2,7,19,1,14,57,6,14,2,9,40,1,2,0,3,1,2,0,3,0,7,3,2,6,2,2,2,0,2,0,3,1,2,12,2,2,3,4,2,0,2,5,3,9,3,1,35,0,24,1,7,9,12,0,2,0,2,0,5,9,2,35,5,19,2,5,5,7,2,35,10,0,58,73,7,77,3,37,11,42,2,0,4,328,2,3,3,6,2,0,2,3,3,40,2,3,3,32,2,3,3,6,2,0,2,3,3,14,2,56,2,3,3,66,5,0,33,15,17,84,13,619,3,16,2,25,6,74,22,12,2,6,12,20,12,19,13,12,2,2,2,1,13,51,3,29,4,0,5,1,3,9,34,2,3,9,7,87,9,42,6,69,11,28,4,11,5,11,11,39,3,4,12,43,5,25,7,10,38,27,5,62,2,28,3,10,7,9,14,0,89,75,5,9,18,8,13,42,4,11,71,55,9,9,4,48,83,2,2,30,14,230,23,280,3,5,3,37,3,5,3,7,2,0,2,0,2,0,2,30,3,52,2,6,2,0,4,2,2,6,4,3,3,5,5,12,6,2,2,6,67,1,20,0,29,0,14,0,17,4,60,12,5,0,4,11,18,0,5,0,3,9,2,0,4,4,7,0,2,0,2,0,2,3,2,10,3,3,6,4,5,0,53,1,2684,46,2,46,2,132,7,6,15,37,11,53,10,0,17,22,10,6,2,6,2,6,2,6,2,6,2,6,2,6,2,6,2,31,48,0,470,1,36,5,2,4,6,1,5,85,3,1,3,2,2,89,2,3,6,40,4,93,18,23,57,15,513,6581,75,20939,53,1164,68,45,3,268,4,27,21,31,3,13,13,1,2,24,9,69,11,1,38,8,3,102,3,1,111,44,25,51,13,68,12,9,7,23,4,0,5,45,3,35,13,28,4,64,15,10,39,54,10,13,3,9,7,22,4,1,5,66,25,2,227,42,2,1,3,9,7,11171,13,22,5,48,8453,301,3,61,3,105,39,6,13,4,6,11,2,12,2,4,2,0,2,1,2,1,2,107,34,362,19,63,3,53,41,11,5,15,17,6,13,1,25,2,33,4,2,134,20,9,8,25,5,0,2,25,12,88,4,5,3,5,3,5,3,2],M=0,S=[],a=0;a<R.length;a+=2)S.push(M+=R[a]),R[a+1]&&S.push(45,M+=R[a+1]);k.wordChars=String.fromCharCode.apply(null,S)});ace.define("ace/mode/text",["require","exports","module","ace/config","ace/tokenizer","ace/mode/text_highlight_rules","ace/mode/behaviour/cstyle","ace/unicode","ace/lib/lang","ace/token_iterator","ace/range"],function(x,k,H){"use strict";var R=x("../config"),M=x("../tokenizer").Tokenizer,S=x("./text_highlight_rules").TextHighlightRules,a=x("./behaviour/cstyle").CstyleBehaviour,l=x("../unicode"),t=x("../lib/lang"),n=x("../token_iterator").TokenIterator,r=x("../range").Range,e=function(){this.HighlightRules=S};(function(){this.$defaultBehaviour=new a,this.tokenRe=new RegExp("^["+l.wordChars+"\\$_]+","g"),this.nonTokenRe=new RegExp("^(?:[^"+l.wordChars+"\\$_]|\\s])+","g"),this.getTokenizer=function(){return this.$tokenizer||(this.$highlightRules=this.$highlightRules||new this.HighlightRules(this.$highlightRuleConfig),this.$tokenizer=new M(this.$highlightRules.getRules())),this.$tokenizer},this.lineCommentStart="",this.blockComment="",this.toggleCommentLines=function(i,o,s,c){var g=o.doc,w=!0,p=!0,b=1/0,y=o.getTabSize(),d=!1;if(this.lineCommentStart){if(Array.isArray(this.lineCommentStart))var f=this.lineCommentStart.map(t.escapeRegExp).join("|"),v=this.lineCommentStart[0];else var f=t.escapeRegExp(this.lineCommentStart),v=this.lineCommentStart;f=new RegExp("^(\\s*)(?:"+f+") ?"),d=o.getUseSoftTabs();var m=function(W,F){var D=W.match(f);if(D){var B=D[1].length,U=D[0].length;!L(W,B,U)&&D[0][U-1]==" "&&U--,g.removeInLine(F,B,U)}},C=v+" ",A=function(W,F){(!w||/\S/.test(W))&&(L(W,b,b)?g.insertInLine({row:F,column:b},C):g.insertInLine({row:F,column:b},v))},h=function(W,F){return f.test(W)},L=function(W,F,D){for(var B=0;F--&&W.charAt(F)==" ";)B++;if(B%y!=0)return!1;for(var B=0;W.charAt(D++)==" ";)B++;return y>2?B%y!=y-1:B%y==0}}else{if(!this.blockComment)return!1;var v=this.blockComment.start,$=this.blockComment.end,f=new RegExp("^(\\s*)(?:"+t.escapeRegExp(v)+")"),u=new RegExp("(?:"+t.escapeRegExp($)+")\\s*$"),A=function(_,O){h(_,O)||(!w||/\S/.test(_))&&(g.insertInLine({row:O,column:_.length},$),g.insertInLine({row:O,column:b},v))},m=function(_,O){var I;(I=_.match(u))&&g.removeInLine(O,_.length-I[0].length,_.length),(I=_.match(f))&&g.removeInLine(O,I[1].length,I[0].length)},h=function(_,O){if(f.test(_))return!0;for(var I=o.getTokens(O),W=0;W<I.length;W++)if(I[W].type==="comment")return!0}}function T(_){for(var O=s;O<=c;O++)_(g.getLine(O),O)}var E=1/0;T(function(_,O){var I=_.search(/\S/);I!==-1?(I<b&&(b=I),p&&!h(_,O)&&(p=!1)):E>_.length&&(E=_.length)}),b==1/0&&(b=E,w=!1,p=!1),d&&b%y!=0&&(b=Math.floor(b/y)*y),T(p?m:A)},this.toggleBlockComment=function(i,o,s,c){var g=this.blockComment;if(g){!g.start&&g[0]&&(g=g[0]);var w=new n(o,c.row,c.column),p=w.getCurrentToken(),b=o.selection,y=o.selection.toOrientedRange(),d,v;if(p&&/comment/.test(p.type)){for(var $,f;p&&/comment/.test(p.type);){var u=p.value.indexOf(g.start);if(u!=-1){var A=w.getCurrentTokenRow(),m=w.getCurrentTokenColumn()+u;$=new r(A,m,A,m+g.start.length);break}p=w.stepBackward()}for(var w=new n(o,c.row,c.column),p=w.getCurrentToken();p&&/comment/.test(p.type);){var u=p.value.indexOf(g.end);if(u!=-1){var A=w.getCurrentTokenRow(),m=w.getCurrentTokenColumn()+u;f=new r(A,m,A,m+g.end.length);break}p=w.stepForward()}f&&o.remove(f),$&&(o.remove($),d=$.start.row,v=-g.start.length)}else v=g.start.length,d=s.start.row,o.insert(s.end,g.end),o.insert(s.start,g.start);y.start.row==d&&(y.start.column+=v),y.end.row==d&&(y.end.column+=v),o.selection.fromOrientedRange(y)}},this.getNextLineIndent=function(i,o,s){return this.$getIndent(o)},this.checkOutdent=function(i,o,s){return!1},this.autoOutdent=function(i,o,s){},this.$getIndent=function(i){return i.match(/^\s*/)[0]},this.createWorker=function(i){return null},this.createModeDelegates=function(i){this.$embeds=[],this.$modes={};for(var o in i)if(i[o]){var s=i[o],c=s.prototype.$id,g=R.$modes[c];g||(R.$modes[c]=g=new s),R.$modes[o]||(R.$modes[o]=g),this.$embeds.push(o),this.$modes[o]=g}for(var w=["toggleBlockComment","toggleCommentLines","getNextLineIndent","checkOutdent","autoOutdent","transformAction","getCompletions"],o=0;o<w.length;o++)(function(b){var y=w[o],d=b[y];b[w[o]]=function(){return this.$delegator(y,arguments,d)}})(this)},this.$delegator=function(i,o,s){var c=o[0]||"start";if(typeof c!="string"){if(Array.isArray(c[2])){var g=c[2][c[2].length-1],w=this.$modes[g];if(w)return w[i].apply(w,[c[1]].concat([].slice.call(o,1)))}c=c[0]||"start"}for(var p=0;p<this.$embeds.length;p++)if(this.$modes[this.$embeds[p]]){var b=c.split(this.$embeds[p]);if(!b[0]&&b[1]){o[0]=b[1];var w=this.$modes[this.$embeds[p]];return w[i].apply(w,o)}}var y=s.apply(this,o);return s?y:void 0},this.transformAction=function(i,o,s,c,g){if(this.$behaviour){var w=this.$behaviour.getBehaviours();for(var p in w)if(w[p][o]){var b=w[p][o].apply(this,arguments);if(b)return b}}},this.getKeywords=function(i){if(!this.completionKeywords){var o=this.$tokenizer.rules,s=[];for(var c in o)for(var g=o[c],w=0,p=g.length;w<p;w++)if(typeof g[w].token=="string")/keyword|support|storage/.test(g[w].token)&&s.push(g[w].regex);else if(typeof g[w].token=="object"){for(var b=0,y=g[w].token.length;b<y;b++)if(/keyword|support|storage/.test(g[w].token[b])){var c=g[w].regex.match(/\(.+?\)/g)[b];s.push(c.substr(1,c.length-2))}}this.completionKeywords=s}return i?s.concat(this.$keywordList||[]):this.$keywordList},this.$createKeywordList=function(){return this.$highlightRules||this.getTokenizer(),this.$keywordList=this.$highlightRules.$keywordList||[]},this.getCompletions=function(i,o,s,c){var g=this.$keywordList||this.$createKeywordList();return g.map(function(w){return{name:w,value:w,score:0,meta:"keyword"}})},this.$id="ace/mode/text"}).call(e.prototype),k.Mode=e});ace.define("ace/apply_delta",["require","exports","module"],function(x,k,H){"use strict";function R(a,l){throw console.log("Invalid Delta:",a),"Invalid Delta: "+l}function M(a,l){return l.row>=0&&l.row<a.length&&l.column>=0&&l.column<=a[l.row].length}function S(a,l){l.action!="insert"&&l.action!="remove"&&R(l,"delta.action must be 'insert' or 'remove'"),l.lines instanceof Array||R(l,"delta.lines must be an Array"),(!l.start||!l.end)&&R(l,"delta.start/end must be an present");var t=l.start;M(a,l.start)||R(l,"delta.start must be contained in document");var n=l.end;l.action=="remove"&&!M(a,n)&&R(l,"delta.end must contained in document for 'remove' actions");var r=n.row-t.row,e=n.column-(r==0?t.column:0);(r!=l.lines.length-1||l.lines[r].length!=e)&&R(l,"delta.range must match delta lines")}k.applyDelta=function(a,l,t){var n=l.start.row,r=l.start.column,e=a[n]||"";switch(l.action){case"insert":var i=l.lines;if(i.length===1)a[n]=e.substring(0,r)+l.lines[0]+e.substring(r);else{var o=[n,1].concat(l.lines);a.splice.apply(a,o),a[n]=e.substring(0,r)+a[n],a[n+l.lines.length-1]+=e.substring(r)}break;case"remove":var s=l.end.column,c=l.end.row;n===c?a[n]=e.substring(0,r)+e.substring(s):a.splice(n,c-n+1,e.substring(0,r)+a[c].substring(s));break}}});ace.define("ace/anchor",["require","exports","module","ace/lib/oop","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./lib/event_emitter").EventEmitter,S=function(){function t(n,r,e){this.$onChange=this.onChange.bind(this),this.attach(n),typeof e>"u"?this.setPosition(r.row,r.column):this.setPosition(r,e)}return t.prototype.getPosition=function(){return this.$clipPositionToDocument(this.row,this.column)},t.prototype.getDocument=function(){return this.document},t.prototype.onChange=function(n){if(!(n.start.row==n.end.row&&n.start.row!=this.row)&&!(n.start.row>this.row)){var r=l(n,{row:this.row,column:this.column},this.$insertRight);this.setPosition(r.row,r.column,!0)}},t.prototype.setPosition=function(n,r,e){var i;if(e?i={row:n,column:r}:i=this.$clipPositionToDocument(n,r),!(this.row==i.row&&this.column==i.column)){var o={row:this.row,column:this.column};this.row=i.row,this.column=i.column,this._signal("change",{old:o,value:i})}},t.prototype.detach=function(){this.document.off("change",this.$onChange)},t.prototype.attach=function(n){this.document=n||this.document,this.document.on("change",this.$onChange)},t.prototype.$clipPositionToDocument=function(n,r){var e={};return n>=this.document.getLength()?(e.row=Math.max(0,this.document.getLength()-1),e.column=this.document.getLine(e.row).length):n<0?(e.row=0,e.column=0):(e.row=n,e.column=Math.min(this.document.getLine(e.row).length,Math.max(0,r))),r<0&&(e.column=0),e},t}();S.prototype.$insertRight=!1,R.implement(S.prototype,M);function a(t,n,r){var e=r?t.column<=n.column:t.column<n.column;return t.row<n.row||t.row==n.row&&e}function l(t,n,r){var e=t.action=="insert",i=(e?1:-1)*(t.end.row-t.start.row),o=(e?1:-1)*(t.end.column-t.start.column),s=t.start,c=e?s:t.end;return a(n,s,r)?{row:n.row,column:n.column}:a(c,n,!r)?{row:n.row+i,column:n.column+(n.row==c.row?o:0)}:{row:s.row,column:s.column}}k.Anchor=S});ace.define("ace/document",["require","exports","module","ace/lib/oop","ace/apply_delta","ace/lib/event_emitter","ace/range","ace/anchor"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./apply_delta").applyDelta,S=x("./lib/event_emitter").EventEmitter,a=x("./range").Range,l=x("./anchor").Anchor,t=function(){function n(r){this.$lines=[""],r.length===0?this.$lines=[""]:Array.isArray(r)?this.insertMergedLines({row:0,column:0},r):this.insert({row:0,column:0},r)}return n.prototype.setValue=function(r){var e=this.getLength()-1;this.remove(new a(0,0,e,this.getLine(e).length)),this.insert({row:0,column:0},r||"")},n.prototype.getValue=function(){return this.getAllLines().join(this.getNewLineCharacter())},n.prototype.createAnchor=function(r,e){return new l(this,r,e)},n.prototype.$detectNewLine=function(r){var e=r.match(/^.*?(\r\n|\r|\n)/m);this.$autoNewLine=e?e[1]:`
`,this._signal("changeNewLineMode")},n.prototype.getNewLineCharacter=function(){switch(this.$newLineMode){case"windows":return`\r
`;case"unix":return`
`;default:return this.$autoNewLine||`
`}},n.prototype.setNewLineMode=function(r){this.$newLineMode!==r&&(this.$newLineMode=r,this._signal("changeNewLineMode"))},n.prototype.getNewLineMode=function(){return this.$newLineMode},n.prototype.isNewLine=function(r){return r==`\r
`||r=="\r"||r==`
`},n.prototype.getLine=function(r){return this.$lines[r]||""},n.prototype.getLines=function(r,e){return this.$lines.slice(r,e+1)},n.prototype.getAllLines=function(){return this.getLines(0,this.getLength())},n.prototype.getLength=function(){return this.$lines.length},n.prototype.getTextRange=function(r){return this.getLinesForRange(r).join(this.getNewLineCharacter())},n.prototype.getLinesForRange=function(r){var e;if(r.start.row===r.end.row)e=[this.getLine(r.start.row).substring(r.start.column,r.end.column)];else{e=this.getLines(r.start.row,r.end.row),e[0]=(e[0]||"").substring(r.start.column);var i=e.length-1;r.end.row-r.start.row==i&&(e[i]=e[i].substring(0,r.end.column))}return e},n.prototype.insertLines=function(r,e){return console.warn("Use of document.insertLines is deprecated. Use the insertFullLines method instead."),this.insertFullLines(r,e)},n.prototype.removeLines=function(r,e){return console.warn("Use of document.removeLines is deprecated. Use the removeFullLines method instead."),this.removeFullLines(r,e)},n.prototype.insertNewLine=function(r){return console.warn("Use of document.insertNewLine is deprecated. Use insertMergedLines(position, ['', '']) instead."),this.insertMergedLines(r,["",""])},n.prototype.insert=function(r,e){return this.getLength()<=1&&this.$detectNewLine(e),this.insertMergedLines(r,this.$split(e))},n.prototype.insertInLine=function(r,e){var i=this.clippedPos(r.row,r.column),o=this.pos(r.row,r.column+e.length);return this.applyDelta({start:i,end:o,action:"insert",lines:[e]},!0),this.clonePos(o)},n.prototype.clippedPos=function(r,e){var i=this.getLength();r===void 0?r=i:r<0?r=0:r>=i&&(r=i-1,e=void 0);var o=this.getLine(r);return e==null&&(e=o.length),e=Math.min(Math.max(e,0),o.length),{row:r,column:e}},n.prototype.clonePos=function(r){return{row:r.row,column:r.column}},n.prototype.pos=function(r,e){return{row:r,column:e}},n.prototype.$clipPosition=function(r){var e=this.getLength();return r.row>=e?(r.row=Math.max(0,e-1),r.column=this.getLine(e-1).length):(r.row=Math.max(0,r.row),r.column=Math.min(Math.max(r.column,0),this.getLine(r.row).length)),r},n.prototype.insertFullLines=function(r,e){r=Math.min(Math.max(r,0),this.getLength());var i=0;r<this.getLength()?(e=e.concat([""]),i=0):(e=[""].concat(e),r--,i=this.$lines[r].length),this.insertMergedLines({row:r,column:i},e)},n.prototype.insertMergedLines=function(r,e){var i=this.clippedPos(r.row,r.column),o={row:i.row+e.length-1,column:(e.length==1?i.column:0)+e[e.length-1].length};return this.applyDelta({start:i,end:o,action:"insert",lines:e}),this.clonePos(o)},n.prototype.remove=function(r){var e=this.clippedPos(r.start.row,r.start.column),i=this.clippedPos(r.end.row,r.end.column);return this.applyDelta({start:e,end:i,action:"remove",lines:this.getLinesForRange({start:e,end:i})}),this.clonePos(e)},n.prototype.removeInLine=function(r,e,i){var o=this.clippedPos(r,e),s=this.clippedPos(r,i);return this.applyDelta({start:o,end:s,action:"remove",lines:this.getLinesForRange({start:o,end:s})},!0),this.clonePos(o)},n.prototype.removeFullLines=function(r,e){r=Math.min(Math.max(0,r),this.getLength()-1),e=Math.min(Math.max(0,e),this.getLength()-1);var i=e==this.getLength()-1&&r>0,o=e<this.getLength()-1,s=i?r-1:r,c=i?this.getLine(s).length:0,g=o?e+1:e,w=o?0:this.getLine(g).length,p=new a(s,c,g,w),b=this.$lines.slice(r,e+1);return this.applyDelta({start:p.start,end:p.end,action:"remove",lines:this.getLinesForRange(p)}),b},n.prototype.removeNewLine=function(r){r<this.getLength()-1&&r>=0&&this.applyDelta({start:this.pos(r,this.getLine(r).length),end:this.pos(r+1,0),action:"remove",lines:["",""]})},n.prototype.replace=function(r,e){if(r instanceof a||(r=a.fromPoints(r.start,r.end)),e.length===0&&r.isEmpty())return r.start;if(e==this.getTextRange(r))return r.end;this.remove(r);var i;return e?i=this.insert(r.start,e):i=r.start,i},n.prototype.applyDeltas=function(r){for(var e=0;e<r.length;e++)this.applyDelta(r[e])},n.prototype.revertDeltas=function(r){for(var e=r.length-1;e>=0;e--)this.revertDelta(r[e])},n.prototype.applyDelta=function(r,e){var i=r.action=="insert";(i?r.lines.length<=1&&!r.lines[0]:!a.comparePoints(r.start,r.end))||(i&&r.lines.length>2e4?this.$splitAndapplyLargeDelta(r,2e4):(M(this.$lines,r,e),this._signal("change",r)))},n.prototype.$safeApplyDelta=function(r){var e=this.$lines.length;(r.action=="remove"&&r.start.row<e&&r.end.row<e||r.action=="insert"&&r.start.row<=e)&&this.applyDelta(r)},n.prototype.$splitAndapplyLargeDelta=function(r,e){for(var i=r.lines,o=i.length-e+1,s=r.start.row,c=r.start.column,g=0,w=0;g<o;g=w){w+=e-1;var p=i.slice(g,w);p.push(""),this.applyDelta({start:this.pos(s+g,c),end:this.pos(s+w,c=0),action:r.action,lines:p},!0)}r.lines=i.slice(g),r.start.row=s+g,r.start.column=c,this.applyDelta(r,!0)},n.prototype.revertDelta=function(r){this.$safeApplyDelta({start:this.clonePos(r.start),end:this.clonePos(r.end),action:r.action=="insert"?"remove":"insert",lines:r.lines.slice()})},n.prototype.indexToPosition=function(r,e){for(var i=this.$lines||this.getAllLines(),o=this.getNewLineCharacter().length,s=e||0,c=i.length;s<c;s++)if(r-=i[s].length+o,r<0)return{row:s,column:r+i[s].length+o};return{row:c-1,column:r+i[c-1].length+o}},n.prototype.positionToIndex=function(r,e){for(var i=this.$lines||this.getAllLines(),o=this.getNewLineCharacter().length,s=0,c=Math.min(r.row,i.length),g=e||0;g<c;++g)s+=i[g].length+o;return s+r.column},n.prototype.$split=function(r){return r.split(/\r\n|\r|\n/)},n}();t.prototype.$autoNewLine="",t.prototype.$newLineMode="auto",R.implement(t.prototype,S),k.Document=t});ace.define("ace/background_tokenizer",["require","exports","module","ace/lib/oop","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./lib/event_emitter").EventEmitter,S=function(){function a(l,t){this.running=!1,this.lines=[],this.states=[],this.currentLine=0,this.tokenizer=l;var n=this;this.$worker=function(){if(n.running){for(var r=new Date,e=n.currentLine,i=-1,o=n.doc,s=e;n.lines[e];)e++;var c=o.getLength(),g=0;for(n.running=!1;e<c;){n.$tokenizeRow(e),i=e;do e++;while(n.lines[e]);if(g++,g%5===0&&new Date-r>20){n.running=setTimeout(n.$worker,20);break}}n.currentLine=e,i==-1&&(i=e),s<=i&&n.fireUpdateEvent(s,i)}}}return a.prototype.setTokenizer=function(l){this.tokenizer=l,this.lines=[],this.states=[],this.start(0)},a.prototype.setDocument=function(l){this.doc=l,this.lines=[],this.states=[],this.stop()},a.prototype.fireUpdateEvent=function(l,t){var n={first:l,last:t};this._signal("update",{data:n})},a.prototype.start=function(l){this.currentLine=Math.min(l||0,this.currentLine,this.doc.getLength()),this.lines.splice(this.currentLine,this.lines.length),this.states.splice(this.currentLine,this.states.length),this.stop(),this.running=setTimeout(this.$worker,700)},a.prototype.scheduleStart=function(){this.running||(this.running=setTimeout(this.$worker,700))},a.prototype.$updateOnChange=function(l){var t=l.start.row,n=l.end.row-t;if(n===0)this.lines[t]=null;else if(l.action=="remove")this.lines.splice(t,n+1,null),this.states.splice(t,n+1,null);else{var r=Array(n+1);r.unshift(t,1),this.lines.splice.apply(this.lines,r),this.states.splice.apply(this.states,r)}this.currentLine=Math.min(t,this.currentLine,this.doc.getLength()),this.stop()},a.prototype.stop=function(){this.running&&clearTimeout(this.running),this.running=!1},a.prototype.getTokens=function(l){return this.lines[l]||this.$tokenizeRow(l)},a.prototype.getState=function(l){return this.currentLine==l&&this.$tokenizeRow(l),this.states[l]||"start"},a.prototype.$tokenizeRow=function(l){var t=this.doc.getLine(l),n=this.states[l-1],r=this.tokenizer.getLineTokens(t,n,l);return this.states[l]+""!=r.state+""?(this.states[l]=r.state,this.lines[l+1]=null,this.currentLine>l+1&&(this.currentLine=l+1)):this.currentLine==l&&(this.currentLine=l+1),this.lines[l]=r.tokens},a.prototype.cleanup=function(){this.running=!1,this.lines=[],this.states=[],this.currentLine=0,this.removeAllListeners()},a}();R.implement(S.prototype,M),k.BackgroundTokenizer=S});ace.define("ace/search_highlight",["require","exports","module","ace/lib/lang","ace/range"],function(x,k,H){"use strict";var R=x("./lib/lang"),M=x("./range").Range,S=function(){function a(l,t,n){n===void 0&&(n="text"),this.setRegexp(l),this.clazz=t,this.type=n}return a.prototype.setRegexp=function(l){this.regExp+""!=l+""&&(this.regExp=l,this.cache=[])},a.prototype.update=function(l,t,n,r){if(this.regExp)for(var e=r.firstRow,i=r.lastRow,o={},s=e;s<=i;s++){var c=this.cache[s];c==null&&(c=R.getMatchOffsets(n.getLine(s),this.regExp),c.length>this.MAX_RANGES&&(c=c.slice(0,this.MAX_RANGES)),c=c.map(function(b){return new M(s,b.offset,s,b.offset+b.length)}),this.cache[s]=c.length?c:"");for(var g=c.length;g--;){var w=c[g].toScreenRange(n),p=w.toString();o[p]||(o[p]=!0,t.drawSingleLineMarker(l,w,this.clazz,r))}}},a}();S.prototype.MAX_RANGES=500,k.SearchHighlight=S});ace.define("ace/edit_session/fold_line",["require","exports","module","ace/range"],function(x,k,H){"use strict";var R=x("../range").Range,M=function(){function S(a,l){this.foldData=a,Array.isArray(l)?this.folds=l:l=this.folds=[l];var t=l[l.length-1];this.range=new R(l[0].start.row,l[0].start.column,t.end.row,t.end.column),this.start=this.range.start,this.end=this.range.end,this.folds.forEach(function(n){n.setFoldLine(this)},this)}return S.prototype.shiftRow=function(a){this.start.row+=a,this.end.row+=a,this.folds.forEach(function(l){l.start.row+=a,l.end.row+=a})},S.prototype.addFold=function(a){if(a.sameRow){if(a.start.row<this.startRow||a.endRow>this.endRow)throw new Error("Can't add a fold to this FoldLine as it has no connection");this.folds.push(a),this.folds.sort(function(l,t){return-l.range.compareEnd(t.start.row,t.start.column)}),this.range.compareEnd(a.start.row,a.start.column)>0?(this.end.row=a.end.row,this.end.column=a.end.column):this.range.compareStart(a.end.row,a.end.column)<0&&(this.start.row=a.start.row,this.start.column=a.start.column)}else if(a.start.row==this.end.row)this.folds.push(a),this.end.row=a.end.row,this.end.column=a.end.column;else if(a.end.row==this.start.row)this.folds.unshift(a),this.start.row=a.start.row,this.start.column=a.start.column;else throw new Error("Trying to add fold to FoldRow that doesn't have a matching row");a.foldLine=this},S.prototype.containsRow=function(a){return a>=this.start.row&&a<=this.end.row},S.prototype.walk=function(a,l,t){var n=0,r=this.folds,e,i,o,s=!0;l==null&&(l=this.end.row,t=this.end.column);for(var c=0;c<r.length;c++){if(e=r[c],i=e.range.compareStart(l,t),i==-1){a(null,l,t,n,s);return}if(o=a(null,e.start.row,e.start.column,n,s),o=!o&&a(e.placeholder,e.start.row,e.start.column,n),o||i===0)return;s=!e.sameRow,n=e.end.column}a(null,l,t,n,s)},S.prototype.getNextFoldTo=function(a,l){for(var t,n,r=0;r<this.folds.length;r++){if(t=this.folds[r],n=t.range.compareEnd(a,l),n==-1)return{fold:t,kind:"after"};if(n===0)return{fold:t,kind:"inside"}}return null},S.prototype.addRemoveChars=function(a,l,t){var n=this.getNextFoldTo(a,l),r,e;if(n){if(r=n.fold,n.kind=="inside"&&r.start.column!=l&&r.start.row!=a)window.console&&window.console.log(a,l,r);else if(r.start.row==a){e=this.folds;var i=e.indexOf(r);for(i===0&&(this.start.column+=t),i;i<e.length;i++){if(r=e[i],r.start.column+=t,!r.sameRow)return;r.end.column+=t}this.end.column+=t}}},S.prototype.split=function(a,l){var t=this.getNextFoldTo(a,l);if(!t||t.kind=="inside")return null;var n=t.fold,r=this.folds,e=this.foldData,i=r.indexOf(n),o=r[i-1];this.end.row=o.end.row,this.end.column=o.end.column,r=r.splice(i,r.length-i);var s=new S(e,r);return e.splice(e.indexOf(this)+1,0,s),s},S.prototype.merge=function(a){for(var l=a.folds,t=0;t<l.length;t++)this.addFold(l[t]);var n=this.foldData;n.splice(n.indexOf(a),1)},S.prototype.toString=function(){var a=[this.range.toString()+": ["];return this.folds.forEach(function(l){a.push("  "+l.toString())}),a.push("]"),a.join(`
`)},S.prototype.idxToPosition=function(a){for(var l=0,t=0;t<this.folds.length;t++){var n=this.folds[t];if(a-=n.start.column-l,a<0)return{row:n.start.row,column:n.start.column+a};if(a-=n.placeholder.length,a<0)return n.start;l=n.end.column}return{row:this.end.row,column:this.end.column+a}},S}();k.FoldLine=M});ace.define("ace/range_list",["require","exports","module","ace/range"],function(x,k,H){"use strict";var R=x("./range").Range,M=R.comparePoints,S=function(){function a(){this.ranges=[],this.$bias=1}return a.prototype.pointIndex=function(l,t,n){for(var r=this.ranges,e=n||0;e<r.length;e++){var i=r[e],o=M(l,i.end);if(!(o>0)){var s=M(l,i.start);return o===0?t&&s!==0?-e-2:e:s>0||s===0&&!t?e:-e-1}}return-e-1},a.prototype.add=function(l){var t=!l.isEmpty(),n=this.pointIndex(l.start,t);n<0&&(n=-n-1);var r=this.pointIndex(l.end,t,n);return r<0?r=-r-1:r++,this.ranges.splice(n,r-n,l)},a.prototype.addList=function(l){for(var t=[],n=l.length;n--;)t.push.apply(t,this.add(l[n]));return t},a.prototype.substractPoint=function(l){var t=this.pointIndex(l);if(t>=0)return this.ranges.splice(t,1)},a.prototype.merge=function(){var l=[],t=this.ranges;t=t.sort(function(o,s){return M(o.start,s.start)});for(var n=t[0],r,e=1;e<t.length;e++){r=n,n=t[e];var i=M(r.end,n.start);i<0||i==0&&!r.isEmpty()&&!n.isEmpty()||(M(r.end,n.end)<0&&(r.end.row=n.end.row,r.end.column=n.end.column),t.splice(e,1),l.push(n),n=r,e--)}return this.ranges=t,l},a.prototype.contains=function(l,t){return this.pointIndex({row:l,column:t})>=0},a.prototype.containsPoint=function(l){return this.pointIndex(l)>=0},a.prototype.rangeAtPoint=function(l){var t=this.pointIndex(l);if(t>=0)return this.ranges[t]},a.prototype.clipRows=function(l,t){var n=this.ranges;if(n[0].start.row>t||n[n.length-1].start.row<l)return[];var r=this.pointIndex({row:l,column:0});r<0&&(r=-r-1);var e=this.pointIndex({row:t,column:0},r);e<0&&(e=-e-1);for(var i=[],o=r;o<e;o++)i.push(n[o]);return i},a.prototype.removeAll=function(){return this.ranges.splice(0,this.ranges.length)},a.prototype.attach=function(l){this.session&&this.detach(),this.session=l,this.onChange=this.$onChange.bind(this),this.session.on("change",this.onChange)},a.prototype.detach=function(){this.session&&(this.session.removeListener("change",this.onChange),this.session=null)},a.prototype.$onChange=function(l){for(var t=l.start,n=l.end,r=t.row,e=n.row,i=this.ranges,o=0,s=i.length;o<s;o++){var c=i[o];if(c.end.row>=r)break}if(l.action=="insert")for(var g=e-r,w=-t.column+n.column;o<s;o++){var c=i[o];if(c.start.row>r)break;if(c.start.row==r&&c.start.column>=t.column&&(c.start.column==t.column&&this.$bias<=0||(c.start.column+=w,c.start.row+=g)),c.end.row==r&&c.end.column>=t.column){if(c.end.column==t.column&&this.$bias<0)continue;c.end.column==t.column&&w>0&&o<s-1&&c.end.column>c.start.column&&c.end.column==i[o+1].start.column&&(c.end.column-=w),c.end.column+=w,c.end.row+=g}}else for(var g=r-e,w=t.column-n.column;o<s;o++){var c=i[o];if(c.start.row>e)break;c.end.row<e&&(r<c.end.row||r==c.end.row&&t.column<c.end.column)?(c.end.row=r,c.end.column=t.column):c.end.row==e?c.end.column<=n.column?(g||c.end.column>t.column)&&(c.end.column=t.column,c.end.row=t.row):(c.end.column+=w,c.end.row+=g):c.end.row>e&&(c.end.row+=g),c.start.row<e&&(r<c.start.row||r==c.start.row&&t.column<c.start.column)?(c.start.row=r,c.start.column=t.column):c.start.row==e?c.start.column<=n.column?(g||c.start.column>t.column)&&(c.start.column=t.column,c.start.row=t.row):(c.start.column+=w,c.start.row+=g):c.start.row>e&&(c.start.row+=g)}if(g!=0&&o<s)for(;o<s;o++){var c=i[o];c.start.row+=g,c.end.row+=g}},a}();S.prototype.comparePoints=M,k.RangeList=S});ace.define("ace/edit_session/fold",["require","exports","module","ace/range_list"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var r=function(e,i){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,s){o.__proto__=s}||function(o,s){for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&(o[c]=s[c])},r(e,i)};return function(e,i){if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");r(e,i);function o(){this.constructor=e}e.prototype=i===null?Object.create(i):(o.prototype=i.prototype,new o)}}(),M=x("../range_list").RangeList,S=function(r){R(e,r);function e(i,o){var s=r.call(this)||this;return s.foldLine=null,s.placeholder=o,s.range=i,s.start=i.start,s.end=i.end,s.sameRow=i.start.row==i.end.row,s.subFolds=s.ranges=[],s}return e.prototype.toString=function(){return'"'+this.placeholder+'" '+this.range.toString()},e.prototype.setFoldLine=function(i){this.foldLine=i,this.subFolds.forEach(function(o){o.setFoldLine(i)})},e.prototype.clone=function(){var i=this.range.clone(),o=new e(i,this.placeholder);return this.subFolds.forEach(function(s){o.subFolds.push(s.clone())}),o.collapseChildren=this.collapseChildren,o},e.prototype.addSubFold=function(i){if(!this.range.isEqual(i)){l(i,this.start);for(var w=i.start.row,p=i.start.column,o=0,s=-1;o<this.subFolds.length&&(s=this.subFolds[o].range.compare(w,p),s==1);o++);var c=this.subFolds[o],g=0;if(s==0){if(c.range.containsRange(i))return c.addSubFold(i);g=1}for(var w=i.range.end.row,p=i.range.end.column,b=o,s=-1;b<this.subFolds.length&&(s=this.subFolds[b].range.compare(w,p),s==1);b++);s==0&&b++;for(var y=this.subFolds.splice(o,b-o,i),d=s==0?y.length-1:y.length,v=g;v<d;v++)i.addSubFold(y[v]);return i.setFoldLine(this.foldLine),i}},e.prototype.restoreRange=function(i){return n(i,this.start)},e}(M);function a(r,e){r.row-=e.row,r.row==0&&(r.column-=e.column)}function l(r,e){a(r.start,e),a(r.end,e)}function t(r,e){r.row==0&&(r.column+=e.column),r.row+=e.row}function n(r,e){t(r.start,e),t(r.end,e)}k.Fold=S});ace.define("ace/edit_session/folding",["require","exports","module","ace/range","ace/edit_session/fold_line","ace/edit_session/fold","ace/token_iterator","ace/mouse/mouse_event"],function(x,k,H){"use strict";var R=x("../range").Range,M=x("./fold_line").FoldLine,S=x("./fold").Fold,a=x("../token_iterator").TokenIterator,l=x("../mouse/mouse_event").MouseEvent;function t(){this.getFoldAt=function(n,r,e){var i=this.getFoldLine(n);if(!i)return null;for(var o=i.folds,s=0;s<o.length;s++){var c=o[s].range;if(c.contains(n,r)){if(e==1&&c.isEnd(n,r)&&!c.isEmpty())continue;if(e==-1&&c.isStart(n,r)&&!c.isEmpty())continue;return o[s]}}},this.getFoldsInRange=function(n){var r=n.start,e=n.end,i=this.$foldData,o=[];r.column+=1,e.column-=1;for(var s=0;s<i.length;s++){var c=i[s].range.compareRange(n);if(c!=2){if(c==-2)break;for(var g=i[s].folds,w=0;w<g.length;w++){var p=g[w];if(c=p.range.compareRange(n),c==-2)break;if(c==2)continue;if(c==42)break;o.push(p)}}}return r.column-=1,e.column+=1,o},this.getFoldsInRangeList=function(n){if(Array.isArray(n)){var r=[];n.forEach(function(e){r=r.concat(this.getFoldsInRange(e))},this)}else var r=this.getFoldsInRange(n);return r},this.getAllFolds=function(){for(var n=[],r=this.$foldData,e=0;e<r.length;e++)for(var i=0;i<r[e].folds.length;i++)n.push(r[e].folds[i]);return n},this.getFoldStringAt=function(n,r,e,i){if(i=i||this.getFoldLine(n),!i)return null;for(var o={end:{column:0}},s,c,g=0;g<i.folds.length;g++){c=i.folds[g];var w=c.range.compareEnd(n,r);if(w==-1){s=this.getLine(c.start.row).substring(o.end.column,c.start.column);break}else if(w===0)return null;o=c}return s||(s=this.getLine(c.start.row).substring(o.end.column)),e==-1?s.substring(0,r-o.end.column):e==1?s.substring(r-o.end.column):s},this.getFoldLine=function(n,r){var e=this.$foldData,i=0;for(r&&(i=e.indexOf(r)),i==-1&&(i=0),i;i<e.length;i++){var o=e[i];if(o.start.row<=n&&o.end.row>=n)return o;if(o.end.row>n)return null}return null},this.getNextFoldLine=function(n,r){var e=this.$foldData,i=0;for(r&&(i=e.indexOf(r)),i==-1&&(i=0),i;i<e.length;i++){var o=e[i];if(o.end.row>=n)return o}return null},this.getFoldedRowCount=function(n,r){for(var e=this.$foldData,i=r-n+1,o=0;o<e.length;o++){var s=e[o],c=s.end.row,g=s.start.row;if(c>=r){g<r&&(g>=n?i-=r-g:i=0);break}else c>=n&&(g>=n?i-=c-g:i-=c-n+1)}return i},this.$addFoldLine=function(n){return this.$foldData.push(n),this.$foldData.sort(function(r,e){return r.start.row-e.start.row}),n},this.addFold=function(n,r){var e=this.$foldData,i=!1,o;n instanceof S?o=n:(o=new S(r,n),o.collapseChildren=r.collapseChildren),this.$clipRangeToDocument(o.range);var s=o.start.row,c=o.start.column,g=o.end.row,w=o.end.column,p=this.getFoldAt(s,c,1),b=this.getFoldAt(g,w,-1);if(p&&b==p)return p.addSubFold(o);p&&!p.range.isStart(s,c)&&this.removeFold(p),b&&!b.range.isEnd(g,w)&&this.removeFold(b);var y=this.getFoldsInRange(o.range);y.length>0&&(this.removeFolds(y),o.collapseChildren||y.forEach(function(f){o.addSubFold(f)}));for(var d=0;d<e.length;d++){var v=e[d];if(g==v.start.row){v.addFold(o),i=!0;break}else if(s==v.end.row){if(v.addFold(o),i=!0,!o.sameRow){var $=e[d+1];if($&&$.start.row==g){v.merge($);break}}break}else if(g<=v.start.row)break}return i||(v=this.$addFoldLine(new M(this.$foldData,o))),this.$useWrapMode?this.$updateWrapData(v.start.row,v.start.row):this.$updateRowLengthCache(v.start.row,v.start.row),this.$modified=!0,this._signal("changeFold",{data:o,action:"add"}),o},this.addFolds=function(n){n.forEach(function(r){this.addFold(r)},this)},this.removeFold=function(n){var r=n.foldLine,e=r.start.row,i=r.end.row,o=this.$foldData,s=r.folds;if(s.length==1)o.splice(o.indexOf(r),1);else if(r.range.isEnd(n.end.row,n.end.column))s.pop(),r.end.row=s[s.length-1].end.row,r.end.column=s[s.length-1].end.column;else if(r.range.isStart(n.start.row,n.start.column))s.shift(),r.start.row=s[0].start.row,r.start.column=s[0].start.column;else if(n.sameRow)s.splice(s.indexOf(n),1);else{var c=r.split(n.start.row,n.start.column);s=c.folds,s.shift(),c.start.row=s[0].start.row,c.start.column=s[0].start.column}this.$updating||(this.$useWrapMode?this.$updateWrapData(e,i):this.$updateRowLengthCache(e,i)),this.$modified=!0,this._signal("changeFold",{data:n,action:"remove"})},this.removeFolds=function(n){for(var r=[],e=0;e<n.length;e++)r.push(n[e]);r.forEach(function(i){this.removeFold(i)},this),this.$modified=!0},this.expandFold=function(n){this.removeFold(n),n.subFolds.forEach(function(r){n.restoreRange(r),this.addFold(r)},this),n.collapseChildren>0&&this.foldAll(n.start.row+1,n.end.row,n.collapseChildren-1),n.subFolds=[]},this.expandFolds=function(n){n.forEach(function(r){this.expandFold(r)},this)},this.unfold=function(n,r){var e,i;if(n==null)e=new R(0,0,this.getLength(),0),r==null&&(r=!0);else if(typeof n=="number")e=new R(n,0,n,this.getLine(n).length);else if("row"in n)e=R.fromPoints(n,n);else{if(Array.isArray(n))return i=[],n.forEach(function(s){i=i.concat(this.unfold(s))},this),i;e=n}i=this.getFoldsInRangeList(e);for(var o=i;i.length==1&&R.comparePoints(i[0].start,e.start)<0&&R.comparePoints(i[0].end,e.end)>0;)this.expandFolds(i),i=this.getFoldsInRangeList(e);if(r!=!1?this.removeFolds(i):this.expandFolds(i),o.length)return o},this.isRowFolded=function(n,r){return!!this.getFoldLine(n,r)},this.getRowFoldEnd=function(n,r){var e=this.getFoldLine(n,r);return e?e.end.row:n},this.getRowFoldStart=function(n,r){var e=this.getFoldLine(n,r);return e?e.start.row:n},this.getFoldDisplayLine=function(n,r,e,i,o){i==null&&(i=n.start.row),o==null&&(o=0),r==null&&(r=n.end.row),e==null&&(e=this.getLine(r).length);var s=this.doc,c="";return n.walk(function(g,w,p,b){if(!(w<i)){if(w==i){if(p<o)return;b=Math.max(o,b)}g!=null?c+=g:c+=s.getLine(w).substring(b,p)}},r,e),c},this.getDisplayLine=function(n,r,e,i){var o=this.getFoldLine(n);if(o)return this.getFoldDisplayLine(o,n,r,e,i);var s;return s=this.doc.getLine(n),s.substring(i||0,r||s.length)},this.$cloneFoldData=function(){var n=[];return n=this.$foldData.map(function(r){var e=r.folds.map(function(i){return i.clone()});return new M(n,e)}),n},this.toggleFold=function(n){var r=this.selection,e=r.getRange(),i,o;if(e.isEmpty()){var s=e.start;if(i=this.getFoldAt(s.row,s.column),i){this.expandFold(i);return}else(o=this.findMatchingBracket(s))?e.comparePoint(o)==1?e.end=o:(e.start=o,e.start.column++,e.end.column--):(o=this.findMatchingBracket({row:s.row,column:s.column+1}))?(e.comparePoint(o)==1?e.end=o:e.start=o,e.start.column++):e=this.getCommentFoldRange(s.row,s.column)||e}else{var c=this.getFoldsInRange(e);if(n&&c.length){this.expandFolds(c);return}else c.length==1&&(i=c[0])}if(i||(i=this.getFoldAt(e.start.row,e.start.column)),i&&i.range.toString()==e.toString()){this.expandFold(i);return}var g="...";if(!e.isMultiLine()){if(g=this.getTextRange(e),g.length<4)return;g=g.trim().substring(0,2)+".."}this.addFold(g,e)},this.getCommentFoldRange=function(n,r,e){var i=new a(this,n,r),o=i.getCurrentToken(),s=o&&o.type;if(o&&/^comment|string/.test(s)){s=s.match(/comment|string/)[0],s=="comment"&&(s+="|doc-start|\\.doc");var c=new RegExp(s),g=new R;if(e!=1){do o=i.stepBackward();while(o&&c.test(o.type)&&!/^comment.end/.test(o.type));o=i.stepForward()}if(g.start.row=i.getCurrentTokenRow(),g.start.column=i.getCurrentTokenColumn()+(/^comment.start/.test(o.type)?o.value.length:2),i=new a(this,n,r),e!=-1){var w=-1;do if(o=i.stepForward(),w==-1){var p=this.getState(i.$row);c.test(p)||(w=i.$row)}else if(i.$row>w)break;while(o&&c.test(o.type)&&!/^comment.start/.test(o.type));o=i.stepBackward()}else o=i.getCurrentToken();return g.end.row=i.getCurrentTokenRow(),g.end.column=i.getCurrentTokenColumn(),/^comment.end/.test(o.type)||(g.end.column+=o.value.length-2),g}},this.foldAll=function(n,r,e,i){e==null&&(e=1e5);var o=this.foldWidgets;if(o){r=r||this.getLength(),n=n||0;for(var s=n;s<r;s++)if(o[s]==null&&(o[s]=this.getFoldWidget(s)),o[s]=="start"&&!(i&&!i(s))){var c=this.getFoldWidgetRange(s);c&&c.isMultiLine()&&c.end.row<=r&&c.start.row>=n&&(s=c.end.row,c.collapseChildren=e,this.addFold("...",c))}}},this.foldToLevel=function(n){for(this.foldAll();n-- >0;)this.unfold(null,!1)},this.foldAllComments=function(){var n=this;this.foldAll(null,null,null,function(r){for(var e=n.getTokens(r),i=0;i<e.length;i++){var o=e[i];if(!(o.type=="text"&&/^\s+$/.test(o.value)))return!!/comment/.test(o.type)}})},this.$foldStyles={manual:1,markbegin:1,markbeginend:1},this.$foldStyle="markbegin",this.setFoldStyle=function(n){if(!this.$foldStyles[n])throw new Error("invalid fold style: "+n+"["+Object.keys(this.$foldStyles).join(", ")+"]");if(this.$foldStyle!=n){this.$foldStyle=n,n=="manual"&&this.unfold();var r=this.$foldMode;this.$setFolding(null),this.$setFolding(r)}},this.$setFolding=function(n){if(this.$foldMode!=n){if(this.$foldMode=n,this.off("change",this.$updateFoldWidgets),this.off("tokenizerUpdate",this.$tokenizerUpdateFoldWidgets),this._signal("changeAnnotation"),!n||this.$foldStyle=="manual"){this.foldWidgets=null;return}this.foldWidgets=[],this.getFoldWidget=n.getFoldWidget.bind(n,this,this.$foldStyle),this.getFoldWidgetRange=n.getFoldWidgetRange.bind(n,this,this.$foldStyle),this.$updateFoldWidgets=this.updateFoldWidgets.bind(this),this.$tokenizerUpdateFoldWidgets=this.tokenizerUpdateFoldWidgets.bind(this),this.on("change",this.$updateFoldWidgets),this.on("tokenizerUpdate",this.$tokenizerUpdateFoldWidgets)}},this.getParentFoldRangeData=function(n,r){var e=this.foldWidgets;if(!e||r&&e[n])return{};for(var i=n-1,o;i>=0;){var s=e[i];if(s==null&&(s=e[i]=this.getFoldWidget(i)),s=="start"){var c=this.getFoldWidgetRange(i);if(o||(o=c),c&&c.end.row>=n)break}i--}return{range:i!==-1&&c,firstRange:o}},this.onFoldWidgetClick=function(n,r){r instanceof l&&(r=r.domEvent);var e={children:r.shiftKey,all:r.ctrlKey||r.metaKey,siblings:r.altKey},i=this.$toggleFoldWidget(n,e);if(!i){var o=r.target||r.srcElement;o&&/ace_fold-widget/.test(o.className)&&(o.className+=" ace_invalid")}},this.$toggleFoldWidget=function(n,r){if(this.getFoldWidget){var e=this.getFoldWidget(n),i=this.getLine(n),o=e==="end"?-1:1,s=this.getFoldAt(n,o===-1?0:i.length,o);if(s)return r.children||r.all?this.removeFold(s):this.expandFold(s),s;var c=this.getFoldWidgetRange(n,!0);if(c&&!c.isMultiLine()&&(s=this.getFoldAt(c.start.row,c.start.column,1),s&&c.isEqual(s.range)))return this.removeFold(s),s;if(r.siblings){var g=this.getParentFoldRangeData(n);if(g.range)var w=g.range.start.row+1,p=g.range.end.row;this.foldAll(w,p,r.all?1e4:0)}else r.children?(p=c?c.end.row:this.getLength(),this.foldAll(n+1,p,r.all?1e4:0)):c&&(r.all&&(c.collapseChildren=1e4),this.addFold("...",c));return c}},this.toggleFoldWidget=function(n){var r=this.selection.getCursor().row;r=this.getRowFoldStart(r);var e=this.$toggleFoldWidget(r,{});if(!e){var i=this.getParentFoldRangeData(r,!0);if(e=i.range||i.firstRange,e){r=e.start.row;var o=this.getFoldAt(r,this.getLine(r).length,1);o?this.removeFold(o):this.addFold("...",e)}}},this.updateFoldWidgets=function(n){var r=n.start.row,e=n.end.row-r;if(e===0)this.foldWidgets[r]=null;else if(n.action=="remove")this.foldWidgets.splice(r,e+1,null);else{var i=Array(e+1);i.unshift(r,1),this.foldWidgets.splice.apply(this.foldWidgets,i)}},this.tokenizerUpdateFoldWidgets=function(n){var r=n.data;r.first!=r.last&&this.foldWidgets.length>r.first&&this.foldWidgets.splice(r.first,this.foldWidgets.length)}}k.Folding=t});ace.define("ace/edit_session/bracket_match",["require","exports","module","ace/token_iterator","ace/range"],function(x,k,H){"use strict";var R=x("../token_iterator").TokenIterator,M=x("../range").Range;function S(){this.findMatchingBracket=function(a,l){if(a.column==0)return null;var t=l||this.getLine(a.row).charAt(a.column-1);if(t=="")return null;var n=t.match(/([\(\[\{])|([\)\]\}])/);return n?n[1]?this.$findClosingBracket(n[1],a):this.$findOpeningBracket(n[2],a):null},this.getBracketRange=function(a){var l=this.getLine(a.row),t=!0,n,r=l.charAt(a.column-1),e=r&&r.match(/([\(\[\{])|([\)\]\}])/);if(e||(r=l.charAt(a.column),a={row:a.row,column:a.column+1},e=r&&r.match(/([\(\[\{])|([\)\]\}])/),t=!1),!e)return null;if(e[1]){var i=this.$findClosingBracket(e[1],a);if(!i)return null;n=M.fromPoints(a,i),t||(n.end.column++,n.start.column--),n.cursor=n.end}else{var i=this.$findOpeningBracket(e[2],a);if(!i)return null;n=M.fromPoints(i,a),t||(n.start.column++,n.end.column--),n.cursor=n.start}return n},this.getMatchingBracketRanges=function(a,l){var t=this.getLine(a.row),n=/([\(\[\{])|([\)\]\}])/,r=!l&&t.charAt(a.column-1),e=r&&r.match(n);if(e||(r=(l===void 0||l)&&t.charAt(a.column),a={row:a.row,column:a.column+1},e=r&&r.match(n)),!e)return null;var i=new M(a.row,a.column-1,a.row,a.column),o=e[1]?this.$findClosingBracket(e[1],a):this.$findOpeningBracket(e[2],a);if(!o)return[i];var s=new M(o.row,o.column,o.row,o.column+1);return[i,s]},this.$brackets={")":"(","(":")","]":"[","[":"]","{":"}","}":"{","<":">",">":"<"},this.$findOpeningBracket=function(a,l,t){var n=this.$brackets[a],r=1,e=new R(this,l.row,l.column),i=e.getCurrentToken();if(i||(i=e.stepForward()),!!i){t||(t=new RegExp("(\\.?"+i.type.replace(".","\\.").replace("rparen",".paren").replace(/\b(?:end)\b/,"(?:start|begin|end)").replace(/-close\b/,"-(close|open)")+")+"));for(var o=l.column-e.getCurrentTokenColumn()-2,s=i.value;;){for(;o>=0;){var c=s.charAt(o);if(c==n){if(r-=1,r==0)return{row:e.getCurrentTokenRow(),column:o+e.getCurrentTokenColumn()}}else c==a&&(r+=1);o-=1}do i=e.stepBackward();while(i&&!t.test(i.type));if(i==null)break;s=i.value,o=s.length-1}return null}},this.$findClosingBracket=function(a,l,t){var n=this.$brackets[a],r=1,e=new R(this,l.row,l.column),i=e.getCurrentToken();if(i||(i=e.stepForward()),!!i){t||(t=new RegExp("(\\.?"+i.type.replace(".","\\.").replace("lparen",".paren").replace(/\b(?:start|begin)\b/,"(?:start|begin|end)").replace(/-open\b/,"-(close|open)")+")+"));for(var o=l.column-e.getCurrentTokenColumn();;){for(var s=i.value,c=s.length;o<c;){var g=s.charAt(o);if(g==n){if(r-=1,r==0)return{row:e.getCurrentTokenRow(),column:o+e.getCurrentTokenColumn()}}else g==a&&(r+=1);o+=1}do i=e.stepForward();while(i&&!t.test(i.type));if(i==null)break;o=0}return null}},this.getMatchingTags=function(a){var l=new R(this,a.row,a.column),t=this.$findTagName(l);if(t){var n=l.stepBackward();return n.value==="<"?this.$findClosingTag(l,t):this.$findOpeningTag(l,t)}},this.$findTagName=function(a){var l=a.getCurrentToken(),t=!1,n=!1;if(l&&l.type.indexOf("tag-name")===-1)do n?l=a.stepBackward():l=a.stepForward(),l&&(l.value==="/>"?n=!0:l.type.indexOf("tag-name")!==-1&&(t=!0));while(l&&!t);return l},this.$findClosingTag=function(a,l){var t,n=l.value,r=l.value,e=0,i=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1);l=a.stepForward();var o=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+l.value.length),s=!1;do if(t=l,l=a.stepForward(),l){if(l.value===">"&&!s){var c=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1);s=!0}if(l.type.indexOf("tag-name")!==-1){if(n=l.value,r===n){if(t.value==="<")e++;else if(t.value==="</"&&(e--,e<0)){a.stepBackward();var g=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+2);l=a.stepForward();var w=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+l.value.length);if(l=a.stepForward(),l&&l.value===">")var p=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1);else return}}}else if(r===n&&l.value==="/>"&&(e--,e<0))var g=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+2),w=g,p=w,c=new M(o.end.row,o.end.column,o.end.row,o.end.column+1)}while(l&&e>=0);if(i&&c&&g&&p&&o&&w)return{openTag:new M(i.start.row,i.start.column,c.end.row,c.end.column),closeTag:new M(g.start.row,g.start.column,p.end.row,p.end.column),openTagName:o,closeTagName:w}},this.$findOpeningTag=function(a,l){var t=a.getCurrentToken(),n=l.value,r=0,e=a.getCurrentTokenRow(),i=a.getCurrentTokenColumn(),o=i+2,s=new M(e,i,e,o);a.stepForward();var c=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+l.value.length);if(l=a.stepForward(),!(!l||l.value!==">")){var g=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1);a.stepBackward(),a.stepBackward();do if(l=t,e=a.getCurrentTokenRow(),i=a.getCurrentTokenColumn(),o=i+l.value.length,t=a.stepBackward(),l){if(l.type.indexOf("tag-name")!==-1){if(n===l.value)if(t.value==="<"){if(r++,r>0){var w=new M(e,i,e,o),p=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1);do l=a.stepForward();while(l&&l.value!==">");var b=new M(a.getCurrentTokenRow(),a.getCurrentTokenColumn(),a.getCurrentTokenRow(),a.getCurrentTokenColumn()+1)}}else t.value==="</"&&r--}else if(l.value==="/>"){for(var y=0,d=t;d;){if(d.type.indexOf("tag-name")!==-1&&d.value===n){r--;break}else if(d.value==="<")break;d=a.stepBackward(),y++}for(var v=0;v<y;v++)a.stepForward()}}while(t&&r<=0);if(p&&b&&s&&g&&w&&c)return{openTag:new M(p.start.row,p.start.column,b.end.row,b.end.column),closeTag:new M(s.start.row,s.start.column,g.end.row,g.end.column),openTagName:w,closeTagName:c}}}}k.BracketMatch=S});ace.define("ace/edit_session",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/bidihandler","ace/config","ace/lib/event_emitter","ace/selection","ace/mode/text","ace/range","ace/document","ace/background_tokenizer","ace/search_highlight","ace/edit_session/folding","ace/edit_session/bracket_match"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./lib/lang"),S=x("./bidihandler").BidiHandler,a=x("./config"),l=x("./lib/event_emitter").EventEmitter,t=x("./selection").Selection,n=x("./mode/text").Mode,r=x("./range").Range,e=x("./document").Document,i=x("./background_tokenizer").BackgroundTokenizer,o=x("./search_highlight").SearchHighlight,s=function(){function f(u,A){this.$breakpoints=[],this.$decorations=[],this.$frontMarkers={},this.$backMarkers={},this.$markerId=1,this.$undoSelect=!0,this.$foldData=[],this.id="session"+ ++f.$uid,this.$foldData.toString=function(){return this.join(`
`)},this.bgTokenizer=new i(new n().getTokenizer(),this);var m=this;this.bgTokenizer.on("update",function(h){m._signal("tokenizerUpdate",h)}),this.on("changeFold",this.onChangeFold.bind(this)),this.$onChange=this.onChange.bind(this),(typeof u!="object"||!u.getLine)&&(u=new e(u)),this.setDocument(u),this.selection=new t(this),this.$bidiHandler=new S(this),a.resetOptions(this),this.setMode(A),a._signal("session",this),this.destroyed=!1}return f.prototype.setDocument=function(u){this.doc&&this.doc.off("change",this.$onChange),this.doc=u,u.on("change",this.$onChange,!0),this.bgTokenizer.setDocument(this.getDocument()),this.resetCaches()},f.prototype.getDocument=function(){return this.doc},f.prototype.$resetRowCache=function(u){if(!u){this.$docRowCache=[],this.$screenRowCache=[];return}var A=this.$docRowCache.length,m=this.$getRowCacheIndex(this.$docRowCache,u)+1;A>m&&(this.$docRowCache.splice(m,A),this.$screenRowCache.splice(m,A))},f.prototype.$getRowCacheIndex=function(u,A){for(var m=0,h=u.length-1;m<=h;){var C=m+h>>1,L=u[C];if(A>L)m=C+1;else if(A<L)h=C-1;else return C}return m-1},f.prototype.resetCaches=function(){this.$modified=!0,this.$wrapData=[],this.$rowLengthCache=[],this.$resetRowCache(0),this.destroyed||this.bgTokenizer.start(0)},f.prototype.onChangeFold=function(u){var A=u.data;this.$resetRowCache(A.start.row)},f.prototype.onChange=function(u){this.$modified=!0,this.$bidiHandler.onChange(u),this.$resetRowCache(u.start.row);var A=this.$updateInternalDataOnChange(u);!this.$fromUndo&&this.$undoManager&&(A&&A.length&&(this.$undoManager.add({action:"removeFolds",folds:A},this.mergeUndoDeltas),this.mergeUndoDeltas=!0),this.$undoManager.add(u,this.mergeUndoDeltas),this.mergeUndoDeltas=!0,this.$informUndoManager.schedule()),this.bgTokenizer.$updateOnChange(u),this._signal("change",u)},f.prototype.setValue=function(u){this.doc.setValue(u),this.selection.moveTo(0,0),this.$resetRowCache(0),this.setUndoManager(this.$undoManager),this.getUndoManager().reset()},f.prototype.toString=function(){return this.doc.getValue()},f.prototype.getSelection=function(){return this.selection},f.prototype.getState=function(u){return this.bgTokenizer.getState(u)},f.prototype.getTokens=function(u){return this.bgTokenizer.getTokens(u)},f.prototype.getTokenAt=function(u,A){var m=this.bgTokenizer.getTokens(u),h,C=0;if(A==null){var L=m.length-1;C=this.getLine(u).length}else for(var L=0;L<m.length&&(C+=m[L].value.length,!(C>=A));L++);return h=m[L],h?(h.index=L,h.start=C-h.value.length,h):null},f.prototype.setUndoManager=function(u){if(this.$undoManager=u,this.$informUndoManager&&this.$informUndoManager.cancel(),u){var A=this;u.addSession(this),this.$syncInformUndoManager=function(){A.$informUndoManager.cancel(),A.mergeUndoDeltas=!1},this.$informUndoManager=M.delayedCall(this.$syncInformUndoManager)}else this.$syncInformUndoManager=function(){}},f.prototype.markUndoGroup=function(){this.$syncInformUndoManager&&this.$syncInformUndoManager()},f.prototype.getUndoManager=function(){return this.$undoManager||this.$defaultUndoManager},f.prototype.getTabString=function(){return this.getUseSoftTabs()?M.stringRepeat(" ",this.getTabSize()):"	"},f.prototype.setUseSoftTabs=function(u){this.setOption("useSoftTabs",u)},f.prototype.getUseSoftTabs=function(){return this.$useSoftTabs&&!this.$mode.$indentWithTabs},f.prototype.setTabSize=function(u){this.setOption("tabSize",u)},f.prototype.getTabSize=function(){return this.$tabSize},f.prototype.isTabStop=function(u){return this.$useSoftTabs&&u.column%this.$tabSize===0},f.prototype.setNavigateWithinSoftTabs=function(u){this.setOption("navigateWithinSoftTabs",u)},f.prototype.getNavigateWithinSoftTabs=function(){return this.$navigateWithinSoftTabs},f.prototype.setOverwrite=function(u){this.setOption("overwrite",u)},f.prototype.getOverwrite=function(){return this.$overwrite},f.prototype.toggleOverwrite=function(){this.setOverwrite(!this.$overwrite)},f.prototype.addGutterDecoration=function(u,A){this.$decorations[u]||(this.$decorations[u]=""),this.$decorations[u]+=" "+A,this._signal("changeBreakpoint",{})},f.prototype.removeGutterDecoration=function(u,A){this.$decorations[u]=(this.$decorations[u]||"").replace(" "+A,""),this._signal("changeBreakpoint",{})},f.prototype.getBreakpoints=function(){return this.$breakpoints},f.prototype.setBreakpoints=function(u){this.$breakpoints=[];for(var A=0;A<u.length;A++)this.$breakpoints[u[A]]="ace_breakpoint";this._signal("changeBreakpoint",{})},f.prototype.clearBreakpoints=function(){this.$breakpoints=[],this._signal("changeBreakpoint",{})},f.prototype.setBreakpoint=function(u,A){A===void 0&&(A="ace_breakpoint"),A?this.$breakpoints[u]=A:delete this.$breakpoints[u],this._signal("changeBreakpoint",{})},f.prototype.clearBreakpoint=function(u){delete this.$breakpoints[u],this._signal("changeBreakpoint",{})},f.prototype.addMarker=function(u,A,m,h){var C=this.$markerId++,L={range:u,type:m||"line",renderer:typeof m=="function"?m:null,clazz:A,inFront:!!h,id:C};return h?(this.$frontMarkers[C]=L,this._signal("changeFrontMarker")):(this.$backMarkers[C]=L,this._signal("changeBackMarker")),C},f.prototype.addDynamicMarker=function(u,A){if(u.update){var m=this.$markerId++;return u.id=m,u.inFront=!!A,A?(this.$frontMarkers[m]=u,this._signal("changeFrontMarker")):(this.$backMarkers[m]=u,this._signal("changeBackMarker")),u}},f.prototype.removeMarker=function(u){var A=this.$frontMarkers[u]||this.$backMarkers[u];if(A){var m=A.inFront?this.$frontMarkers:this.$backMarkers;delete m[u],this._signal(A.inFront?"changeFrontMarker":"changeBackMarker")}},f.prototype.getMarkers=function(u){return u?this.$frontMarkers:this.$backMarkers},f.prototype.highlight=function(u){if(!this.$searchHighlight){var A=new o(null,"ace_selected-word","text");this.$searchHighlight=this.addDynamicMarker(A)}this.$searchHighlight.setRegexp(u)},f.prototype.highlightLines=function(u,A,m,h){typeof A!="number"&&(m=A,A=u),m||(m="ace_step");var C=new r(u,0,A,1/0);return C.id=this.addMarker(C,m,"fullLine",h),C},f.prototype.setAnnotations=function(u){this.$annotations=u,this._signal("changeAnnotation",{})},f.prototype.getAnnotations=function(){return this.$annotations||[]},f.prototype.clearAnnotations=function(){this.setAnnotations([])},f.prototype.$detectNewLine=function(u){var A=u.match(/^.*?(\r?\n)/m);A?this.$autoNewLine=A[1]:this.$autoNewLine=`
`},f.prototype.getWordRange=function(u,A){var m=this.getLine(u),h=!1;if(A>0&&(h=!!m.charAt(A-1).match(this.tokenRe)),h||(h=!!m.charAt(A).match(this.tokenRe)),h)var C=this.tokenRe;else if(/^\s+$/.test(m.slice(A-1,A+1)))var C=/\s/;else var C=this.nonTokenRe;var L=A;if(L>0){do L--;while(L>=0&&m.charAt(L).match(C));L++}for(var T=A;T<m.length&&m.charAt(T).match(C);)T++;return new r(u,L,u,T)},f.prototype.getAWordRange=function(u,A){for(var m=this.getWordRange(u,A),h=this.getLine(m.end.row);h.charAt(m.end.column).match(/[ \t]/);)m.end.column+=1;return m},f.prototype.setNewLineMode=function(u){this.doc.setNewLineMode(u)},f.prototype.getNewLineMode=function(){return this.doc.getNewLineMode()},f.prototype.setUseWorker=function(u){this.setOption("useWorker",u)},f.prototype.getUseWorker=function(){return this.$useWorker},f.prototype.onReloadTokenizer=function(u){var A=u.data;this.bgTokenizer.start(A.first),this._signal("tokenizerUpdate",u)},f.prototype.setMode=function(u,A){if(u&&typeof u=="object"){if(u.getTokenizer)return this.$onChangeMode(u);var m=u,h=m.path}else h=u||"ace/mode/text";if(this.$modes["ace/mode/text"]||(this.$modes["ace/mode/text"]=new n),this.$modes[h]&&!m){this.$onChangeMode(this.$modes[h]),A&&A();return}this.$modeId=h,a.loadModule(["mode",h],function(C){if(this.$modeId!==h)return A&&A();this.$modes[h]&&!m?this.$onChangeMode(this.$modes[h]):C&&C.Mode&&(C=new C.Mode(m),m||(this.$modes[h]=C,C.$id=h),this.$onChangeMode(C)),A&&A()}.bind(this)),this.$mode||this.$onChangeMode(this.$modes["ace/mode/text"],!0)},f.prototype.$onChangeMode=function(u,A){if(A||(this.$modeId=u.$id),this.$mode!==u){var m=this.$mode;this.$mode=u,this.$stopWorker(),this.$useWorker&&this.$startWorker();var h=u.getTokenizer();if(h.on!==void 0){var C=this.onReloadTokenizer.bind(this);h.on("update",C)}this.bgTokenizer.setTokenizer(h),this.bgTokenizer.setDocument(this.getDocument()),this.tokenRe=u.tokenRe,this.nonTokenRe=u.nonTokenRe,A||(u.attachToSession&&u.attachToSession(this),this.$options.wrapMethod.set.call(this,this.$wrapMethod),this.$setFolding(u.foldingRules),this.bgTokenizer.start(0),this._emit("changeMode",{oldMode:m,mode:u}))}},f.prototype.$stopWorker=function(){this.$worker&&(this.$worker.terminate(),this.$worker=null)},f.prototype.$startWorker=function(){try{this.$worker=this.$mode.createWorker(this)}catch(u){a.warn("Could not load worker",u),this.$worker=null}},f.prototype.getMode=function(){return this.$mode},f.prototype.setScrollTop=function(u){this.$scrollTop===u||isNaN(u)||(this.$scrollTop=u,this._signal("changeScrollTop",u))},f.prototype.getScrollTop=function(){return this.$scrollTop},f.prototype.setScrollLeft=function(u){this.$scrollLeft===u||isNaN(u)||(this.$scrollLeft=u,this._signal("changeScrollLeft",u))},f.prototype.getScrollLeft=function(){return this.$scrollLeft},f.prototype.getScreenWidth=function(){return this.$computeWidth(),this.lineWidgets?Math.max(this.getLineWidgetMaxWidth(),this.screenWidth):this.screenWidth},f.prototype.getLineWidgetMaxWidth=function(){if(this.lineWidgetsWidth!=null)return this.lineWidgetsWidth;var u=0;return this.lineWidgets.forEach(function(A){A&&A.screenWidth>u&&(u=A.screenWidth)}),this.lineWidgetWidth=u},f.prototype.$computeWidth=function(u){if(this.$modified||u){if(this.$modified=!1,this.$useWrapMode)return this.screenWidth=this.$wrapLimit;for(var A=this.doc.getAllLines(),m=this.$rowLengthCache,h=0,C=0,L=this.$foldData[C],T=L?L.start.row:1/0,E=A.length,_=0;_<E;_++){if(_>T){if(_=L.end.row+1,_>=E)break;L=this.$foldData[C++],T=L?L.start.row:1/0}m[_]==null&&(m[_]=this.$getStringScreenWidth(A[_])[0]),m[_]>h&&(h=m[_])}this.screenWidth=h}},f.prototype.getLine=function(u){return this.doc.getLine(u)},f.prototype.getLines=function(u,A){return this.doc.getLines(u,A)},f.prototype.getLength=function(){return this.doc.getLength()},f.prototype.getTextRange=function(u){return this.doc.getTextRange(u||this.selection.getRange())},f.prototype.insert=function(u,A){return this.doc.insert(u,A)},f.prototype.remove=function(u){return this.doc.remove(u)},f.prototype.removeFullLines=function(u,A){return this.doc.removeFullLines(u,A)},f.prototype.undoChanges=function(u,A){if(u.length){this.$fromUndo=!0;for(var m=u.length-1;m!=-1;m--){var h=u[m];h.action=="insert"||h.action=="remove"?this.doc.revertDelta(h):h.folds&&this.addFolds(h.folds)}!A&&this.$undoSelect&&(u.selectionBefore?this.selection.fromJSON(u.selectionBefore):this.selection.setRange(this.$getUndoSelection(u,!0))),this.$fromUndo=!1}},f.prototype.redoChanges=function(u,A){if(u.length){this.$fromUndo=!0;for(var m=0;m<u.length;m++){var h=u[m];(h.action=="insert"||h.action=="remove")&&this.doc.$safeApplyDelta(h)}!A&&this.$undoSelect&&(u.selectionAfter?this.selection.fromJSON(u.selectionAfter):this.selection.setRange(this.$getUndoSelection(u,!1))),this.$fromUndo=!1}},f.prototype.setUndoSelect=function(u){this.$undoSelect=u},f.prototype.$getUndoSelection=function(u,A){function m(E){return A?E.action!=="insert":E.action==="insert"}for(var h,C,L=0;L<u.length;L++){var T=u[L];if(T.start){if(!h){m(T)?h=r.fromPoints(T.start,T.end):h=r.fromPoints(T.start,T.start);continue}m(T)?(C=T.start,h.compare(C.row,C.column)==-1&&h.setStart(C),C=T.end,h.compare(C.row,C.column)==1&&h.setEnd(C)):(C=T.start,h.compare(C.row,C.column)==-1&&(h=r.fromPoints(T.start,T.start)))}}return h},f.prototype.replace=function(u,A){return this.doc.replace(u,A)},f.prototype.moveText=function(u,A,m){var h=this.getTextRange(u),C=this.getFoldsInRange(u),L=r.fromPoints(A,A);if(!m){this.remove(u);var T=u.start.row-u.end.row,E=T?-u.end.column:u.start.column-u.end.column;E&&(L.start.row==u.end.row&&L.start.column>u.end.column&&(L.start.column+=E),L.end.row==u.end.row&&L.end.column>u.end.column&&(L.end.column+=E)),T&&L.start.row>=u.end.row&&(L.start.row+=T,L.end.row+=T)}if(L.end=this.insert(L.start,h),C.length){var _=u.start,O=L.start,T=O.row-_.row,E=O.column-_.column;this.addFolds(C.map(function(F){return F=F.clone(),F.start.row==_.row&&(F.start.column+=E),F.end.row==_.row&&(F.end.column+=E),F.start.row+=T,F.end.row+=T,F}))}return L},f.prototype.indentRows=function(u,A,m){m=m.replace(/\t/g,this.getTabString());for(var h=u;h<=A;h++)this.doc.insertInLine({row:h,column:0},m)},f.prototype.outdentRows=function(u){for(var A=u.collapseRows(),m=new r(0,0,0,0),h=this.getTabSize(),C=A.start.row;C<=A.end.row;++C){var L=this.getLine(C);m.start.row=C,m.end.row=C;for(var T=0;T<h&&L.charAt(T)==" ";++T);T<h&&L.charAt(T)=="	"?(m.start.column=T,m.end.column=T+1):(m.start.column=0,m.end.column=T),this.remove(m)}},f.prototype.$moveLines=function(u,A,m){if(u=this.getRowFoldStart(u),A=this.getRowFoldEnd(A),m<0){var h=this.getRowFoldStart(u+m);if(h<0)return 0;var C=h-u}else if(m>0){var h=this.getRowFoldEnd(A+m);if(h>this.doc.getLength()-1)return 0;var C=h-A}else{u=this.$clipRowToDocument(u),A=this.$clipRowToDocument(A);var C=A-u+1}var L=new r(u,0,A,Number.MAX_VALUE),T=this.getFoldsInRange(L).map(function(_){return _=_.clone(),_.start.row+=C,_.end.row+=C,_}),E=m==0?this.doc.getLines(u,A):this.doc.removeFullLines(u,A);return this.doc.insertFullLines(u+C,E),T.length&&this.addFolds(T),C},f.prototype.moveLinesUp=function(u,A){return this.$moveLines(u,A,-1)},f.prototype.moveLinesDown=function(u,A){return this.$moveLines(u,A,1)},f.prototype.duplicateLines=function(u,A){return this.$moveLines(u,A,0)},f.prototype.$clipRowToDocument=function(u){return Math.max(0,Math.min(u,this.doc.getLength()-1))},f.prototype.$clipColumnToRow=function(u,A){return A<0?0:Math.min(this.doc.getLine(u).length,A)},f.prototype.$clipPositionToDocument=function(u,A){if(A=Math.max(0,A),u<0)u=0,A=0;else{var m=this.doc.getLength();u>=m?(u=m-1,A=this.doc.getLine(m-1).length):A=Math.min(this.doc.getLine(u).length,A)}return{row:u,column:A}},f.prototype.$clipRangeToDocument=function(u){u.start.row<0?(u.start.row=0,u.start.column=0):u.start.column=this.$clipColumnToRow(u.start.row,u.start.column);var A=this.doc.getLength()-1;return u.end.row>A?(u.end.row=A,u.end.column=this.doc.getLine(A).length):u.end.column=this.$clipColumnToRow(u.end.row,u.end.column),u},f.prototype.setUseWrapMode=function(u){if(u!=this.$useWrapMode){if(this.$useWrapMode=u,this.$modified=!0,this.$resetRowCache(0),u){var A=this.getLength();this.$wrapData=Array(A),this.$updateWrapData(0,A-1)}this._signal("changeWrapMode")}},f.prototype.getUseWrapMode=function(){return this.$useWrapMode},f.prototype.setWrapLimitRange=function(u,A){(this.$wrapLimitRange.min!==u||this.$wrapLimitRange.max!==A)&&(this.$wrapLimitRange={min:u,max:A},this.$modified=!0,this.$bidiHandler.markAsDirty(),this.$useWrapMode&&this._signal("changeWrapMode"))},f.prototype.adjustWrapLimit=function(u,A){var m=this.$wrapLimitRange;m.max<0&&(m={min:A,max:A});var h=this.$constrainWrapLimit(u,m.min,m.max);return h!=this.$wrapLimit&&h>1?(this.$wrapLimit=h,this.$modified=!0,this.$useWrapMode&&(this.$updateWrapData(0,this.getLength()-1),this.$resetRowCache(0),this._signal("changeWrapLimit")),!0):!1},f.prototype.$constrainWrapLimit=function(u,A,m){return A&&(u=Math.max(A,u)),m&&(u=Math.min(m,u)),u},f.prototype.getWrapLimit=function(){return this.$wrapLimit},f.prototype.setWrapLimit=function(u){this.setWrapLimitRange(u,u)},f.prototype.getWrapLimitRange=function(){return{min:this.$wrapLimitRange.min,max:this.$wrapLimitRange.max}},f.prototype.$updateInternalDataOnChange=function(u){var A=this.$useWrapMode,m=u.action,h=u.start,C=u.end,L=h.row,T=C.row,E=T-L,_=null;if(this.$updating=!0,E!=0)if(m==="remove"){this[A?"$wrapData":"$rowLengthCache"].splice(L,E);var O=this.$foldData;_=this.getFoldsInRange(u),this.removeFolds(_);var I=this.getFoldLine(C.row),W=0;if(I){I.addRemoveChars(C.row,C.column,h.column-C.column),I.shiftRow(-E);var F=this.getFoldLine(L);F&&F!==I&&(F.merge(I),I=F),W=O.indexOf(I)+1}for(W;W<O.length;W++){var I=O[W];I.start.row>=C.row&&I.shiftRow(-E)}T=L}else{var D=Array(E);D.unshift(L,0);var B=A?this.$wrapData:this.$rowLengthCache;B.splice.apply(B,D);var O=this.$foldData,I=this.getFoldLine(L),W=0;if(I){var U=I.range.compareInside(h.row,h.column);U==0?(I=I.split(h.row,h.column),I&&(I.shiftRow(E),I.addRemoveChars(T,0,C.column-h.column))):U==-1&&(I.addRemoveChars(L,0,C.column-h.column),I.shiftRow(E)),W=O.indexOf(I)+1}for(W;W<O.length;W++){var I=O[W];I.start.row>=L&&I.shiftRow(E)}}else{E=Math.abs(u.start.column-u.end.column),m==="remove"&&(_=this.getFoldsInRange(u),this.removeFolds(_),E=-E);var I=this.getFoldLine(L);I&&I.addRemoveChars(L,h.column,E)}return A&&this.$wrapData.length!=this.doc.getLength()&&console.error("doc.getLength() and $wrapData.length have to be the same!"),this.$updating=!1,A?this.$updateWrapData(L,T):this.$updateRowLengthCache(L,T),_},f.prototype.$updateRowLengthCache=function(u,A,m){this.$rowLengthCache[u]=null,this.$rowLengthCache[A]=null},f.prototype.$updateWrapData=function(u,A){var m=this.doc.getAllLines(),h=this.getTabSize(),C=this.$wrapData,L=this.$wrapLimit,T,E,_=u;for(A=Math.min(A,m.length-1);_<=A;)E=this.getFoldLine(_,E),E?(T=[],E.walk(function(O,I,W,F){var D;if(O!=null){D=this.$getDisplayTokens(O,T.length),D[0]=w;for(var B=1;B<D.length;B++)D[B]=p}else D=this.$getDisplayTokens(m[I].substring(F,W),T.length);T=T.concat(D)}.bind(this),E.end.row,m[E.end.row].length+1),C[E.start.row]=this.$computeWrapSplits(T,L,h),_=E.end.row+1):(T=this.$getDisplayTokens(m[_]),C[_]=this.$computeWrapSplits(T,L,h),_++)},f.prototype.$computeWrapSplits=function(u,A,m){if(u.length==0)return[];var h=[],C=u.length,L=0,T=0,E=this.$wrapAsCode,_=this.$indentedSoftWrap,O=A<=Math.max(2*m,8)||_===!1?0:Math.floor(A/2);function I(){var U=0;if(O===0)return U;if(_)for(var P=0;P<u.length;P++){var G=u[P];if(G==y)U+=1;else if(G==d)U+=m;else{if(G==v)continue;break}}return E&&_!==!1&&(U+=m),Math.min(U,O)}function W(U){for(var P=U-L,G=L;G<U;G++){var z=u[G];(z===12||z===2)&&(P-=1)}h.length||(F=I(),h.indent=F),T+=P,h.push(T),L=U}for(var F=0;C-L>A-F;){var D=L+A-F;if(u[D-1]>=y&&u[D]>=y){W(D);continue}if(u[D]==w||u[D]==p){for(D;D!=L-1&&u[D]!=w;D--);if(D>L){W(D);continue}for(D=L+A,D;D<u.length&&u[D]==p;D++);if(D==u.length)break;W(D);continue}for(var B=Math.max(D-(A-(A>>2)),L-1);D>B&&u[D]<w;)D--;if(E){for(;D>B&&u[D]<w;)D--;for(;D>B&&u[D]==b;)D--}else for(;D>B&&u[D]<y;)D--;if(D>B){W(++D);continue}D=L+A,u[D]==g&&D--,W(D-F)}return h},f.prototype.$getDisplayTokens=function(u,A){var m=[],h;A=A||0;for(var C=0;C<u.length;C++){var L=u.charCodeAt(C);if(L==9){h=this.getScreenTabSize(m.length+A),m.push(d);for(var T=1;T<h;T++)m.push(v)}else L==32?m.push(y):L>39&&L<48||L>57&&L<64?m.push(b):L>=4352&&$(L)?m.push(c,g):m.push(c)}return m},f.prototype.$getStringScreenWidth=function(u,A,m){if(A==0)return[0,0];A==null&&(A=1/0),m=m||0;var h,C;for(C=0;C<u.length&&(h=u.charCodeAt(C),h==9?m+=this.getScreenTabSize(m):h>=4352&&$(h)?m+=2:m+=1,!(m>A));C++);return[m,C]},f.prototype.getRowLength=function(u){var A=1;return this.lineWidgets&&(A+=this.lineWidgets[u]&&this.lineWidgets[u].rowCount||0),!this.$useWrapMode||!this.$wrapData[u]?A:this.$wrapData[u].length+A},f.prototype.getRowLineCount=function(u){return!this.$useWrapMode||!this.$wrapData[u]?1:this.$wrapData[u].length+1},f.prototype.getRowWrapIndent=function(u){if(this.$useWrapMode){var A=this.screenToDocumentPosition(u,Number.MAX_VALUE),m=this.$wrapData[A.row];return m.length&&m[0]<A.column?m.indent:0}else return 0},f.prototype.getScreenLastRowColumn=function(u){var A=this.screenToDocumentPosition(u,Number.MAX_VALUE);return this.documentToScreenColumn(A.row,A.column)},f.prototype.getDocumentLastRowColumn=function(u,A){var m=this.documentToScreenRow(u,A);return this.getScreenLastRowColumn(m)},f.prototype.getDocumentLastRowColumnPosition=function(u,A){var m=this.documentToScreenRow(u,A);return this.screenToDocumentPosition(m,Number.MAX_VALUE/10)},f.prototype.getRowSplitData=function(u){if(this.$useWrapMode)return this.$wrapData[u]},f.prototype.getScreenTabSize=function(u){return this.$tabSize-(u%this.$tabSize|0)},f.prototype.screenToDocumentRow=function(u,A){return this.screenToDocumentPosition(u,A).row},f.prototype.screenToDocumentColumn=function(u,A){return this.screenToDocumentPosition(u,A).column},f.prototype.screenToDocumentPosition=function(u,A,m){if(u<0)return{row:0,column:0};var h,C=0,L=0,T,E=0,_=0,O=this.$screenRowCache,I=this.$getRowCacheIndex(O,u),W=O.length;if(W&&I>=0)var E=O[I],C=this.$docRowCache[I],F=u>O[W-1];else var F=!W;for(var D=this.getLength()-1,B=this.getNextFoldLine(C),U=B?B.start.row:1/0;E<=u&&(_=this.getRowLength(C),!(E+_>u||C>=D));)E+=_,C++,C>U&&(C=B.end.row+1,B=this.getNextFoldLine(C,B),U=B?B.start.row:1/0),F&&(this.$docRowCache.push(C),this.$screenRowCache.push(E));if(B&&B.start.row<=C)h=this.getFoldDisplayLine(B),C=B.start.row;else{if(E+_<=u||C>D)return{row:D,column:this.getLine(D).length};h=this.getLine(C),B=null}var P=0,G=Math.floor(u-E);if(this.$useWrapMode){var z=this.$wrapData[C];z&&(T=z[G],G>0&&z.length&&(P=z.indent,L=z[G-1]||z[z.length-1],h=h.substring(L)))}return m!==void 0&&this.$bidiHandler.isBidiRow(E+G,C,G)&&(A=this.$bidiHandler.offsetToCol(m)),L+=this.$getStringScreenWidth(h,A-P)[1],this.$useWrapMode&&L>=T&&(L=T-1),B?B.idxToPosition(L):{row:C,column:L}},f.prototype.documentToScreenPosition=function(u,A){if(typeof A>"u")var m=this.$clipPositionToDocument(u.row,u.column);else m=this.$clipPositionToDocument(u,A);u=m.row,A=m.column;var h=0,C=null,L=null;L=this.getFoldAt(u,A,1),L&&(u=L.start.row,A=L.start.column);var T,E=0,_=this.$docRowCache,O=this.$getRowCacheIndex(_,u),I=_.length;if(I&&O>=0)var E=_[O],h=this.$screenRowCache[O],W=u>_[I-1];else var W=!I;for(var F=this.getNextFoldLine(E),D=F?F.start.row:1/0;E<u;){if(E>=D){if(T=F.end.row+1,T>u)break;F=this.getNextFoldLine(T,F),D=F?F.start.row:1/0}else T=E+1;h+=this.getRowLength(E),E=T,W&&(this.$docRowCache.push(E),this.$screenRowCache.push(h))}var B="";F&&E>=D?(B=this.getFoldDisplayLine(F,u,A),C=F.start.row):(B=this.getLine(u).substring(0,A),C=u);var U=0;if(this.$useWrapMode){var P=this.$wrapData[C];if(P){for(var G=0;B.length>=P[G];)h++,G++;B=B.substring(P[G-1]||0,B.length),U=G>0?P.indent:0}}return this.lineWidgets&&this.lineWidgets[E]&&this.lineWidgets[E].rowsAbove&&(h+=this.lineWidgets[E].rowsAbove),{row:h,column:U+this.$getStringScreenWidth(B)[0]}},f.prototype.documentToScreenColumn=function(u,A){return this.documentToScreenPosition(u,A).column},f.prototype.documentToScreenRow=function(u,A){return this.documentToScreenPosition(u,A).row},f.prototype.getScreenLength=function(){var u=0,A=null;if(this.$useWrapMode)for(var C=this.$wrapData.length,L=0,h=0,A=this.$foldData[h++],T=A?A.start.row:1/0;L<C;){var E=this.$wrapData[L];u+=E?E.length+1:1,L++,L>T&&(L=A.end.row+1,A=this.$foldData[h++],T=A?A.start.row:1/0)}else{u=this.getLength();for(var m=this.$foldData,h=0;h<m.length;h++)A=m[h],u-=A.end.row-A.start.row}return this.lineWidgets&&(u+=this.$getWidgetScreenLength()),u},f.prototype.$setFontMetrics=function(u){this.$enableVarChar&&(this.$getStringScreenWidth=function(A,m,h){if(m===0)return[0,0];m||(m=1/0),h=h||0;var C,L;for(L=0;L<A.length&&(C=A.charAt(L),C==="	"?h+=this.getScreenTabSize(h):h+=u.getCharacterWidth(C),!(h>m));L++);return[h,L]})},f.prototype.destroy=function(){this.destroyed||(this.bgTokenizer.setDocument(null),this.bgTokenizer.cleanup(),this.destroyed=!0),this.$stopWorker(),this.removeAllListeners(),this.doc&&this.doc.off("change",this.$onChange),this.selection.detach()},f}();s.$uid=0,s.prototype.$modes=a.$modes,s.prototype.getValue=s.prototype.toString,s.prototype.$defaultUndoManager={undo:function(){},redo:function(){},hasUndo:function(){},hasRedo:function(){},reset:function(){},add:function(){},addSelection:function(){},startNewGroup:function(){},addSession:function(){}},s.prototype.$overwrite=!1,s.prototype.$mode=null,s.prototype.$modeId=null,s.prototype.$scrollTop=0,s.prototype.$scrollLeft=0,s.prototype.$wrapLimit=80,s.prototype.$useWrapMode=!1,s.prototype.$wrapLimitRange={min:null,max:null},s.prototype.lineWidgets=null,s.prototype.isFullWidth=$,R.implement(s.prototype,l);var c=1,g=2,w=3,p=4,b=9,y=10,d=11,v=12;function $(f){return f<4352?!1:f>=4352&&f<=4447||f>=4515&&f<=4519||f>=4602&&f<=4607||f>=9001&&f<=9002||f>=11904&&f<=11929||f>=11931&&f<=12019||f>=12032&&f<=12245||f>=12272&&f<=12283||f>=12288&&f<=12350||f>=12353&&f<=12438||f>=12441&&f<=12543||f>=12549&&f<=12589||f>=12593&&f<=12686||f>=12688&&f<=12730||f>=12736&&f<=12771||f>=12784&&f<=12830||f>=12832&&f<=12871||f>=12880&&f<=13054||f>=13056&&f<=19903||f>=19968&&f<=42124||f>=42128&&f<=42182||f>=43360&&f<=43388||f>=44032&&f<=55203||f>=55216&&f<=55238||f>=55243&&f<=55291||f>=63744&&f<=64255||f>=65040&&f<=65049||f>=65072&&f<=65106||f>=65108&&f<=65126||f>=65128&&f<=65131||f>=65281&&f<=65376||f>=65504&&f<=65510}x("./edit_session/folding").Folding.call(s.prototype),x("./edit_session/bracket_match").BracketMatch.call(s.prototype),a.defineOptions(s.prototype,"session",{wrap:{set:function(f){if(!f||f=="off"?f=!1:f=="free"?f=!0:f=="printMargin"?f=-1:typeof f=="string"&&(f=parseInt(f,10)||!1),this.$wrap!=f)if(this.$wrap=f,!f)this.setUseWrapMode(!1);else{var u=typeof f=="number"?f:null;this.setWrapLimitRange(u,u),this.setUseWrapMode(!0)}},get:function(){return this.getUseWrapMode()?this.$wrap==-1?"printMargin":this.getWrapLimitRange().min?this.$wrap:"free":"off"},handlesSet:!0},wrapMethod:{set:function(f){f=f=="auto"?this.$mode.type!="text":f!="text",f!=this.$wrapAsCode&&(this.$wrapAsCode=f,this.$useWrapMode&&(this.$useWrapMode=!1,this.setUseWrapMode(!0)))},initialValue:"auto"},indentedSoftWrap:{set:function(){this.$useWrapMode&&(this.$useWrapMode=!1,this.setUseWrapMode(!0))},initialValue:!0},firstLineNumber:{set:function(){this._signal("changeBreakpoint")},initialValue:1},useWorker:{set:function(f){this.$useWorker=f,this.$stopWorker(),f&&this.$startWorker()},initialValue:!0},useSoftTabs:{initialValue:!0},tabSize:{set:function(f){f=parseInt(f),f>0&&this.$tabSize!==f&&(this.$modified=!0,this.$rowLengthCache=[],this.$tabSize=f,this._signal("changeTabSize"))},initialValue:4,handlesSet:!0},navigateWithinSoftTabs:{initialValue:!1},foldStyle:{set:function(f){this.setFoldStyle(f)},handlesSet:!0},overwrite:{set:function(f){this._signal("changeOverwrite")},initialValue:!1},newLineMode:{set:function(f){this.doc.setNewLineMode(f)},get:function(){return this.doc.getNewLineMode()},handlesSet:!0},mode:{set:function(f){this.setMode(f)},get:function(){return this.$modeId},handlesSet:!0}}),k.EditSession=s});ace.define("ace/search",["require","exports","module","ace/lib/lang","ace/lib/oop","ace/range"],function(x,k,H){"use strict";var R=x("./lib/lang"),M=x("./lib/oop"),S=x("./range").Range,a=function(){function t(){this.$options={}}return t.prototype.set=function(n){return M.mixin(this.$options,n),this},t.prototype.getOptions=function(){return R.copyObject(this.$options)},t.prototype.setOptions=function(n){this.$options=n},t.prototype.find=function(n){var r=this.$options,e=this.$matchIterator(n,r);if(!e)return!1;var i=null;return e.forEach(function(o,s,c,g){return i=new S(o,s,c,g),s==g&&r.start&&r.start.start&&r.skipCurrent!=!1&&i.isEqual(r.start)?(i=null,!1):!0}),i},t.prototype.findAll=function(n){var r=this.$options;if(!r.needle)return[];this.$assembleRegExp(r);var e=r.range,i=e?n.getLines(e.start.row,e.end.row):n.doc.getAllLines(),o=[],s=r.re;if(r.$isMultiLine){var c=s.length,g=i.length-c,w;e:for(var p=s.offset||0;p<=g;p++){for(var b=0;b<c;b++)if(i[p+b].search(s[b])==-1)continue e;var y=i[p],d=i[p+c-1],v=y.length-y.match(s[0])[0].length,$=d.match(s[c-1])[0].length;w&&w.end.row===p&&w.end.column>v||(o.push(w=new S(p,v,p+c-1,$)),c>2&&(p=p+c-2))}}else for(var f=0;f<i.length;f++)for(var u=R.getMatchOffsets(i[f],s),b=0;b<u.length;b++){var A=u[b];o.push(new S(f,A.offset,f,A.offset+A.length))}if(e){for(var m=e.start.column,h=e.end.column,f=0,b=o.length-1;f<b&&o[f].start.column<m&&o[f].start.row==0;)f++;for(var C=e.end.row-e.start.row;f<b&&o[b].end.column>h&&o[b].end.row==C;)b--;for(o=o.slice(f,b+1),f=0,b=o.length;f<b;f++)o[f].start.row+=e.start.row,o[f].end.row+=e.start.row}return o},t.prototype.replace=function(n,r){var e=this.$options,i=this.$assembleRegExp(e);if(e.$isMultiLine)return r;if(i){var o=i.exec(n);if(!o||o[0].length!=n.length)return null;if(r=n.replace(i,r),e.preserveCase){r=r.split("");for(var s=Math.min(n.length,n.length);s--;){var c=n[s];c&&c.toLowerCase()!=c?r[s]=r[s].toUpperCase():r[s]=r[s].toLowerCase()}r=r.join("")}return r}},t.prototype.$assembleRegExp=function(n,r){if(n.needle instanceof RegExp)return n.re=n.needle;var e=n.needle;if(!n.needle)return n.re=!1;n.$supportsUnicodeFlag===void 0&&(n.$supportsUnicodeFlag=R.supportsUnicodeFlag());try{new RegExp(e,"u")}catch{n.$supportsUnicodeFlag=!1}n.regExp||(e=R.escapeRegExp(e)),n.wholeWord&&(e=l(e,n));var i=n.caseSensitive?"gm":"gmi";if(n.$supportsUnicodeFlag&&(i+="u"),n.$isMultiLine=!r&&/[\n\r]/.test(e),n.$isMultiLine)return n.re=this.$assembleMultilineRegExp(e,i);try{var o=new RegExp(e,i)}catch{o=!1}return n.re=o},t.prototype.$assembleMultilineRegExp=function(n,r){for(var e=n.replace(/\r\n|\r|\n/g,`$
^`).split(`
`),i=[],o=0;o<e.length;o++)try{i.push(new RegExp(e[o],r))}catch{return!1}return i},t.prototype.$matchIterator=function(n,r){var e=this.$assembleRegExp(r);if(!e)return!1;var i=r.backwards==!0,o=r.skipCurrent!=!1,s=r.range,c=r.start;c||(c=s?s[i?"end":"start"]:n.selection.getRange()),c.start&&(c=c[o!=i?"end":"start"]);var g=s?s.start.row:0,w=s?s.end.row:n.getLength()-1;if(i)var p=function(d){var v=c.row;if(!y(v,c.column,d)){for(v--;v>=g;v--)if(y(v,Number.MAX_VALUE,d))return;if(r.wrap!=!1){for(v=w,g=c.row;v>=g;v--)if(y(v,Number.MAX_VALUE,d))return}}};else var p=function(v){var $=c.row;if(!y($,c.column,v)){for($=$+1;$<=w;$++)if(y($,0,v))return;if(r.wrap!=!1){for($=g,w=c.row;$<=w;$++)if(y($,0,v))return}}};if(r.$isMultiLine)var b=e.length,y=function(d,v,$){var f=i?d-b+1:d;if(!(f<0||f+b>n.getLength())){var u=n.getLine(f),A=u.search(e[0]);if(!(!i&&A<v||A===-1)){for(var m=1;m<b;m++)if(u=n.getLine(f+m),u.search(e[m])==-1)return;var h=u.match(e[b-1])[0].length;if(!(i&&h>v)&&$(f,A,f+b-1,h))return!0}}};else if(i)var y=function(v,$,f){var u=n.getLine(v),A=[],m,h=0;for(e.lastIndex=0;m=e.exec(u);){var C=m[0].length;if(h=m.index,!C){if(h>=u.length)break;e.lastIndex=h+=1}if(m.index+C>$)break;A.push(m.index,C)}for(var L=A.length-1;L>=0;L-=2){var T=A[L-1],C=A[L];if(f(v,T,v,T+C))return!0}};else var y=function(v,$,f){var u=n.getLine(v),A,m;for(e.lastIndex=$;m=e.exec(u);){var h=m[0].length;if(A=m.index,f(v,A,v,A+h))return!0;if(!h&&(e.lastIndex=A+=1,A>=u.length))return!1}};return{forEach:p}},t}();function l(t,n){var r=R.supportsLookbehind();function e(c,g){g===void 0&&(g=!0);var w=r&&n.$supportsUnicodeFlag?new RegExp("[\\p{L}\\p{N}_]","u"):new RegExp("\\w");return w.test(c)||n.regExp?r&&n.$supportsUnicodeFlag?g?"(?<=^|[^\\p{L}\\p{N}_])":"(?=[^\\p{L}\\p{N}_]|$)":"\\b":""}var i=Array.from(t),o=i[0],s=i[i.length-1];return e(o)+t+e(s,!1)}k.Search=a});ace.define("ace/keyboard/hash_handler",["require","exports","module","ace/lib/keys","ace/lib/useragent"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var r=function(e,i){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,s){o.__proto__=s}||function(o,s){for(var c in s)Object.prototype.hasOwnProperty.call(s,c)&&(o[c]=s[c])},r(e,i)};return function(e,i){if(typeof i!="function"&&i!==null)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");r(e,i);function o(){this.constructor=e}e.prototype=i===null?Object.create(i):(o.prototype=i.prototype,new o)}}(),M=x("../lib/keys"),S=x("../lib/useragent"),a=M.KEY_MODS,l=function(){function r(e,i){this.$init(e,i,!1)}return r.prototype.$init=function(e,i,o){this.platform=i||(S.isMac?"mac":"win"),this.commands={},this.commandKeyBinding={},this.addCommands(e),this.$singleCommand=o},r.prototype.addCommand=function(e){this.commands[e.name]&&this.removeCommand(e),this.commands[e.name]=e,e.bindKey&&this._buildKeyHash(e)},r.prototype.removeCommand=function(e,i){var o=e&&(typeof e=="string"?e:e.name);e=this.commands[o],i||delete this.commands[o];var s=this.commandKeyBinding;for(var c in s){var g=s[c];if(g==e)delete s[c];else if(Array.isArray(g)){var w=g.indexOf(e);w!=-1&&(g.splice(w,1),g.length==1&&(s[c]=g[0]))}}},r.prototype.bindKey=function(e,i,o){if(typeof e=="object"&&e&&(o==null&&(o=e.position),e=e[this.platform]),!!e){if(typeof i=="function")return this.addCommand({exec:i,bindKey:e,name:i.name||e});e.split("|").forEach(function(s){var c="";if(s.indexOf(" ")!=-1){var g=s.split(/\s+/);s=g.pop(),g.forEach(function(b){var y=this.parseKeys(b),d=a[y.hashId]+y.key;c+=(c?" ":"")+d,this._addCommandToBinding(c,"chainKeys")},this),c+=" "}var w=this.parseKeys(s),p=a[w.hashId]+w.key;this._addCommandToBinding(c+p,i,o)},this)}},r.prototype._addCommandToBinding=function(e,i,o){var s=this.commandKeyBinding,c;if(!i)delete s[e];else if(!s[e]||this.$singleCommand)s[e]=i;else{Array.isArray(s[e])?(c=s[e].indexOf(i))!=-1&&s[e].splice(c,1):s[e]=[s[e]],typeof o!="number"&&(o=t(i));var g=s[e];for(c=0;c<g.length;c++){var w=g[c],p=t(w);if(p>o)break}g.splice(c,0,i)}},r.prototype.addCommands=function(e){e&&Object.keys(e).forEach(function(i){var o=e[i];if(o){if(typeof o=="string")return this.bindKey(o,i);typeof o=="function"&&(o={exec:o}),typeof o=="object"&&(o.name||(o.name=i),this.addCommand(o))}},this)},r.prototype.removeCommands=function(e){Object.keys(e).forEach(function(i){this.removeCommand(e[i])},this)},r.prototype.bindKeys=function(e){Object.keys(e).forEach(function(i){this.bindKey(i,e[i])},this)},r.prototype._buildKeyHash=function(e){this.bindKey(e.bindKey,e)},r.prototype.parseKeys=function(e){var i=e.toLowerCase().split(/[\-\+]([\-\+])?/).filter(function(p){return p}),o=i.pop(),s=M[o];if(M.FUNCTION_KEYS[s])o=M.FUNCTION_KEYS[s].toLowerCase();else if(i.length){if(i.length==1&&i[0]=="shift")return{key:o.toUpperCase(),hashId:-1}}else return{key:o,hashId:-1};for(var c=0,g=i.length;g--;){var w=M.KEY_MODS[i[g]];if(w==null)return typeof console<"u"&&console.error("invalid modifier "+i[g]+" in "+e),!1;c|=w}return{key:o,hashId:c}},r.prototype.findKeyCommand=function(e,i){var o=a[e]+i;return this.commandKeyBinding[o]},r.prototype.handleKeyboard=function(e,i,o,s){if(!(s<0)){var c=a[i]+o,g=this.commandKeyBinding[c];return e.$keyChain&&(e.$keyChain+=" "+c,g=this.commandKeyBinding[e.$keyChain]||g),g&&(g=="chainKeys"||g[g.length-1]=="chainKeys")?(e.$keyChain=e.$keyChain||c,{command:"null"}):(e.$keyChain&&((!i||i==4)&&o.length==1?e.$keyChain=e.$keyChain.slice(0,-c.length-1):(i==-1||s>0)&&(e.$keyChain="")),{command:g})}},r.prototype.getStatusText=function(e,i){return i.$keyChain||""},r}();function t(r){return typeof r=="object"&&r.bindKey&&r.bindKey.position||(r.isDefault?-100:0)}var n=function(r){R(e,r);function e(i,o){var s=r.call(this,i,o)||this;return s.$singleCommand=!0,s}return e}(l);n.call=function(r,e,i){l.prototype.$init.call(r,e,i,!0)},l.call=function(r,e,i){l.prototype.$init.call(r,e,i,!1)},k.HashHandler=n,k.MultiHashHandler=l});ace.define("ace/commands/command_manager",["require","exports","module","ace/lib/oop","ace/keyboard/hash_handler","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var t=function(n,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])},t(n,r)};return function(n,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");t(n,r);function e(){this.constructor=n}n.prototype=r===null?Object.create(r):(e.prototype=r.prototype,new e)}}(),M=x("../lib/oop"),S=x("../keyboard/hash_handler").MultiHashHandler,a=x("../lib/event_emitter").EventEmitter,l=function(t){R(n,t);function n(r,e){var i=t.call(this,e,r)||this;return i.byName=i.commands,i.setDefaultHandler("exec",function(o){return o.args?o.command.exec(o.editor,o.args,o.event,!1):o.command.exec(o.editor,{},o.event,!0)}),i}return n.prototype.exec=function(r,e,i){if(Array.isArray(r)){for(var o=r.length;o--;)if(this.exec(r[o],e,i))return!0;return!1}if(typeof r=="string"&&(r=this.commands[r]),!r||e&&e.$readOnly&&!r.readOnly||this.$checkCommandState!=!1&&r.isAvailable&&!r.isAvailable(e))return!1;var s={editor:e,command:r,args:i};return s.returnValue=this._emit("exec",s),this._signal("afterExec",s),s.returnValue!==!1},n.prototype.toggleRecording=function(r){if(!this.$inReplay)return r&&r._emit("changeStatus"),this.recording?(this.macro.pop(),this.off("exec",this.$addCommandToMacro),this.macro.length||(this.macro=this.oldMacro),this.recording=!1):(this.$addCommandToMacro||(this.$addCommandToMacro=function(e){this.macro.push([e.command,e.args])}.bind(this)),this.oldMacro=this.macro,this.macro=[],this.on("exec",this.$addCommandToMacro),this.recording=!0)},n.prototype.replay=function(r){if(!(this.$inReplay||!this.macro)){if(this.recording)return this.toggleRecording(r);try{this.$inReplay=!0,this.macro.forEach(function(e){typeof e=="string"?this.exec(e,r):this.exec(e[0],r,e[1])},this)}finally{this.$inReplay=!1}}},n.prototype.trimMacro=function(r){return r.map(function(e){return typeof e[0]!="string"&&(e[0]=e[0].name),e[1]||(e=e[0]),e})},n}(S);M.implement(l.prototype,a),k.CommandManager=l});ace.define("ace/commands/default_commands",["require","exports","module","ace/lib/lang","ace/config","ace/range"],function(x,k,H){"use strict";var R=x("../lib/lang"),M=x("../config"),S=x("../range").Range;function a(t,n){return{win:t,mac:n}}k.commands=[{name:"showSettingsMenu",description:"Show settings menu",bindKey:a("Ctrl-,","Command-,"),exec:function(t){M.loadModule("ace/ext/settings_menu",function(n){n.init(t),t.showSettingsMenu()})},readOnly:!0},{name:"goToNextError",description:"Go to next error",bindKey:a("Alt-E","F4"),exec:function(t){M.loadModule("ace/ext/error_marker",function(n){n.showErrorMarker(t,1)})},scrollIntoView:"animate",readOnly:!0},{name:"goToPreviousError",description:"Go to previous error",bindKey:a("Alt-Shift-E","Shift-F4"),exec:function(t){M.loadModule("ace/ext/error_marker",function(n){n.showErrorMarker(t,-1)})},scrollIntoView:"animate",readOnly:!0},{name:"selectall",description:"Select all",bindKey:a("Ctrl-A","Command-A"),exec:function(t){t.selectAll()},readOnly:!0},{name:"centerselection",description:"Center selection",bindKey:a(null,"Ctrl-L"),exec:function(t){t.centerSelection()},readOnly:!0},{name:"gotoline",description:"Go to line...",bindKey:a("Ctrl-L","Command-L"),exec:function(t,n){typeof n=="number"&&!isNaN(n)&&t.gotoLine(n),t.prompt({$type:"gotoLine"})},readOnly:!0},{name:"fold",bindKey:a("Alt-L|Ctrl-F1","Command-Alt-L|Command-F1"),exec:function(t){t.session.toggleFold(!1)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"unfold",bindKey:a("Alt-Shift-L|Ctrl-Shift-F1","Command-Alt-Shift-L|Command-Shift-F1"),exec:function(t){t.session.toggleFold(!0)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"toggleFoldWidget",description:"Toggle fold widget",bindKey:a("F2","F2"),exec:function(t){t.session.toggleFoldWidget()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"toggleParentFoldWidget",description:"Toggle parent fold widget",bindKey:a("Alt-F2","Alt-F2"),exec:function(t){t.session.toggleFoldWidget(!0)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"foldall",description:"Fold all",bindKey:a(null,"Ctrl-Command-Option-0"),exec:function(t){t.session.foldAll()},scrollIntoView:"center",readOnly:!0},{name:"foldAllComments",description:"Fold all comments",bindKey:a(null,"Ctrl-Command-Option-0"),exec:function(t){t.session.foldAllComments()},scrollIntoView:"center",readOnly:!0},{name:"foldOther",description:"Fold other",bindKey:a("Alt-0","Command-Option-0"),exec:function(t){t.session.foldAll(),t.session.unfold(t.selection.getAllRanges())},scrollIntoView:"center",readOnly:!0},{name:"unfoldall",description:"Unfold all",bindKey:a("Alt-Shift-0","Command-Option-Shift-0"),exec:function(t){t.session.unfold()},scrollIntoView:"center",readOnly:!0},{name:"findnext",description:"Find next",bindKey:a("Ctrl-K","Command-G"),exec:function(t){t.findNext()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"findprevious",description:"Find previous",bindKey:a("Ctrl-Shift-K","Command-Shift-G"),exec:function(t){t.findPrevious()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"selectOrFindNext",description:"Select or find next",bindKey:a("Alt-K","Ctrl-G"),exec:function(t){t.selection.isEmpty()?t.selection.selectWord():t.findNext()},readOnly:!0},{name:"selectOrFindPrevious",description:"Select or find previous",bindKey:a("Alt-Shift-K","Ctrl-Shift-G"),exec:function(t){t.selection.isEmpty()?t.selection.selectWord():t.findPrevious()},readOnly:!0},{name:"find",description:"Find",bindKey:a("Ctrl-F","Command-F"),exec:function(t){M.loadModule("ace/ext/searchbox",function(n){n.Search(t)})},readOnly:!0},{name:"overwrite",description:"Overwrite",bindKey:"Insert",exec:function(t){t.toggleOverwrite()},readOnly:!0},{name:"selecttostart",description:"Select to start",bindKey:a("Ctrl-Shift-Home","Command-Shift-Home|Command-Shift-Up"),exec:function(t){t.getSelection().selectFileStart()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"gotostart",description:"Go to start",bindKey:a("Ctrl-Home","Command-Home|Command-Up"),exec:function(t){t.navigateFileStart()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"selectup",description:"Select up",bindKey:a("Shift-Up","Shift-Up|Ctrl-Shift-P"),exec:function(t){t.getSelection().selectUp()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"golineup",description:"Go line up",bindKey:a("Up","Up|Ctrl-P"),exec:function(t,n){t.navigateUp(n.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttoend",description:"Select to end",bindKey:a("Ctrl-Shift-End","Command-Shift-End|Command-Shift-Down"),exec:function(t){t.getSelection().selectFileEnd()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"gotoend",description:"Go to end",bindKey:a("Ctrl-End","Command-End|Command-Down"),exec:function(t){t.navigateFileEnd()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"selectdown",description:"Select down",bindKey:a("Shift-Down","Shift-Down|Ctrl-Shift-N"),exec:function(t){t.getSelection().selectDown()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"golinedown",description:"Go line down",bindKey:a("Down","Down|Ctrl-N"),exec:function(t,n){t.navigateDown(n.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectwordleft",description:"Select word left",bindKey:a("Ctrl-Shift-Left","Option-Shift-Left"),exec:function(t){t.getSelection().selectWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotowordleft",description:"Go to word left",bindKey:a("Ctrl-Left","Option-Left"),exec:function(t){t.navigateWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttolinestart",description:"Select to line start",bindKey:a("Alt-Shift-Left","Command-Shift-Left|Ctrl-Shift-A"),exec:function(t){t.getSelection().selectLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotolinestart",description:"Go to line start",bindKey:a("Alt-Left|Home","Command-Left|Home|Ctrl-A"),exec:function(t){t.navigateLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectleft",description:"Select left",bindKey:a("Shift-Left","Shift-Left|Ctrl-Shift-B"),exec:function(t){t.getSelection().selectLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotoleft",description:"Go to left",bindKey:a("Left","Left|Ctrl-B"),exec:function(t,n){t.navigateLeft(n.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectwordright",description:"Select word right",bindKey:a("Ctrl-Shift-Right","Option-Shift-Right"),exec:function(t){t.getSelection().selectWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotowordright",description:"Go to word right",bindKey:a("Ctrl-Right","Option-Right"),exec:function(t){t.navigateWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttolineend",description:"Select to line end",bindKey:a("Alt-Shift-Right","Command-Shift-Right|Shift-End|Ctrl-Shift-E"),exec:function(t){t.getSelection().selectLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotolineend",description:"Go to line end",bindKey:a("Alt-Right|End","Command-Right|End|Ctrl-E"),exec:function(t){t.navigateLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectright",description:"Select right",bindKey:a("Shift-Right","Shift-Right"),exec:function(t){t.getSelection().selectRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotoright",description:"Go to right",bindKey:a("Right","Right|Ctrl-F"),exec:function(t,n){t.navigateRight(n.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectpagedown",description:"Select page down",bindKey:"Shift-PageDown",exec:function(t){t.selectPageDown()},readOnly:!0},{name:"pagedown",description:"Page down",bindKey:a(null,"Option-PageDown"),exec:function(t){t.scrollPageDown()},readOnly:!0},{name:"gotopagedown",description:"Go to page down",bindKey:a("PageDown","PageDown|Ctrl-V"),exec:function(t){t.gotoPageDown()},readOnly:!0},{name:"selectpageup",description:"Select page up",bindKey:"Shift-PageUp",exec:function(t){t.selectPageUp()},readOnly:!0},{name:"pageup",description:"Page up",bindKey:a(null,"Option-PageUp"),exec:function(t){t.scrollPageUp()},readOnly:!0},{name:"gotopageup",description:"Go to page up",bindKey:"PageUp",exec:function(t){t.gotoPageUp()},readOnly:!0},{name:"scrollup",description:"Scroll up",bindKey:a("Ctrl-Up",null),exec:function(t){t.renderer.scrollBy(0,-2*t.renderer.layerConfig.lineHeight)},readOnly:!0},{name:"scrolldown",description:"Scroll down",bindKey:a("Ctrl-Down",null),exec:function(t){t.renderer.scrollBy(0,2*t.renderer.layerConfig.lineHeight)},readOnly:!0},{name:"selectlinestart",description:"Select line start",bindKey:"Shift-Home",exec:function(t){t.getSelection().selectLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectlineend",description:"Select line end",bindKey:"Shift-End",exec:function(t){t.getSelection().selectLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"togglerecording",description:"Toggle recording",bindKey:a("Ctrl-Alt-E","Command-Option-E"),exec:function(t){t.commands.toggleRecording(t)},readOnly:!0},{name:"replaymacro",description:"Replay macro",bindKey:a("Ctrl-Shift-E","Command-Shift-E"),exec:function(t){t.commands.replay(t)},readOnly:!0},{name:"jumptomatching",description:"Jump to matching",bindKey:a("Ctrl-\\|Ctrl-P","Command-\\"),exec:function(t){t.jumpToMatching()},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"selecttomatching",description:"Select to matching",bindKey:a("Ctrl-Shift-\\|Ctrl-Shift-P","Command-Shift-\\"),exec:function(t){t.jumpToMatching(!0)},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"expandToMatching",description:"Expand to matching",bindKey:a("Ctrl-Shift-M","Ctrl-Shift-M"),exec:function(t){t.jumpToMatching(!0,!0)},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"passKeysToBrowser",description:"Pass keys to browser",bindKey:a(null,null),exec:function(){},passEvent:!0,readOnly:!0},{name:"copy",description:"Copy",exec:function(t){},readOnly:!0},{name:"cut",description:"Cut",exec:function(t){var n=t.$copyWithEmptySelection&&t.selection.isEmpty(),r=n?t.selection.getLineRange():t.selection.getRange();t._emit("cut",r),r.isEmpty()||t.session.remove(r),t.clearSelection()},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"paste",description:"Paste",exec:function(t,n){t.$handlePaste(n)},scrollIntoView:"cursor"},{name:"removeline",description:"Remove line",bindKey:a("Ctrl-D","Command-D"),exec:function(t){t.removeLines()},scrollIntoView:"cursor",multiSelectAction:"forEachLine"},{name:"duplicateSelection",description:"Duplicate selection",bindKey:a("Ctrl-Shift-D","Command-Shift-D"),exec:function(t){t.duplicateSelection()},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"sortlines",description:"Sort lines",bindKey:a("Ctrl-Alt-S","Command-Alt-S"),exec:function(t){t.sortLines()},scrollIntoView:"selection",multiSelectAction:"forEachLine"},{name:"togglecomment",description:"Toggle comment",bindKey:a("Ctrl-/","Command-/"),exec:function(t){t.toggleCommentLines()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"toggleBlockComment",description:"Toggle block comment",bindKey:a("Ctrl-Shift-/","Command-Shift-/"),exec:function(t){t.toggleBlockComment()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"modifyNumberUp",description:"Modify number up",bindKey:a("Ctrl-Shift-Up","Alt-Shift-Up"),exec:function(t){t.modifyNumber(1)},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"modifyNumberDown",description:"Modify number down",bindKey:a("Ctrl-Shift-Down","Alt-Shift-Down"),exec:function(t){t.modifyNumber(-1)},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"replace",description:"Replace",bindKey:a("Ctrl-H","Command-Option-F"),exec:function(t){M.loadModule("ace/ext/searchbox",function(n){n.Search(t,!0)})}},{name:"undo",description:"Undo",bindKey:a("Ctrl-Z","Command-Z"),exec:function(t){t.undo()}},{name:"redo",description:"Redo",bindKey:a("Ctrl-Shift-Z|Ctrl-Y","Command-Shift-Z|Command-Y"),exec:function(t){t.redo()}},{name:"copylinesup",description:"Copy lines up",bindKey:a("Alt-Shift-Up","Command-Option-Up"),exec:function(t){t.copyLinesUp()},scrollIntoView:"cursor"},{name:"movelinesup",description:"Move lines up",bindKey:a("Alt-Up","Option-Up"),exec:function(t){t.moveLinesUp()},scrollIntoView:"cursor"},{name:"copylinesdown",description:"Copy lines down",bindKey:a("Alt-Shift-Down","Command-Option-Down"),exec:function(t){t.copyLinesDown()},scrollIntoView:"cursor"},{name:"movelinesdown",description:"Move lines down",bindKey:a("Alt-Down","Option-Down"),exec:function(t){t.moveLinesDown()},scrollIntoView:"cursor"},{name:"del",description:"Delete",bindKey:a("Delete","Delete|Ctrl-D|Shift-Delete"),exec:function(t){t.remove("right")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"backspace",description:"Backspace",bindKey:a("Shift-Backspace|Backspace","Ctrl-Backspace|Shift-Backspace|Backspace|Ctrl-H"),exec:function(t){t.remove("left")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"cut_or_delete",description:"Cut or delete",bindKey:a("Shift-Delete",null),exec:function(t){if(t.selection.isEmpty())t.remove("left");else return!1},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolinestart",description:"Remove to line start",bindKey:a("Alt-Backspace","Command-Backspace"),exec:function(t){t.removeToLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolineend",description:"Remove to line end",bindKey:a("Alt-Delete","Ctrl-K|Command-Delete"),exec:function(t){t.removeToLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolinestarthard",description:"Remove to line start hard",bindKey:a("Ctrl-Shift-Backspace",null),exec:function(t){var n=t.selection.getRange();n.start.column=0,t.session.remove(n)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolineendhard",description:"Remove to line end hard",bindKey:a("Ctrl-Shift-Delete",null),exec:function(t){var n=t.selection.getRange();n.end.column=Number.MAX_VALUE,t.session.remove(n)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removewordleft",description:"Remove word left",bindKey:a("Ctrl-Backspace","Alt-Backspace|Ctrl-Alt-Backspace"),exec:function(t){t.removeWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removewordright",description:"Remove word right",bindKey:a("Ctrl-Delete","Alt-Delete"),exec:function(t){t.removeWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"outdent",description:"Outdent",bindKey:a("Shift-Tab","Shift-Tab"),exec:function(t){t.blockOutdent()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"indent",description:"Indent",bindKey:a("Tab","Tab"),exec:function(t){t.indent()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"blockoutdent",description:"Block outdent",bindKey:a("Ctrl-[","Ctrl-["),exec:function(t){t.blockOutdent()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"blockindent",description:"Block indent",bindKey:a("Ctrl-]","Ctrl-]"),exec:function(t){t.blockIndent()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"insertstring",description:"Insert string",exec:function(t,n){t.insert(n)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"inserttext",description:"Insert text",exec:function(t,n){t.insert(R.stringRepeat(n.text||"",n.times||1))},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"splitline",description:"Split line",bindKey:a(null,"Ctrl-O"),exec:function(t){t.splitLine()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"transposeletters",description:"Transpose letters",bindKey:a("Alt-Shift-X","Ctrl-T"),exec:function(t){t.transposeLetters()},multiSelectAction:function(t){t.transposeSelections(1)},scrollIntoView:"cursor"},{name:"touppercase",description:"To uppercase",bindKey:a("Ctrl-U","Ctrl-U"),exec:function(t){t.toUpperCase()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"tolowercase",description:"To lowercase",bindKey:a("Ctrl-Shift-U","Ctrl-Shift-U"),exec:function(t){t.toLowerCase()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"autoindent",description:"Auto Indent",bindKey:a(null,null),exec:function(t){t.autoIndent()},multiSelectAction:"forEachLine",scrollIntoView:"animate"},{name:"expandtoline",description:"Expand to line",bindKey:a("Ctrl-Shift-L","Command-Shift-L"),exec:function(t){var n=t.selection.getRange();n.start.column=n.end.column=0,n.end.row++,t.selection.setRange(n,!1)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"openlink",bindKey:a("Ctrl+F3","F3"),exec:function(t){t.openLink()}},{name:"joinlines",description:"Join lines",bindKey:a(null,null),exec:function(t){for(var n=t.selection.isBackwards(),r=n?t.selection.getSelectionLead():t.selection.getSelectionAnchor(),e=n?t.selection.getSelectionAnchor():t.selection.getSelectionLead(),i=t.session.doc.getLine(r.row).length,o=t.session.doc.getTextRange(t.selection.getRange()),s=o.replace(/\n\s*/," ").length,c=t.session.doc.getLine(r.row),g=r.row+1;g<=e.row+1;g++){var w=R.stringTrimLeft(R.stringTrimRight(t.session.doc.getLine(g)));w.length!==0&&(w=" "+w),c+=w}e.row+1<t.session.doc.getLength()-1&&(c+=t.session.doc.getNewLineCharacter()),t.clearSelection(),t.session.doc.replace(new S(r.row,0,e.row+2,0),c),s>0?(t.selection.moveCursorTo(r.row,r.column),t.selection.selectTo(r.row,r.column+s)):(i=t.session.doc.getLine(r.row).length>i?i+1:i,t.selection.moveCursorTo(r.row,i))},multiSelectAction:"forEach",readOnly:!0},{name:"invertSelection",description:"Invert selection",bindKey:a(null,null),exec:function(t){var n=t.session.doc.getLength()-1,r=t.session.doc.getLine(n).length,e=t.selection.rangeList.ranges,i=[];e.length<1&&(e=[t.selection.getRange()]);for(var o=0;o<e.length;o++)o==e.length-1&&(e[o].end.row===n&&e[o].end.column===r||i.push(new S(e[o].end.row,e[o].end.column,n,r))),o===0?e[o].start.row===0&&e[o].start.column===0||i.push(new S(0,0,e[o].start.row,e[o].start.column)):i.push(new S(e[o-1].end.row,e[o-1].end.column,e[o].start.row,e[o].start.column));t.exitMultiSelectMode(),t.clearSelection();for(var o=0;o<i.length;o++)t.selection.addRange(i[o],!1)},readOnly:!0,scrollIntoView:"none"},{name:"addLineAfter",description:"Add new line after the current line",exec:function(t){t.selection.clearSelection(),t.navigateLineEnd(),t.insert(`
`)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"addLineBefore",description:"Add new line before the current line",exec:function(t){t.selection.clearSelection();var n=t.getCursorPosition();t.selection.moveTo(n.row-1,Number.MAX_VALUE),t.insert(`
`),n.row===0&&t.navigateUp()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"openCommandPallete",description:"Open command palette",bindKey:a("F1","F1"),exec:function(t){t.prompt({$type:"commands"})},readOnly:!0},{name:"modeSelect",description:"Change language mode...",bindKey:a(null,null),exec:function(t){t.prompt({$type:"modes"})},readOnly:!0}];for(var l=1;l<9;l++)k.commands.push({name:"foldToLevel"+l,description:"Fold To Level "+l,level:l,exec:function(t){t.session.foldToLevel(this.level)},scrollIntoView:"center",readOnly:!0})});ace.define("ace/line_widgets",["require","exports","module","ace/lib/dom"],function(x,k,H){"use strict";var R=x("./lib/dom"),M=function(){function S(a){this.session=a,this.session.widgetManager=this,this.session.getRowLength=this.getRowLength,this.session.$getWidgetScreenLength=this.$getWidgetScreenLength,this.updateOnChange=this.updateOnChange.bind(this),this.renderWidgets=this.renderWidgets.bind(this),this.measureWidgets=this.measureWidgets.bind(this),this.session._changedWidgets=[],this.$onChangeEditor=this.$onChangeEditor.bind(this),this.session.on("change",this.updateOnChange),this.session.on("changeFold",this.updateOnFold),this.session.on("changeEditor",this.$onChangeEditor)}return S.prototype.getRowLength=function(a){var l;return this.lineWidgets?l=this.lineWidgets[a]&&this.lineWidgets[a].rowCount||0:l=0,!this.$useWrapMode||!this.$wrapData[a]?1+l:this.$wrapData[a].length+1+l},S.prototype.$getWidgetScreenLength=function(){var a=0;return this.lineWidgets.forEach(function(l){l&&l.rowCount&&!l.hidden&&(a+=l.rowCount)}),a},S.prototype.$onChangeEditor=function(a){this.attach(a.editor)},S.prototype.attach=function(a){a&&a.widgetManager&&a.widgetManager!=this&&a.widgetManager.detach(),this.editor!=a&&(this.detach(),this.editor=a,a&&(a.widgetManager=this,a.renderer.on("beforeRender",this.measureWidgets),a.renderer.on("afterRender",this.renderWidgets)))},S.prototype.detach=function(a){var l=this.editor;if(l){this.editor=null,l.widgetManager=null,l.renderer.off("beforeRender",this.measureWidgets),l.renderer.off("afterRender",this.renderWidgets);var t=this.session.lineWidgets;t&&t.forEach(function(n){n&&n.el&&n.el.parentNode&&(n._inDocument=!1,n.el.parentNode.removeChild(n.el))})}},S.prototype.updateOnFold=function(a,l){var t=l.lineWidgets;if(!(!t||!a.action)){for(var n=a.data,r=n.start.row,e=n.end.row,i=a.action=="add",o=r+1;o<e;o++)t[o]&&(t[o].hidden=i);t[e]&&(i?t[r]?t[e].hidden=i:t[r]=t[e]:(t[r]==t[e]&&(t[r]=void 0),t[e].hidden=i))}},S.prototype.updateOnChange=function(a){var l=this.session.lineWidgets;if(l){var t=a.start.row,n=a.end.row-t;if(n!==0)if(a.action=="remove"){var r=l.splice(t+1,n);!l[t]&&r[r.length-1]&&(l[t]=r.pop()),r.forEach(function(i){i&&this.removeLineWidget(i)},this),this.$updateRows()}else{var e=new Array(n);l[t]&&l[t].column!=null&&a.start.column>l[t].column&&t++,e.unshift(t,0),l.splice.apply(l,e),this.$updateRows()}}},S.prototype.$updateRows=function(){var a=this.session.lineWidgets;if(a){var l=!0;a.forEach(function(t,n){if(t)for(l=!1,t.row=n;t.$oldWidget;)t.$oldWidget.row=n,t=t.$oldWidget}),l&&(this.session.lineWidgets=null)}},S.prototype.$registerLineWidget=function(a){this.session.lineWidgets||(this.session.lineWidgets=new Array(this.session.getLength()));var l=this.session.lineWidgets[a.row];return l&&(a.$oldWidget=l,l.el&&l.el.parentNode&&(l.el.parentNode.removeChild(l.el),l._inDocument=!1)),this.session.lineWidgets[a.row]=a,a},S.prototype.addLineWidget=function(a){if(this.$registerLineWidget(a),a.session=this.session,!this.editor)return a;var l=this.editor.renderer;a.html&&!a.el&&(a.el=R.createElement("div"),a.el.innerHTML=a.html),a.text&&!a.el&&(a.el=R.createElement("div"),a.el.textContent=a.text),a.el&&(R.addCssClass(a.el,"ace_lineWidgetContainer"),a.className&&R.addCssClass(a.el,a.className),a.el.style.position="absolute",a.el.style.zIndex=5,l.container.appendChild(a.el),a._inDocument=!0,a.coverGutter||(a.el.style.zIndex=3),a.pixelHeight==null&&(a.pixelHeight=a.el.offsetHeight)),a.rowCount==null&&(a.rowCount=a.pixelHeight/l.layerConfig.lineHeight);var t=this.session.getFoldAt(a.row,0);if(a.$fold=t,t){var n=this.session.lineWidgets;a.row==t.end.row&&!n[t.start.row]?n[t.start.row]=a:a.hidden=!0}return this.session._emit("changeFold",{data:{start:{row:a.row}}}),this.$updateRows(),this.renderWidgets(null,l),this.onWidgetChanged(a),a},S.prototype.removeLineWidget=function(a){if(a._inDocument=!1,a.session=null,a.el&&a.el.parentNode&&a.el.parentNode.removeChild(a.el),a.editor&&a.editor.destroy)try{a.editor.destroy()}catch{}if(this.session.lineWidgets){var l=this.session.lineWidgets[a.row];if(l==a)this.session.lineWidgets[a.row]=a.$oldWidget,a.$oldWidget&&this.onWidgetChanged(a.$oldWidget);else for(;l;){if(l.$oldWidget==a){l.$oldWidget=a.$oldWidget;break}l=l.$oldWidget}}this.session._emit("changeFold",{data:{start:{row:a.row}}}),this.$updateRows()},S.prototype.getWidgetsAtRow=function(a){for(var l=this.session.lineWidgets,t=l&&l[a],n=[];t;)n.push(t),t=t.$oldWidget;return n},S.prototype.onWidgetChanged=function(a){this.session._changedWidgets.push(a),this.editor&&this.editor.renderer.updateFull()},S.prototype.measureWidgets=function(a,l){var t=this.session._changedWidgets,n=l.layerConfig;if(!(!t||!t.length)){for(var r=1/0,e=0;e<t.length;e++){var i=t[e];if(!(!i||!i.el)&&i.session==this.session){if(!i._inDocument){if(this.session.lineWidgets[i.row]!=i)continue;i._inDocument=!0,l.container.appendChild(i.el)}i.h=i.el.offsetHeight,i.fixedWidth||(i.w=i.el.offsetWidth,i.screenWidth=Math.ceil(i.w/n.characterWidth));var o=i.h/n.lineHeight;i.coverLine&&(o-=this.session.getRowLineCount(i.row),o<0&&(o=0)),i.rowCount!=o&&(i.rowCount=o,i.row<r&&(r=i.row))}}r!=1/0&&(this.session._emit("changeFold",{data:{start:{row:r}}}),this.session.lineWidgetWidth=null),this.session._changedWidgets=[]}},S.prototype.renderWidgets=function(a,l){var t=l.layerConfig,n=this.session.lineWidgets;if(n){for(var r=Math.min(this.firstRow,t.firstRow),e=Math.max(this.lastRow,t.lastRow,n.length);r>0&&!n[r];)r--;this.firstRow=t.firstRow,this.lastRow=t.lastRow,l.$cursorLayer.config=t;for(var i=r;i<=e;i++){var o=n[i];if(!(!o||!o.el)){if(o.hidden){o.el.style.top=-100-(o.pixelHeight||0)+"px";continue}o._inDocument||(o._inDocument=!0,l.container.appendChild(o.el));var s=l.$cursorLayer.getPixelPosition({row:i,column:0},!0).top;o.coverLine||(s+=t.lineHeight*this.session.getRowLineCount(o.row)),o.el.style.top=s-t.offset+"px";var c=o.coverGutter?0:l.gutterWidth;o.fixedWidth||(c-=l.scrollLeft),o.el.style.left=c+"px",o.fullWidth&&o.screenWidth&&(o.el.style.minWidth=t.width+2*t.padding+"px"),o.fixedWidth?o.el.style.right=l.scrollBar.getWidth()+"px":o.el.style.right=""}}}},S}();k.LineWidgets=M});ace.define("ace/keyboard/gutter_handler",["require","exports","module","ace/lib/keys","ace/mouse/default_gutter_handler"],function(x,k,H){"use strict";var R=x("../lib/keys"),M=x("../mouse/default_gutter_handler").GutterTooltip,S=function(){function l(t){this.editor=t,this.gutterLayer=t.renderer.$gutterLayer,this.element=t.renderer.$gutter,this.lines=t.renderer.$gutterLayer.$lines,this.activeRowIndex=null,this.activeLane=null,this.annotationTooltip=new M(this.editor)}return l.prototype.addListener=function(){this.element.addEventListener("keydown",this.$onGutterKeyDown.bind(this)),this.element.addEventListener("focusout",this.$blurGutter.bind(this)),this.editor.on("mousewheel",this.$blurGutter.bind(this))},l.prototype.removeListener=function(){this.element.removeEventListener("keydown",this.$onGutterKeyDown.bind(this)),this.element.removeEventListener("focusout",this.$blurGutter.bind(this)),this.editor.off("mousewheel",this.$blurGutter.bind(this))},l.prototype.$onGutterKeyDown=function(t){if(this.annotationTooltip.isOpen){t.preventDefault(),t.keyCode===R.escape&&this.annotationTooltip.hideTooltip();return}if(t.target===this.element){if(t.keyCode!=R.enter)return;t.preventDefault();var n=this.editor.getCursorPosition().row;this.editor.isRowVisible(n)||this.editor.scrollToLine(n,!0,!0),setTimeout(function(){var r=this.$rowToRowIndex(this.gutterLayer.$cursorCell.row),e=this.$findNearestFoldWidget(r),i=this.$findNearestAnnotation(r);if(!(e===null&&i===null)){if(e===null&&i!==null){this.activeRowIndex=i,this.activeLane="annotation",this.$focusAnnotation(this.activeRowIndex);return}if(e!==null&&i===null){this.activeRowIndex=e,this.activeLane="fold",this.$focusFoldWidget(this.activeRowIndex);return}if(Math.abs(i-r)<Math.abs(e-r)){this.activeRowIndex=i,this.activeLane="annotation",this.$focusAnnotation(this.activeRowIndex);return}else{this.activeRowIndex=e,this.activeLane="fold",this.$focusFoldWidget(this.activeRowIndex);return}}}.bind(this),10);return}this.$handleGutterKeyboardInteraction(t),setTimeout(function(){this.editor._signal("gutterkeydown",new a(t,this))}.bind(this),10)},l.prototype.$handleGutterKeyboardInteraction=function(t){if(t.keyCode===R.tab){t.preventDefault();return}if(t.keyCode===R.escape){t.preventDefault(),this.$blurGutter(),this.element.focus(),this.lane=null;return}if(t.keyCode===R.up){switch(t.preventDefault(),this.activeLane){case"fold":this.$moveFoldWidgetUp();break;case"annotation":this.$moveAnnotationUp();break}return}if(t.keyCode===R.down){switch(t.preventDefault(),this.activeLane){case"fold":this.$moveFoldWidgetDown();break;case"annotation":this.$moveAnnotationDown();break}return}if(t.keyCode===R.left){t.preventDefault(),this.$switchLane("annotation");return}if(t.keyCode===R.right){t.preventDefault(),this.$switchLane("fold");return}if(t.keyCode===R.enter||t.keyCode===R.space){switch(t.preventDefault(),this.activeLane){case"fold":if(this.gutterLayer.session.foldWidgets[this.$rowIndexToRow(this.activeRowIndex)]==="start"){var n=this.$rowIndexToRow(this.activeRowIndex);this.editor.session.onFoldWidgetClick(this.$rowIndexToRow(this.activeRowIndex),t),setTimeout(function(){this.$rowIndexToRow(this.activeRowIndex)!==n&&(this.$blurFoldWidget(this.activeRowIndex),this.activeRowIndex=this.$rowToRowIndex(n),this.$focusFoldWidget(this.activeRowIndex))}.bind(this),10);break}else if(this.gutterLayer.session.foldWidgets[this.$rowIndexToRow(this.activeRowIndex)]==="end")break;return;case"annotation":var r=this.lines.cells[this.activeRowIndex].element.childNodes[2],e=r.getBoundingClientRect(),i=this.annotationTooltip.getElement().style;i.left=e.right+"px",i.top=e.bottom+"px",this.annotationTooltip.showTooltip(this.$rowIndexToRow(this.activeRowIndex));break}return}},l.prototype.$blurGutter=function(){if(this.activeRowIndex!==null)switch(this.activeLane){case"fold":this.$blurFoldWidget(this.activeRowIndex);break;case"annotation":this.$blurAnnotation(this.activeRowIndex);break}this.annotationTooltip.isOpen&&this.annotationTooltip.hideTooltip()},l.prototype.$isFoldWidgetVisible=function(t){var n=this.editor.isRowFullyVisible(this.$rowIndexToRow(t)),r=this.$getFoldWidget(t).style.display!=="none";return n&&r},l.prototype.$isAnnotationVisible=function(t){var n=this.editor.isRowFullyVisible(this.$rowIndexToRow(t)),r=this.$getAnnotation(t).style.display!=="none";return n&&r},l.prototype.$getFoldWidget=function(t){var n=this.lines.get(t),r=n.element;return r.childNodes[1]},l.prototype.$getAnnotation=function(t){var n=this.lines.get(t),r=n.element;return r.childNodes[2]},l.prototype.$findNearestFoldWidget=function(t){if(this.$isFoldWidgetVisible(t))return t;for(var n=0;t-n>0||t+n<this.lines.getLength()-1;){if(n++,t-n>=0&&this.$isFoldWidgetVisible(t-n))return t-n;if(t+n<=this.lines.getLength()-1&&this.$isFoldWidgetVisible(t+n))return t+n}return null},l.prototype.$findNearestAnnotation=function(t){if(this.$isAnnotationVisible(t))return t;for(var n=0;t-n>0||t+n<this.lines.getLength()-1;){if(n++,t-n>=0&&this.$isAnnotationVisible(t-n))return t-n;if(t+n<=this.lines.getLength()-1&&this.$isAnnotationVisible(t+n))return t+n}return null},l.prototype.$focusFoldWidget=function(t){if(t!=null){var n=this.$getFoldWidget(t);n.classList.add(this.editor.renderer.keyboardFocusClassName),n.focus()}},l.prototype.$focusAnnotation=function(t){if(t!=null){var n=this.$getAnnotation(t);n.classList.add(this.editor.renderer.keyboardFocusClassName),n.focus()}},l.prototype.$blurFoldWidget=function(t){var n=this.$getFoldWidget(t);n.classList.remove(this.editor.renderer.keyboardFocusClassName),n.blur()},l.prototype.$blurAnnotation=function(t){var n=this.$getAnnotation(t);n.classList.remove(this.editor.renderer.keyboardFocusClassName),n.blur()},l.prototype.$moveFoldWidgetUp=function(){for(var t=this.activeRowIndex;t>0;)if(t--,this.$isFoldWidgetVisible(t)){this.$blurFoldWidget(this.activeRowIndex),this.activeRowIndex=t,this.$focusFoldWidget(this.activeRowIndex);return}},l.prototype.$moveFoldWidgetDown=function(){for(var t=this.activeRowIndex;t<this.lines.getLength()-1;)if(t++,this.$isFoldWidgetVisible(t)){this.$blurFoldWidget(this.activeRowIndex),this.activeRowIndex=t,this.$focusFoldWidget(this.activeRowIndex);return}},l.prototype.$moveAnnotationUp=function(){for(var t=this.activeRowIndex;t>0;)if(t--,this.$isAnnotationVisible(t)){this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=t,this.$focusAnnotation(this.activeRowIndex);return}},l.prototype.$moveAnnotationDown=function(){for(var t=this.activeRowIndex;t<this.lines.getLength()-1;)if(t++,this.$isAnnotationVisible(t)){this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=t,this.$focusAnnotation(this.activeRowIndex);return}},l.prototype.$switchLane=function(t){switch(t){case"annotation":if(this.activeLane==="annotation")break;var n=this.$findNearestAnnotation(this.activeRowIndex);if(n==null)break;this.activeLane="annotation",this.$blurFoldWidget(this.activeRowIndex),this.activeRowIndex=n,this.$focusAnnotation(this.activeRowIndex);break;case"fold":if(this.activeLane==="fold")break;var r=this.$findNearestFoldWidget(this.activeRowIndex);if(r==null)break;this.activeLane="fold",this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=r,this.$focusFoldWidget(this.activeRowIndex);break}},l.prototype.$rowIndexToRow=function(t){var n=this.lines.get(t);return n?n.row:null},l.prototype.$rowToRowIndex=function(t){for(var n=0;n<this.lines.getLength();n++){var r=this.lines.get(n);if(r.row==t)return n}return null},l}();k.GutterKeyboardHandler=S;var a=function(){function l(t,n){this.gutterKeyboardHandler=n,this.domEvent=t}return l.prototype.getKey=function(){return R.keyCodeToString(this.domEvent.keyCode)},l.prototype.getRow=function(){return this.gutterKeyboardHandler.$rowIndexToRow(this.gutterKeyboardHandler.activeRowIndex)},l.prototype.isInAnnotationLane=function(){return this.gutterKeyboardHandler.activeLane==="annotation"},l.prototype.isInFoldLane=function(){return this.gutterKeyboardHandler.activeLane==="fold"},l}();k.GutterKeyboardEvent=a});ace.define("ace/editor",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/lib/useragent","ace/keyboard/textinput","ace/mouse/mouse_handler","ace/mouse/fold_handler","ace/keyboard/keybinding","ace/edit_session","ace/search","ace/range","ace/lib/event_emitter","ace/commands/command_manager","ace/commands/default_commands","ace/config","ace/token_iterator","ace/line_widgets","ace/keyboard/gutter_handler","ace/config","ace/clipboard","ace/lib/keys"],function(x,k,H){"use strict";var R=this&&this.__values||function(m){var h=typeof Symbol=="function"&&Symbol.iterator,C=h&&m[h],L=0;if(C)return C.call(m);if(m&&typeof m.length=="number")return{next:function(){return m&&L>=m.length&&(m=void 0),{value:m&&m[L++],done:!m}}};throw new TypeError(h?"Object is not iterable.":"Symbol.iterator is not defined.")},M=x("./lib/oop"),S=x("./lib/dom"),a=x("./lib/lang"),l=x("./lib/useragent"),t=x("./keyboard/textinput").TextInput,n=x("./mouse/mouse_handler").MouseHandler,r=x("./mouse/fold_handler").FoldHandler,e=x("./keyboard/keybinding").KeyBinding,i=x("./edit_session").EditSession,o=x("./search").Search,s=x("./range").Range,c=x("./lib/event_emitter").EventEmitter,g=x("./commands/command_manager").CommandManager,w=x("./commands/default_commands").commands,p=x("./config"),b=x("./token_iterator").TokenIterator,y=x("./line_widgets").LineWidgets,d=x("./keyboard/gutter_handler").GutterKeyboardHandler,v=x("./config").nls,$=x("./clipboard"),f=x("./lib/keys"),u=function(){function m(h,C,L){this.$toDestroy=[];var T=h.getContainerElement();this.container=T,this.renderer=h,this.id="editor"+ ++m.$uid,this.commands=new g(l.isMac?"mac":"win",w),typeof document=="object"&&(this.textInput=new t(h.getTextAreaContainer(),this),this.renderer.textarea=this.textInput.getElement(),this.$mouseHandler=new n(this),new r(this)),this.keyBinding=new e(this),this.$search=new o().set({wrap:!0}),this.$historyTracker=this.$historyTracker.bind(this),this.commands.on("exec",this.$historyTracker),this.$initOperationListeners(),this._$emitInputEvent=a.delayedCall(function(){this._signal("input",{}),this.session&&!this.session.destroyed&&this.session.bgTokenizer.scheduleStart()}.bind(this)),this.on("change",function(E,_){_._$emitInputEvent.schedule(31)}),this.setSession(C||L&&L.session||new i("")),p.resetOptions(this),L&&this.setOptions(L),p._signal("editor",this)}return m.prototype.$initOperationListeners=function(){this.commands.on("exec",this.startOperation.bind(this),!0),this.commands.on("afterExec",this.endOperation.bind(this),!0),this.$opResetTimer=a.delayedCall(this.endOperation.bind(this,!0)),this.on("change",function(){this.curOp||(this.startOperation(),this.curOp.selectionBefore=this.$lastSel),this.curOp.docChanged=!0}.bind(this),!0),this.on("changeSelection",function(){this.curOp||(this.startOperation(),this.curOp.selectionBefore=this.$lastSel),this.curOp.selectionChanged=!0}.bind(this),!0)},m.prototype.startOperation=function(h){if(this.curOp){if(!h||this.curOp.command)return;this.prevOp=this.curOp}h||(this.previousCommand=null,h={}),this.$opResetTimer.schedule(),this.curOp=this.session.curOp={command:h.command||{},args:h.args,scrollTop:this.renderer.scrollTop},this.curOp.selectionBefore=this.selection.toJSON()},m.prototype.endOperation=function(h){if(this.curOp&&this.session){if(h&&h.returnValue===!1||!this.session)return this.curOp=null;if(h==!0&&this.curOp.command&&this.curOp.command.name=="mouse"||(this._signal("beforeEndOperation"),!this.curOp))return;var C=this.curOp.command,L=C&&C.scrollIntoView;if(L){switch(L){case"center-animate":L="animate";case"center":this.renderer.scrollCursorIntoView(null,.5);break;case"animate":case"cursor":this.renderer.scrollCursorIntoView();break;case"selectionPart":var T=this.selection.getRange(),E=this.renderer.layerConfig;(T.start.row>=E.lastRow||T.end.row<=E.firstRow)&&this.renderer.scrollSelectionIntoView(this.selection.anchor,this.selection.lead);break;default:break}L=="animate"&&this.renderer.animateScrolling(this.curOp.scrollTop)}var _=this.selection.toJSON();this.curOp.selectionAfter=_,this.$lastSel=this.selection.toJSON(),this.session.getUndoManager().addSelection(_),this.prevOp=this.curOp,this.curOp=null}},m.prototype.$historyTracker=function(h){if(this.$mergeUndoDeltas){var C=this.prevOp,L=this.$mergeableCommands,T=C.command&&h.command.name==C.command.name;if(h.command.name=="insertstring"){var E=h.args;this.mergeNextCommand===void 0&&(this.mergeNextCommand=!0),T=T&&this.mergeNextCommand&&(!/\s/.test(E)||/\s/.test(C.args)),this.mergeNextCommand=!0}else T=T&&L.indexOf(h.command.name)!==-1;this.$mergeUndoDeltas!="always"&&Date.now()-this.sequenceStartTime>2e3&&(T=!1),T?this.session.mergeUndoDeltas=!0:L.indexOf(h.command.name)!==-1&&(this.sequenceStartTime=Date.now())}},m.prototype.setKeyboardHandler=function(h,C){if(h&&typeof h=="string"&&h!="ace"){this.$keybindingId=h;var L=this;p.loadModule(["keybinding",h],function(T){L.$keybindingId==h&&L.keyBinding.setKeyboardHandler(T&&T.handler),C&&C()})}else this.$keybindingId=null,this.keyBinding.setKeyboardHandler(h),C&&C()},m.prototype.getKeyboardHandler=function(){return this.keyBinding.getKeyboardHandler()},m.prototype.setSession=function(h){if(this.session!=h){this.curOp&&this.endOperation(),this.curOp={};var C=this.session;if(C){this.session.off("change",this.$onDocumentChange),this.session.off("changeMode",this.$onChangeMode),this.session.off("tokenizerUpdate",this.$onTokenizerUpdate),this.session.off("changeTabSize",this.$onChangeTabSize),this.session.off("changeWrapLimit",this.$onChangeWrapLimit),this.session.off("changeWrapMode",this.$onChangeWrapMode),this.session.off("changeFold",this.$onChangeFold),this.session.off("changeFrontMarker",this.$onChangeFrontMarker),this.session.off("changeBackMarker",this.$onChangeBackMarker),this.session.off("changeBreakpoint",this.$onChangeBreakpoint),this.session.off("changeAnnotation",this.$onChangeAnnotation),this.session.off("changeOverwrite",this.$onCursorChange),this.session.off("changeScrollTop",this.$onScrollTopChange),this.session.off("changeScrollLeft",this.$onScrollLeftChange);var L=this.session.getSelection();L.off("changeCursor",this.$onCursorChange),L.off("changeSelection",this.$onSelectionChange)}this.session=h,h?(this.$onDocumentChange=this.onDocumentChange.bind(this),h.on("change",this.$onDocumentChange),this.renderer.setSession(h),this.$onChangeMode=this.onChangeMode.bind(this),h.on("changeMode",this.$onChangeMode),this.$onTokenizerUpdate=this.onTokenizerUpdate.bind(this),h.on("tokenizerUpdate",this.$onTokenizerUpdate),this.$onChangeTabSize=this.renderer.onChangeTabSize.bind(this.renderer),h.on("changeTabSize",this.$onChangeTabSize),this.$onChangeWrapLimit=this.onChangeWrapLimit.bind(this),h.on("changeWrapLimit",this.$onChangeWrapLimit),this.$onChangeWrapMode=this.onChangeWrapMode.bind(this),h.on("changeWrapMode",this.$onChangeWrapMode),this.$onChangeFold=this.onChangeFold.bind(this),h.on("changeFold",this.$onChangeFold),this.$onChangeFrontMarker=this.onChangeFrontMarker.bind(this),this.session.on("changeFrontMarker",this.$onChangeFrontMarker),this.$onChangeBackMarker=this.onChangeBackMarker.bind(this),this.session.on("changeBackMarker",this.$onChangeBackMarker),this.$onChangeBreakpoint=this.onChangeBreakpoint.bind(this),this.session.on("changeBreakpoint",this.$onChangeBreakpoint),this.$onChangeAnnotation=this.onChangeAnnotation.bind(this),this.session.on("changeAnnotation",this.$onChangeAnnotation),this.$onCursorChange=this.onCursorChange.bind(this),this.session.on("changeOverwrite",this.$onCursorChange),this.$onScrollTopChange=this.onScrollTopChange.bind(this),this.session.on("changeScrollTop",this.$onScrollTopChange),this.$onScrollLeftChange=this.onScrollLeftChange.bind(this),this.session.on("changeScrollLeft",this.$onScrollLeftChange),this.selection=h.getSelection(),this.selection.on("changeCursor",this.$onCursorChange),this.$onSelectionChange=this.onSelectionChange.bind(this),this.selection.on("changeSelection",this.$onSelectionChange),this.onChangeMode(),this.onCursorChange(),this.onScrollTopChange(),this.onScrollLeftChange(),this.onSelectionChange(),this.onChangeFrontMarker(),this.onChangeBackMarker(),this.onChangeBreakpoint(),this.onChangeAnnotation(),this.session.getUseWrapMode()&&this.renderer.adjustWrapLimit(),this.renderer.updateFull()):(this.selection=null,this.renderer.setSession(h)),this._signal("changeSession",{session:h,oldSession:C}),this.curOp=null,C&&C._signal("changeEditor",{oldEditor:this}),h&&h._signal("changeEditor",{editor:this}),h&&!h.destroyed&&h.bgTokenizer.scheduleStart()}},m.prototype.getSession=function(){return this.session},m.prototype.setValue=function(h,C){return this.session.doc.setValue(h),C?C==1?this.navigateFileEnd():C==-1&&this.navigateFileStart():this.selectAll(),h},m.prototype.getValue=function(){return this.session.getValue()},m.prototype.getSelection=function(){return this.selection},m.prototype.resize=function(h){this.renderer.onResize(h)},m.prototype.setTheme=function(h,C){this.renderer.setTheme(h,C)},m.prototype.getTheme=function(){return this.renderer.getTheme()},m.prototype.setStyle=function(h){this.renderer.setStyle(h)},m.prototype.unsetStyle=function(h){this.renderer.unsetStyle(h)},m.prototype.getFontSize=function(){return this.getOption("fontSize")||S.computedStyle(this.container).fontSize},m.prototype.setFontSize=function(h){this.setOption("fontSize",h)},m.prototype.$highlightBrackets=function(){if(!this.$highlightPending){var h=this;this.$highlightPending=!0,setTimeout(function(){h.$highlightPending=!1;var C=h.session;if(!(!C||C.destroyed)){C.$bracketHighlight&&(C.$bracketHighlight.markerIds.forEach(function(D){C.removeMarker(D)}),C.$bracketHighlight=null);var L=h.getCursorPosition(),T=h.getKeyboardHandler(),E=T&&T.$getDirectionForHighlight&&T.$getDirectionForHighlight(h),_=C.getMatchingBracketRanges(L,E);if(!_){var O=new b(C,L.row,L.column),I=O.getCurrentToken();if(I&&/\b(?:tag-open|tag-name)/.test(I.type)){var W=C.getMatchingTags(L);W&&(_=[W.openTagName,W.closeTagName])}}if(!_&&C.$mode.getMatching&&(_=C.$mode.getMatching(h.session)),!_){h.getHighlightIndentGuides()&&h.renderer.$textLayer.$highlightIndentGuide();return}var F="ace_bracket";Array.isArray(_)?_.length==1&&(F="ace_error_bracket"):_=[_],_.length==2&&(s.comparePoints(_[0].end,_[1].start)==0?_=[s.fromPoints(_[0].start,_[1].end)]:s.comparePoints(_[0].start,_[1].end)==0&&(_=[s.fromPoints(_[1].start,_[0].end)])),C.$bracketHighlight={ranges:_,markerIds:_.map(function(D){return C.addMarker(D,F,"text")})},h.getHighlightIndentGuides()&&h.renderer.$textLayer.$highlightIndentGuide()}},50)}},m.prototype.focus=function(){this.textInput.focus()},m.prototype.isFocused=function(){return this.textInput.isFocused()},m.prototype.blur=function(){this.textInput.blur()},m.prototype.onFocus=function(h){this.$isFocused||(this.$isFocused=!0,this.renderer.showCursor(),this.renderer.visualizeFocus(),this._emit("focus",h))},m.prototype.onBlur=function(h){this.$isFocused&&(this.$isFocused=!1,this.renderer.hideCursor(),this.renderer.visualizeBlur(),this._emit("blur",h))},m.prototype.$cursorChange=function(){this.renderer.updateCursor(),this.$highlightBrackets(),this.$updateHighlightActiveLine()},m.prototype.onDocumentChange=function(h){var C=this.session.$useWrapMode,L=h.start.row==h.end.row?h.end.row:1/0;this.renderer.updateLines(h.start.row,L,C),this._signal("change",h),this.$cursorChange()},m.prototype.onTokenizerUpdate=function(h){var C=h.data;this.renderer.updateLines(C.first,C.last)},m.prototype.onScrollTopChange=function(){this.renderer.scrollToY(this.session.getScrollTop())},m.prototype.onScrollLeftChange=function(){this.renderer.scrollToX(this.session.getScrollLeft())},m.prototype.onCursorChange=function(){this.$cursorChange(),this._signal("changeSelection")},m.prototype.$updateHighlightActiveLine=function(){var h=this.getSession(),C;if(this.$highlightActiveLine&&((this.$selectionStyle!="line"||!this.selection.isMultiLine())&&(C=this.getCursorPosition()),this.renderer.theme&&this.renderer.theme.$selectionColorConflict&&!this.selection.isEmpty()&&(C=!1),this.renderer.$maxLines&&this.session.getLength()===1&&!(this.renderer.$minLines>1)&&(C=!1)),h.$highlightLineMarker&&!C)h.removeMarker(h.$highlightLineMarker.id),h.$highlightLineMarker=null;else if(!h.$highlightLineMarker&&C){var L=new s(C.row,C.column,C.row,1/0);L.id=h.addMarker(L,"ace_active-line","screenLine"),h.$highlightLineMarker=L}else C&&(h.$highlightLineMarker.start.row=C.row,h.$highlightLineMarker.end.row=C.row,h.$highlightLineMarker.start.column=C.column,h._signal("changeBackMarker"))},m.prototype.onSelectionChange=function(h){var C=this.session;if(C.$selectionMarker&&C.removeMarker(C.$selectionMarker),C.$selectionMarker=null,this.selection.isEmpty())this.$updateHighlightActiveLine();else{var L=this.selection.getRange(),T=this.getSelectionStyle();C.$selectionMarker=C.addMarker(L,"ace_selection",T)}var E=this.$highlightSelectedWord&&this.$getSelectionHighLightRegexp();this.session.highlight(E),this._signal("changeSelection")},m.prototype.$getSelectionHighLightRegexp=function(){var h=this.session,C=this.getSelectionRange();if(!(C.isEmpty()||C.isMultiLine())){var L=C.start.column,T=C.end.column,E=h.getLine(C.start.row),_=E.substring(L,T);if(!(_.length>5e3||!/[\w\d]/.test(_))){var O=this.$search.$assembleRegExp({wholeWord:!0,caseSensitive:!0,needle:_}),I=E.substring(L-1,T+1);if(O.test(I))return O}}},m.prototype.onChangeFrontMarker=function(){this.renderer.updateFrontMarkers()},m.prototype.onChangeBackMarker=function(){this.renderer.updateBackMarkers()},m.prototype.onChangeBreakpoint=function(){this.renderer.updateBreakpoints()},m.prototype.onChangeAnnotation=function(){this.renderer.setAnnotations(this.session.getAnnotations())},m.prototype.onChangeMode=function(h){this.renderer.updateText(),this._emit("changeMode",h)},m.prototype.onChangeWrapLimit=function(){this.renderer.updateFull()},m.prototype.onChangeWrapMode=function(){this.renderer.onResize(!0)},m.prototype.onChangeFold=function(){this.$updateHighlightActiveLine(),this.renderer.updateFull()},m.prototype.getSelectedText=function(){return this.session.getTextRange(this.getSelectionRange())},m.prototype.getCopyText=function(){var h=this.getSelectedText(),C=this.session.doc.getNewLineCharacter(),L=!1;if(!h&&this.$copyWithEmptySelection){L=!0;for(var T=this.selection.getAllRanges(),E=0;E<T.length;E++){var _=T[E];E&&T[E-1].start.row==_.start.row||(h+=this.session.getLine(_.start.row)+C)}}var O={text:h};return this._signal("copy",O),$.lineMode=L?O.text:!1,O.text},m.prototype.onCopy=function(){this.commands.exec("copy",this)},m.prototype.onCut=function(){this.commands.exec("cut",this)},m.prototype.onPaste=function(h,C){var L={text:h,event:C};this.commands.exec("paste",this,L)},m.prototype.$handlePaste=function(h){typeof h=="string"&&(h={text:h}),this._signal("paste",h);var C=h.text,L=C===$.lineMode,T=this.session;if(!this.inMultiSelectMode||this.inVirtualSelectionMode)L?T.insert({row:this.selection.lead.row,column:0},C):this.insert(C);else if(L)this.selection.rangeList.ranges.forEach(function(F){T.insert({row:F.start.row,column:0},C)});else{var E=C.split(/\r\n|\r|\n/),_=this.selection.rangeList.ranges,O=E.length==2&&(!E[0]||!E[1]);if(E.length!=_.length||O)return this.commands.exec("insertstring",this,C);for(var I=_.length;I--;){var W=_[I];W.isEmpty()||T.remove(W),T.insert(W.start,E[I])}}},m.prototype.execCommand=function(h,C){return this.commands.exec(h,this,C)},m.prototype.insert=function(h,C){var L=this.session,T=L.getMode(),E=this.getCursorPosition();if(this.getBehavioursEnabled()&&!C){var _=T.transformAction(L.getState(E.row),"insertion",this,L,h);_&&(h!==_.text&&(this.inVirtualSelectionMode||(this.session.mergeUndoDeltas=!1,this.mergeNextCommand=!1)),h=_.text)}if(h=="	"&&(h=this.session.getTabString()),this.selection.isEmpty()){if(this.session.getOverwrite()&&h.indexOf(`
`)==-1){var O=new s.fromPoints(E,E);O.end.column+=h.length,this.session.remove(O)}}else{var O=this.getSelectionRange();E=this.session.remove(O),this.clearSelection()}if(h==`
`||h==`\r
`){var D=L.getLine(E.row);if(E.column>D.search(/\S|$/)){var I=D.substr(E.column).search(/\S|$/);L.doc.removeInLine(E.row,E.column,E.column+I)}}this.clearSelection();var W=E.column,F=L.getState(E.row),D=L.getLine(E.row),B=T.checkOutdent(F,D,h);if(L.insert(E,h),_&&_.selection&&(_.selection.length==2?this.selection.setSelectionRange(new s(E.row,W+_.selection[0],E.row,W+_.selection[1])):this.selection.setSelectionRange(new s(E.row+_.selection[0],_.selection[1],E.row+_.selection[2],_.selection[3]))),this.$enableAutoIndent){if(L.getDocument().isNewLine(h)){var U=T.getNextLineIndent(F,D.slice(0,E.column),L.getTabString());L.insert({row:E.row+1,column:0},U)}B&&T.autoOutdent(F,L,E.row)}},m.prototype.autoIndent=function(){var h=this.session,C=h.getMode(),L,T;if(this.selection.isEmpty())L=0,T=h.doc.getLength()-1;else{var E=this.getSelectionRange();L=E.start.row,T=E.end.row}for(var _="",O="",I="",W,F,D,B=h.getTabString(),U=L;U<=T;U++)U>0&&(_=h.getState(U-1),O=h.getLine(U-1),I=C.getNextLineIndent(_,O,B)),W=h.getLine(U),F=C.$getIndent(W),I!==F&&(F.length>0&&(D=new s(U,0,U,F.length),h.remove(D)),I.length>0&&h.insert({row:U,column:0},I)),C.autoOutdent(_,h,U)},m.prototype.onTextInput=function(h,C){if(!C)return this.keyBinding.onTextInput(h);this.startOperation({command:{name:"insertstring"}});var L=this.applyComposition.bind(this,h,C);this.selection.rangeCount?this.forEachSelection(L):L(),this.endOperation()},m.prototype.applyComposition=function(h,C){if(C.extendLeft||C.extendRight){var L=this.selection.getRange();L.start.column-=C.extendLeft,L.end.column+=C.extendRight,L.start.column<0&&(L.start.row--,L.start.column+=this.session.getLine(L.start.row).length+1),this.selection.setRange(L),!h&&!L.isEmpty()&&this.remove()}if((h||!this.selection.isEmpty())&&this.insert(h,!0),C.restoreStart||C.restoreEnd){var L=this.selection.getRange();L.start.column-=C.restoreStart,L.end.column-=C.restoreEnd,this.selection.setRange(L)}},m.prototype.onCommandKey=function(h,C,L){return this.keyBinding.onCommandKey(h,C,L)},m.prototype.setOverwrite=function(h){this.session.setOverwrite(h)},m.prototype.getOverwrite=function(){return this.session.getOverwrite()},m.prototype.toggleOverwrite=function(){this.session.toggleOverwrite()},m.prototype.setScrollSpeed=function(h){this.setOption("scrollSpeed",h)},m.prototype.getScrollSpeed=function(){return this.getOption("scrollSpeed")},m.prototype.setDragDelay=function(h){this.setOption("dragDelay",h)},m.prototype.getDragDelay=function(){return this.getOption("dragDelay")},m.prototype.setSelectionStyle=function(h){this.setOption("selectionStyle",h)},m.prototype.getSelectionStyle=function(){return this.getOption("selectionStyle")},m.prototype.setHighlightActiveLine=function(h){this.setOption("highlightActiveLine",h)},m.prototype.getHighlightActiveLine=function(){return this.getOption("highlightActiveLine")},m.prototype.setHighlightGutterLine=function(h){this.setOption("highlightGutterLine",h)},m.prototype.getHighlightGutterLine=function(){return this.getOption("highlightGutterLine")},m.prototype.setHighlightSelectedWord=function(h){this.setOption("highlightSelectedWord",h)},m.prototype.getHighlightSelectedWord=function(){return this.$highlightSelectedWord},m.prototype.setAnimatedScroll=function(h){this.renderer.setAnimatedScroll(h)},m.prototype.getAnimatedScroll=function(){return this.renderer.getAnimatedScroll()},m.prototype.setShowInvisibles=function(h){this.renderer.setShowInvisibles(h)},m.prototype.getShowInvisibles=function(){return this.renderer.getShowInvisibles()},m.prototype.setDisplayIndentGuides=function(h){this.renderer.setDisplayIndentGuides(h)},m.prototype.getDisplayIndentGuides=function(){return this.renderer.getDisplayIndentGuides()},m.prototype.setHighlightIndentGuides=function(h){this.renderer.setHighlightIndentGuides(h)},m.prototype.getHighlightIndentGuides=function(){return this.renderer.getHighlightIndentGuides()},m.prototype.setShowPrintMargin=function(h){this.renderer.setShowPrintMargin(h)},m.prototype.getShowPrintMargin=function(){return this.renderer.getShowPrintMargin()},m.prototype.setPrintMarginColumn=function(h){this.renderer.setPrintMarginColumn(h)},m.prototype.getPrintMarginColumn=function(){return this.renderer.getPrintMarginColumn()},m.prototype.setReadOnly=function(h){this.setOption("readOnly",h)},m.prototype.getReadOnly=function(){return this.getOption("readOnly")},m.prototype.setBehavioursEnabled=function(h){this.setOption("behavioursEnabled",h)},m.prototype.getBehavioursEnabled=function(){return this.getOption("behavioursEnabled")},m.prototype.setWrapBehavioursEnabled=function(h){this.setOption("wrapBehavioursEnabled",h)},m.prototype.getWrapBehavioursEnabled=function(){return this.getOption("wrapBehavioursEnabled")},m.prototype.setShowFoldWidgets=function(h){this.setOption("showFoldWidgets",h)},m.prototype.getShowFoldWidgets=function(){return this.getOption("showFoldWidgets")},m.prototype.setFadeFoldWidgets=function(h){this.setOption("fadeFoldWidgets",h)},m.prototype.getFadeFoldWidgets=function(){return this.getOption("fadeFoldWidgets")},m.prototype.remove=function(h){this.selection.isEmpty()&&(h=="left"?this.selection.selectLeft():this.selection.selectRight());var C=this.getSelectionRange();if(this.getBehavioursEnabled()){var L=this.session,T=L.getState(C.start.row),E=L.getMode().transformAction(T,"deletion",this,L,C);if(C.end.column===0){var _=L.getTextRange(C);if(_[_.length-1]==`
`){var O=L.getLine(C.end.row);/^\s+$/.test(O)&&(C.end.column=O.length)}}E&&(C=E)}this.session.remove(C),this.clearSelection()},m.prototype.removeWordRight=function(){this.selection.isEmpty()&&this.selection.selectWordRight(),this.session.remove(this.getSelectionRange()),this.clearSelection()},m.prototype.removeWordLeft=function(){this.selection.isEmpty()&&this.selection.selectWordLeft(),this.session.remove(this.getSelectionRange()),this.clearSelection()},m.prototype.removeToLineStart=function(){this.selection.isEmpty()&&this.selection.selectLineStart(),this.selection.isEmpty()&&this.selection.selectLeft(),this.session.remove(this.getSelectionRange()),this.clearSelection()},m.prototype.removeToLineEnd=function(){this.selection.isEmpty()&&this.selection.selectLineEnd();var h=this.getSelectionRange();h.start.column==h.end.column&&h.start.row==h.end.row&&(h.end.column=0,h.end.row++),this.session.remove(h),this.clearSelection()},m.prototype.splitLine=function(){this.selection.isEmpty()||(this.session.remove(this.getSelectionRange()),this.clearSelection());var h=this.getCursorPosition();this.insert(`
`),this.moveCursorToPosition(h)},m.prototype.setGhostText=function(h,C){this.session.widgetManager||(this.session.widgetManager=new y(this.session),this.session.widgetManager.attach(this)),this.renderer.setGhostText(h,C)},m.prototype.removeGhostText=function(){this.session.widgetManager&&this.renderer.removeGhostText()},m.prototype.transposeLetters=function(){if(this.selection.isEmpty()){var h=this.getCursorPosition(),C=h.column;if(C!==0){var L=this.session.getLine(h.row),T,E;C<L.length?(T=L.charAt(C)+L.charAt(C-1),E=new s(h.row,C-1,h.row,C+1)):(T=L.charAt(C-1)+L.charAt(C-2),E=new s(h.row,C-2,h.row,C)),this.session.replace(E,T),this.session.selection.moveToPosition(E.end)}}},m.prototype.toLowerCase=function(){var h=this.getSelectionRange();this.selection.isEmpty()&&this.selection.selectWord();var C=this.getSelectionRange(),L=this.session.getTextRange(C);this.session.replace(C,L.toLowerCase()),this.selection.setSelectionRange(h)},m.prototype.toUpperCase=function(){var h=this.getSelectionRange();this.selection.isEmpty()&&this.selection.selectWord();var C=this.getSelectionRange(),L=this.session.getTextRange(C);this.session.replace(C,L.toUpperCase()),this.selection.setSelectionRange(h)},m.prototype.indent=function(){var h=this.session,C=this.getSelectionRange();if(C.start.row<C.end.row){var L=this.$getSelectedRows();h.indentRows(L.first,L.last,"	");return}else if(C.start.column<C.end.column){var T=h.getTextRange(C);if(!/^\s+$/.test(T)){var L=this.$getSelectedRows();h.indentRows(L.first,L.last,"	");return}}var E=h.getLine(C.start.row),_=C.start,O=h.getTabSize(),I=h.documentToScreenColumn(_.row,_.column);if(this.session.getUseSoftTabs())var W=O-I%O,F=a.stringRepeat(" ",W);else{for(var W=I%O;E[C.start.column-1]==" "&&W;)C.start.column--,W--;this.selection.setSelectionRange(C),F="	"}return this.insert(F)},m.prototype.blockIndent=function(){var h=this.$getSelectedRows();this.session.indentRows(h.first,h.last,"	")},m.prototype.blockOutdent=function(){var h=this.session.getSelection();this.session.outdentRows(h.getRange())},m.prototype.sortLines=function(){for(var h=this.$getSelectedRows(),C=this.session,L=[],T=h.first;T<=h.last;T++)L.push(C.getLine(T));L.sort(function(O,I){return O.toLowerCase()<I.toLowerCase()?-1:O.toLowerCase()>I.toLowerCase()?1:0});for(var E=new s(0,0,0,0),T=h.first;T<=h.last;T++){var _=C.getLine(T);E.start.row=T,E.end.row=T,E.end.column=_.length,C.replace(E,L[T-h.first])}},m.prototype.toggleCommentLines=function(){var h=this.session.getState(this.getCursorPosition().row),C=this.$getSelectedRows();this.session.getMode().toggleCommentLines(h,this.session,C.first,C.last)},m.prototype.toggleBlockComment=function(){var h=this.getCursorPosition(),C=this.session.getState(h.row),L=this.getSelectionRange();this.session.getMode().toggleBlockComment(C,this.session,L,h)},m.prototype.getNumberAt=function(h,C){var L=/[\-]?[0-9]+(?:\.[0-9]+)?/g;L.lastIndex=0;for(var T=this.session.getLine(h);L.lastIndex<C;){var E=L.exec(T);if(E.index<=C&&E.index+E[0].length>=C){var _={value:E[0],start:E.index,end:E.index+E[0].length};return _}}return null},m.prototype.modifyNumber=function(h){var C=this.selection.getCursor().row,L=this.selection.getCursor().column,T=new s(C,L-1,C,L),E=this.session.getTextRange(T);if(!isNaN(parseFloat(E))&&isFinite(E)){var _=this.getNumberAt(C,L);if(_){var O=_.value.indexOf(".")>=0?_.start+_.value.indexOf(".")+1:_.end,I=_.start+_.value.length-O,W=parseFloat(_.value);W*=Math.pow(10,I),O!==_.end&&L<O?h*=Math.pow(10,_.end-L-1):h*=Math.pow(10,_.end-L),W+=h,W/=Math.pow(10,I);var F=W.toFixed(I),D=new s(C,_.start,C,_.end);this.session.replace(D,F),this.moveCursorTo(C,Math.max(_.start+1,L+F.length-_.value.length))}}else this.toggleWord()},m.prototype.toggleWord=function(){var h=this.selection.getCursor().row,C=this.selection.getCursor().column;this.selection.selectWord();var L=this.getSelectedText(),T=this.selection.getWordRange().start.column,E=L.replace(/([a-z]+|[A-Z]+)(?=[A-Z_]|$)/g,"$1 ").split(/\s/),_=C-T-1;_<0&&(_=0);var O=0,I=0,W=this;L.match(/[A-Za-z0-9_]+/)&&E.forEach(function(Y,V){I=O+Y.length,_>=O&&_<=I&&(L=Y,W.selection.clearSelection(),W.moveCursorTo(h,O+T),W.selection.selectTo(h,I+T)),O=I});for(var F=this.$toggleWordPairs,D,B=0;B<F.length;B++)for(var U=F[B],P=0;P<=1;P++){var G=+!P,z=L.match(new RegExp("^\\s?_?("+a.escapeRegExp(U[P])+")\\s?$","i"));if(z){var K=L.match(new RegExp("([_]|^|\\s)("+a.escapeRegExp(z[1])+")($|\\s)","g"));K&&(D=L.replace(new RegExp(a.escapeRegExp(U[P]),"i"),function(Y){var V=U[G];return Y.toUpperCase()==Y?V=V.toUpperCase():Y.charAt(0).toUpperCase()==Y.charAt(0)&&(V=V.substr(0,0)+U[G].charAt(0).toUpperCase()+V.substr(1)),V}),this.insert(D),D="")}}},m.prototype.findLinkAt=function(h,C){var L,T,E=this.session.getLine(h),_=E.split(/((?:https?|ftp):\/\/[\S]+)/),O=C;O<0&&(O=0);var I=0,W=0,F;try{for(var D=R(_),B=D.next();!B.done;B=D.next()){var U=B.value;if(W=I+U.length,O>=I&&O<=W&&U.match(/((?:https?|ftp):\/\/[\S]+)/)){F=U.replace(/[\s:.,'";}\]]+$/,"");break}I=W}}catch(P){L={error:P}}finally{try{B&&!B.done&&(T=D.return)&&T.call(D)}finally{if(L)throw L.error}}return F},m.prototype.openLink=function(){var h=this.selection.getCursor(),C=this.findLinkAt(h.row,h.column);return C&&window.open(C,"_blank"),C!=null},m.prototype.removeLines=function(){var h=this.$getSelectedRows();this.session.removeFullLines(h.first,h.last),this.clearSelection()},m.prototype.duplicateSelection=function(){var h=this.selection,C=this.session,L=h.getRange(),T=h.isBackwards();if(L.isEmpty()){var E=L.start.row;C.duplicateLines(E,E)}else{var _=T?L.start:L.end,O=C.insert(_,C.getTextRange(L),!1);L.start=_,L.end=O,h.setSelectionRange(L,T)}},m.prototype.moveLinesDown=function(){this.$moveLines(1,!1)},m.prototype.moveLinesUp=function(){this.$moveLines(-1,!1)},m.prototype.moveText=function(h,C,L){return this.session.moveText(h,C,L)},m.prototype.copyLinesUp=function(){this.$moveLines(-1,!0)},m.prototype.copyLinesDown=function(){this.$moveLines(1,!0)},m.prototype.$moveLines=function(h,C){var L,T,E=this.selection;if(!E.inMultiSelectMode||this.inVirtualSelectionMode){var _=E.toOrientedRange();L=this.$getSelectedRows(_),T=this.session.$moveLines(L.first,L.last,C?0:h),C&&h==-1&&(T=0),_.moveBy(T,0),E.fromOrientedRange(_)}else{var O=E.rangeList.ranges;E.rangeList.detach(this.session),this.inVirtualSelectionMode=!0;for(var I=0,W=0,F=O.length,D=0;D<F;D++){var B=D;O[D].moveBy(I,0),L=this.$getSelectedRows(O[D]);for(var U=L.first,P=L.last;++D<F;){W&&O[D].moveBy(W,0);var G=this.$getSelectedRows(O[D]);if(C&&G.first!=P)break;if(!C&&G.first>P+1)break;P=G.last}for(D--,I=this.session.$moveLines(U,P,C?0:h),C&&h==-1&&(B=D+1);B<=D;)O[B].moveBy(I,0),B++;C||(I=0),W+=I}E.fromOrientedRange(E.ranges[0]),E.rangeList.attach(this.session),this.inVirtualSelectionMode=!1}},m.prototype.$getSelectedRows=function(h){return h=(h||this.getSelectionRange()).collapseRows(),{first:this.session.getRowFoldStart(h.start.row),last:this.session.getRowFoldEnd(h.end.row)}},m.prototype.onCompositionStart=function(h){this.renderer.showComposition(h)},m.prototype.onCompositionUpdate=function(h){this.renderer.setCompositionText(h)},m.prototype.onCompositionEnd=function(){this.renderer.hideComposition()},m.prototype.getFirstVisibleRow=function(){return this.renderer.getFirstVisibleRow()},m.prototype.getLastVisibleRow=function(){return this.renderer.getLastVisibleRow()},m.prototype.isRowVisible=function(h){return h>=this.getFirstVisibleRow()&&h<=this.getLastVisibleRow()},m.prototype.isRowFullyVisible=function(h){return h>=this.renderer.getFirstFullyVisibleRow()&&h<=this.renderer.getLastFullyVisibleRow()},m.prototype.$getVisibleRowCount=function(){return this.renderer.getScrollBottomRow()-this.renderer.getScrollTopRow()+1},m.prototype.$moveByPage=function(h,C){var L=this.renderer,T=this.renderer.layerConfig,E=h*Math.floor(T.height/T.lineHeight);C===!0?this.selection.$moveSelection(function(){this.moveCursorBy(E,0)}):C===!1&&(this.selection.moveCursorBy(E,0),this.selection.clearSelection());var _=L.scrollTop;L.scrollBy(0,E*T.lineHeight),C!=null&&L.scrollCursorIntoView(null,.5),L.animateScrolling(_)},m.prototype.selectPageDown=function(){this.$moveByPage(1,!0)},m.prototype.selectPageUp=function(){this.$moveByPage(-1,!0)},m.prototype.gotoPageDown=function(){this.$moveByPage(1,!1)},m.prototype.gotoPageUp=function(){this.$moveByPage(-1,!1)},m.prototype.scrollPageDown=function(){this.$moveByPage(1)},m.prototype.scrollPageUp=function(){this.$moveByPage(-1)},m.prototype.scrollToRow=function(h){this.renderer.scrollToRow(h)},m.prototype.scrollToLine=function(h,C,L,T){this.renderer.scrollToLine(h,C,L,T)},m.prototype.centerSelection=function(){var h=this.getSelectionRange(),C={row:Math.floor(h.start.row+(h.end.row-h.start.row)/2),column:Math.floor(h.start.column+(h.end.column-h.start.column)/2)};this.renderer.alignCursor(C,.5)},m.prototype.getCursorPosition=function(){return this.selection.getCursor()},m.prototype.getCursorPositionScreen=function(){return this.session.documentToScreenPosition(this.getCursorPosition())},m.prototype.getSelectionRange=function(){return this.selection.getRange()},m.prototype.selectAll=function(){this.selection.selectAll()},m.prototype.clearSelection=function(){this.selection.clearSelection()},m.prototype.moveCursorTo=function(h,C){this.selection.moveCursorTo(h,C)},m.prototype.moveCursorToPosition=function(h){this.selection.moveCursorToPosition(h)},m.prototype.jumpToMatching=function(h,C){var L=this.getCursorPosition(),T=new b(this.session,L.row,L.column),E=T.getCurrentToken(),_=0;E&&E.type.indexOf("tag-name")!==-1&&(E=T.stepBackward());var O=E||T.stepForward();if(O){var I,W=!1,F={},D=L.column-O.start,B,U={")":"(","(":"(","]":"[","[":"[","{":"{","}":"{"};do{if(O.value.match(/[{}()\[\]]/g)){for(;D<O.value.length&&!W;D++)if(U[O.value[D]])switch(B=U[O.value[D]]+"."+O.type.replace("rparen","lparen"),isNaN(F[B])&&(F[B]=0),O.value[D]){case"(":case"[":case"{":F[B]++;break;case")":case"]":case"}":F[B]--,F[B]===-1&&(I="bracket",W=!0);break}}else O.type.indexOf("tag-name")!==-1&&(isNaN(F[O.value])&&(F[O.value]=0),E.value==="<"&&_>1?F[O.value]++:E.value==="</"&&F[O.value]--,F[O.value]===-1&&(I="tag",W=!0));W||(E=O,_++,O=T.stepForward(),D=0)}while(O&&!W);if(I){var P,G;if(I==="bracket")P=this.session.getBracketRange(L),P||(P=new s(T.getCurrentTokenRow(),T.getCurrentTokenColumn()+D-1,T.getCurrentTokenRow(),T.getCurrentTokenColumn()+D-1),G=P.start,(C||G.row===L.row&&Math.abs(G.column-L.column)<2)&&(P=this.session.getBracketRange(G)));else if(I==="tag"){if(!O||O.type.indexOf("tag-name")===-1)return;if(P=new s(T.getCurrentTokenRow(),T.getCurrentTokenColumn()-2,T.getCurrentTokenRow(),T.getCurrentTokenColumn()-2),P.compare(L.row,L.column)===0){var z=this.session.getMatchingTags(L);z&&(z.openTag.contains(L.row,L.column)?(P=z.closeTag,G=P.start):(P=z.openTag,z.closeTag.start.row===L.row&&z.closeTag.start.column===L.column?G=P.end:G=P.start))}G=G||P.start}G=P&&P.cursor||G,G&&(h?P&&C?this.selection.setRange(P):P&&P.isEqual(this.getSelectionRange())?this.clearSelection():this.selection.selectTo(G.row,G.column):this.selection.moveTo(G.row,G.column))}}},m.prototype.gotoLine=function(h,C,L){this.selection.clearSelection(),this.session.unfold({row:h-1,column:C||0}),this.exitMultiSelectMode&&this.exitMultiSelectMode(),this.moveCursorTo(h-1,C||0),this.isRowFullyVisible(h-1)||this.scrollToLine(h-1,!0,L)},m.prototype.navigateTo=function(h,C){this.selection.moveTo(h,C)},m.prototype.navigateUp=function(h){if(this.selection.isMultiLine()&&!this.selection.isBackwards()){var C=this.selection.anchor.getPosition();return this.moveCursorToPosition(C)}this.selection.clearSelection(),this.selection.moveCursorBy(-h||-1,0)},m.prototype.navigateDown=function(h){if(this.selection.isMultiLine()&&this.selection.isBackwards()){var C=this.selection.anchor.getPosition();return this.moveCursorToPosition(C)}this.selection.clearSelection(),this.selection.moveCursorBy(h||1,0)},m.prototype.navigateLeft=function(h){if(this.selection.isEmpty())for(h=h||1;h--;)this.selection.moveCursorLeft();else{var C=this.getSelectionRange().start;this.moveCursorToPosition(C)}this.clearSelection()},m.prototype.navigateRight=function(h){if(this.selection.isEmpty())for(h=h||1;h--;)this.selection.moveCursorRight();else{var C=this.getSelectionRange().end;this.moveCursorToPosition(C)}this.clearSelection()},m.prototype.navigateLineStart=function(){this.selection.moveCursorLineStart(),this.clearSelection()},m.prototype.navigateLineEnd=function(){this.selection.moveCursorLineEnd(),this.clearSelection()},m.prototype.navigateFileEnd=function(){this.selection.moveCursorFileEnd(),this.clearSelection()},m.prototype.navigateFileStart=function(){this.selection.moveCursorFileStart(),this.clearSelection()},m.prototype.navigateWordRight=function(){this.selection.moveCursorWordRight(),this.clearSelection()},m.prototype.navigateWordLeft=function(){this.selection.moveCursorWordLeft(),this.clearSelection()},m.prototype.replace=function(h,C){C&&this.$search.set(C);var L=this.$search.find(this.session),T=0;return L&&(this.$tryReplace(L,h)&&(T=1),this.selection.setSelectionRange(L),this.renderer.scrollSelectionIntoView(L.start,L.end)),T},m.prototype.replaceAll=function(h,C){C&&this.$search.set(C);var L=this.$search.findAll(this.session),T=0;if(!L.length)return T;var E=this.getSelectionRange();this.selection.moveTo(0,0);for(var _=L.length-1;_>=0;--_)this.$tryReplace(L[_],h)&&T++;return this.selection.setSelectionRange(E),T},m.prototype.$tryReplace=function(h,C){var L=this.session.getTextRange(h);return C=this.$search.replace(L,C),C!==null?(h.end=this.session.replace(h,C),h):null},m.prototype.getLastSearchOptions=function(){return this.$search.getOptions()},m.prototype.find=function(h,C,L){C||(C={}),typeof h=="string"||h instanceof RegExp?C.needle=h:typeof h=="object"&&M.mixin(C,h);var T=this.selection.getRange();C.needle==null&&(h=this.session.getTextRange(T)||this.$search.$options.needle,h||(T=this.session.getWordRange(T.start.row,T.start.column),h=this.session.getTextRange(T)),this.$search.set({needle:h})),this.$search.set(C),C.start||this.$search.set({start:T});var E=this.$search.find(this.session);if(C.preventScroll)return E;if(E)return this.revealRange(E,L),E;C.backwards?T.start=T.end:T.end=T.start,this.selection.setRange(T)},m.prototype.findNext=function(h,C){this.find({skipCurrent:!0,backwards:!1},h,C)},m.prototype.findPrevious=function(h,C){this.find(h,{skipCurrent:!0,backwards:!0},C)},m.prototype.revealRange=function(h,C){this.session.unfold(h),this.selection.setSelectionRange(h);var L=this.renderer.scrollTop;this.renderer.scrollSelectionIntoView(h.start,h.end,.5),C!==!1&&this.renderer.animateScrolling(L)},m.prototype.undo=function(){this.session.getUndoManager().undo(this.session),this.renderer.scrollCursorIntoView(null,.5)},m.prototype.redo=function(){this.session.getUndoManager().redo(this.session),this.renderer.scrollCursorIntoView(null,.5)},m.prototype.destroy=function(){this.$toDestroy&&(this.$toDestroy.forEach(function(h){h.destroy()}),this.$toDestroy=null),this.$mouseHandler&&this.$mouseHandler.destroy(),this.renderer.destroy(),this._signal("destroy",this),this.session&&this.session.destroy(),this._$emitInputEvent&&this._$emitInputEvent.cancel(),this.removeAllListeners()},m.prototype.setAutoScrollEditorIntoView=function(h){if(h){var C,L=this,T=!1;this.$scrollAnchor||(this.$scrollAnchor=document.createElement("div"));var E=this.$scrollAnchor;E.style.cssText="position:absolute",this.container.insertBefore(E,this.container.firstChild);var _=this.on("changeSelection",function(){T=!0}),O=this.renderer.on("beforeRender",function(){T&&(C=L.renderer.container.getBoundingClientRect())}),I=this.renderer.on("afterRender",function(){if(T&&C&&(L.isFocused()||L.searchBox&&L.searchBox.isFocused())){var W=L.renderer,F=W.$cursorLayer.$pixelPos,D=W.layerConfig,B=F.top-D.offset;F.top>=0&&B+C.top<0?T=!0:F.top<D.height&&F.top+C.top+D.lineHeight>window.innerHeight?T=!1:T=null,T!=null&&(E.style.top=B+"px",E.style.left=F.left+"px",E.style.height=D.lineHeight+"px",E.scrollIntoView(T)),T=C=null}});this.setAutoScrollEditorIntoView=function(W){W||(delete this.setAutoScrollEditorIntoView,this.off("changeSelection",_),this.renderer.off("afterRender",I),this.renderer.off("beforeRender",O))}}},m.prototype.$resetCursorStyle=function(){var h=this.$cursorStyle||"ace",C=this.renderer.$cursorLayer;C&&(C.setSmoothBlinking(/smooth/.test(h)),C.isBlinking=!this.$readOnly&&h!="wide",S.setCssClass(C.element,"ace_slim-cursors",/slim/.test(h)))},m.prototype.prompt=function(h,C,L){var T=this;p.loadModule("ace/ext/prompt",function(E){E.prompt(T,h,C,L)})},m}();u.$uid=0,u.prototype.curOp=null,u.prototype.prevOp={},u.prototype.$mergeableCommands=["backspace","del","insertstring"],u.prototype.$toggleWordPairs=[["first","last"],["true","false"],["yes","no"],["width","height"],["top","bottom"],["right","left"],["on","off"],["x","y"],["get","set"],["max","min"],["horizontal","vertical"],["show","hide"],["add","remove"],["up","down"],["before","after"],["even","odd"],["in","out"],["inside","outside"],["next","previous"],["increase","decrease"],["attach","detach"],["&&","||"],["==","!="]],M.implement(u.prototype,c),p.defineOptions(u.prototype,"editor",{selectionStyle:{set:function(m){this.onSelectionChange(),this._signal("changeSelectionStyle",{data:m})},initialValue:"line"},highlightActiveLine:{set:function(){this.$updateHighlightActiveLine()},initialValue:!0},highlightSelectedWord:{set:function(m){this.$onSelectionChange()},initialValue:!0},readOnly:{set:function(m){this.textInput.setReadOnly(m),this.$resetCursorStyle()},initialValue:!1},copyWithEmptySelection:{set:function(m){this.textInput.setCopyWithEmptySelection(m)},initialValue:!1},cursorStyle:{set:function(m){this.$resetCursorStyle()},values:["ace","slim","smooth","wide"],initialValue:"ace"},mergeUndoDeltas:{values:[!1,!0,"always"],initialValue:!0},behavioursEnabled:{initialValue:!0},wrapBehavioursEnabled:{initialValue:!0},enableAutoIndent:{initialValue:!0},autoScrollEditorIntoView:{set:function(m){this.setAutoScrollEditorIntoView(m)}},keyboardHandler:{set:function(m){this.setKeyboardHandler(m)},get:function(){return this.$keybindingId},handlesSet:!0},value:{set:function(m){this.session.setValue(m)},get:function(){return this.getValue()},handlesSet:!0,hidden:!0},session:{set:function(m){this.setSession(m)},get:function(){return this.session},handlesSet:!0,hidden:!0},showLineNumbers:{set:function(m){this.renderer.$gutterLayer.setShowLineNumbers(m),this.renderer.$loop.schedule(this.renderer.CHANGE_GUTTER),m&&this.$relativeLineNumbers?A.attach(this):A.detach(this)},initialValue:!0},relativeLineNumbers:{set:function(m){this.$showLineNumbers&&m?A.attach(this):A.detach(this)}},placeholder:{set:function(m){this.$updatePlaceholder||(this.$updatePlaceholder=function(){var h=this.session&&(this.renderer.$composition||this.session.getLength()>1||this.session.getLine(0).length>0);if(h&&this.renderer.placeholderNode)this.renderer.off("afterRender",this.$updatePlaceholder),S.removeCssClass(this.container,"ace_hasPlaceholder"),this.renderer.placeholderNode.remove(),this.renderer.placeholderNode=null;else if(!h&&!this.renderer.placeholderNode){this.renderer.on("afterRender",this.$updatePlaceholder),S.addCssClass(this.container,"ace_hasPlaceholder");var C=S.createElement("div");C.className="ace_placeholder",C.textContent=this.$placeholder||"",this.renderer.placeholderNode=C,this.renderer.content.appendChild(this.renderer.placeholderNode)}else!h&&this.renderer.placeholderNode&&(this.renderer.placeholderNode.textContent=this.$placeholder||"")}.bind(this),this.on("input",this.$updatePlaceholder)),this.$updatePlaceholder()}},enableKeyboardAccessibility:{set:function(m){var h={name:"blurTextInput",description:"Set focus to the editor content div to allow tabbing through the page",bindKey:"Esc",exec:function(T){T.blur(),T.renderer.scroller.focus()},readOnly:!0},C=function(T){if(T.target==this.renderer.scroller&&T.keyCode===f.enter){T.preventDefault();var E=this.getCursorPosition().row;this.isRowVisible(E)||this.scrollToLine(E,!0,!0),this.focus()}},L;m?(this.renderer.enableKeyboardAccessibility=!0,this.renderer.keyboardFocusClassName="ace_keyboard-focus",this.textInput.getElement().setAttribute("tabindex",-1),this.textInput.setNumberOfExtraLines(l.isWin?3:0),this.renderer.scroller.setAttribute("tabindex",0),this.renderer.scroller.setAttribute("role","group"),this.renderer.scroller.setAttribute("aria-roledescription",v("editor")),this.renderer.scroller.classList.add(this.renderer.keyboardFocusClassName),this.renderer.scroller.setAttribute("aria-label",v("Editor content, press Enter to start editing, press Escape to exit")),this.renderer.scroller.addEventListener("keyup",C.bind(this)),this.commands.addCommand(h),this.renderer.$gutter.setAttribute("tabindex",0),this.renderer.$gutter.setAttribute("aria-hidden",!1),this.renderer.$gutter.setAttribute("role","group"),this.renderer.$gutter.setAttribute("aria-roledescription",v("editor")),this.renderer.$gutter.setAttribute("aria-label",v("Editor gutter, press Enter to interact with controls using arrow keys, press Escape to exit")),this.renderer.$gutter.classList.add(this.renderer.keyboardFocusClassName),this.renderer.content.setAttribute("aria-hidden",!0),L||(L=new d(this)),L.addListener()):(this.renderer.enableKeyboardAccessibility=!1,this.textInput.getElement().setAttribute("tabindex",0),this.textInput.setNumberOfExtraLines(0),this.renderer.scroller.setAttribute("tabindex",-1),this.renderer.scroller.removeAttribute("role"),this.renderer.scroller.removeAttribute("aria-roledescription"),this.renderer.scroller.classList.remove(this.renderer.keyboardFocusClassName),this.renderer.scroller.removeAttribute("aria-label"),this.renderer.scroller.removeEventListener("keyup",C.bind(this)),this.commands.removeCommand(h),this.renderer.content.removeAttribute("aria-hidden"),this.renderer.$gutter.setAttribute("tabindex",-1),this.renderer.$gutter.setAttribute("aria-hidden",!0),this.renderer.$gutter.removeAttribute("role"),this.renderer.$gutter.removeAttribute("aria-roledescription"),this.renderer.$gutter.removeAttribute("aria-label"),this.renderer.$gutter.classList.remove(this.renderer.keyboardFocusClassName),L&&L.removeListener())},initialValue:!1},customScrollbar:"renderer",hScrollBarAlwaysVisible:"renderer",vScrollBarAlwaysVisible:"renderer",highlightGutterLine:"renderer",animatedScroll:"renderer",showInvisibles:"renderer",showPrintMargin:"renderer",printMarginColumn:"renderer",printMargin:"renderer",fadeFoldWidgets:"renderer",showFoldWidgets:"renderer",displayIndentGuides:"renderer",highlightIndentGuides:"renderer",showGutter:"renderer",fontSize:"renderer",fontFamily:"renderer",maxLines:"renderer",minLines:"renderer",scrollPastEnd:"renderer",fixedWidthGutter:"renderer",theme:"renderer",hasCssTransforms:"renderer",maxPixelHeight:"renderer",useTextareaForIME:"renderer",useResizeObserver:"renderer",useSvgGutterIcons:"renderer",showFoldedAnnotations:"renderer",scrollSpeed:"$mouseHandler",dragDelay:"$mouseHandler",dragEnabled:"$mouseHandler",focusTimeout:"$mouseHandler",tooltipFollowsMouse:"$mouseHandler",firstLineNumber:"session",overwrite:"session",newLineMode:"session",useWorker:"session",useSoftTabs:"session",navigateWithinSoftTabs:"session",tabSize:"session",wrap:"session",indentedSoftWrap:"session",foldStyle:"session",mode:"session"});var A={getText:function(m,h){return(Math.abs(m.selection.lead.row-h)||h+1+(h<9?"\xB7":""))+""},getWidth:function(m,h,C){return Math.max(h.toString().length,(C.lastRow+1).toString().length,2)*C.characterWidth},update:function(m,h){h.renderer.$loop.schedule(h.renderer.CHANGE_GUTTER)},attach:function(m){m.renderer.$gutterLayer.$renderer=this,m.on("changeSelection",this.update),this.update(null,m)},detach:function(m){m.renderer.$gutterLayer.$renderer==this&&(m.renderer.$gutterLayer.$renderer=null),m.off("changeSelection",this.update),this.update(null,m)}};k.Editor=u});ace.define("ace/undomanager",["require","exports","module","ace/range"],function(x,k,H){"use strict";var R=function(){function d(){this.$maxRev=0,this.$fromUndo=!1,this.$undoDepth=1/0,this.reset()}return d.prototype.addSession=function(v){this.$session=v},d.prototype.add=function(v,$,f){if(!this.$fromUndo&&v!=this.$lastDelta){if(this.$keepRedoStack||(this.$redoStack.length=0),$===!1||!this.lastDeltas){this.lastDeltas=[];var u=this.$undoStack.length;u>this.$undoDepth-1&&this.$undoStack.splice(0,u-this.$undoDepth+1),this.$undoStack.push(this.lastDeltas),v.id=this.$rev=++this.$maxRev}(v.action=="remove"||v.action=="insert")&&(this.$lastDelta=v),this.lastDeltas.push(v)}},d.prototype.addSelection=function(v,$){this.selections.push({value:v,rev:$||this.$rev})},d.prototype.startNewGroup=function(){return this.lastDeltas=null,this.$rev},d.prototype.markIgnored=function(v,$){$==null&&($=this.$rev+1);for(var f=this.$undoStack,u=f.length;u--;){var A=f[u][0];if(A.id<=v)break;A.id<$&&(A.ignore=!0)}this.lastDeltas=null},d.prototype.getSelection=function(v,$){for(var f=this.selections,u=f.length;u--;){var A=f[u];if(A.rev<v)return $&&(A=f[u+1]),A}},d.prototype.getRevision=function(){return this.$rev},d.prototype.getDeltas=function(v,$){$==null&&($=this.$rev+1);for(var f=this.$undoStack,u=null,A=0,m=f.length;m--;){var h=f[m][0];if(h.id<$&&!u&&(u=m+1),h.id<=v){A=m+1;break}}return f.slice(A,u)},d.prototype.getChangedRanges=function(v,$){$==null&&($=this.$rev+1)},d.prototype.getChangedLines=function(v,$){$==null&&($=this.$rev+1)},d.prototype.undo=function(v,$){this.lastDeltas=null;var f=this.$undoStack;if(M(f,f.length)){v||(v=this.$session),this.$redoStackBaseRev!==this.$rev&&this.$redoStack.length&&(this.$redoStack=[]),this.$fromUndo=!0;var u=f.pop(),A=null;return u&&(A=v.undoChanges(u,$),this.$redoStack.push(u),this.$syncRev()),this.$fromUndo=!1,A}},d.prototype.redo=function(v,$){if(this.lastDeltas=null,v||(v=this.$session),this.$fromUndo=!0,this.$redoStackBaseRev!=this.$rev){var f=this.getDeltas(this.$redoStackBaseRev,this.$rev+1);y(this.$redoStack,f),this.$redoStackBaseRev=this.$rev,this.$redoStack.forEach(function(m){m[0].id=++this.$maxRev},this)}var u=this.$redoStack.pop(),A=null;return u&&(A=v.redoChanges(u,$),this.$undoStack.push(u),this.$syncRev()),this.$fromUndo=!1,A},d.prototype.$syncRev=function(){var v=this.$undoStack,$=v[v.length-1],f=$&&$[0].id||0;this.$redoStackBaseRev=f,this.$rev=f},d.prototype.reset=function(){this.lastDeltas=null,this.$lastDelta=null,this.$undoStack=[],this.$redoStack=[],this.$rev=0,this.mark=0,this.$redoStackBaseRev=this.$rev,this.selections=[]},d.prototype.canUndo=function(){return this.$undoStack.length>0},d.prototype.canRedo=function(){return this.$redoStack.length>0},d.prototype.bookmark=function(v){v==null&&(v=this.$rev),this.mark=v},d.prototype.isAtBookmark=function(){return this.$rev===this.mark},d.prototype.toJSON=function(){},d.prototype.fromJSON=function(){},d.prototype.$prettyPrint=function(v){return v?e(v):e(this.$undoStack)+`
---
`+e(this.$redoStack)},d}();R.prototype.hasUndo=R.prototype.canUndo,R.prototype.hasRedo=R.prototype.canRedo,R.prototype.isClean=R.prototype.isAtBookmark,R.prototype.markClean=R.prototype.bookmark;function M(d,v){for(var $=v;$--;){var f=d[$];if(f&&!f[0].ignore){for(;$<v-1;){var u=s(d[$],d[$+1]);d[$]=u[0],d[$+1]=u[1],$++}return!0}}}var S=x("./range").Range,a=S.comparePoints,l=S.comparePoints;function t(d){var v=d.action=="insert",$=d.start,f=d.end,u=(f.row-$.row)*(v?1:-1),A=(f.column-$.column)*(v?1:-1);v&&(f=$);for(var m in this.marks){var h=this.marks[m],C=l(h,$);if(!(C<0)){if(C===0&&v)if(h.bias==1)C=1;else{h.bias==-1;continue}var L=v?C:l(h,f);if(L>0){h.row+=u,h.column+=h.row==f.row?A:0;continue}!v&&L<=0&&(h.row=$.row,h.column=$.column,L===0&&(h.bias=1))}}}function n(d){return{row:d.row,column:d.column}}function r(d){return{start:n(d.start),end:n(d.end),action:d.action,lines:d.lines.slice()}}function e(d){if(d=d||this,Array.isArray(d))return d.map(e).join(`
`);var v="";return d.action?(v=d.action=="insert"?"+":"-",v+="["+d.lines+"]"):d.value&&(Array.isArray(d.value)?v=d.value.map(i).join(`
`):v=i(d.value)),d.start&&(v+=i(d)),(d.id||d.rev)&&(v+="	("+(d.id||d.rev)+")"),v}function i(d){return d.start.row+":"+d.start.column+"=>"+d.end.row+":"+d.end.column}function o(d,v){var $=d.action=="insert",f=v.action=="insert";if($&&f)if(a(v.start,d.end)>=0)g(v,d,-1);else if(a(v.start,d.start)<=0)g(d,v,1);else return null;else if($&&!f)if(a(v.start,d.end)>=0)g(v,d,-1);else if(a(v.end,d.start)<=0)g(d,v,-1);else return null;else if(!$&&f)if(a(v.start,d.start)>=0)g(v,d,1);else if(a(v.start,d.start)<=0)g(d,v,1);else return null;else if(!$&&!f)if(a(v.start,d.start)>=0)g(v,d,1);else if(a(v.end,d.start)<=0)g(d,v,-1);else return null;return[v,d]}function s(d,v){for(var $=d.length;$--;)for(var f=0;f<v.length;f++)if(!o(d[$],v[f])){for(;$<d.length;){for(;f--;)o(v[f],d[$]);f=v.length,$++}return[d,v]}return d.selectionBefore=v.selectionBefore=d.selectionAfter=v.selectionAfter=null,[v,d]}function c(d,v){var $=d.action=="insert",f=v.action=="insert";if($&&f)a(d.start,v.start)<0?g(v,d,1):g(d,v,1);else if($&&!f)a(d.start,v.end)>=0?g(d,v,-1):(a(d.start,v.start)<=0||g(d,S.fromPoints(v.start,d.start),-1),g(v,d,1));else if(!$&&f)a(v.start,d.end)>=0?g(v,d,-1):(a(v.start,d.start)<=0||g(v,S.fromPoints(d.start,v.start),-1),g(d,v,1));else if(!$&&!f)if(a(v.start,d.end)>=0)g(v,d,-1);else if(a(v.end,d.start)<=0)g(d,v,-1);else{var u,A;return a(d.start,v.start)<0&&(u=d,d=p(d,v.start)),a(d.end,v.end)>0&&(A=p(d,v.end)),w(v.end,d.start,d.end,-1),A&&!u&&(d.lines=A.lines,d.start=A.start,d.end=A.end,A=d),[v,u,A].filter(Boolean)}return[v,d]}function g(d,v,$){w(d.start,v.start,v.end,$),w(d.end,v.start,v.end,$)}function w(d,v,$,f){d.row==(f==1?v:$).row&&(d.column+=f*($.column-v.column)),d.row+=f*($.row-v.row)}function p(d,v){var $=d.lines,f=d.end;d.end=n(v);var u=d.end.row-d.start.row,A=$.splice(u,$.length),m=u?v.column:v.column-d.start.column;$.push(A[0].substring(0,m)),A[0]=A[0].substr(m);var h={start:n(v),end:f,lines:A,action:d.action};return h}function b(d,v){v=r(v);for(var $=d.length;$--;){for(var f=d[$],u=0;u<f.length;u++){var A=f[u],m=c(A,v);v=m[0],m.length!=2&&(m[2]?(f.splice(u+1,1,m[1],m[2]),u++):m[1]||(f.splice(u,1),u--))}f.length||d.splice($,1)}return d}function y(d,v){for(var $=0;$<v.length;$++)for(var f=v[$],u=0;u<f.length;u++)b(d,f[u])}k.UndoManager=R});ace.define("ace/layer/lines",["require","exports","module","ace/lib/dom"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=function(){function S(a,l){this.element=a,this.canvasHeight=l||5e5,this.element.style.height=this.canvasHeight*2+"px",this.cells=[],this.cellCache=[],this.$offsetCoefficient=0}return S.prototype.moveContainer=function(a){R.translate(this.element,0,-(a.firstRowScreen*a.lineHeight%this.canvasHeight)-a.offset*this.$offsetCoefficient)},S.prototype.pageChanged=function(a,l){return Math.floor(a.firstRowScreen*a.lineHeight/this.canvasHeight)!==Math.floor(l.firstRowScreen*l.lineHeight/this.canvasHeight)},S.prototype.computeLineTop=function(a,l,t){var n=l.firstRowScreen*l.lineHeight,r=Math.floor(n/this.canvasHeight),e=t.documentToScreenRow(a,0)*l.lineHeight;return e-r*this.canvasHeight},S.prototype.computeLineHeight=function(a,l,t){return l.lineHeight*t.getRowLineCount(a)},S.prototype.getLength=function(){return this.cells.length},S.prototype.get=function(a){return this.cells[a]},S.prototype.shift=function(){this.$cacheCell(this.cells.shift())},S.prototype.pop=function(){this.$cacheCell(this.cells.pop())},S.prototype.push=function(a){if(Array.isArray(a)){this.cells.push.apply(this.cells,a);for(var l=R.createFragment(this.element),t=0;t<a.length;t++)l.appendChild(a[t].element);this.element.appendChild(l)}else this.cells.push(a),this.element.appendChild(a.element)},S.prototype.unshift=function(a){if(Array.isArray(a)){this.cells.unshift.apply(this.cells,a);for(var l=R.createFragment(this.element),t=0;t<a.length;t++)l.appendChild(a[t].element);this.element.firstChild?this.element.insertBefore(l,this.element.firstChild):this.element.appendChild(l)}else this.cells.unshift(a),this.element.insertAdjacentElement("afterbegin",a.element)},S.prototype.last=function(){return this.cells.length?this.cells[this.cells.length-1]:null},S.prototype.$cacheCell=function(a){a&&(a.element.remove(),this.cellCache.push(a))},S.prototype.createCell=function(a,l,t,n){var r=this.cellCache.pop();if(!r){var e=R.createElement("div");n&&n(e),this.element.appendChild(e),r={element:e,text:"",row:a}}return r.row=a,r},S}();k.Lines=M});ace.define("ace/layer/gutter",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/layer/lines","ace/config"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=x("../lib/oop"),S=x("../lib/lang"),a=x("../lib/event_emitter").EventEmitter,l=x("./lines").Lines,t=x("../config").nls,n=function(){function e(i){this.element=R.createElement("div"),this.element.className="ace_layer ace_gutter-layer",i.appendChild(this.element),this.setShowFoldWidgets(this.$showFoldWidgets),this.gutterWidth=0,this.$annotations=[],this.$updateAnnotations=this.$updateAnnotations.bind(this),this.$lines=new l(this.element),this.$lines.$offsetCoefficient=1}return e.prototype.setSession=function(i){this.session&&this.session.off("change",this.$updateAnnotations),this.session=i,i&&i.on("change",this.$updateAnnotations)},e.prototype.addGutterDecoration=function(i,o){window.console&&console.warn&&console.warn("deprecated use session.addGutterDecoration"),this.session.addGutterDecoration(i,o)},e.prototype.removeGutterDecoration=function(i,o){window.console&&console.warn&&console.warn("deprecated use session.removeGutterDecoration"),this.session.removeGutterDecoration(i,o)},e.prototype.setAnnotations=function(i){this.$annotations=[];for(var o=0;o<i.length;o++){var s=i[o],c=s.row,g=this.$annotations[c];g||(g=this.$annotations[c]={text:[],type:[]});var w=s.text,p=s.type;w=w?S.escapeHTML(w):s.html||"",g.text.indexOf(w)===-1&&(g.text.push(w),g.type.push(p));var b=s.className;b?g.className=b:p=="error"?g.className=" ace_error":p=="warning"&&g.className!=" ace_error"?g.className=" ace_warning":p=="info"&&!g.className&&(g.className=" ace_info")}},e.prototype.$updateAnnotations=function(i){if(this.$annotations.length){var o=i.start.row,s=i.end.row-o;if(s!==0)if(i.action=="remove")this.$annotations.splice(o,s+1,null);else{var c=new Array(s+1);c.unshift(o,1),this.$annotations.splice.apply(this.$annotations,c)}}},e.prototype.update=function(i){this.config=i;var o=this.session,s=i.firstRow,c=Math.min(i.lastRow+i.gutterOffset,o.getLength()-1);this.oldLastRow=c,this.config=i,this.$lines.moveContainer(i),this.$updateCursorRow();for(var g=o.getNextFoldLine(s),w=g?g.start.row:1/0,p=null,b=-1,y=s;;){if(y>w&&(y=g.end.row+1,g=o.getNextFoldLine(y,g),w=g?g.start.row:1/0),y>c){for(;this.$lines.getLength()>b+1;)this.$lines.pop();break}p=this.$lines.get(++b),p?p.row=y:(p=this.$lines.createCell(y,i,this.session,r),this.$lines.push(p)),this.$renderCell(p,i,g,y),y++}this._signal("afterRender"),this.$updateGutterWidth(i)},e.prototype.$updateGutterWidth=function(i){var o=this.session,s=o.gutterRenderer||this.$renderer,c=o.$firstLineNumber,g=this.$lines.last()?this.$lines.last().text:"";(this.$fixedWidth||o.$useWrapMode)&&(g=o.getLength()+c-1);var w=s?s.getWidth(o,g,i):g.toString().length*i.characterWidth,p=this.$padding||this.$computePadding();w+=p.left+p.right,w!==this.gutterWidth&&!isNaN(w)&&(this.gutterWidth=w,this.element.parentNode.style.width=this.element.style.width=Math.ceil(this.gutterWidth)+"px",this._signal("changeGutterWidth",w))},e.prototype.$updateCursorRow=function(){if(this.$highlightGutterLine){var i=this.session.selection.getCursor();this.$cursorRow!==i.row&&(this.$cursorRow=i.row)}},e.prototype.updateLineHighlight=function(){if(this.$highlightGutterLine){var i=this.session.selection.cursor.row;if(this.$cursorRow=i,!(this.$cursorCell&&this.$cursorCell.row==i)){this.$cursorCell&&(this.$cursorCell.element.className=this.$cursorCell.element.className.replace("ace_gutter-active-line ",""));var o=this.$lines.cells;this.$cursorCell=null;for(var s=0;s<o.length;s++){var c=o[s];if(c.row>=this.$cursorRow){if(c.row>this.$cursorRow){var g=this.session.getFoldLine(this.$cursorRow);if(s>0&&g&&g.start.row==o[s-1].row)c=o[s-1];else break}c.element.className="ace_gutter-active-line "+c.element.className,this.$cursorCell=c;break}}}}},e.prototype.scrollLines=function(i){var o=this.config;if(this.config=i,this.$updateCursorRow(),this.$lines.pageChanged(o,i))return this.update(i);this.$lines.moveContainer(i);var s=Math.min(i.lastRow+i.gutterOffset,this.session.getLength()-1),c=this.oldLastRow;if(this.oldLastRow=s,!o||c<i.firstRow)return this.update(i);if(s<o.firstRow)return this.update(i);if(o.firstRow<i.firstRow)for(var g=this.session.getFoldedRowCount(o.firstRow,i.firstRow-1);g>0;g--)this.$lines.shift();if(c>s)for(var g=this.session.getFoldedRowCount(s+1,c);g>0;g--)this.$lines.pop();i.firstRow<o.firstRow&&this.$lines.unshift(this.$renderLines(i,i.firstRow,o.firstRow-1)),s>c&&this.$lines.push(this.$renderLines(i,c+1,s)),this.updateLineHighlight(),this._signal("afterRender"),this.$updateGutterWidth(i)},e.prototype.$renderLines=function(i,o,s){for(var c=[],g=o,w=this.session.getNextFoldLine(g),p=w?w.start.row:1/0;g>p&&(g=w.end.row+1,w=this.session.getNextFoldLine(g,w),p=w?w.start.row:1/0),!(g>s);){var b=this.$lines.createCell(g,i,this.session,r);this.$renderCell(b,i,w,g),c.push(b),g++}return c},e.prototype.$renderCell=function(i,o,s,c){var g=i.element,w=this.session,p=g.childNodes[0],b=g.childNodes[1],y=g.childNodes[2],d=y.firstChild,v=w.$firstLineNumber,$=w.$breakpoints,f=w.$decorations,u=w.gutterRenderer||this.$renderer,A=this.$showFoldWidgets&&w.foldWidgets,m=s?s.start.row:Number.MAX_VALUE,h=o.lineHeight+"px",C=this.$useSvgGutterIcons?"ace_gutter-cell_svg-icons ":"ace_gutter-cell ",L=this.$useSvgGutterIcons?"ace_icon_svg":"ace_icon",T=(u?u.getText(w,c):c+v).toString();if(this.$highlightGutterLine&&(c==this.$cursorRow||s&&c<this.$cursorRow&&c>=m&&this.$cursorRow<=s.end.row)&&(C+="ace_gutter-active-line ",this.$cursorCell!=i&&(this.$cursorCell&&(this.$cursorCell.element.className=this.$cursorCell.element.className.replace("ace_gutter-active-line ","")),this.$cursorCell=i)),$[c]&&(C+=$[c]),f[c]&&(C+=f[c]),this.$annotations[c]&&c!==m&&(C+=this.$annotations[c].className),A){var E=A[c];E==null&&(E=A[c]=w.getFoldWidget(c))}if(E){var _="ace_fold-widget ace_"+E,O=E=="start"&&c==m&&c<s.end.row;if(O){_+=" ace_closed";for(var I="",W=!1,F=c+1;F<=s.end.row;F++)if(this.$annotations[F]){if(this.$annotations[F].className===" ace_error"){W=!0,I=" ace_error_fold";break}if(this.$annotations[F].className===" ace_warning"){W=!0,I=" ace_warning_fold";continue}}C+=I}else _+=" ace_open";b.className!=_&&(b.className=_),R.setStyle(b.style,"height",h),R.setStyle(b.style,"display","inline-block"),b.setAttribute("role","button"),b.setAttribute("tabindex","-1");var D=w.getFoldWidgetRange(c);D?b.setAttribute("aria-label",t("Toggle code folding, rows $0 through $1",[D.start.row+1,D.end.row+1])):s?b.setAttribute("aria-label",t("Toggle code folding, rows $0 through $1",[s.start.row+1,s.end.row+1])):b.setAttribute("aria-label",t("Toggle code folding, row $0",[c+1])),O?(b.setAttribute("aria-expanded","false"),b.setAttribute("title",t("Unfold code"))):(b.setAttribute("aria-expanded","true"),b.setAttribute("title",t("Fold code")))}else b&&(R.setStyle(b.style,"display","none"),b.setAttribute("tabindex","0"),b.removeAttribute("role"),b.removeAttribute("aria-label"));return W&&this.$showFoldedAnnotations?(y.className="ace_gutter_annotation",d.className=L,d.className+=I,R.setStyle(d.style,"height",h),R.setStyle(y.style,"display","block"),R.setStyle(y.style,"height",h),y.setAttribute("aria-label",t("Read annotations row $0",[T])),y.setAttribute("tabindex","-1"),y.setAttribute("role","button")):this.$annotations[c]?(y.className="ace_gutter_annotation",d.className=L,this.$useSvgGutterIcons?d.className+=this.$annotations[c].className:g.classList.add(this.$annotations[c].className.replace(" ","")),R.setStyle(d.style,"height",h),R.setStyle(y.style,"display","block"),R.setStyle(y.style,"height",h),y.setAttribute("aria-label",t("Read annotations row $0",[T])),y.setAttribute("tabindex","-1"),y.setAttribute("role","button")):(R.setStyle(y.style,"display","none"),y.removeAttribute("aria-label"),y.removeAttribute("role"),y.setAttribute("tabindex","0")),T!==p.data&&(p.data=T),g.className!=C&&(g.className=C),R.setStyle(i.element.style,"height",this.$lines.computeLineHeight(c,o,w)+"px"),R.setStyle(i.element.style,"top",this.$lines.computeLineTop(c,o,w)+"px"),i.text=T,y.style.display==="none"&&b.style.display==="none"?i.element.setAttribute("aria-hidden",!0):i.element.setAttribute("aria-hidden",!1),i},e.prototype.setHighlightGutterLine=function(i){this.$highlightGutterLine=i},e.prototype.setShowLineNumbers=function(i){this.$renderer=!i&&{getWidth:function(){return 0},getText:function(){return""}}},e.prototype.getShowLineNumbers=function(){return this.$showLineNumbers},e.prototype.setShowFoldWidgets=function(i){i?R.addCssClass(this.element,"ace_folding-enabled"):R.removeCssClass(this.element,"ace_folding-enabled"),this.$showFoldWidgets=i,this.$padding=null},e.prototype.getShowFoldWidgets=function(){return this.$showFoldWidgets},e.prototype.$computePadding=function(){if(!this.element.firstChild)return{left:0,right:0};var i=R.computedStyle(this.element.firstChild);return this.$padding={},this.$padding.left=(parseInt(i.borderLeftWidth)||0)+(parseInt(i.paddingLeft)||0)+1,this.$padding.right=(parseInt(i.borderRightWidth)||0)+(parseInt(i.paddingRight)||0),this.$padding},e.prototype.getRegion=function(i){var o=this.$padding||this.$computePadding(),s=this.element.getBoundingClientRect();if(i.x<o.left+s.left)return"markers";if(this.$showFoldWidgets&&i.x>s.right-o.right)return"foldWidgets"},e}();n.prototype.$fixedWidth=!1,n.prototype.$highlightGutterLine=!0,n.prototype.$renderer="",n.prototype.$showLineNumbers=!0,n.prototype.$showFoldWidgets=!0,M.implement(n.prototype,a);function r(e){var i=document.createTextNode("");e.appendChild(i);var o=R.createElement("span");e.appendChild(o);var s=R.createElement("span");e.appendChild(s);var c=R.createElement("span");return s.appendChild(c),e}k.Gutter=n});ace.define("ace/layer/marker",["require","exports","module","ace/range","ace/lib/dom"],function(x,k,H){"use strict";var R=x("../range").Range,M=x("../lib/dom"),S=function(){function l(t){this.element=M.createElement("div"),this.element.className="ace_layer ace_marker-layer",t.appendChild(this.element)}return l.prototype.setPadding=function(t){this.$padding=t},l.prototype.setSession=function(t){this.session=t},l.prototype.setMarkers=function(t){this.markers=t},l.prototype.elt=function(t,n){var r=this.i!=-1&&this.element.childNodes[this.i];r?this.i++:(r=document.createElement("div"),this.element.appendChild(r),this.i=-1),r.style.cssText=n,r.className=t},l.prototype.update=function(t){if(t){this.config=t,this.i=0;var n;for(var r in this.markers){var e=this.markers[r];if(!e.range){e.update(n,this,this.session,t);continue}var i=e.range.clipRows(t.firstRow,t.lastRow);if(!i.isEmpty())if(i=i.toScreenRange(this.session),e.renderer){var o=this.$getTop(i.start.row,t),s=this.$padding+i.start.column*t.characterWidth;e.renderer(n,i,s,o,t)}else e.type=="fullLine"?this.drawFullLineMarker(n,i,e.clazz,t):e.type=="screenLine"?this.drawScreenLineMarker(n,i,e.clazz,t):i.isMultiLine()?e.type=="text"?this.drawTextMarker(n,i,e.clazz,t):this.drawMultiLineMarker(n,i,e.clazz,t):this.drawSingleLineMarker(n,i,e.clazz+" ace_start ace_br15",t)}if(this.i!=-1)for(;this.i<this.element.childElementCount;)this.element.removeChild(this.element.lastChild)}},l.prototype.$getTop=function(t,n){return(t-n.firstRowScreen)*n.lineHeight},l.prototype.drawTextMarker=function(t,n,r,e,i){for(var o=this.session,s=n.start.row,c=n.end.row,g=s,w=0,p=0,b=o.getScreenLastRowColumn(g),y=new R(g,n.start.column,g,p);g<=c;g++)y.start.row=y.end.row=g,y.start.column=g==s?n.start.column:o.getRowWrapIndent(g),y.end.column=b,w=p,p=b,b=g+1<c?o.getScreenLastRowColumn(g+1):g==c?0:n.end.column,this.drawSingleLineMarker(t,y,r+(g==s?" ace_start":"")+" ace_br"+a(g==s||g==s+1&&n.start.column,w<p,p>b,g==c),e,g==c?0:1,i)},l.prototype.drawMultiLineMarker=function(t,n,r,e,i){var o=this.$padding,s=e.lineHeight,c=this.$getTop(n.start.row,e),g=o+n.start.column*e.characterWidth;if(i=i||"",this.session.$bidiHandler.isBidiRow(n.start.row)){var w=n.clone();w.end.row=w.start.row,w.end.column=this.session.getLine(w.start.row).length,this.drawBidiSingleLineMarker(t,w,r+" ace_br1 ace_start",e,null,i)}else this.elt(r+" ace_br1 ace_start","height:"+s+"px;right:0;top:"+c+"px;left:"+g+"px;"+(i||""));if(this.session.$bidiHandler.isBidiRow(n.end.row)){var w=n.clone();w.start.row=w.end.row,w.start.column=0,this.drawBidiSingleLineMarker(t,w,r+" ace_br12",e,null,i)}else{c=this.$getTop(n.end.row,e);var p=n.end.column*e.characterWidth;this.elt(r+" ace_br12","height:"+s+"px;width:"+p+"px;top:"+c+"px;left:"+o+"px;"+(i||""))}if(s=(n.end.row-n.start.row-1)*e.lineHeight,!(s<=0)){c=this.$getTop(n.start.row+1,e);var b=(n.start.column?1:0)|(n.end.column?0:8);this.elt(r+(b?" ace_br"+b:""),"height:"+s+"px;right:0;top:"+c+"px;left:"+o+"px;"+(i||""))}},l.prototype.drawSingleLineMarker=function(t,n,r,e,i,o){if(this.session.$bidiHandler.isBidiRow(n.start.row))return this.drawBidiSingleLineMarker(t,n,r,e,i,o);var s=e.lineHeight,c=(n.end.column+(i||0)-n.start.column)*e.characterWidth,g=this.$getTop(n.start.row,e),w=this.$padding+n.start.column*e.characterWidth;this.elt(r,"height:"+s+"px;width:"+c+"px;top:"+g+"px;left:"+w+"px;"+(o||""))},l.prototype.drawBidiSingleLineMarker=function(t,n,r,e,i,o){var s=e.lineHeight,c=this.$getTop(n.start.row,e),g=this.$padding,w=this.session.$bidiHandler.getSelections(n.start.column,n.end.column);w.forEach(function(p){this.elt(r,"height:"+s+"px;width:"+(p.width+(i||0))+"px;top:"+c+"px;left:"+(g+p.left)+"px;"+(o||""))},this)},l.prototype.drawFullLineMarker=function(t,n,r,e,i){var o=this.$getTop(n.start.row,e),s=e.lineHeight;n.start.row!=n.end.row&&(s+=this.$getTop(n.end.row,e)-o),this.elt(r,"height:"+s+"px;top:"+o+"px;left:0;right:0;"+(i||""))},l.prototype.drawScreenLineMarker=function(t,n,r,e,i){var o=this.$getTop(n.start.row,e),s=e.lineHeight;this.elt(r,"height:"+s+"px;top:"+o+"px;left:0;right:0;"+(i||""))},l}();S.prototype.$padding=0;function a(l,t,n,r){return(l?1:0)|(t?2:0)|(n?4:0)|(r?8:0)}k.Marker=S});ace.define("ace/layer/text",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/layer/lines","ace/lib/event_emitter","ace/config"],function(x,k,H){"use strict";var R=x("../lib/oop"),M=x("../lib/dom"),S=x("../lib/lang"),a=x("./lines").Lines,l=x("../lib/event_emitter").EventEmitter,t=x("../config").nls,n=function(){function r(e){this.dom=M,this.element=this.dom.createElement("div"),this.element.className="ace_layer ace_text-layer",e.appendChild(this.element),this.$updateEolChar=this.$updateEolChar.bind(this),this.$lines=new a(this.element)}return r.prototype.$updateEolChar=function(){var e=this.session.doc,i=e.getNewLineCharacter()==`
`&&e.getNewLineMode()!="windows",o=i?this.EOL_CHAR_LF:this.EOL_CHAR_CRLF;if(this.EOL_CHAR!=o)return this.EOL_CHAR=o,!0},r.prototype.setPadding=function(e){this.$padding=e,this.element.style.margin="0 "+e+"px"},r.prototype.getLineHeight=function(){return this.$fontMetrics.$characterSize.height||0},r.prototype.getCharacterWidth=function(){return this.$fontMetrics.$characterSize.width||0},r.prototype.$setFontMetrics=function(e){this.$fontMetrics=e,this.$fontMetrics.on("changeCharacterSize",function(i){this._signal("changeCharacterSize",i)}.bind(this)),this.$pollSizeChanges()},r.prototype.checkForSizeChanges=function(){this.$fontMetrics.checkForSizeChanges()},r.prototype.$pollSizeChanges=function(){return this.$pollSizeChangesTimer=this.$fontMetrics.$pollSizeChanges()},r.prototype.setSession=function(e){this.session=e,e&&this.$computeTabString()},r.prototype.setShowInvisibles=function(e){return this.showInvisibles==e?!1:(this.showInvisibles=e,typeof e=="string"?(this.showSpaces=/tab/i.test(e),this.showTabs=/space/i.test(e),this.showEOL=/eol/i.test(e)):this.showSpaces=this.showTabs=this.showEOL=e,this.$computeTabString(),!0)},r.prototype.setDisplayIndentGuides=function(e){return this.displayIndentGuides==e?!1:(this.displayIndentGuides=e,this.$computeTabString(),!0)},r.prototype.setHighlightIndentGuides=function(e){return this.$highlightIndentGuides===e?!1:(this.$highlightIndentGuides=e,e)},r.prototype.$computeTabString=function(){var e=this.session.getTabSize();this.tabSize=e;for(var i=this.$tabStrings=[0],o=1;o<e+1;o++)if(this.showTabs){var s=this.dom.createElement("span");s.className="ace_invisible ace_invisible_tab",s.textContent=S.stringRepeat(this.TAB_CHAR,o),i.push(s)}else i.push(this.dom.createTextNode(S.stringRepeat(" ",o),this.element));if(this.displayIndentGuides){this.$indentGuideRe=/\s\S| \t|\t |\s$/;var c="ace_indent-guide",g=this.showSpaces?" ace_invisible ace_invisible_space":"",w=this.showSpaces?S.stringRepeat(this.SPACE_CHAR,this.tabSize):S.stringRepeat(" ",this.tabSize),p=this.showTabs?" ace_invisible ace_invisible_tab":"",b=this.showTabs?S.stringRepeat(this.TAB_CHAR,this.tabSize):w,s=this.dom.createElement("span");s.className=c+g,s.textContent=w,this.$tabStrings[" "]=s;var s=this.dom.createElement("span");s.className=c+p,s.textContent=b,this.$tabStrings["	"]=s}},r.prototype.updateLines=function(e,i,o){if(this.config.lastRow!=e.lastRow||this.config.firstRow!=e.firstRow)return this.update(e);this.config=e;for(var s=Math.max(i,e.firstRow),c=Math.min(o,e.lastRow),g=this.element.childNodes,w=0,b=e.firstRow;b<s;b++){var y=this.session.getFoldLine(b);if(y)if(y.containsRow(s)){s=y.start.row;break}else b=y.end.row;w++}for(var p=!1,b=s,y=this.session.getNextFoldLine(b),d=y?y.start.row:1/0;b>d&&(b=y.end.row+1,y=this.session.getNextFoldLine(b,y),d=y?y.start.row:1/0),!(b>c);){var v=g[w++];if(v){this.dom.removeChildren(v),this.$renderLine(v,b,b==d?y:!1),p&&(v.style.top=this.$lines.computeLineTop(b,e,this.session)+"px");var $=e.lineHeight*this.session.getRowLength(b)+"px";v.style.height!=$&&(p=!0,v.style.height=$)}b++}if(p)for(;w<this.$lines.cells.length;){var f=this.$lines.cells[w++];f.element.style.top=this.$lines.computeLineTop(f.row,e,this.session)+"px"}},r.prototype.scrollLines=function(e){var i=this.config;if(this.config=e,this.$lines.pageChanged(i,e))return this.update(e);this.$lines.moveContainer(e);var o=e.lastRow,s=i?i.lastRow:-1;if(!i||s<e.firstRow)return this.update(e);if(o<i.firstRow)return this.update(e);if(!i||i.lastRow<e.firstRow)return this.update(e);if(e.lastRow<i.firstRow)return this.update(e);if(i.firstRow<e.firstRow)for(var c=this.session.getFoldedRowCount(i.firstRow,e.firstRow-1);c>0;c--)this.$lines.shift();if(i.lastRow>e.lastRow)for(var c=this.session.getFoldedRowCount(e.lastRow+1,i.lastRow);c>0;c--)this.$lines.pop();e.firstRow<i.firstRow&&this.$lines.unshift(this.$renderLinesFragment(e,e.firstRow,i.firstRow-1)),e.lastRow>i.lastRow&&this.$lines.push(this.$renderLinesFragment(e,i.lastRow+1,e.lastRow)),this.$highlightIndentGuide()},r.prototype.$renderLinesFragment=function(e,i,o){for(var s=[],c=i,g=this.session.getNextFoldLine(c),w=g?g.start.row:1/0;c>w&&(c=g.end.row+1,g=this.session.getNextFoldLine(c,g),w=g?g.start.row:1/0),!(c>o);){var p=this.$lines.createCell(c,e,this.session),b=p.element;this.dom.removeChildren(b),M.setStyle(b.style,"height",this.$lines.computeLineHeight(c,e,this.session)+"px"),M.setStyle(b.style,"top",this.$lines.computeLineTop(c,e,this.session)+"px"),this.$renderLine(b,c,c==w?g:!1),this.$useLineGroups()?b.className="ace_line_group":b.className="ace_line",s.push(p),c++}return s},r.prototype.update=function(e){this.$lines.moveContainer(e),this.config=e;for(var i=e.firstRow,o=e.lastRow,s=this.$lines;s.getLength();)s.pop();s.push(this.$renderLinesFragment(e,i,o))},r.prototype.$renderToken=function(e,i,o,s){for(var c=this,g=/(\t)|( +)|([\x00-\x1f\x80-\xa0\xad\u1680\u180E\u2000-\u200f\u2028\u2029\u202F\u205F\uFEFF\uFFF9-\uFFFC\u2066\u2067\u2068\u202A\u202B\u202D\u202E\u202C\u2069]+)|(\u3000)|([\u1100-\u115F\u11A3-\u11A7\u11FA-\u11FF\u2329-\u232A\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3001-\u303E\u3041-\u3096\u3099-\u30FF\u3105-\u312D\u3131-\u318E\u3190-\u31BA\u31C0-\u31E3\u31F0-\u321E\u3220-\u3247\u3250-\u32FE\u3300-\u4DBF\u4E00-\uA48C\uA490-\uA4C6\uA960-\uA97C\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFAFF\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE66\uFE68-\uFE6B\uFF01-\uFF60\uFFE0-\uFFE6]|[\uD800-\uDBFF][\uDC00-\uDFFF])/g,w=this.dom.createFragment(this.element),p,b=0;p=g.exec(s);){var y=p[1],d=p[2],v=p[3],$=p[4],f=p[5];if(!(!c.showSpaces&&d)){var u=b!=p.index?s.slice(b,p.index):"";if(b=p.index+p[0].length,u&&w.appendChild(this.dom.createTextNode(u,this.element)),y){var A=c.session.getScreenTabSize(i+p.index);w.appendChild(c.$tabStrings[A].cloneNode(!0)),i+=A-1}else if(d)if(c.showSpaces){var m=this.dom.createElement("span");m.className="ace_invisible ace_invisible_space",m.textContent=S.stringRepeat(c.SPACE_CHAR,d.length),w.appendChild(m)}else w.appendChild(this.com.createTextNode(d,this.element));else if(v){var m=this.dom.createElement("span");m.className="ace_invisible ace_invisible_space ace_invalid",m.textContent=S.stringRepeat(c.SPACE_CHAR,v.length),w.appendChild(m)}else if($){i+=1;var m=this.dom.createElement("span");m.style.width=c.config.characterWidth*2+"px",m.className=c.showSpaces?"ace_cjk ace_invisible ace_invisible_space":"ace_cjk",m.textContent=c.showSpaces?c.SPACE_CHAR:$,w.appendChild(m)}else if(f){i+=1;var m=this.dom.createElement("span");m.style.width=c.config.characterWidth*2+"px",m.className="ace_cjk",m.textContent=f,w.appendChild(m)}}}if(w.appendChild(this.dom.createTextNode(b?s.slice(b):s,this.element)),this.$textToken[o.type])e.appendChild(w);else{var h="ace_"+o.type.replace(/\./g," ace_"),m=this.dom.createElement("span");o.type=="fold"&&(m.style.width=o.value.length*this.config.characterWidth+"px",m.setAttribute("title",t("Unfold code"))),m.className=h,m.appendChild(w),e.appendChild(m)}return i+s.length},r.prototype.renderIndentGuide=function(e,i,o){var s=i.search(this.$indentGuideRe);if(s<=0||s>=o)return i;if(i[0]==" "){s-=s%this.tabSize;for(var c=s/this.tabSize,g=0;g<c;g++)e.appendChild(this.$tabStrings[" "].cloneNode(!0));return this.$highlightIndentGuide(),i.substr(s)}else if(i[0]=="	"){for(var g=0;g<s;g++)e.appendChild(this.$tabStrings["	"].cloneNode(!0));return this.$highlightIndentGuide(),i.substr(s)}return this.$highlightIndentGuide(),i},r.prototype.$highlightIndentGuide=function(){if(!(!this.$highlightIndentGuides||!this.displayIndentGuides)){this.$highlightIndentGuideMarker={indentLevel:void 0,start:void 0,end:void 0,dir:void 0};var e=this.session.doc.$lines;if(e){var i=this.session.selection.getCursor(),o=/^\s*/.exec(this.session.doc.getLine(i.row))[0].length,s=Math.floor(o/this.tabSize);this.$highlightIndentGuideMarker={indentLevel:s,start:i.row};var c=this.session.$bracketHighlight;if(c){for(var g=this.session.$bracketHighlight.ranges,w=0;w<g.length;w++)if(i.row!==g[w].start.row){this.$highlightIndentGuideMarker.end=g[w].start.row,i.row>g[w].start.row?this.$highlightIndentGuideMarker.dir=-1:this.$highlightIndentGuideMarker.dir=1;break}}if(!this.$highlightIndentGuideMarker.end&&e[i.row]!==""&&i.column===e[i.row].length){this.$highlightIndentGuideMarker.dir=1;for(var w=i.row+1;w<e.length;w++){var p=e[w],b=/^\s*/.exec(p)[0].length;if(p!==""&&(this.$highlightIndentGuideMarker.end=w,b<=o))break}}this.$renderHighlightIndentGuide()}}},r.prototype.$clearActiveIndentGuide=function(){for(var e=this.$lines.cells,i=0;i<e.length;i++){var o=e[i],s=o.element.childNodes;if(s.length>0){for(var c=0;c<s.length;c++)if(s[c].classList&&s[c].classList.contains("ace_indent-guide-active")){s[c].classList.remove("ace_indent-guide-active");break}}}},r.prototype.$setIndentGuideActive=function(e,i){var o=this.session.doc.getLine(e.row);if(o!==""){var s=e.element.childNodes;if(s){var c=s[i-1];c&&c.classList&&c.classList.contains("ace_indent-guide")&&c.classList.add("ace_indent-guide-active")}}},r.prototype.$renderHighlightIndentGuide=function(){if(this.$lines){var e=this.$lines.cells;this.$clearActiveIndentGuide();var i=this.$highlightIndentGuideMarker.indentLevel;if(i!==0)if(this.$highlightIndentGuideMarker.dir===1)for(var o=0;o<e.length;o++){var s=e[o];if(this.$highlightIndentGuideMarker.end&&s.row>=this.$highlightIndentGuideMarker.start+1){if(s.row>=this.$highlightIndentGuideMarker.end)break;this.$setIndentGuideActive(s,i)}}else for(var o=e.length-1;o>=0;o--){var s=e[o];if(this.$highlightIndentGuideMarker.end&&s.row<this.$highlightIndentGuideMarker.start){if(s.row<=this.$highlightIndentGuideMarker.end)break;this.$setIndentGuideActive(s,i)}}}},r.prototype.$createLineElement=function(e){var i=this.dom.createElement("div");return i.className="ace_line",i.style.height=this.config.lineHeight+"px",i},r.prototype.$renderWrappedLine=function(e,i,o){var s=0,c=0,g=o[0],w=0,p=this.$createLineElement();e.appendChild(p);for(var b=0;b<i.length;b++){var y=i[b],d=y.value;if(b==0&&this.displayIndentGuides){if(s=d.length,d=this.renderIndentGuide(p,d,g),!d)continue;s-=d.length}if(s+d.length<g)w=this.$renderToken(p,w,y,d),s+=d.length;else{for(;s+d.length>=g;)w=this.$renderToken(p,w,y,d.substring(0,g-s)),d=d.substring(g-s),s=g,p=this.$createLineElement(),e.appendChild(p),p.appendChild(this.dom.createTextNode(S.stringRepeat("\xA0",o.indent),this.element)),c++,w=0,g=o[c]||Number.MAX_VALUE;d.length!=0&&(s+=d.length,w=this.$renderToken(p,w,y,d))}}o[o.length-1]>this.MAX_LINE_LENGTH&&this.$renderOverflowMessage(p,w,null,"",!0)},r.prototype.$renderSimpleLine=function(e,i){for(var o=0,s=0;s<i.length;s++){var c=i[s],g=c.value;if(!(s==0&&this.displayIndentGuides&&(g=this.renderIndentGuide(e,g),!g))){if(o+g.length>this.MAX_LINE_LENGTH)return this.$renderOverflowMessage(e,o,c,g);o=this.$renderToken(e,o,c,g)}}},r.prototype.$renderOverflowMessage=function(e,i,o,s,c){o&&this.$renderToken(e,i,o,s.slice(0,this.MAX_LINE_LENGTH-i));var g=this.dom.createElement("span");g.className="ace_inline_button ace_keyword ace_toggle_wrap",g.textContent=c?"<hide>":"<click to see more...>",e.appendChild(g)},r.prototype.$renderLine=function(e,i,o){if(!o&&o!=!1&&(o=this.session.getFoldLine(i)),o)var s=this.$getFoldLineTokens(i,o);else var s=this.session.getTokens(i);var c=e;if(s.length){var g=this.session.getRowSplitData(i);if(g&&g.length){this.$renderWrappedLine(e,s,g);var c=e.lastChild}else{var c=e;this.$useLineGroups()&&(c=this.$createLineElement(),e.appendChild(c)),this.$renderSimpleLine(c,s)}}else this.$useLineGroups()&&(c=this.$createLineElement(),e.appendChild(c));if(this.showEOL&&c){o&&(i=o.end.row);var w=this.dom.createElement("span");w.className="ace_invisible ace_invisible_eol",w.textContent=i==this.session.getLength()-1?this.EOF_CHAR:this.EOL_CHAR,c.appendChild(w)}},r.prototype.$getFoldLineTokens=function(e,i){var o=this.session,s=[];function c(w,p,b){for(var y=0,d=0;d+w[y].value.length<p;)if(d+=w[y].value.length,y++,y==w.length)return;if(d!=p){var v=w[y].value.substring(p-d);v.length>b-p&&(v=v.substring(0,b-p)),s.push({type:w[y].type,value:v}),d=p+v.length,y+=1}for(;d<b&&y<w.length;){var v=w[y].value;v.length+d>b?s.push({type:w[y].type,value:v.substring(0,b-d)}):s.push(w[y]),d+=v.length,y+=1}}var g=o.getTokens(e);return i.walk(function(w,p,b,y,d){w!=null?s.push({type:"fold",value:w}):(d&&(g=o.getTokens(p)),g.length&&c(g,y,b))},i.end.row,this.session.getLine(i.end.row).length),s},r.prototype.$useLineGroups=function(){return this.session.getUseWrapMode()},r}();n.prototype.$textToken={text:!0,rparen:!0,lparen:!0},n.prototype.EOF_CHAR="\xB6",n.prototype.EOL_CHAR_LF="\xAC",n.prototype.EOL_CHAR_CRLF="\xA4",n.prototype.EOL_CHAR=n.prototype.EOL_CHAR_LF,n.prototype.TAB_CHAR="\u2014",n.prototype.SPACE_CHAR="\xB7",n.prototype.$padding=0,n.prototype.MAX_LINE_LENGTH=1e4,n.prototype.showInvisibles=!1,n.prototype.showSpaces=!1,n.prototype.showTabs=!1,n.prototype.showEOL=!1,n.prototype.displayIndentGuides=!0,n.prototype.$highlightIndentGuides=!0,n.prototype.$tabStrings=[],n.prototype.destroy={},n.prototype.onChangeTabSize=n.prototype.$computeTabString,R.implement(n.prototype,l),k.Text=n});ace.define("ace/layer/cursor",["require","exports","module","ace/lib/dom"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=function(){function S(a){this.element=R.createElement("div"),this.element.className="ace_layer ace_cursor-layer",a.appendChild(this.element),this.isVisible=!1,this.isBlinking=!0,this.blinkInterval=1e3,this.smoothBlinking=!1,this.cursors=[],this.cursor=this.addCursor(),R.addCssClass(this.element,"ace_hidden-cursors"),this.$updateCursors=this.$updateOpacity.bind(this)}return S.prototype.$updateOpacity=function(a){for(var l=this.cursors,t=l.length;t--;)R.setStyle(l[t].style,"opacity",a?"":"0")},S.prototype.$startCssAnimation=function(){for(var a=this.cursors,l=a.length;l--;)a[l].style.animationDuration=this.blinkInterval+"ms";this.$isAnimating=!0,setTimeout(function(){this.$isAnimating&&R.addCssClass(this.element,"ace_animate-blinking")}.bind(this))},S.prototype.$stopCssAnimation=function(){this.$isAnimating=!1,R.removeCssClass(this.element,"ace_animate-blinking")},S.prototype.setPadding=function(a){this.$padding=a},S.prototype.setSession=function(a){this.session=a},S.prototype.setBlinking=function(a){a!=this.isBlinking&&(this.isBlinking=a,this.restartTimer())},S.prototype.setBlinkInterval=function(a){a!=this.blinkInterval&&(this.blinkInterval=a,this.restartTimer())},S.prototype.setSmoothBlinking=function(a){a!=this.smoothBlinking&&(this.smoothBlinking=a,R.setCssClass(this.element,"ace_smooth-blinking",a),this.$updateCursors(!0),this.restartTimer())},S.prototype.addCursor=function(){var a=R.createElement("div");return a.className="ace_cursor",this.element.appendChild(a),this.cursors.push(a),a},S.prototype.removeCursor=function(){if(this.cursors.length>1){var a=this.cursors.pop();return a.parentNode.removeChild(a),a}},S.prototype.hideCursor=function(){this.isVisible=!1,R.addCssClass(this.element,"ace_hidden-cursors"),this.restartTimer()},S.prototype.showCursor=function(){this.isVisible=!0,R.removeCssClass(this.element,"ace_hidden-cursors"),this.restartTimer()},S.prototype.restartTimer=function(){var a=this.$updateCursors;if(clearInterval(this.intervalId),clearTimeout(this.timeoutId),this.$stopCssAnimation(),this.smoothBlinking&&(this.$isSmoothBlinking=!1,R.removeCssClass(this.element,"ace_smooth-blinking")),a(!0),!this.isBlinking||!this.blinkInterval||!this.isVisible){this.$stopCssAnimation();return}if(this.smoothBlinking&&(this.$isSmoothBlinking=!0,setTimeout(function(){this.$isSmoothBlinking&&R.addCssClass(this.element,"ace_smooth-blinking")}.bind(this))),R.HAS_CSS_ANIMATION)this.$startCssAnimation();else{var l=function(){this.timeoutId=setTimeout(function(){a(!1)},.6*this.blinkInterval)}.bind(this);this.intervalId=setInterval(function(){a(!0),l()},this.blinkInterval),l()}},S.prototype.getPixelPosition=function(a,l){if(!this.config||!this.session)return{left:0,top:0};a||(a=this.session.selection.getCursor());var t=this.session.documentToScreenPosition(a),n=this.$padding+(this.session.$bidiHandler.isBidiRow(t.row,a.row)?this.session.$bidiHandler.getPosLeft(t.column):t.column*this.config.characterWidth),r=(t.row-(l?this.config.firstRowScreen:0))*this.config.lineHeight;return{left:n,top:r}},S.prototype.isCursorInView=function(a,l){return a.top>=0&&a.top<l.maxHeight},S.prototype.update=function(a){this.config=a;var l=this.session.$selectionMarkers,t=0,n=0;(l===void 0||l.length===0)&&(l=[{cursor:null}]);for(var t=0,r=l.length;t<r;t++){var e=this.getPixelPosition(l[t].cursor,!0);if(!((e.top>a.height+a.offset||e.top<0)&&t>1)){var i=this.cursors[n++]||this.addCursor(),o=i.style;this.drawCursor?this.drawCursor(i,e,a,l[t],this.session):this.isCursorInView(e,a)?(R.setStyle(o,"display","block"),R.translate(i,e.left,e.top),R.setStyle(o,"width",Math.round(a.characterWidth)+"px"),R.setStyle(o,"height",a.lineHeight+"px")):R.setStyle(o,"display","none")}}for(;this.cursors.length>n;)this.removeCursor();var s=this.session.getOverwrite();this.$setOverwrite(s),this.$pixelPos=e,this.restartTimer()},S.prototype.$setOverwrite=function(a){a!=this.overwrite&&(this.overwrite=a,a?R.addCssClass(this.element,"ace_overwrite-cursors"):R.removeCssClass(this.element,"ace_overwrite-cursors"))},S.prototype.destroy=function(){clearInterval(this.intervalId),clearTimeout(this.timeoutId)},S}();M.prototype.$padding=0,M.prototype.drawCursor=null,k.Cursor=M});ace.define("ace/scrollbar",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/event","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var i=function(o,s){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,g){c.__proto__=g}||function(c,g){for(var w in g)Object.prototype.hasOwnProperty.call(g,w)&&(c[w]=g[w])},i(o,s)};return function(o,s){if(typeof s!="function"&&s!==null)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");i(o,s);function c(){this.constructor=o}o.prototype=s===null?Object.create(s):(c.prototype=s.prototype,new c)}}(),M=x("./lib/oop"),S=x("./lib/dom"),a=x("./lib/event"),l=x("./lib/event_emitter").EventEmitter,t=32768,n=function(){function i(o,s){this.element=S.createElement("div"),this.element.className="ace_scrollbar ace_scrollbar"+s,this.inner=S.createElement("div"),this.inner.className="ace_scrollbar-inner",this.inner.textContent="\xA0",this.element.appendChild(this.inner),o.appendChild(this.element),this.setVisible(!1),this.skipEvent=!1,a.addListener(this.element,"scroll",this.onScroll.bind(this)),a.addListener(this.element,"mousedown",a.preventDefault)}return i.prototype.setVisible=function(o){this.element.style.display=o?"":"none",this.isVisible=o,this.coeff=1},i}();M.implement(n.prototype,l);var r=function(i){R(o,i);function o(s,c){var g=i.call(this,s,"-v")||this;return g.scrollTop=0,g.scrollHeight=0,c.$scrollbarWidth=g.width=S.scrollbarWidth(s.ownerDocument),g.inner.style.width=g.element.style.width=(g.width||15)+5+"px",g.$minWidth=0,g}return o.prototype.onScroll=function(){if(!this.skipEvent){if(this.scrollTop=this.element.scrollTop,this.coeff!=1){var s=this.element.clientHeight/this.scrollHeight;this.scrollTop=this.scrollTop*(1-s)/(this.coeff-s)}this._emit("scroll",{data:this.scrollTop})}this.skipEvent=!1},o.prototype.getWidth=function(){return Math.max(this.isVisible?this.width:0,this.$minWidth||0)},o.prototype.setHeight=function(s){this.element.style.height=s+"px"},o.prototype.setScrollHeight=function(s){this.scrollHeight=s,s>t?(this.coeff=t/s,s=t):this.coeff!=1&&(this.coeff=1),this.inner.style.height=s+"px"},o.prototype.setScrollTop=function(s){this.scrollTop!=s&&(this.skipEvent=!0,this.scrollTop=s,this.element.scrollTop=s*this.coeff)},o}(n);r.prototype.setInnerHeight=r.prototype.setScrollHeight;var e=function(i){R(o,i);function o(s,c){var g=i.call(this,s,"-h")||this;return g.scrollLeft=0,g.height=c.$scrollbarWidth,g.inner.style.height=g.element.style.height=(g.height||15)+5+"px",g}return o.prototype.onScroll=function(){this.skipEvent||(this.scrollLeft=this.element.scrollLeft,this._emit("scroll",{data:this.scrollLeft})),this.skipEvent=!1},o.prototype.getHeight=function(){return this.isVisible?this.height:0},o.prototype.setWidth=function(s){this.element.style.width=s+"px"},o.prototype.setInnerWidth=function(s){this.inner.style.width=s+"px"},o.prototype.setScrollWidth=function(s){this.inner.style.width=s+"px"},o.prototype.setScrollLeft=function(s){this.scrollLeft!=s&&(this.skipEvent=!0,this.scrollLeft=this.element.scrollLeft=s)},o}(n);k.ScrollBar=r,k.ScrollBarV=r,k.ScrollBarH=e,k.VScrollBar=r,k.HScrollBar=e});ace.define("ace/scrollbar_custom",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/event","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=this&&this.__extends||function(){var e=function(i,o){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,c){s.__proto__=c}||function(s,c){for(var g in c)Object.prototype.hasOwnProperty.call(c,g)&&(s[g]=c[g])},e(i,o)};return function(i,o){if(typeof o!="function"&&o!==null)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");e(i,o);function s(){this.constructor=i}i.prototype=o===null?Object.create(o):(s.prototype=o.prototype,new s)}}(),M=x("./lib/oop"),S=x("./lib/dom"),a=x("./lib/event"),l=x("./lib/event_emitter").EventEmitter;S.importCssString(`.ace_editor>.ace_sb-v div, .ace_editor>.ace_sb-h div{
  position: absolute;
  background: rgba(128, 128, 128, 0.6);
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  border: 1px solid #bbb;
  border-radius: 2px;
  z-index: 8;
}
.ace_editor>.ace_sb-v, .ace_editor>.ace_sb-h {
  position: absolute;
  z-index: 6;
  background: none;
  overflow: hidden!important;
}
.ace_editor>.ace_sb-v {
  z-index: 6;
  right: 0;
  top: 0;
  width: 12px;
}
.ace_editor>.ace_sb-v div {
  z-index: 8;
  right: 0;
  width: 100%;
}
.ace_editor>.ace_sb-h {
  bottom: 0;
  left: 0;
  height: 12px;
}
.ace_editor>.ace_sb-h div {
  bottom: 0;
  height: 100%;
}
.ace_editor>.ace_sb_grabbed {
  z-index: 8;
  background: #000;
}`,"ace_scrollbar.css",!1);var t=function(){function e(i,o){this.element=S.createElement("div"),this.element.className="ace_sb"+o,this.inner=S.createElement("div"),this.inner.className="",this.element.appendChild(this.inner),this.VScrollWidth=12,this.HScrollHeight=12,i.appendChild(this.element),this.setVisible(!1),this.skipEvent=!1,a.addMultiMouseDownListener(this.element,[500,300,300],this,"onMouseDown")}return e.prototype.setVisible=function(i){this.element.style.display=i?"":"none",this.isVisible=i,this.coeff=1},e}();M.implement(t.prototype,l);var n=function(e){R(i,e);function i(o,s){var c=e.call(this,o,"-v")||this;return c.scrollTop=0,c.scrollHeight=0,c.parent=o,c.width=c.VScrollWidth,c.renderer=s,c.inner.style.width=c.element.style.width=(c.width||15)+"px",c.$minWidth=0,c}return i.prototype.onMouseDown=function(o,s){if(o==="mousedown"&&!(a.getButton(s)!==0||s.detail===2)){if(s.target===this.inner){var c=this,g=s.clientY,w=function(f){g=f.clientY},p=function(){clearInterval(v)},b=s.clientY,y=this.thumbTop,d=function(){if(g!==void 0){var f=c.scrollTopFromThumbTop(y+g-b);f!==c.scrollTop&&c._emit("scroll",{data:f})}};a.capture(this.inner,w,p);var v=setInterval(d,20);return a.preventDefault(s)}var $=s.clientY-this.element.getBoundingClientRect().top-this.thumbHeight/2;return this._emit("scroll",{data:this.scrollTopFromThumbTop($)}),a.preventDefault(s)}},i.prototype.getHeight=function(){return this.height},i.prototype.scrollTopFromThumbTop=function(o){var s=o*(this.pageHeight-this.viewHeight)/(this.slideHeight-this.thumbHeight);return s=s>>0,s<0?s=0:s>this.pageHeight-this.viewHeight&&(s=this.pageHeight-this.viewHeight),s},i.prototype.getWidth=function(){return Math.max(this.isVisible?this.width:0,this.$minWidth||0)},i.prototype.setHeight=function(o){this.height=Math.max(0,o),this.slideHeight=this.height,this.viewHeight=this.height,this.setScrollHeight(this.pageHeight,!0)},i.prototype.setScrollHeight=function(o,s){this.pageHeight===o&&!s||(this.pageHeight=o,this.thumbHeight=this.slideHeight*this.viewHeight/this.pageHeight,this.thumbHeight>this.slideHeight&&(this.thumbHeight=this.slideHeight),this.thumbHeight<15&&(this.thumbHeight=15),this.inner.style.height=this.thumbHeight+"px",this.scrollTop>this.pageHeight-this.viewHeight&&(this.scrollTop=this.pageHeight-this.viewHeight,this.scrollTop<0&&(this.scrollTop=0),this._emit("scroll",{data:this.scrollTop})))},i.prototype.setScrollTop=function(o){this.scrollTop=o,o<0&&(o=0),this.thumbTop=o*(this.slideHeight-this.thumbHeight)/(this.pageHeight-this.viewHeight),this.inner.style.top=this.thumbTop+"px"},i}(t);n.prototype.setInnerHeight=n.prototype.setScrollHeight;var r=function(e){R(i,e);function i(o,s){var c=e.call(this,o,"-h")||this;return c.scrollLeft=0,c.scrollWidth=0,c.height=c.HScrollHeight,c.inner.style.height=c.element.style.height=(c.height||12)+"px",c.renderer=s,c}return i.prototype.onMouseDown=function(o,s){if(o==="mousedown"&&!(a.getButton(s)!==0||s.detail===2)){if(s.target===this.inner){var c=this,g=s.clientX,w=function(f){g=f.clientX},p=function(){clearInterval(v)},b=s.clientX,y=this.thumbLeft,d=function(){if(g!==void 0){var f=c.scrollLeftFromThumbLeft(y+g-b);f!==c.scrollLeft&&c._emit("scroll",{data:f})}};a.capture(this.inner,w,p);var v=setInterval(d,20);return a.preventDefault(s)}var $=s.clientX-this.element.getBoundingClientRect().left-this.thumbWidth/2;return this._emit("scroll",{data:this.scrollLeftFromThumbLeft($)}),a.preventDefault(s)}},i.prototype.getHeight=function(){return this.isVisible?this.height:0},i.prototype.scrollLeftFromThumbLeft=function(o){var s=o*(this.pageWidth-this.viewWidth)/(this.slideWidth-this.thumbWidth);return s=s>>0,s<0?s=0:s>this.pageWidth-this.viewWidth&&(s=this.pageWidth-this.viewWidth),s},i.prototype.setWidth=function(o){this.width=Math.max(0,o),this.element.style.width=this.width+"px",this.slideWidth=this.width,this.viewWidth=this.width,this.setScrollWidth(this.pageWidth,!0)},i.prototype.setScrollWidth=function(o,s){this.pageWidth===o&&!s||(this.pageWidth=o,this.thumbWidth=this.slideWidth*this.viewWidth/this.pageWidth,this.thumbWidth>this.slideWidth&&(this.thumbWidth=this.slideWidth),this.thumbWidth<15&&(this.thumbWidth=15),this.inner.style.width=this.thumbWidth+"px",this.scrollLeft>this.pageWidth-this.viewWidth&&(this.scrollLeft=this.pageWidth-this.viewWidth,this.scrollLeft<0&&(this.scrollLeft=0),this._emit("scroll",{data:this.scrollLeft})))},i.prototype.setScrollLeft=function(o){this.scrollLeft=o,o<0&&(o=0),this.thumbLeft=o*(this.slideWidth-this.thumbWidth)/(this.pageWidth-this.viewWidth),this.inner.style.left=this.thumbLeft+"px"},i}(t);r.prototype.setInnerWidth=r.prototype.setScrollWidth,k.ScrollBar=n,k.ScrollBarV=n,k.ScrollBarH=r,k.VScrollBar=n,k.HScrollBar=r});ace.define("ace/renderloop",["require","exports","module","ace/lib/event"],function(x,k,H){"use strict";var R=x("./lib/event"),M=function(){function S(a,l){this.onRender=a,this.pending=!1,this.changes=0,this.$recursionLimit=2,this.window=l||window;var t=this;this._flush=function(n){t.pending=!1;var r=t.changes;if(r&&(R.blockIdle(100),t.changes=0,t.onRender(r)),t.changes){if(t.$recursionLimit--<0)return;t.schedule()}else t.$recursionLimit=2}}return S.prototype.schedule=function(a){this.changes=this.changes|a,this.changes&&!this.pending&&(R.nextFrame(this._flush),this.pending=!0)},S.prototype.clear=function(a){var l=this.changes;return this.changes=0,l},S}();k.RenderLoop=M});ace.define("ace/layer/font_metrics",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/lib/event","ace/lib/useragent","ace/lib/event_emitter"],function(x,k,H){var R=x("../lib/oop"),M=x("../lib/dom"),S=x("../lib/lang"),a=x("../lib/event"),l=x("../lib/useragent"),t=x("../lib/event_emitter").EventEmitter,n=512,r=typeof ResizeObserver=="function",e=200,i=function(){function o(s){this.el=M.createElement("div"),this.$setMeasureNodeStyles(this.el.style,!0),this.$main=M.createElement("div"),this.$setMeasureNodeStyles(this.$main.style),this.$measureNode=M.createElement("div"),this.$setMeasureNodeStyles(this.$measureNode.style),this.el.appendChild(this.$main),this.el.appendChild(this.$measureNode),s.appendChild(this.el),this.$measureNode.textContent=S.stringRepeat("X",n),this.$characterSize={width:0,height:0},r?this.$addObserver():this.checkForSizeChanges()}return o.prototype.$setMeasureNodeStyles=function(s,c){s.width=s.height="auto",s.left=s.top="0px",s.visibility="hidden",s.position="absolute",s.whiteSpace="pre",l.isIE<8?s["font-family"]="inherit":s.font="inherit",s.overflow=c?"hidden":"visible"},o.prototype.checkForSizeChanges=function(s){if(s===void 0&&(s=this.$measureSizes()),s&&(this.$characterSize.width!==s.width||this.$characterSize.height!==s.height)){this.$measureNode.style.fontWeight="bold";var c=this.$measureSizes();this.$measureNode.style.fontWeight="",this.$characterSize=s,this.charSizes=Object.create(null),this.allowBoldFonts=c&&c.width===s.width&&c.height===s.height,this._emit("changeCharacterSize",{data:s})}},o.prototype.$addObserver=function(){var s=this;this.$observer=new window.ResizeObserver(function(c){s.checkForSizeChanges()}),this.$observer.observe(this.$measureNode)},o.prototype.$pollSizeChanges=function(){if(this.$pollSizeChangesTimer||this.$observer)return this.$pollSizeChangesTimer;var s=this;return this.$pollSizeChangesTimer=a.onIdle(function c(){s.checkForSizeChanges(),a.onIdle(c,500)},500)},o.prototype.setPolling=function(s){s?this.$pollSizeChanges():this.$pollSizeChangesTimer&&(clearInterval(this.$pollSizeChangesTimer),this.$pollSizeChangesTimer=0)},o.prototype.$measureSizes=function(s){var c={height:(s||this.$measureNode).clientHeight,width:(s||this.$measureNode).clientWidth/n};return c.width===0||c.height===0?null:c},o.prototype.$measureCharWidth=function(s){this.$main.textContent=S.stringRepeat(s,n);var c=this.$main.getBoundingClientRect();return c.width/n},o.prototype.getCharacterWidth=function(s){var c=this.charSizes[s];return c===void 0&&(c=this.charSizes[s]=this.$measureCharWidth(s)/this.$characterSize.width),c},o.prototype.destroy=function(){clearInterval(this.$pollSizeChangesTimer),this.$observer&&this.$observer.disconnect(),this.el&&this.el.parentNode&&this.el.parentNode.removeChild(this.el)},o.prototype.$getZoom=function(s){return!s||!s.parentElement?1:(window.getComputedStyle(s).zoom||1)*this.$getZoom(s.parentElement)},o.prototype.$initTransformMeasureNodes=function(){var s=function(c,g){return["div",{style:"position: absolute;top:"+c+"px;left:"+g+"px;"}]};this.els=M.buildDom([s(0,0),s(e,0),s(0,e),s(e,e)],this.el)},o.prototype.transformCoordinates=function(s,c){if(s){var g=this.$getZoom(this.el);s=y(1/g,s)}function w(O,I,W){var F=O[1]*I[0]-O[0]*I[1];return[(-I[1]*W[0]+I[0]*W[1])/F,(+O[1]*W[0]-O[0]*W[1])/F]}function p(O,I){return[O[0]-I[0],O[1]-I[1]]}function b(O,I){return[O[0]+I[0],O[1]+I[1]]}function y(O,I){return[O*I[0],O*I[1]]}this.els||this.$initTransformMeasureNodes();function d(O){var I=O.getBoundingClientRect();return[I.left,I.top]}var v=d(this.els[0]),$=d(this.els[1]),f=d(this.els[2]),u=d(this.els[3]),A=w(p(u,$),p(u,f),p(b($,f),b(u,v))),m=y(1+A[0],p($,v)),h=y(1+A[1],p(f,v));if(c){var C=c,L=A[0]*C[0]/e+A[1]*C[1]/e+1,T=b(y(C[0],m),y(C[1],h));return b(y(1/L/e,T),v)}var E=p(s,v),_=w(p(m,y(A[0],E)),p(h,y(A[1],E)),E);return y(e,_)},o}();i.prototype.$characterSize={width:0,height:0},R.implement(i.prototype,t),k.FontMetrics=i});ace.define("ace/css/editor-css",["require","exports","module"],function(x,k,H){H.exports=`
.ace_br1 {border-top-left-radius    : 3px;}
.ace_br2 {border-top-right-radius   : 3px;}
.ace_br3 {border-top-left-radius    : 3px; border-top-right-radius:    3px;}
.ace_br4 {border-bottom-right-radius: 3px;}
.ace_br5 {border-top-left-radius    : 3px; border-bottom-right-radius: 3px;}
.ace_br6 {border-top-right-radius   : 3px; border-bottom-right-radius: 3px;}
.ace_br7 {border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px;}
.ace_br8 {border-bottom-left-radius : 3px;}
.ace_br9 {border-top-left-radius    : 3px; border-bottom-left-radius:  3px;}
.ace_br10{border-top-right-radius   : 3px; border-bottom-left-radius:  3px;}
.ace_br11{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-left-radius:  3px;}
.ace_br12{border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}
.ace_br13{border-top-left-radius    : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}
.ace_br14{border-top-right-radius   : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}
.ace_br15{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;}


.ace_editor {
    position: relative;
    overflow: hidden;
    padding: 0;
    font: 12px/normal 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Source Code Pro', 'source-code-pro', monospace;
    direction: ltr;
    text-align: left;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.ace_scroller {
    position: absolute;
    overflow: hidden;
    top: 0;
    bottom: 0;
    background-color: inherit;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    cursor: text;
}

.ace_content {
    position: absolute;
    box-sizing: border-box;
    min-width: 100%;
    contain: style size layout;
    font-variant-ligatures: no-common-ligatures;
}

.ace_keyboard-focus:focus {
    box-shadow: inset 0 0 0 2px #5E9ED6;
    outline: none;
}

.ace_dragging .ace_scroller:before{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    background: rgba(250, 250, 250, 0.01);
    z-index: 1000;
}
.ace_dragging.ace_dark .ace_scroller:before{
    background: rgba(0, 0, 0, 0.01);
}

.ace_gutter {
    position: absolute;
    overflow : hidden;
    width: auto;
    top: 0;
    bottom: 0;
    left: 0;
    cursor: default;
    z-index: 4;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    contain: style size layout;
}

.ace_gutter-active-line {
    position: absolute;
    left: 0;
    right: 0;
}

.ace_scroller.ace_scroll-left:after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: 17px 0 16px -16px rgba(0, 0, 0, 0.4) inset;
    pointer-events: none;
}

.ace_gutter-cell, .ace_gutter-cell_svg-icons {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding-left: 19px;
    padding-right: 6px;
    background-repeat: no-repeat;
}

.ace_gutter-cell_svg-icons .ace_gutter_annotation {
    margin-left: -14px;
    float: left;
}

.ace_gutter-cell .ace_gutter_annotation {
    margin-left: -19px;
    float: left;
}

.ace_gutter-cell.ace_error, .ace_icon.ace_error, .ace_icon.ace_error_fold {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAABOFBMVEX/////////QRswFAb/Ui4wFAYwFAYwFAaWGAfDRymzOSH/PxswFAb/SiUwFAYwFAbUPRvjQiDllog5HhHdRybsTi3/Tyv9Tir+Syj/UC3////XurebMBIwFAb/RSHbPx/gUzfdwL3kzMivKBAwFAbbvbnhPx66NhowFAYwFAaZJg8wFAaxKBDZurf/RB6mMxb/SCMwFAYwFAbxQB3+RB4wFAb/Qhy4Oh+4QifbNRcwFAYwFAYwFAb/QRzdNhgwFAYwFAbav7v/Uy7oaE68MBK5LxLewr/r2NXewLswFAaxJw4wFAbkPRy2PyYwFAaxKhLm1tMwFAazPiQwFAaUGAb/QBrfOx3bvrv/VC/maE4wFAbRPBq6MRO8Qynew8Dp2tjfwb0wFAbx6eju5+by6uns4uH9/f36+vr/GkHjAAAAYnRSTlMAGt+64rnWu/bo8eAA4InH3+DwoN7j4eLi4xP99Nfg4+b+/u9B/eDs1MD1mO7+4PHg2MXa347g7vDizMLN4eG+Pv7i5evs/v79yu7S3/DV7/498Yv24eH+4ufQ3Ozu/v7+y13sRqwAAADLSURBVHjaZc/XDsFgGIBhtDrshlitmk2IrbHFqL2pvXf/+78DPokj7+Fz9qpU/9UXJIlhmPaTaQ6QPaz0mm+5gwkgovcV6GZzd5JtCQwgsxoHOvJO15kleRLAnMgHFIESUEPmawB9ngmelTtipwwfASilxOLyiV5UVUyVAfbG0cCPHig+GBkzAENHS0AstVF6bacZIOzgLmxsHbt2OecNgJC83JERmePUYq8ARGkJx6XtFsdddBQgZE2nPR6CICZhawjA4Fb/chv+399kfR+MMMDGOQAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    background-position: 2px center;
}

.ace_gutter-cell.ace_warning, .ace_icon.ace_warning, .ace_icon.ace_warning_fold {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAmVBMVEX///8AAAD///8AAAAAAABPSzb/5sAAAAB/blH/73z/ulkAAAAAAAD85pkAAAAAAAACAgP/vGz/rkDerGbGrV7/pkQICAf////e0IsAAAD/oED/qTvhrnUAAAD/yHD/njcAAADuv2r/nz//oTj/p064oGf/zHAAAAA9Nir/tFIAAAD/tlTiuWf/tkIAAACynXEAAAAAAAAtIRW7zBpBAAAAM3RSTlMAABR1m7RXO8Ln31Z36zT+neXe5OzooRDfn+TZ4p3h2hTf4t3k3ucyrN1K5+Xaks52Sfs9CXgrAAAAjklEQVR42o3PbQ+CIBQFYEwboPhSYgoYunIqqLn6/z8uYdH8Vmdnu9vz4WwXgN/xTPRD2+sgOcZjsge/whXZgUaYYvT8QnuJaUrjrHUQreGczuEafQCO/SJTufTbroWsPgsllVhq3wJEk2jUSzX3CUEDJC84707djRc5MTAQxoLgupWRwW6UB5fS++NV8AbOZgnsC7BpEAAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    background-position: 2px center;
}

.ace_gutter-cell.ace_info, .ace_icon.ace_info {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAJ0Uk5TAAB2k804AAAAPklEQVQY02NgIB68QuO3tiLznjAwpKTgNyDbMegwisCHZUETUZV0ZqOquBpXj2rtnpSJT1AEnnRmL2OgGgAAIKkRQap2htgAAAAASUVORK5CYII=");
    background-repeat: no-repeat;
    background-position: 2px center;
}
.ace_dark .ace_gutter-cell.ace_info, .ace_dark .ace_icon.ace_info {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJFBMVEUAAAChoaGAgIAqKiq+vr6tra1ZWVmUlJSbm5s8PDxubm56enrdgzg3AAAAAXRSTlMAQObYZgAAAClJREFUeNpjYMAPdsMYHegyJZFQBlsUlMFVCWUYKkAZMxZAGdxlDMQBAG+TBP4B6RyJAAAAAElFTkSuQmCC");
}

.ace_icon_svg.ace_error {
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJyZWQiIHNoYXBlLXJlbmRlcmluZz0iZ2VvbWV0cmljUHJlY2lzaW9uIj4KPGNpcmNsZSBmaWxsPSJub25lIiBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPGxpbmUgeDE9IjExIiB5MT0iNSIgeDI9IjUiIHkyPSIxMSIvPgo8bGluZSB4MT0iMTEiIHkxPSIxMSIgeDI9IjUiIHkyPSI1Ii8+CjwvZz4KPC9zdmc+");
    background-color: crimson;
}
.ace_icon_svg.ace_warning {
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJkYXJrb3JhbmdlIiBzaGFwZS1yZW5kZXJpbmc9Imdlb21ldHJpY1ByZWNpc2lvbiI+Cjxwb2x5Z29uIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGZpbGw9Im5vbmUiIHBvaW50cz0iOCAxIDE1IDE1IDEgMTUgOCAxIi8+CjxyZWN0IHg9IjgiIHk9IjEyIiB3aWR0aD0iMC4wMSIgaGVpZ2h0PSIwLjAxIi8+CjxsaW5lIHgxPSI4IiB5MT0iNiIgeDI9IjgiIHkyPSIxMCIvPgo8L2c+Cjwvc3ZnPg==");
    background-color: darkorange;
}
.ace_icon_svg.ace_info {
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJibHVlIiBzaGFwZS1yZW5kZXJpbmc9Imdlb21ldHJpY1ByZWNpc2lvbiI+CjxjaXJjbGUgZmlsbD0ibm9uZSIgY3g9IjgiIGN5PSI4IiByPSI3IiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjxwb2x5bGluZSBwb2ludHM9IjggMTEgOCA4Ii8+Cjxwb2x5bGluZSBwb2ludHM9IjkgOCA2IDgiLz4KPGxpbmUgeDE9IjEwIiB5MT0iMTEiIHgyPSI2IiB5Mj0iMTEiLz4KPHJlY3QgeD0iOCIgeT0iNSIgd2lkdGg9IjAuMDEiIGhlaWdodD0iMC4wMSIvPgo8L2c+Cjwvc3ZnPg==");
    background-color: royalblue;
}

.ace_icon_svg.ace_error_fold {
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiIgZmlsbD0ibm9uZSI+CiAgPHBhdGggZD0ibSAxOC45Mjk4NTEsNy44Mjk4MDc2IGMgMC4xNDYzNTMsNi4zMzc0NjA0IC02LjMyMzE0Nyw3Ljc3Nzg0NDQgLTcuNDc3OTEyLDcuNzc3ODQ0NCAtMi4xMDcyNzI2LC0wLjEyODc1IDUuMTE3Njc4LDAuMzU2MjQ5IDUuMDUxNjk4LC03Ljg3MDA2MTggLTAuNjA0NjcyLC04LjAwMzk3MzQ5IC03LjA3NzI3MDYsLTcuNTYzMTE4OSAtNC44NTczLC03LjQzMDM5NTU2IDEuNjA2LC0wLjExNTE0MjI1IDYuODk3NDg1LDEuMjYyNTQ1OTYgNy4yODM1MTQsNy41MjI2MTI5NiB6IiBmaWxsPSJjcmltc29uIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0ibSA4LjExNDc1NjIsMi4wNTI5ODI4IGMgMy4zNDkxNjk4LDAgNi4wNjQxMzI4LDIuNjc2ODYyNyA2LjA2NDEzMjgsNS45Nzg5NTMgMCwzLjMwMjExMjIgLTIuNzE0OTYzLDUuOTc4OTIwMiAtNi4wNjQxMzI4LDUuOTc4OTIwMiAtMy4zNDkxNDczLDAgLTYuMDY0MTc3MiwtMi42NzY4MDggLTYuMDY0MTc3MiwtNS45Nzg5MjAyIDAuMDA1MzksLTMuMjk5ODg2MSAyLjcxNzI2NTYsLTUuOTczNjQwOCA2LjA2NDE3NzIsLTUuOTc4OTUzIHogbSAwLC0xLjczNTgyNzE5IGMgLTQuMzIxNDgzNiwwIC03LjgyNDc0MDM4LDMuNDU0MDE4NDkgLTcuODI0NzQwMzgsNy43MTQ3ODAxOSAwLDQuMjYwNzI4MiAzLjUwMzI1Njc4LDcuNzE0NzQ1MiA3LjgyNDc0MDM4LDcuNzE0NzQ1MiA0LjMyMTQ0OTgsMCA3LjgyNDY5OTgsLTMuNDU0MDE3IDcuODI0Njk5OCwtNy43MTQ3NDUyIDAsLTIuMDQ2MDkxNCAtMC44MjQzOTIsLTQuMDA4MzY3MiAtMi4yOTE3NTYsLTUuNDU1MTc0NiBDIDEyLjE4MDIyNSwxLjEyOTk2NDggMTAuMTkwMDEzLDAuMzE3MTU1NjEgOC4xMTQ3NTYyLDAuMzE3MTU1NjEgWiBNIDYuOTM3NDU2Myw4LjI0MDU5ODUgNC42NzE4Njg1LDEwLjQ4NTg1MiA2LjAwODY4MTQsMTEuODc2NzI4IDguMzE3MDAzNSw5LjYwMDc5MTEgMTAuNjI1MzM3LDExLjg3NjcyOCAxMS45NjIxMzgsMTAuNDg1ODUyIDkuNjk2NTUwOCw4LjI0MDU5ODUgMTEuOTYyMTM4LDYuMDA2ODA2NiAxMC41NzMyNDYsNC42Mzc0MzM1IDguMzE3MDAzNSw2Ljg3MzQyOTcgNi4wNjA3NjA3LDQuNjM3NDMzNSA0LjY3MTg2ODUsNi4wMDY4MDY2IFoiIGZpbGw9ImNyaW1zb24iIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4=");
    background-color: crimson;
}
.ace_icon_svg.ace_warning_fold {
    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyMCAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNC43NzY5IDE0LjczMzdMOC42NTE5MiAyLjQ4MzY5QzguMzI5NDYgMS44Mzg3NyA3LjQwOTEzIDEuODM4NzcgNy4wODY2NyAyLjQ4MzY5TDAuOTYxNjY5IDE0LjczMzdDMC42NzA3NzUgMTUuMzE1NSAxLjA5MzgzIDE2IDEuNzQ0MjkgMTZIMTMuOTk0M0MxNC42NDQ4IDE2IDE1LjA2NzggMTUuMzE1NSAxNC43NzY5IDE0LjczMzdaTTMuMTYwMDcgMTQuMjVMNy44NjkyOSA0LjgzMTU2TDEyLjU3ODUgMTQuMjVIMy4xNjAwN1pNOC43NDQyOSAxMS42MjVWMTMuMzc1SDYuOTk0MjlWMTEuNjI1SDguNzQ0MjlaTTYuOTk0MjkgMTAuNzVWNy4yNUg4Ljc0NDI5VjEwLjc1SDYuOTk0MjlaIiBmaWxsPSIjRUM3MjExIi8+CjxwYXRoIGQ9Ik0xMS4xOTkxIDIuOTUyMzhDMTAuODgwOSAyLjMxNDY3IDEwLjM1MzcgMS44MDUyNiA5LjcwNTUgMS41MDlMMTEuMDQxIDEuMDY5NzhDMTEuNjg4MyAwLjk0OTgxNCAxMi4zMzcgMS4yNzI2MyAxMi42MzE3IDEuODYxNDFMMTcuNjEzNiAxMS44MTYxQzE4LjM1MjcgMTMuMjkyOSAxNy41OTM4IDE1LjA4MDQgMTYuMDE4IDE1LjU3NDVDMTYuNDA0NCAxNC40NTA3IDE2LjMyMzEgMTMuMjE4OCAxNS43OTI0IDEyLjE1NTVMMTEuMTk5MSAyLjk1MjM4WiIgZmlsbD0iI0VDNzIxMSIvPgo8L3N2Zz4=");
    background-color: darkorange;
}

.ace_scrollbar {
    contain: strict;
    position: absolute;
    right: 0;
    bottom: 0;
    z-index: 6;
}

.ace_scrollbar-inner {
    position: absolute;
    cursor: text;
    left: 0;
    top: 0;
}

.ace_scrollbar-v{
    overflow-x: hidden;
    overflow-y: scroll;
    top: 0;
}

.ace_scrollbar-h {
    overflow-x: scroll;
    overflow-y: hidden;
    left: 0;
}

.ace_print-margin {
    position: absolute;
    height: 100%;
}

.ace_text-input {
    position: absolute;
    z-index: 0;
    width: 0.5em;
    height: 1em;
    opacity: 0;
    background: transparent;
    -moz-appearance: none;
    appearance: none;
    border: none;
    resize: none;
    outline: none;
    overflow: hidden;
    font: inherit;
    padding: 0 1px;
    margin: 0 -1px;
    contain: strict;
    -ms-user-select: text;
    -moz-user-select: text;
    -webkit-user-select: text;
    user-select: text;
    /*with \`pre-line\` chrome inserts &nbsp; instead of space*/
    white-space: pre!important;
}
.ace_text-input.ace_composition {
    background: transparent;
    color: inherit;
    z-index: 1000;
    opacity: 1;
}
.ace_composition_placeholder { color: transparent }
.ace_composition_marker { 
    border-bottom: 1px solid;
    position: absolute;
    border-radius: 0;
    margin-top: 1px;
}

[ace_nocontext=true] {
    transform: none!important;
    filter: none!important;
    clip-path: none!important;
    mask : none!important;
    contain: none!important;
    perspective: none!important;
    mix-blend-mode: initial!important;
    z-index: auto;
}

.ace_layer {
    z-index: 1;
    position: absolute;
    overflow: hidden;
    /* workaround for chrome bug https://github.com/ajaxorg/ace/issues/2312*/
    word-wrap: normal;
    white-space: pre;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    /* setting pointer-events: auto; on node under the mouse, which changes
        during scroll, will break mouse wheel scrolling in Safari */
    pointer-events: none;
}

.ace_gutter-layer {
    position: relative;
    width: auto;
    text-align: right;
    pointer-events: auto;
    height: 1000000px;
    contain: style size layout;
}

.ace_text-layer {
    font: inherit !important;
    position: absolute;
    height: 1000000px;
    width: 1000000px;
    contain: style size layout;
}

.ace_text-layer > .ace_line, .ace_text-layer > .ace_line_group {
    contain: style size layout;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

.ace_hidpi .ace_text-layer,
.ace_hidpi .ace_gutter-layer,
.ace_hidpi .ace_content,
.ace_hidpi .ace_gutter {
    contain: strict;
}
.ace_hidpi .ace_text-layer > .ace_line, 
.ace_hidpi .ace_text-layer > .ace_line_group {
    contain: strict;
}

.ace_cjk {
    display: inline-block;
    text-align: center;
}

.ace_cursor-layer {
    z-index: 4;
}

.ace_cursor {
    z-index: 4;
    position: absolute;
    box-sizing: border-box;
    border-left: 2px solid;
    /* workaround for smooth cursor repaintng whole screen in chrome */
    transform: translatez(0);
}

.ace_multiselect .ace_cursor {
    border-left-width: 1px;
}

.ace_slim-cursors .ace_cursor {
    border-left-width: 1px;
}

.ace_overwrite-cursors .ace_cursor {
    border-left-width: 0;
    border-bottom: 1px solid;
}

.ace_hidden-cursors .ace_cursor {
    opacity: 0.2;
}

.ace_hasPlaceholder .ace_hidden-cursors .ace_cursor {
    opacity: 0;
}

.ace_smooth-blinking .ace_cursor {
    transition: opacity 0.18s;
}

.ace_animate-blinking .ace_cursor {
    animation-duration: 1000ms;
    animation-timing-function: step-end;
    animation-name: blink-ace-animate;
    animation-iteration-count: infinite;
}

.ace_animate-blinking.ace_smooth-blinking .ace_cursor {
    animation-duration: 1000ms;
    animation-timing-function: ease-in-out;
    animation-name: blink-ace-animate-smooth;
}
    
@keyframes blink-ace-animate {
    from, to { opacity: 1; }
    60% { opacity: 0; }
}

@keyframes blink-ace-animate-smooth {
    from, to { opacity: 1; }
    45% { opacity: 1; }
    60% { opacity: 0; }
    85% { opacity: 0; }
}

.ace_marker-layer .ace_step, .ace_marker-layer .ace_stack {
    position: absolute;
    z-index: 3;
}

.ace_marker-layer .ace_selection {
    position: absolute;
    z-index: 5;
}

.ace_marker-layer .ace_bracket {
    position: absolute;
    z-index: 6;
}

.ace_marker-layer .ace_error_bracket {
    position: absolute;
    border-bottom: 1px solid #DE5555;
    border-radius: 0;
}

.ace_marker-layer .ace_active-line {
    position: absolute;
    z-index: 2;
}

.ace_marker-layer .ace_selected-word {
    position: absolute;
    z-index: 4;
    box-sizing: border-box;
}

.ace_line .ace_fold {
    box-sizing: border-box;

    display: inline-block;
    height: 11px;
    margin-top: -2px;
    vertical-align: middle;

    background-image:
        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),
        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi+P//fxgTAwPDBxDxD078RSX+YeEyDFMCIMAAI3INmXiwf2YAAAAASUVORK5CYII=");
    background-repeat: no-repeat, repeat-x;
    background-position: center center, top left;
    color: transparent;

    border: 1px solid black;
    border-radius: 2px;

    cursor: pointer;
    pointer-events: auto;
}

.ace_dark .ace_fold {
}

.ace_fold:hover{
    background-image:
        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),
        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi+P//fz4TAwPDZxDxD5X4i5fLMEwJgAADAEPVDbjNw87ZAAAAAElFTkSuQmCC");
}

.ace_tooltip {
    background-color: #f5f5f5;
    border: 1px solid gray;
    border-radius: 1px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    color: black;
    max-width: 100%;
    padding: 3px 4px;
    position: fixed;
    z-index: 999999;
    box-sizing: border-box;
    cursor: default;
    white-space: pre;
    word-wrap: break-word;
    line-height: normal;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    pointer-events: none;
}

.ace_tooltip.ace_dark {
    background-color: #636363;
    color: #fff;
}

.ace_tooltip:focus {
    outline: 1px solid #5E9ED6;
}

.ace_icon {
    display: inline-block;
    width: 18px;
    vertical-align: top;
}

.ace_icon_svg {
    display: inline-block;
    width: 12px;
    vertical-align: top;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-size: 12px;
    -webkit-mask-position: center;
}

.ace_folding-enabled > .ace_gutter-cell, .ace_folding-enabled > .ace_gutter-cell_svg-icons {
    padding-right: 13px;
}

.ace_fold-widget {
    box-sizing: border-box;

    margin: 0 -12px 0 1px;
    display: none;
    width: 11px;
    vertical-align: top;

    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42mWKsQ0AMAzC8ixLlrzQjzmBiEjp0A6WwBCSPgKAXoLkqSot7nN3yMwR7pZ32NzpKkVoDBUxKAAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    background-position: center;

    border-radius: 3px;
    
    border: 1px solid transparent;
    cursor: pointer;
}

.ace_folding-enabled .ace_fold-widget {
    display: inline-block;   
}

.ace_fold-widget.ace_end {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42m3HwQkAMAhD0YzsRchFKI7sAikeWkrxwScEB0nh5e7KTPWimZki4tYfVbX+MNl4pyZXejUO1QAAAABJRU5ErkJggg==");
}

.ace_fold-widget.ace_closed {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAGCAYAAAAG5SQMAAAAOUlEQVR42jXKwQkAMAgDwKwqKD4EwQ26sSOkVWjgIIHAzPiCgaqiqnJHZnKICBERHN194O5b9vbLuAVRL+l0YWnZAAAAAElFTkSuQmCCXA==");
}

.ace_fold-widget:hover {
    border: 1px solid rgba(0, 0, 0, 0.3);
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);
}

.ace_fold-widget:active {
    border: 1px solid rgba(0, 0, 0, 0.4);
    background-color: rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}
/**
 * Dark version for fold widgets
 */
.ace_dark .ace_fold-widget {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHklEQVQIW2P4//8/AzoGEQ7oGCaLLAhWiSwB146BAQCSTPYocqT0AAAAAElFTkSuQmCC");
}
.ace_dark .ace_fold-widget.ace_end {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAH0lEQVQIW2P4//8/AxQ7wNjIAjDMgC4AxjCVKBirIAAF0kz2rlhxpAAAAABJRU5ErkJggg==");
}
.ace_dark .ace_fold-widget.ace_closed {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAFCAYAAACAcVaiAAAAHElEQVQIW2P4//+/AxAzgDADlOOAznHAKgPWAwARji8UIDTfQQAAAABJRU5ErkJggg==");
}
.ace_dark .ace_fold-widget:hover {
    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
}
.ace_dark .ace_fold-widget:active {
    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);
}

.ace_inline_button {
    border: 1px solid lightgray;
    display: inline-block;
    margin: -1px 8px;
    padding: 0 5px;
    pointer-events: auto;
    cursor: pointer;
}
.ace_inline_button:hover {
    border-color: gray;
    background: rgba(200,200,200,0.2);
    display: inline-block;
    pointer-events: auto;
}

.ace_fold-widget.ace_invalid {
    background-color: #FFB4B4;
    border-color: #DE5555;
}

.ace_fade-fold-widgets .ace_fold-widget {
    transition: opacity 0.4s ease 0.05s;
    opacity: 0;
}

.ace_fade-fold-widgets:hover .ace_fold-widget {
    transition: opacity 0.05s ease 0.05s;
    opacity:1;
}

.ace_underline {
    text-decoration: underline;
}

.ace_bold {
    font-weight: bold;
}

.ace_nobold .ace_bold {
    font-weight: normal;
}

.ace_italic {
    font-style: italic;
}


.ace_error-marker {
    background-color: rgba(255, 0, 0,0.2);
    position: absolute;
    z-index: 9;
}

.ace_highlight-marker {
    background-color: rgba(255, 255, 0,0.2);
    position: absolute;
    z-index: 8;
}

.ace_mobile-menu {
    position: absolute;
    line-height: 1.5;
    border-radius: 4px;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    background: white;
    box-shadow: 1px 3px 2px grey;
    border: 1px solid #dcdcdc;
    color: black;
}
.ace_dark > .ace_mobile-menu {
    background: #333;
    color: #ccc;
    box-shadow: 1px 3px 2px grey;
    border: 1px solid #444;

}
.ace_mobile-button {
    padding: 2px;
    cursor: pointer;
    overflow: hidden;
}
.ace_mobile-button:hover {
    background-color: #eee;
    opacity:1;
}
.ace_mobile-button:active {
    background-color: #ddd;
}

.ace_placeholder {
    font-family: arial;
    transform: scale(0.9);
    transform-origin: left;
    white-space: pre;
    opacity: 0.7;
    margin: 0 10px;
}

.ace_ghost_text {
    opacity: 0.5;
    font-style: italic;
    white-space: pre;
}`});ace.define("ace/layer/decorators",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter"],function(x,k,H){"use strict";var R=x("../lib/dom"),M=x("../lib/oop"),S=x("../lib/event_emitter").EventEmitter,a=function(){function l(t,n){this.canvas=R.createElement("canvas"),this.renderer=n,this.pixelRatio=1,this.maxHeight=n.layerConfig.maxHeight,this.lineHeight=n.layerConfig.lineHeight,this.canvasHeight=t.parent.scrollHeight,this.heightRatio=this.canvasHeight/this.maxHeight,this.canvasWidth=t.width,this.minDecorationHeight=2*this.pixelRatio|0,this.halfMinDecorationHeight=this.minDecorationHeight/2|0,this.canvas.width=this.canvasWidth,this.canvas.height=this.canvasHeight,this.canvas.style.top="0px",this.canvas.style.right="0px",this.canvas.style.zIndex="7px",this.canvas.style.position="absolute",this.colors={},this.colors.dark={error:"rgba(255, 18, 18, 1)",warning:"rgba(18, 136, 18, 1)",info:"rgba(18, 18, 136, 1)"},this.colors.light={error:"rgb(255,51,51)",warning:"rgb(32,133,72)",info:"rgb(35,68,138)"},t.element.appendChild(this.canvas)}return l.prototype.$updateDecorators=function(t){var n=this.renderer.theme.isDark===!0?this.colors.dark:this.colors.light;if(t){this.maxHeight=t.maxHeight,this.lineHeight=t.lineHeight,this.canvasHeight=t.height;var r=(t.lastRow+1)*this.lineHeight;r<this.canvasHeight?this.heightRatio=1:this.heightRatio=this.canvasHeight/this.maxHeight}var e=this.canvas.getContext("2d");function i(u,A){return u.priority<A.priority?-1:u.priority>A.priority?1:0}var o=this.renderer.session.$annotations;if(e.clearRect(0,0,this.canvas.width,this.canvas.height),o){var s={info:1,warning:2,error:3};o.forEach(function(u){u.priority=s[u.type]||null}),o=o.sort(i);for(var c=this.renderer.session.$foldData,g=0;g<o.length;g++){var w=o[g].row,p=this.compensateFoldRows(w,c),b=Math.round((w-p)*this.lineHeight*this.heightRatio),y=Math.round((w-p)*this.lineHeight*this.heightRatio),d=Math.round(((w-p)*this.lineHeight+this.lineHeight)*this.heightRatio),v=d-y;if(v<this.minDecorationHeight){var $=(y+d)/2|0;$<this.halfMinDecorationHeight?$=this.halfMinDecorationHeight:$+this.halfMinDecorationHeight>this.canvasHeight&&($=this.canvasHeight-this.halfMinDecorationHeight),y=Math.round($-this.halfMinDecorationHeight),d=Math.round($+this.halfMinDecorationHeight)}e.fillStyle=n[o[g].type]||null,e.fillRect(0,b,this.canvasWidth,d-y)}}var f=this.renderer.session.selection.getCursor();if(f){var p=this.compensateFoldRows(f.row,c),b=Math.round((f.row-p)*this.lineHeight*this.heightRatio);e.fillStyle="rgba(0, 0, 0, 0.5)",e.fillRect(0,b,this.canvasWidth,2)}},l.prototype.compensateFoldRows=function(t,n){var r=0;if(n&&n.length>0)for(var e=0;e<n.length;e++)t>n[e].start.row&&t<n[e].end.row?r+=t-n[e].start.row:t>=n[e].end.row&&(r+=n[e].end.row-n[e].start.row);return r},l}();M.implement(a.prototype,S),k.Decorator=a});ace.define("ace/virtual_renderer",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/config","ace/layer/gutter","ace/layer/marker","ace/layer/text","ace/layer/cursor","ace/scrollbar","ace/scrollbar","ace/scrollbar_custom","ace/scrollbar_custom","ace/renderloop","ace/layer/font_metrics","ace/lib/event_emitter","ace/css/editor-css","ace/layer/decorators","ace/lib/useragent"],function(x,k,H){"use strict";var R=x("./lib/oop"),M=x("./lib/dom"),S=x("./lib/lang"),a=x("./config"),l=x("./layer/gutter").Gutter,t=x("./layer/marker").Marker,n=x("./layer/text").Text,r=x("./layer/cursor").Cursor,e=x("./scrollbar").HScrollBar,i=x("./scrollbar").VScrollBar,o=x("./scrollbar_custom").HScrollBar,s=x("./scrollbar_custom").VScrollBar,c=x("./renderloop").RenderLoop,g=x("./layer/font_metrics").FontMetrics,w=x("./lib/event_emitter").EventEmitter,p=x("./css/editor-css"),b=x("./layer/decorators").Decorator,y=x("./lib/useragent");M.importCssString(p,"ace_editor.css",!1);var d=function(){function v($,f){var u=this;this.container=$||M.createElement("div"),M.addCssClass(this.container,"ace_editor"),M.HI_DPI&&M.addCssClass(this.container,"ace_hidpi"),this.setTheme(f),a.get("useStrictCSP")==null&&a.set("useStrictCSP",!1),this.$gutter=M.createElement("div"),this.$gutter.className="ace_gutter",this.container.appendChild(this.$gutter),this.$gutter.setAttribute("aria-hidden",!0),this.scroller=M.createElement("div"),this.scroller.className="ace_scroller",this.container.appendChild(this.scroller),this.content=M.createElement("div"),this.content.className="ace_content",this.scroller.appendChild(this.content),this.$gutterLayer=new l(this.$gutter),this.$gutterLayer.on("changeGutterWidth",this.onGutterResize.bind(this)),this.$markerBack=new t(this.content);var A=this.$textLayer=new n(this.content);this.canvas=A.element,this.$markerFront=new t(this.content),this.$cursorLayer=new r(this.content),this.$horizScroll=!1,this.$vScroll=!1,this.scrollBar=this.scrollBarV=new i(this.container,this),this.scrollBarH=new e(this.container,this),this.scrollBarV.on("scroll",function(m){u.$scrollAnimation||u.session.setScrollTop(m.data-u.scrollMargin.top)}),this.scrollBarH.on("scroll",function(m){u.$scrollAnimation||u.session.setScrollLeft(m.data-u.scrollMargin.left)}),this.scrollTop=0,this.scrollLeft=0,this.cursorPos={row:0,column:0},this.$fontMetrics=new g(this.container),this.$textLayer.$setFontMetrics(this.$fontMetrics),this.$textLayer.on("changeCharacterSize",function(m){u.updateCharacterSize(),u.onResize(!0,u.gutterWidth,u.$size.width,u.$size.height),u._signal("changeCharacterSize",m)}),this.$size={width:0,height:0,scrollerHeight:0,scrollerWidth:0,$dirty:!0},this.layerConfig={width:1,padding:0,firstRow:0,firstRowScreen:0,lastRow:0,lineHeight:0,characterWidth:0,minHeight:1,maxHeight:1,offset:0,height:1,gutterOffset:1},this.scrollMargin={left:0,right:0,top:0,bottom:0,v:0,h:0},this.margin={left:0,right:0,top:0,bottom:0,v:0,h:0},this.$keepTextAreaAtCursor=!y.isIOS,this.$loop=new c(this.$renderChanges.bind(this),this.container.ownerDocument.defaultView),this.$loop.schedule(this.CHANGE_FULL),this.updateCharacterSize(),this.setPadding(4),this.$addResizeObserver(),a.resetOptions(this),a._signal("renderer",this)}return v.prototype.updateCharacterSize=function(){this.$textLayer.allowBoldFonts!=this.$allowBoldFonts&&(this.$allowBoldFonts=this.$textLayer.allowBoldFonts,this.setStyle("ace_nobold",!this.$allowBoldFonts)),this.layerConfig.characterWidth=this.characterWidth=this.$textLayer.getCharacterWidth(),this.layerConfig.lineHeight=this.lineHeight=this.$textLayer.getLineHeight(),this.$updatePrintMargin(),M.setStyle(this.scroller.style,"line-height",this.lineHeight+"px")},v.prototype.setSession=function($){this.session&&this.session.doc.off("changeNewLineMode",this.onChangeNewLineMode),this.session=$,$&&this.scrollMargin.top&&$.getScrollTop()<=0&&$.setScrollTop(-this.scrollMargin.top),this.$cursorLayer.setSession($),this.$markerBack.setSession($),this.$markerFront.setSession($),this.$gutterLayer.setSession($),this.$textLayer.setSession($),$&&(this.$loop.schedule(this.CHANGE_FULL),this.session.$setFontMetrics(this.$fontMetrics),this.scrollBarH.scrollLeft=this.scrollBarV.scrollTop=null,this.onChangeNewLineMode=this.onChangeNewLineMode.bind(this),this.onChangeNewLineMode(),this.session.doc.on("changeNewLineMode",this.onChangeNewLineMode))},v.prototype.updateLines=function($,f,u){if(f===void 0&&(f=1/0),this.$changedLines?(this.$changedLines.firstRow>$&&(this.$changedLines.firstRow=$),this.$changedLines.lastRow<f&&(this.$changedLines.lastRow=f)):this.$changedLines={firstRow:$,lastRow:f},this.$changedLines.lastRow<this.layerConfig.firstRow)if(u)this.$changedLines.lastRow=this.layerConfig.lastRow;else return;this.$changedLines.firstRow>this.layerConfig.lastRow||this.$loop.schedule(this.CHANGE_LINES)},v.prototype.onChangeNewLineMode=function(){this.$loop.schedule(this.CHANGE_TEXT),this.$textLayer.$updateEolChar(),this.session.$bidiHandler.setEolChar(this.$textLayer.EOL_CHAR)},v.prototype.onChangeTabSize=function(){this.$loop.schedule(this.CHANGE_TEXT|this.CHANGE_MARKER),this.$textLayer.onChangeTabSize()},v.prototype.updateText=function(){this.$loop.schedule(this.CHANGE_TEXT)},v.prototype.updateFull=function($){$?this.$renderChanges(this.CHANGE_FULL,!0):this.$loop.schedule(this.CHANGE_FULL)},v.prototype.updateFontSize=function(){this.$textLayer.checkForSizeChanges()},v.prototype.$updateSizeAsync=function(){this.$loop.pending?this.$size.$dirty=!0:this.onResize()},v.prototype.onResize=function($,f,u,A){if(!(this.resizing>2)){this.resizing>0?this.resizing++:this.resizing=$?1:0;var m=this.container;A||(A=m.clientHeight||m.scrollHeight),u||(u=m.clientWidth||m.scrollWidth);var h=this.$updateCachedSize($,f,u,A);if(this.$resizeTimer&&this.$resizeTimer.cancel(),!this.$size.scrollerHeight||!u&&!A)return this.resizing=0;$&&(this.$gutterLayer.$padding=null),$?this.$renderChanges(h|this.$changes,!0):this.$loop.schedule(h|this.$changes),this.resizing&&(this.resizing=0),this.scrollBarH.scrollLeft=this.scrollBarV.scrollTop=null,this.$customScrollbar&&this.$updateCustomScrollbar(!0)}},v.prototype.$updateCachedSize=function($,f,u,A){A-=this.$extraHeight||0;var m=0,h=this.$size,C={width:h.width,height:h.height,scrollerHeight:h.scrollerHeight,scrollerWidth:h.scrollerWidth};if(A&&($||h.height!=A)&&(h.height=A,m|=this.CHANGE_SIZE,h.scrollerHeight=h.height,this.$horizScroll&&(h.scrollerHeight-=this.scrollBarH.getHeight()),this.scrollBarV.setHeight(h.scrollerHeight),this.scrollBarV.element.style.bottom=this.scrollBarH.getHeight()+"px",m=m|this.CHANGE_SCROLL),u&&($||h.width!=u)){m|=this.CHANGE_SIZE,h.width=u,f==null&&(f=this.$showGutter?this.$gutter.offsetWidth:0),this.gutterWidth=f,M.setStyle(this.scrollBarH.element.style,"left",f+"px"),M.setStyle(this.scroller.style,"left",f+this.margin.left+"px"),h.scrollerWidth=Math.max(0,u-f-this.scrollBarV.getWidth()-this.margin.h),M.setStyle(this.$gutter.style,"left",this.margin.left+"px");var L=this.scrollBarV.getWidth()+"px";M.setStyle(this.scrollBarH.element.style,"right",L),M.setStyle(this.scroller.style,"right",L),M.setStyle(this.scroller.style,"bottom",this.scrollBarH.getHeight()),this.scrollBarH.setWidth(h.scrollerWidth),(this.session&&this.session.getUseWrapMode()&&this.adjustWrapLimit()||$)&&(m|=this.CHANGE_FULL)}return h.$dirty=!u||!A,m&&this._signal("resize",C),m},v.prototype.onGutterResize=function($){var f=this.$showGutter?$:0;f!=this.gutterWidth&&(this.$changes|=this.$updateCachedSize(!0,f,this.$size.width,this.$size.height)),this.session.getUseWrapMode()&&this.adjustWrapLimit()?this.$loop.schedule(this.CHANGE_FULL):this.$size.$dirty?this.$loop.schedule(this.CHANGE_FULL):this.$computeLayerConfig()},v.prototype.adjustWrapLimit=function(){var $=this.$size.scrollerWidth-this.$padding*2,f=Math.floor($/this.characterWidth);return this.session.adjustWrapLimit(f,this.$showPrintMargin&&this.$printMarginColumn)},v.prototype.setAnimatedScroll=function($){this.setOption("animatedScroll",$)},v.prototype.getAnimatedScroll=function(){return this.$animatedScroll},v.prototype.setShowInvisibles=function($){this.setOption("showInvisibles",$),this.session.$bidiHandler.setShowInvisibles($)},v.prototype.getShowInvisibles=function(){return this.getOption("showInvisibles")},v.prototype.getDisplayIndentGuides=function(){return this.getOption("displayIndentGuides")},v.prototype.setDisplayIndentGuides=function($){this.setOption("displayIndentGuides",$)},v.prototype.getHighlightIndentGuides=function(){return this.getOption("highlightIndentGuides")},v.prototype.setHighlightIndentGuides=function($){this.setOption("highlightIndentGuides",$)},v.prototype.setShowPrintMargin=function($){this.setOption("showPrintMargin",$)},v.prototype.getShowPrintMargin=function(){return this.getOption("showPrintMargin")},v.prototype.setPrintMarginColumn=function($){this.setOption("printMarginColumn",$)},v.prototype.getPrintMarginColumn=function(){return this.getOption("printMarginColumn")},v.prototype.getShowGutter=function(){return this.getOption("showGutter")},v.prototype.setShowGutter=function($){return this.setOption("showGutter",$)},v.prototype.getFadeFoldWidgets=function(){return this.getOption("fadeFoldWidgets")},v.prototype.setFadeFoldWidgets=function($){this.setOption("fadeFoldWidgets",$)},v.prototype.setHighlightGutterLine=function($){this.setOption("highlightGutterLine",$)},v.prototype.getHighlightGutterLine=function(){return this.getOption("highlightGutterLine")},v.prototype.$updatePrintMargin=function(){if(!(!this.$showPrintMargin&&!this.$printMarginEl)){if(!this.$printMarginEl){var $=M.createElement("div");$.className="ace_layer ace_print-margin-layer",this.$printMarginEl=M.createElement("div"),this.$printMarginEl.className="ace_print-margin",$.appendChild(this.$printMarginEl),this.content.insertBefore($,this.content.firstChild)}var f=this.$printMarginEl.style;f.left=Math.round(this.characterWidth*this.$printMarginColumn+this.$padding)+"px",f.visibility=this.$showPrintMargin?"visible":"hidden",this.session&&this.session.$wrap==-1&&this.adjustWrapLimit()}},v.prototype.getContainerElement=function(){return this.container},v.prototype.getMouseEventTarget=function(){return this.scroller},v.prototype.getTextAreaContainer=function(){return this.container},v.prototype.$moveTextAreaToCursor=function(){if(!this.$isMousePressed){var $=this.textarea.style,f=this.$composition;if(!this.$keepTextAreaAtCursor&&!f){M.translate(this.textarea,-100,0);return}var u=this.$cursorLayer.$pixelPos;if(u){f&&f.markerRange&&(u=this.$cursorLayer.getPixelPosition(f.markerRange.start,!0));var A=this.layerConfig,m=u.top,h=u.left;m-=A.offset;var C=f&&f.useTextareaForIME||y.isMobile?this.lineHeight:1;if(m<0||m>A.height-C){M.translate(this.textarea,0,0);return}var L=1,T=this.$size.height-C;if(!f)m+=this.lineHeight;else if(f.useTextareaForIME){var E=this.textarea.value;L=this.characterWidth*this.session.$getStringScreenWidth(E)[0]}else m+=this.lineHeight+2;h-=this.scrollLeft,h>this.$size.scrollerWidth-L&&(h=this.$size.scrollerWidth-L),h+=this.gutterWidth+this.margin.left,M.setStyle($,"height",C+"px"),M.setStyle($,"width",L+"px"),M.translate(this.textarea,Math.min(h,this.$size.scrollerWidth-L),Math.min(m,T))}}},v.prototype.getFirstVisibleRow=function(){return this.layerConfig.firstRow},v.prototype.getFirstFullyVisibleRow=function(){return this.layerConfig.firstRow+(this.layerConfig.offset===0?0:1)},v.prototype.getLastFullyVisibleRow=function(){var $=this.layerConfig,f=$.lastRow,u=this.session.documentToScreenRow(f,0)*$.lineHeight;return u-this.session.getScrollTop()>$.height-$.lineHeight?f-1:f},v.prototype.getLastVisibleRow=function(){return this.layerConfig.lastRow},v.prototype.setPadding=function($){this.$padding=$,this.$textLayer.setPadding($),this.$cursorLayer.setPadding($),this.$markerFront.setPadding($),this.$markerBack.setPadding($),this.$loop.schedule(this.CHANGE_FULL),this.$updatePrintMargin()},v.prototype.setScrollMargin=function($,f,u,A){var m=this.scrollMargin;m.top=$|0,m.bottom=f|0,m.right=A|0,m.left=u|0,m.v=m.top+m.bottom,m.h=m.left+m.right,m.top&&this.scrollTop<=0&&this.session&&this.session.setScrollTop(-m.top),this.updateFull()},v.prototype.setMargin=function($,f,u,A){var m=this.margin;m.top=$|0,m.bottom=f|0,m.right=A|0,m.left=u|0,m.v=m.top+m.bottom,m.h=m.left+m.right,this.$updateCachedSize(!0,this.gutterWidth,this.$size.width,this.$size.height),this.updateFull()},v.prototype.getHScrollBarAlwaysVisible=function(){return this.$hScrollBarAlwaysVisible},v.prototype.setHScrollBarAlwaysVisible=function($){this.setOption("hScrollBarAlwaysVisible",$)},v.prototype.getVScrollBarAlwaysVisible=function(){return this.$vScrollBarAlwaysVisible},v.prototype.setVScrollBarAlwaysVisible=function($){this.setOption("vScrollBarAlwaysVisible",$)},v.prototype.$updateScrollBarV=function(){var $=this.layerConfig.maxHeight,f=this.$size.scrollerHeight;!this.$maxLines&&this.$scrollPastEnd&&($-=(f-this.lineHeight)*this.$scrollPastEnd,this.scrollTop>$-f&&($=this.scrollTop+f,this.scrollBarV.scrollTop=null)),this.scrollBarV.setScrollHeight($+this.scrollMargin.v),this.scrollBarV.setScrollTop(this.scrollTop+this.scrollMargin.top)},v.prototype.$updateScrollBarH=function(){this.scrollBarH.setScrollWidth(this.layerConfig.width+2*this.$padding+this.scrollMargin.h),this.scrollBarH.setScrollLeft(this.scrollLeft+this.scrollMargin.left)},v.prototype.freeze=function(){this.$frozen=!0},v.prototype.unfreeze=function(){this.$frozen=!1},v.prototype.$renderChanges=function($,f){if(this.$changes&&($|=this.$changes,this.$changes=0),!this.session||!this.container.offsetWidth||this.$frozen||!$&&!f){this.$changes|=$;return}if(this.$size.$dirty)return this.$changes|=$,this.onResize(!0);this.lineHeight||this.$textLayer.checkForSizeChanges(),this._signal("beforeRender",$),this.session&&this.session.$bidiHandler&&this.session.$bidiHandler.updateCharacterWidths(this.$fontMetrics);var u=this.layerConfig;if($&this.CHANGE_FULL||$&this.CHANGE_SIZE||$&this.CHANGE_TEXT||$&this.CHANGE_LINES||$&this.CHANGE_SCROLL||$&this.CHANGE_H_SCROLL){if($|=this.$computeLayerConfig()|this.$loop.clear(),u.firstRow!=this.layerConfig.firstRow&&u.firstRowScreen==this.layerConfig.firstRowScreen){var A=this.scrollTop+(u.firstRow-Math.max(this.layerConfig.firstRow,0))*this.lineHeight;A>0&&(this.scrollTop=A,$=$|this.CHANGE_SCROLL,$|=this.$computeLayerConfig()|this.$loop.clear())}u=this.layerConfig,this.$updateScrollBarV(),$&this.CHANGE_H_SCROLL&&this.$updateScrollBarH(),M.translate(this.content,-this.scrollLeft,-u.offset);var m=u.width+2*this.$padding+"px",h=u.minHeight+"px";M.setStyle(this.content.style,"width",m),M.setStyle(this.content.style,"height",h)}if($&this.CHANGE_H_SCROLL&&(M.translate(this.content,-this.scrollLeft,-u.offset),this.scroller.className=this.scrollLeft<=0?"ace_scroller ":"ace_scroller ace_scroll-left ",this.enableKeyboardAccessibility&&(this.scroller.className+=this.keyboardFocusClassName)),$&this.CHANGE_FULL){this.$changedLines=null,this.$textLayer.update(u),this.$showGutter&&this.$gutterLayer.update(u),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u),this.$markerBack.update(u),this.$markerFront.update(u),this.$cursorLayer.update(u),this.$moveTextAreaToCursor(),this._signal("afterRender",$);return}if($&this.CHANGE_SCROLL){this.$changedLines=null,$&this.CHANGE_TEXT||$&this.CHANGE_LINES?this.$textLayer.update(u):this.$textLayer.scrollLines(u),this.$showGutter&&($&this.CHANGE_GUTTER||$&this.CHANGE_LINES?this.$gutterLayer.update(u):this.$gutterLayer.scrollLines(u)),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u),this.$markerBack.update(u),this.$markerFront.update(u),this.$cursorLayer.update(u),this.$moveTextAreaToCursor(),this._signal("afterRender",$);return}$&this.CHANGE_TEXT?(this.$changedLines=null,this.$textLayer.update(u),this.$showGutter&&this.$gutterLayer.update(u),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u)):$&this.CHANGE_LINES?((this.$updateLines()||$&this.CHANGE_GUTTER&&this.$showGutter)&&this.$gutterLayer.update(u),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u)):$&this.CHANGE_TEXT||$&this.CHANGE_GUTTER?(this.$showGutter&&this.$gutterLayer.update(u),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u)):$&this.CHANGE_CURSOR&&(this.$highlightGutterLine&&this.$gutterLayer.updateLineHighlight(u),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(u)),$&this.CHANGE_CURSOR&&(this.$cursorLayer.update(u),this.$moveTextAreaToCursor()),$&(this.CHANGE_MARKER|this.CHANGE_MARKER_FRONT)&&this.$markerFront.update(u),$&(this.CHANGE_MARKER|this.CHANGE_MARKER_BACK)&&this.$markerBack.update(u),this._signal("afterRender",$)},v.prototype.$autosize=function(){var $=this.session.getScreenLength()*this.lineHeight,f=this.$maxLines*this.lineHeight,u=Math.min(f,Math.max((this.$minLines||1)*this.lineHeight,$))+this.scrollMargin.v+(this.$extraHeight||0);this.$horizScroll&&(u+=this.scrollBarH.getHeight()),this.$maxPixelHeight&&u>this.$maxPixelHeight&&(u=this.$maxPixelHeight);var A=u<=2*this.lineHeight,m=!A&&$>f;if(u!=this.desiredHeight||this.$size.height!=this.desiredHeight||m!=this.$vScroll){m!=this.$vScroll&&(this.$vScroll=m,this.scrollBarV.setVisible(m));var h=this.container.clientWidth;this.container.style.height=u+"px",this.$updateCachedSize(!0,this.$gutterWidth,h,u),this.desiredHeight=u,this._signal("autosize")}},v.prototype.$computeLayerConfig=function(){var $=this.session,f=this.$size,u=f.height<=2*this.lineHeight,A=this.session.getScreenLength(),m=A*this.lineHeight,h=this.$getLongestLine(),C=!u&&(this.$hScrollBarAlwaysVisible||f.scrollerWidth-h-2*this.$padding<0),L=this.$horizScroll!==C;L&&(this.$horizScroll=C,this.scrollBarH.setVisible(C));var T=this.$vScroll;this.$maxLines&&this.lineHeight>1&&this.$autosize();var E=f.scrollerHeight+this.lineHeight,_=!this.$maxLines&&this.$scrollPastEnd?(f.scrollerHeight-this.lineHeight)*this.$scrollPastEnd:0;m+=_;var O=this.scrollMargin;this.session.setScrollTop(Math.max(-O.top,Math.min(this.scrollTop,m-f.scrollerHeight+O.bottom))),this.session.setScrollLeft(Math.max(-O.left,Math.min(this.scrollLeft,h+2*this.$padding-f.scrollerWidth+O.right)));var I=!u&&(this.$vScrollBarAlwaysVisible||f.scrollerHeight-m+_<0||this.scrollTop>O.top),W=T!==I;W&&(this.$vScroll=I,this.scrollBarV.setVisible(I));var F=this.scrollTop%this.lineHeight,D=Math.ceil(E/this.lineHeight)-1,B=Math.max(0,Math.round((this.scrollTop-F)/this.lineHeight)),U=B+D,P,G,z=this.lineHeight;B=$.screenToDocumentRow(B,0);var K=$.getFoldLine(B);K&&(B=K.start.row),P=$.documentToScreenRow(B,0),G=$.getRowLength(B)*z,U=Math.min($.screenToDocumentRow(U,0),$.getLength()-1),E=f.scrollerHeight+$.getRowLength(U)*z+G,F=this.scrollTop-P*z;var Y=0;return(this.layerConfig.width!=h||L)&&(Y=this.CHANGE_H_SCROLL),(L||W)&&(Y|=this.$updateCachedSize(!0,this.gutterWidth,f.width,f.height),this._signal("scrollbarVisibilityChanged"),W&&(h=this.$getLongestLine())),this.layerConfig={width:h,padding:this.$padding,firstRow:B,firstRowScreen:P,lastRow:U,lineHeight:z,characterWidth:this.characterWidth,minHeight:E,maxHeight:m,offset:F,gutterOffset:z?Math.max(0,Math.ceil((F+f.height-f.scrollerHeight)/z)):0,height:this.$size.scrollerHeight},this.session.$bidiHandler&&this.session.$bidiHandler.setContentWidth(h-this.$padding),Y},v.prototype.$updateLines=function(){if(this.$changedLines){var $=this.$changedLines.firstRow,f=this.$changedLines.lastRow;this.$changedLines=null;var u=this.layerConfig;if(!($>u.lastRow+1)&&!(f<u.firstRow)){if(f===1/0){this.$showGutter&&this.$gutterLayer.update(u),this.$textLayer.update(u);return}return this.$textLayer.updateLines(u,$,f),!0}}},v.prototype.$getLongestLine=function(){var $=this.session.getScreenWidth();return this.showInvisibles&&!this.session.$useWrapMode&&($+=1),this.$textLayer&&$>this.$textLayer.MAX_LINE_LENGTH&&($=this.$textLayer.MAX_LINE_LENGTH+30),Math.max(this.$size.scrollerWidth-2*this.$padding,Math.round($*this.characterWidth))},v.prototype.updateFrontMarkers=function(){this.$markerFront.setMarkers(this.session.getMarkers(!0)),this.$loop.schedule(this.CHANGE_MARKER_FRONT)},v.prototype.updateBackMarkers=function(){this.$markerBack.setMarkers(this.session.getMarkers()),this.$loop.schedule(this.CHANGE_MARKER_BACK)},v.prototype.addGutterDecoration=function($,f){this.$gutterLayer.addGutterDecoration($,f)},v.prototype.removeGutterDecoration=function($,f){this.$gutterLayer.removeGutterDecoration($,f)},v.prototype.updateBreakpoints=function($){this.$loop.schedule(this.CHANGE_GUTTER)},v.prototype.setAnnotations=function($){this.$gutterLayer.setAnnotations($),this.$loop.schedule(this.CHANGE_GUTTER)},v.prototype.updateCursor=function(){this.$loop.schedule(this.CHANGE_CURSOR)},v.prototype.hideCursor=function(){this.$cursorLayer.hideCursor()},v.prototype.showCursor=function(){this.$cursorLayer.showCursor()},v.prototype.scrollSelectionIntoView=function($,f,u){this.scrollCursorIntoView($,u),this.scrollCursorIntoView(f,u)},v.prototype.scrollCursorIntoView=function($,f,u){if(this.$size.scrollerHeight!==0){var A=this.$cursorLayer.getPixelPosition($),m=A.left,h=A.top,C=u&&u.top||0,L=u&&u.bottom||0;this.$scrollAnimation&&(this.$stopAnimation=!0);var T=this.$scrollAnimation?this.session.getScrollTop():this.scrollTop;T+C>h?(f&&T+C>h+this.lineHeight&&(h-=f*this.$size.scrollerHeight),h===0&&(h=-this.scrollMargin.top),this.session.setScrollTop(h)):T+this.$size.scrollerHeight-L<h+this.lineHeight&&(f&&T+this.$size.scrollerHeight-L<h-this.lineHeight&&(h+=f*this.$size.scrollerHeight),this.session.setScrollTop(h+this.lineHeight+L-this.$size.scrollerHeight));var E=this.scrollLeft,_=2*this.layerConfig.characterWidth;m-_<E?(m-=_,m<this.$padding+_&&(m=-this.scrollMargin.left),this.session.setScrollLeft(m)):(m+=_,E+this.$size.scrollerWidth<m+this.characterWidth?this.session.setScrollLeft(Math.round(m+this.characterWidth-this.$size.scrollerWidth)):E<=this.$padding&&m-E<this.characterWidth&&this.session.setScrollLeft(0))}},v.prototype.getScrollTop=function(){return this.session.getScrollTop()},v.prototype.getScrollLeft=function(){return this.session.getScrollLeft()},v.prototype.getScrollTopRow=function(){return this.scrollTop/this.lineHeight},v.prototype.getScrollBottomRow=function(){return Math.max(0,Math.floor((this.scrollTop+this.$size.scrollerHeight)/this.lineHeight)-1)},v.prototype.scrollToRow=function($){this.session.setScrollTop($*this.lineHeight)},v.prototype.alignCursor=function($,f){typeof $=="number"&&($={row:$,column:0});var u=this.$cursorLayer.getPixelPosition($),A=this.$size.scrollerHeight-this.lineHeight,m=u.top-A*(f||0);return this.session.setScrollTop(m),m},v.prototype.$calcSteps=function($,f){var u=0,A=this.STEPS,m=[],h=function(C,L,T){return T*(Math.pow(C-1,3)+1)+L};for(u=0;u<A;++u)m.push(h(u/this.STEPS,$,f-$));return m},v.prototype.scrollToLine=function($,f,u,A){var m=this.$cursorLayer.getPixelPosition({row:$,column:0}),h=m.top;f&&(h-=this.$size.scrollerHeight/2);var C=this.scrollTop;this.session.setScrollTop(h),u!==!1&&this.animateScrolling(C,A)},v.prototype.animateScrolling=function($,f){var u=this.scrollTop;if(!this.$animatedScroll)return;var A=this;if($==u)return;if(this.$scrollAnimation){var m=this.$scrollAnimation.steps;if(m.length&&($=m[0],$==u))return}var h=A.$calcSteps($,u);this.$scrollAnimation={from:$,to:u,steps:h},clearInterval(this.$timer),A.session.setScrollTop(h.shift()),A.session.$scrollTop=u;function C(){A.$timer=clearInterval(A.$timer),A.$scrollAnimation=null,A.$stopAnimation=!1,f&&f()}this.$timer=setInterval(function(){if(A.$stopAnimation){C();return}if(!A.session)return clearInterval(A.$timer);h.length?(A.session.setScrollTop(h.shift()),A.session.$scrollTop=u):u!=null?(A.session.$scrollTop=-1,A.session.setScrollTop(u),u=null):C()},10)},v.prototype.scrollToY=function($){this.scrollTop!==$&&(this.$loop.schedule(this.CHANGE_SCROLL),this.scrollTop=$)},v.prototype.scrollToX=function($){this.scrollLeft!==$&&(this.scrollLeft=$),this.$loop.schedule(this.CHANGE_H_SCROLL)},v.prototype.scrollTo=function($,f){this.session.setScrollTop(f),this.session.setScrollLeft($)},v.prototype.scrollBy=function($,f){f&&this.session.setScrollTop(this.session.getScrollTop()+f),$&&this.session.setScrollLeft(this.session.getScrollLeft()+$)},v.prototype.isScrollableBy=function($,f){if(f<0&&this.session.getScrollTop()>=1-this.scrollMargin.top||f>0&&this.session.getScrollTop()+this.$size.scrollerHeight-this.layerConfig.maxHeight<-1+this.scrollMargin.bottom||$<0&&this.session.getScrollLeft()>=1-this.scrollMargin.left||$>0&&this.session.getScrollLeft()+this.$size.scrollerWidth-this.layerConfig.width<-1+this.scrollMargin.right)return!0},v.prototype.pixelToScreenCoordinates=function($,f){var u;if(this.$hasCssTransforms){u={top:0,left:0};var A=this.$fontMetrics.transformCoordinates([$,f]);$=A[1]-this.gutterWidth-this.margin.left,f=A[0]}else u=this.scroller.getBoundingClientRect();var m=$+this.scrollLeft-u.left-this.$padding,h=m/this.characterWidth,C=Math.floor((f+this.scrollTop-u.top)/this.lineHeight),L=this.$blockCursor?Math.floor(h):Math.round(h);return{row:C,column:L,side:h-L>0?1:-1,offsetX:m}},v.prototype.screenToTextCoordinates=function($,f){var u;if(this.$hasCssTransforms){u={top:0,left:0};var A=this.$fontMetrics.transformCoordinates([$,f]);$=A[1]-this.gutterWidth-this.margin.left,f=A[0]}else u=this.scroller.getBoundingClientRect();var m=$+this.scrollLeft-u.left-this.$padding,h=m/this.characterWidth,C=this.$blockCursor?Math.floor(h):Math.round(h),L=Math.floor((f+this.scrollTop-u.top)/this.lineHeight);return this.session.screenToDocumentPosition(L,Math.max(C,0),m)},v.prototype.textToScreenCoordinates=function($,f){var u=this.scroller.getBoundingClientRect(),A=this.session.documentToScreenPosition($,f),m=this.$padding+(this.session.$bidiHandler.isBidiRow(A.row,$)?this.session.$bidiHandler.getPosLeft(A.column):Math.round(A.column*this.characterWidth)),h=A.row*this.lineHeight;return{pageX:u.left+m-this.scrollLeft,pageY:u.top+h-this.scrollTop}},v.prototype.visualizeFocus=function(){M.addCssClass(this.container,"ace_focus")},v.prototype.visualizeBlur=function(){M.removeCssClass(this.container,"ace_focus")},v.prototype.showComposition=function($){this.$composition=$,$.cssText||($.cssText=this.textarea.style.cssText),$.useTextareaForIME==null&&($.useTextareaForIME=this.$useTextareaForIME),this.$useTextareaForIME?(M.addCssClass(this.textarea,"ace_composition"),this.textarea.style.cssText="",this.$moveTextAreaToCursor(),this.$cursorLayer.element.style.display="none"):$.markerId=this.session.addMarker($.markerRange,"ace_composition_marker","text")},v.prototype.setCompositionText=function($){var f=this.session.selection.cursor;this.addToken($,"composition_placeholder",f.row,f.column),this.$moveTextAreaToCursor()},v.prototype.hideComposition=function(){if(this.$composition){this.$composition.markerId&&this.session.removeMarker(this.$composition.markerId),M.removeCssClass(this.textarea,"ace_composition"),this.textarea.style.cssText=this.$composition.cssText;var $=this.session.selection.cursor;this.removeExtraToken($.row,$.column),this.$composition=null,this.$cursorLayer.element.style.display=""}},v.prototype.setGhostText=function($,f){var u=this.session.selection.cursor,A=f||{row:u.row,column:u.column};this.removeGhostText();var m=$.split(`
`);this.addToken(m[0],"ghost_text",A.row,A.column),this.$ghostText={text:$,position:{row:A.row,column:A.column}},m.length>1&&(this.$ghostTextWidget={text:m.slice(1).join(`
`),row:A.row,column:A.column,className:"ace_ghost_text"},this.session.widgetManager.addLineWidget(this.$ghostTextWidget))},v.prototype.removeGhostText=function(){if(this.$ghostText){var $=this.$ghostText.position;this.removeExtraToken($.row,$.column),this.$ghostTextWidget&&(this.session.widgetManager.removeLineWidget(this.$ghostTextWidget),this.$ghostTextWidget=null),this.$ghostText=null}},v.prototype.addToken=function($,f,u,A){var m=this.session;m.bgTokenizer.lines[u]=null;var h={type:f,value:$},C=m.getTokens(u);if(A==null||!C.length)C.push(h);else for(var L=0,T=0;T<C.length;T++){var E=C[T];if(L+=E.value.length,A<=L){var _=E.value.length-(L-A),O=E.value.slice(0,_),I=E.value.slice(_);C.splice(T,1,{type:E.type,value:O},h,{type:E.type,value:I});break}}this.updateLines(u,u)},v.prototype.removeExtraToken=function($,f){this.session.bgTokenizer.lines[$]=null,this.updateLines($,$)},v.prototype.setTheme=function($,f){var u=this;if(this.$themeId=$,u._dispatchEvent("themeChange",{theme:$}),!$||typeof $=="string"){var A=$||this.$options.theme.initialValue;a.loadModule(["theme",A],m)}else m($);function m(h){if(u.$themeId!=$)return f&&f();if(!h||!h.cssClass)throw new Error("couldn't load module "+$+" or it didn't call define");h.$id&&(u.$themeId=h.$id),M.importCssString(h.cssText,h.cssClass,u.container),u.theme&&M.removeCssClass(u.container,u.theme.cssClass);var C="padding"in h?h.padding:"padding"in(u.theme||{})?4:u.$padding;u.$padding&&C!=u.$padding&&u.setPadding(C),u.$theme=h.cssClass,u.theme=h,M.addCssClass(u.container,h.cssClass),M.setCssClass(u.container,"ace_dark",h.isDark),u.$size&&(u.$size.width=0,u.$updateSizeAsync()),u._dispatchEvent("themeLoaded",{theme:h}),f&&f()}},v.prototype.getTheme=function(){return this.$themeId},v.prototype.setStyle=function($,f){M.setCssClass(this.container,$,f!==!1)},v.prototype.unsetStyle=function($){M.removeCssClass(this.container,$)},v.prototype.setCursorStyle=function($){M.setStyle(this.scroller.style,"cursor",$)},v.prototype.setMouseCursor=function($){M.setStyle(this.scroller.style,"cursor",$)},v.prototype.attachToShadowRoot=function(){M.importCssString(p,"ace_editor.css",this.container)},v.prototype.destroy=function(){this.freeze(),this.$fontMetrics.destroy(),this.$cursorLayer.destroy(),this.removeAllListeners(),this.container.textContent="",this.setOption("useResizeObserver",!1)},v.prototype.$updateCustomScrollbar=function($){var f=this;this.$horizScroll=this.$vScroll=null,this.scrollBarV.element.remove(),this.scrollBarH.element.remove(),this.$scrollDecorator&&delete this.$scrollDecorator,$===!0?(this.scrollBarV=new s(this.container,this),this.scrollBarH=new o(this.container,this),this.scrollBarV.setHeight(this.$size.scrollerHeight),this.scrollBarH.setWidth(this.$size.scrollerWidth),this.scrollBarV.addEventListener("scroll",function(u){f.$scrollAnimation||f.session.setScrollTop(u.data-f.scrollMargin.top)}),this.scrollBarH.addEventListener("scroll",function(u){f.$scrollAnimation||f.session.setScrollLeft(u.data-f.scrollMargin.left)}),this.$scrollDecorator=new b(this.scrollBarV,this),this.$scrollDecorator.$updateDecorators()):(this.scrollBarV=new i(this.container,this),this.scrollBarH=new e(this.container,this),this.scrollBarV.addEventListener("scroll",function(u){f.$scrollAnimation||f.session.setScrollTop(u.data-f.scrollMargin.top)}),this.scrollBarH.addEventListener("scroll",function(u){f.$scrollAnimation||f.session.setScrollLeft(u.data-f.scrollMargin.left)}))},v.prototype.$addResizeObserver=function(){if(!(!window.ResizeObserver||this.$resizeObserver)){var $=this;this.$resizeTimer=S.delayedCall(function(){$.destroyed||$.onResize()},50),this.$resizeObserver=new window.ResizeObserver(function(f){var u=f[0].contentRect.width,A=f[0].contentRect.height;Math.abs($.$size.width-u)>1||Math.abs($.$size.height-A)>1?$.$resizeTimer.delay():$.$resizeTimer.cancel()}),this.$resizeObserver.observe(this.container)}},v}();d.prototype.CHANGE_CURSOR=1,d.prototype.CHANGE_MARKER=2,d.prototype.CHANGE_GUTTER=4,d.prototype.CHANGE_SCROLL=8,d.prototype.CHANGE_LINES=16,d.prototype.CHANGE_TEXT=32,d.prototype.CHANGE_SIZE=64,d.prototype.CHANGE_MARKER_BACK=128,d.prototype.CHANGE_MARKER_FRONT=256,d.prototype.CHANGE_FULL=512,d.prototype.CHANGE_H_SCROLL=1024,d.prototype.$changes=0,d.prototype.$padding=null,d.prototype.$frozen=!1,d.prototype.STEPS=8,R.implement(d.prototype,w),a.defineOptions(d.prototype,"renderer",{useResizeObserver:{set:function(v){!v&&this.$resizeObserver?(this.$resizeObserver.disconnect(),this.$resizeTimer.cancel(),this.$resizeTimer=this.$resizeObserver=null):v&&!this.$resizeObserver&&this.$addResizeObserver()}},animatedScroll:{initialValue:!1},showInvisibles:{set:function(v){this.$textLayer.setShowInvisibles(v)&&this.$loop.schedule(this.CHANGE_TEXT)},initialValue:!1},showPrintMargin:{set:function(){this.$updatePrintMargin()},initialValue:!0},printMarginColumn:{set:function(){this.$updatePrintMargin()},initialValue:80},printMargin:{set:function(v){typeof v=="number"&&(this.$printMarginColumn=v),this.$showPrintMargin=!!v,this.$updatePrintMargin()},get:function(){return this.$showPrintMargin&&this.$printMarginColumn}},showGutter:{set:function(v){this.$gutter.style.display=v?"block":"none",this.$loop.schedule(this.CHANGE_FULL),this.onGutterResize()},initialValue:!0},useSvgGutterIcons:{set:function(v){this.$gutterLayer.$useSvgGutterIcons=v},initialValue:!1},showFoldedAnnotations:{set:function(v){this.$gutterLayer.$showFoldedAnnotations=v},initialValue:!1},fadeFoldWidgets:{set:function(v){M.setCssClass(this.$gutter,"ace_fade-fold-widgets",v)},initialValue:!1},showFoldWidgets:{set:function(v){this.$gutterLayer.setShowFoldWidgets(v),this.$loop.schedule(this.CHANGE_GUTTER)},initialValue:!0},displayIndentGuides:{set:function(v){this.$textLayer.setDisplayIndentGuides(v)&&this.$loop.schedule(this.CHANGE_TEXT)},initialValue:!0},highlightIndentGuides:{set:function(v){this.$textLayer.setHighlightIndentGuides(v)==!0?this.$textLayer.$highlightIndentGuide():this.$textLayer.$clearActiveIndentGuide(this.$textLayer.$lines.cells)},initialValue:!0},highlightGutterLine:{set:function(v){this.$gutterLayer.setHighlightGutterLine(v),this.$loop.schedule(this.CHANGE_GUTTER)},initialValue:!0},hScrollBarAlwaysVisible:{set:function(v){(!this.$hScrollBarAlwaysVisible||!this.$horizScroll)&&this.$loop.schedule(this.CHANGE_SCROLL)},initialValue:!1},vScrollBarAlwaysVisible:{set:function(v){(!this.$vScrollBarAlwaysVisible||!this.$vScroll)&&this.$loop.schedule(this.CHANGE_SCROLL)},initialValue:!1},fontSize:{set:function(v){typeof v=="number"&&(v=v+"px"),this.container.style.fontSize=v,this.updateFontSize()},initialValue:12},fontFamily:{set:function(v){this.container.style.fontFamily=v,this.updateFontSize()}},maxLines:{set:function(v){this.updateFull()}},minLines:{set:function(v){this.$minLines<562949953421311||(this.$minLines=0),this.updateFull()}},maxPixelHeight:{set:function(v){this.updateFull()},initialValue:0},scrollPastEnd:{set:function(v){v=+v||0,this.$scrollPastEnd!=v&&(this.$scrollPastEnd=v,this.$loop.schedule(this.CHANGE_SCROLL))},initialValue:0,handlesSet:!0},fixedWidthGutter:{set:function(v){this.$gutterLayer.$fixedWidth=!!v,this.$loop.schedule(this.CHANGE_GUTTER)}},customScrollbar:{set:function(v){this.$updateCustomScrollbar(v)},initialValue:!1},theme:{set:function(v){this.setTheme(v)},get:function(){return this.$themeId||this.theme},initialValue:"./theme/textmate",handlesSet:!0},hasCssTransforms:{},useTextareaForIME:{initialValue:!y.isMobile&&!y.isIE}}),k.VirtualRenderer=d});ace.define("ace/worker/worker_client",["require","exports","module","ace/lib/oop","ace/lib/net","ace/lib/event_emitter","ace/config"],function(x,k,H){"use strict";var R=x("../lib/oop"),M=x("../lib/net"),S=x("../lib/event_emitter").EventEmitter,a=x("../config");function l(e){var i="importScripts('"+M.qualifyURL(e)+"');";try{return new Blob([i],{type:"application/javascript"})}catch{var o=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder,s=new o;return s.append(i),s.getBlob("application/javascript")}}function t(e){if(typeof Worker>"u")return{postMessage:function(){},terminate:function(){}};if(a.get("loadWorkerFromBlob")){var i=l(e),o=window.URL||window.webkitURL,s=o.createObjectURL(i);return new Worker(s)}return new Worker(e)}var n=function(e){e.postMessage||(e=this.$createWorkerFromOldConfig.apply(this,arguments)),this.$worker=e,this.$sendDeltaQueue=this.$sendDeltaQueue.bind(this),this.changeListener=this.changeListener.bind(this),this.onMessage=this.onMessage.bind(this),this.callbackId=1,this.callbacks={},this.$worker.onmessage=this.onMessage};(function(){R.implement(this,S),this.$createWorkerFromOldConfig=function(e,i,o,s,c){if(x.nameToUrl&&!x.toUrl&&(x.toUrl=x.nameToUrl),a.get("packaged")||!x.toUrl)s=s||a.moduleUrl(i,"worker");else{var g=this.$normalizePath;s=s||g(x.toUrl("ace/worker/worker.js",null,"_"));var w={};e.forEach(function(p){w[p]=g(x.toUrl(p,null,"_").replace(/(\.js)?(\?.*)?$/,""))})}return this.$worker=t(s),c&&this.send("importScripts",c),this.$worker.postMessage({init:!0,tlns:w,module:i,classname:o}),this.$worker},this.onMessage=function(e){var i=e.data;switch(i.type){case"event":this._signal(i.name,{data:i.data});break;case"call":var o=this.callbacks[i.id];o&&(o(i.data),delete this.callbacks[i.id]);break;case"error":this.reportError(i.data);break;case"log":window.console&&console.log&&console.log.apply(console,i.data);break}},this.reportError=function(e){window.console&&console.error&&console.error(e)},this.$normalizePath=function(e){return M.qualifyURL(e)},this.terminate=function(){this._signal("terminate",{}),this.deltaQueue=null,this.$worker.terminate(),this.$worker.onerror=function(e){e.preventDefault()},this.$worker=null,this.$doc&&this.$doc.off("change",this.changeListener),this.$doc=null},this.send=function(e,i){this.$worker.postMessage({command:e,args:i})},this.call=function(e,i,o){if(o){var s=this.callbackId++;this.callbacks[s]=o,i.push(s)}this.send(e,i)},this.emit=function(e,i){try{i.data&&i.data.err&&(i.data.err={message:i.data.err.message,stack:i.data.err.stack,code:i.data.err.code}),this.$worker&&this.$worker.postMessage({event:e,data:{data:i.data}})}catch(o){console.error(o.stack)}},this.attachToDocument=function(e){this.$doc&&this.terminate(),this.$doc=e,this.call("setValue",[e.getValue()]),e.on("change",this.changeListener,!0)},this.changeListener=function(e){this.deltaQueue||(this.deltaQueue=[],setTimeout(this.$sendDeltaQueue,0)),e.action=="insert"?this.deltaQueue.push(e.start,e.lines):this.deltaQueue.push(e.start,e.end)},this.$sendDeltaQueue=function(){var e=this.deltaQueue;e&&(this.deltaQueue=null,e.length>50&&e.length>this.$doc.getLength()>>1?this.call("setValue",[this.$doc.getValue()]):this.emit("change",{data:e}))}}).call(n.prototype);var r=function(e,i,o){var s=null,c=!1,g=Object.create(S),w=[],p=new n({messageBuffer:w,terminate:function(){},postMessage:function(y){w.push(y),s&&(c?setTimeout(b):b())}});p.setEmitSync=function(y){c=y};var b=function(){var y=w.shift();y.command?s[y.command].apply(s,y.args):y.event&&g._signal(y.event,y.data)};return g.postMessage=function(y){p.onMessage({data:y})},g.callback=function(y,d){this.postMessage({type:"call",id:d,data:y})},g.emit=function(y,d){this.postMessage({type:"event",name:y,data:d})},a.loadModule(["worker",i],function(y){for(s=new y[o](g);w.length;)b()}),p};k.UIWorkerClient=r,k.WorkerClient=n,k.createWorker=t});ace.define("ace/placeholder",["require","exports","module","ace/range","ace/lib/event_emitter","ace/lib/oop"],function(x,k,H){"use strict";var R=x("./range").Range,M=x("./lib/event_emitter").EventEmitter,S=x("./lib/oop"),a=function(){function l(t,n,r,e,i,o){var s=this;this.length=n,this.session=t,this.doc=t.getDocument(),this.mainClass=i,this.othersClass=o,this.$onUpdate=this.onUpdate.bind(this),this.doc.on("change",this.$onUpdate,!0),this.$others=e,this.$onCursorChange=function(){setTimeout(function(){s.onCursorChange()})},this.$pos=r;var c=t.getUndoManager().$undoStack||t.getUndoManager().$undostack||{length:-1};this.$undoStackDepth=c.length,this.setup(),t.selection.on("changeCursor",this.$onCursorChange)}return l.prototype.setup=function(){var t=this,n=this.doc,r=this.session;this.selectionBefore=r.selection.toJSON(),r.selection.inMultiSelectMode&&r.selection.toSingleRange(),this.pos=n.createAnchor(this.$pos.row,this.$pos.column);var e=this.pos;e.$insertRight=!0,e.detach(),e.markerId=r.addMarker(new R(e.row,e.column,e.row,e.column+this.length),this.mainClass,null,!1),this.others=[],this.$others.forEach(function(i){var o=n.createAnchor(i.row,i.column);o.$insertRight=!0,o.detach(),t.others.push(o)}),r.setUndoSelect(!1)},l.prototype.showOtherMarkers=function(){if(!this.othersActive){var t=this.session,n=this;this.othersActive=!0,this.others.forEach(function(r){r.markerId=t.addMarker(new R(r.row,r.column,r.row,r.column+n.length),n.othersClass,null,!1)})}},l.prototype.hideOtherMarkers=function(){if(this.othersActive){this.othersActive=!1;for(var t=0;t<this.others.length;t++)this.session.removeMarker(this.others[t].markerId)}},l.prototype.onUpdate=function(t){if(this.$updating)return this.updateAnchors(t);var n=t;if(n.start.row===n.end.row&&n.start.row===this.pos.row){this.$updating=!0;var r=t.action==="insert"?n.end.column-n.start.column:n.start.column-n.end.column,e=n.start.column>=this.pos.column&&n.start.column<=this.pos.column+this.length+1,i=n.start.column-this.pos.column;if(this.updateAnchors(t),e&&(this.length+=r),e&&!this.session.$fromUndo){if(t.action==="insert")for(var o=this.others.length-1;o>=0;o--){var s=this.others[o],c={row:s.row,column:s.column+i};this.doc.insertMergedLines(c,t.lines)}else if(t.action==="remove")for(var o=this.others.length-1;o>=0;o--){var s=this.others[o],c={row:s.row,column:s.column+i};this.doc.remove(new R(c.row,c.column,c.row,c.column-r))}}this.$updating=!1,this.updateMarkers()}},l.prototype.updateAnchors=function(t){this.pos.onChange(t);for(var n=this.others.length;n--;)this.others[n].onChange(t);this.updateMarkers()},l.prototype.updateMarkers=function(){if(!this.$updating){var t=this,n=this.session,r=function(i,o){n.removeMarker(i.markerId),i.markerId=n.addMarker(new R(i.row,i.column,i.row,i.column+t.length),o,null,!1)};r(this.pos,this.mainClass);for(var e=this.others.length;e--;)r(this.others[e],this.othersClass)}},l.prototype.onCursorChange=function(t){if(!(this.$updating||!this.session)){var n=this.session.selection.getCursor();n.row===this.pos.row&&n.column>=this.pos.column&&n.column<=this.pos.column+this.length?(this.showOtherMarkers(),this._emit("cursorEnter",t)):(this.hideOtherMarkers(),this._emit("cursorLeave",t))}},l.prototype.detach=function(){this.session.removeMarker(this.pos&&this.pos.markerId),this.hideOtherMarkers(),this.doc.off("change",this.$onUpdate),this.session.selection.off("changeCursor",this.$onCursorChange),this.session.setUndoSelect(!0),this.session=null},l.prototype.cancel=function(){if(this.$undoStackDepth!==-1){for(var t=this.session.getUndoManager(),n=(t.$undoStack||t.$undostack).length-this.$undoStackDepth,r=0;r<n;r++)t.undo(this.session,!0);this.selectionBefore&&this.session.selection.fromJSON(this.selectionBefore)}},l}();S.implement(a.prototype,M),k.PlaceHolder=a});ace.define("ace/mouse/multi_select_handler",["require","exports","module","ace/lib/event","ace/lib/useragent"],function(x,k,H){var R=x("../lib/event"),M=x("../lib/useragent");function S(l,t){return l.row==t.row&&l.column==t.column}function a(l){var t=l.domEvent,n=t.altKey,r=t.shiftKey,e=t.ctrlKey,i=l.getAccelKey(),o=l.getButton();if(e&&M.isMac&&(o=t.button),l.editor.inMultiSelectMode&&o==2){l.editor.textInput.onContextMenu(l.domEvent);return}if(!e&&!n&&!i){o===0&&l.editor.inMultiSelectMode&&l.editor.exitMultiSelectMode();return}if(o===0){var s=l.editor,c=s.selection,g=s.inMultiSelectMode,w=l.getDocumentPosition(),p=c.getCursor(),b=l.inSelection()||c.isEmpty()&&S(w,p),y=l.x,d=l.y,v=function(I){y=I.clientX,d=I.clientY},$=s.session,f=s.renderer.pixelToScreenCoordinates(y,d),u=f,A;if(s.$mouseHandler.$enableJumpToDef)e&&n||i&&n?A=r?"block":"add":n&&s.$blockSelectEnabled&&(A="block");else if(i&&!n){if(A="add",!g&&r)return}else n&&s.$blockSelectEnabled&&(A="block");if(A&&M.isMac&&t.ctrlKey&&s.$mouseHandler.cancelContextMenu(),A=="add"){if(!g&&b)return;if(!g){var m=c.toOrientedRange();s.addSelectionMarker(m)}var h=c.rangeList.rangeAtPoint(w);s.inVirtualSelectionMode=!0,r&&(h=null,m=c.ranges[0]||m,s.removeSelectionMarker(m)),s.once("mouseup",function(){var I=c.toOrientedRange();h&&I.isEmpty()&&S(h.cursor,I.cursor)?c.substractPoint(I.cursor):(r?c.substractPoint(m.cursor):m&&(s.removeSelectionMarker(m),c.addRange(m)),c.addRange(I)),s.inVirtualSelectionMode=!1})}else if(A=="block"){l.stop(),s.inVirtualSelectionMode=!0;var C,L=[],T=function(){var I=s.renderer.pixelToScreenCoordinates(y,d),W=$.screenToDocumentPosition(I.row,I.column,I.offsetX);S(u,I)&&S(W,c.lead)||(u=I,s.selection.moveToPosition(W),s.renderer.scrollCursorIntoView(),s.removeSelectionMarkers(L),L=c.rectangularRangeBlock(u,f),s.$mouseHandler.$clickSelection&&L.length==1&&L[0].isEmpty()&&(L[0]=s.$mouseHandler.$clickSelection.clone()),L.forEach(s.addSelectionMarker,s),s.updateSelectionMarkers())};g&&!i?c.toSingleRange():!g&&i&&(C=c.toOrientedRange(),s.addSelectionMarker(C)),r?f=$.documentToScreenPosition(c.lead):c.moveToPosition(w),u={row:-1,column:-1};var E=function(I){T(),clearInterval(O),s.removeSelectionMarkers(L),L.length||(L=[c.toOrientedRange()]),C&&(s.removeSelectionMarker(C),c.toSingleRange(C));for(var W=0;W<L.length;W++)c.addRange(L[W]);s.inVirtualSelectionMode=!1,s.$mouseHandler.$clickSelection=null},_=T;R.capture(s.container,v,E);var O=setInterval(function(){_()},20);return l.preventDefault()}}}k.onMouseDown=a});ace.define("ace/commands/multi_select_commands",["require","exports","module","ace/keyboard/hash_handler"],function(x,k,H){k.defaultCommands=[{name:"addCursorAbove",description:"Add cursor above",exec:function(M){M.selectMoreLines(-1)},bindKey:{win:"Ctrl-Alt-Up",mac:"Ctrl-Alt-Up"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorBelow",description:"Add cursor below",exec:function(M){M.selectMoreLines(1)},bindKey:{win:"Ctrl-Alt-Down",mac:"Ctrl-Alt-Down"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorAboveSkipCurrent",description:"Add cursor above (skip current)",exec:function(M){M.selectMoreLines(-1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Up",mac:"Ctrl-Alt-Shift-Up"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorBelowSkipCurrent",description:"Add cursor below (skip current)",exec:function(M){M.selectMoreLines(1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Down",mac:"Ctrl-Alt-Shift-Down"},scrollIntoView:"cursor",readOnly:!0},{name:"selectMoreBefore",description:"Select more before",exec:function(M){M.selectMore(-1)},bindKey:{win:"Ctrl-Alt-Left",mac:"Ctrl-Alt-Left"},scrollIntoView:"cursor",readOnly:!0},{name:"selectMoreAfter",description:"Select more after",exec:function(M){M.selectMore(1)},bindKey:{win:"Ctrl-Alt-Right",mac:"Ctrl-Alt-Right"},scrollIntoView:"cursor",readOnly:!0},{name:"selectNextBefore",description:"Select next before",exec:function(M){M.selectMore(-1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Left",mac:"Ctrl-Alt-Shift-Left"},scrollIntoView:"cursor",readOnly:!0},{name:"selectNextAfter",description:"Select next after",exec:function(M){M.selectMore(1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Right",mac:"Ctrl-Alt-Shift-Right"},scrollIntoView:"cursor",readOnly:!0},{name:"toggleSplitSelectionIntoLines",description:"Split selection into lines",exec:function(M){M.multiSelect.rangeCount>1?M.multiSelect.joinSelections():M.multiSelect.splitIntoLines()},bindKey:{win:"Ctrl-Alt-L",mac:"Ctrl-Alt-L"},readOnly:!0},{name:"splitSelectionIntoLines",description:"Split into lines",exec:function(M){M.multiSelect.splitIntoLines()},readOnly:!0},{name:"alignCursors",description:"Align cursors",exec:function(M){M.alignCursors()},bindKey:{win:"Ctrl-Alt-A",mac:"Ctrl-Alt-A"},scrollIntoView:"cursor"},{name:"findAll",description:"Find all",exec:function(M){M.findAll()},bindKey:{win:"Ctrl-Alt-K",mac:"Ctrl-Alt-G"},scrollIntoView:"cursor",readOnly:!0}],k.multiSelectCommands=[{name:"singleSelection",description:"Single selection",bindKey:"esc",exec:function(M){M.exitMultiSelectMode()},scrollIntoView:"cursor",readOnly:!0,isAvailable:function(M){return M&&M.inMultiSelectMode}}];var R=x("../keyboard/hash_handler").HashHandler;k.keyboardHandler=new R(k.multiSelectCommands)});ace.define("ace/multi_select",["require","exports","module","ace/range_list","ace/range","ace/selection","ace/mouse/multi_select_handler","ace/lib/event","ace/lib/lang","ace/commands/multi_select_commands","ace/search","ace/edit_session","ace/editor","ace/config"],function(x,k,H){var R=x("./range_list").RangeList,M=x("./range").Range,S=x("./selection").Selection,a=x("./mouse/multi_select_handler").onMouseDown,l=x("./lib/event"),t=x("./lib/lang"),n=x("./commands/multi_select_commands");k.commands=n.defaultCommands.concat(n.multiSelectCommands);var r=x("./search").Search,e=new r;function i(p,b,y){return e.$options.wrap=!0,e.$options.needle=b,e.$options.backwards=y==-1,e.find(p)}var o=x("./edit_session").EditSession;(function(){this.getSelectionMarkers=function(){return this.$selectionMarkers}}).call(o.prototype),function(){this.ranges=null,this.rangeList=null,this.addRange=function(p,b){if(p){if(!this.inMultiSelectMode&&this.rangeCount===0){var y=this.toOrientedRange();if(this.rangeList.add(y),this.rangeList.add(p),this.rangeList.ranges.length!=2)return this.rangeList.removeAll(),b||this.fromOrientedRange(p);this.rangeList.removeAll(),this.rangeList.add(y),this.$onAddRange(y)}p.cursor||(p.cursor=p.end);var d=this.rangeList.add(p);return this.$onAddRange(p),d.length&&this.$onRemoveRange(d),this.rangeCount>1&&!this.inMultiSelectMode&&(this._signal("multiSelect"),this.inMultiSelectMode=!0,this.session.$undoSelect=!1,this.rangeList.attach(this.session)),b||this.fromOrientedRange(p)}},this.toSingleRange=function(p){p=p||this.ranges[0];var b=this.rangeList.removeAll();b.length&&this.$onRemoveRange(b),p&&this.fromOrientedRange(p)},this.substractPoint=function(p){var b=this.rangeList.substractPoint(p);if(b)return this.$onRemoveRange(b),b[0]},this.mergeOverlappingRanges=function(){var p=this.rangeList.merge();p.length&&this.$onRemoveRange(p)},this.$onAddRange=function(p){this.rangeCount=this.rangeList.ranges.length,this.ranges.unshift(p),this._signal("addRange",{range:p})},this.$onRemoveRange=function(p){if(this.rangeCount=this.rangeList.ranges.length,this.rangeCount==1&&this.inMultiSelectMode){var b=this.rangeList.ranges.pop();p.push(b),this.rangeCount=0}for(var y=p.length;y--;){var d=this.ranges.indexOf(p[y]);this.ranges.splice(d,1)}this._signal("removeRange",{ranges:p}),this.rangeCount===0&&this.inMultiSelectMode&&(this.inMultiSelectMode=!1,this._signal("singleSelect"),this.session.$undoSelect=!0,this.rangeList.detach(this.session)),b=b||this.ranges[0],b&&!b.isEqual(this.getRange())&&this.fromOrientedRange(b)},this.$initRangeList=function(){this.rangeList||(this.rangeList=new R,this.ranges=[],this.rangeCount=0)},this.getAllRanges=function(){return this.rangeCount?this.rangeList.ranges.concat():[this.getRange()]},this.splitIntoLines=function(){for(var p=this.ranges.length?this.ranges:[this.getRange()],b=[],y=0;y<p.length;y++){var d=p[y],v=d.start.row,$=d.end.row;if(v===$)b.push(d.clone());else{for(b.push(new M(v,d.start.column,v,this.session.getLine(v).length));++v<$;)b.push(this.getLineRange(v,!0));b.push(new M($,0,$,d.end.column))}y==0&&!this.isBackwards()&&(b=b.reverse())}this.toSingleRange();for(var y=b.length;y--;)this.addRange(b[y])},this.joinSelections=function(){var p=this.rangeList.ranges,b=p[p.length-1],y=M.fromPoints(p[0].start,b.end);this.toSingleRange(),this.setSelectionRange(y,b.cursor==b.start)},this.toggleBlockSelection=function(){if(this.rangeCount>1){var p=this.rangeList.ranges,b=p[p.length-1],y=M.fromPoints(p[0].start,b.end);this.toSingleRange(),this.setSelectionRange(y,b.cursor==b.start)}else{var d=this.session.documentToScreenPosition(this.cursor),v=this.session.documentToScreenPosition(this.anchor),$=this.rectangularRangeBlock(d,v);$.forEach(this.addRange,this)}},this.rectangularRangeBlock=function(p,b,y){var d=[],v=p.column<b.column;if(v)var $=p.column,f=b.column,u=p.offsetX,A=b.offsetX;else var $=b.column,f=p.column,u=b.offsetX,A=p.offsetX;var m=p.row<b.row;if(m)var h=p.row,C=b.row;else var h=b.row,C=p.row;$<0&&($=0),h<0&&(h=0),h==C&&(y=!0);for(var L,T=h;T<=C;T++){var E=M.fromPoints(this.session.screenToDocumentPosition(T,$,u),this.session.screenToDocumentPosition(T,f,A));if(E.isEmpty()){if(L&&c(E.end,L))break;L=E.end}E.cursor=v?E.start:E.end,d.push(E)}if(m&&d.reverse(),!y){for(var _=d.length-1;d[_].isEmpty()&&_>0;)_--;if(_>0)for(var O=0;d[O].isEmpty();)O++;for(var I=_;I>=O;I--)d[I].isEmpty()&&d.splice(I,1)}return d}}.call(S.prototype);var s=x("./editor").Editor;(function(){this.updateSelectionMarkers=function(){this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.addSelectionMarker=function(p){p.cursor||(p.cursor=p.end);var b=this.getSelectionStyle();return p.marker=this.session.addMarker(p,"ace_selection",b),this.session.$selectionMarkers.push(p),this.session.selectionMarkerCount=this.session.$selectionMarkers.length,p},this.removeSelectionMarker=function(p){if(p.marker){this.session.removeMarker(p.marker);var b=this.session.$selectionMarkers.indexOf(p);b!=-1&&this.session.$selectionMarkers.splice(b,1),this.session.selectionMarkerCount=this.session.$selectionMarkers.length}},this.removeSelectionMarkers=function(p){for(var b=this.session.$selectionMarkers,y=p.length;y--;){var d=p[y];if(d.marker){this.session.removeMarker(d.marker);var v=b.indexOf(d);v!=-1&&b.splice(v,1)}}this.session.selectionMarkerCount=b.length},this.$onAddRange=function(p){this.addSelectionMarker(p.range),this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.$onRemoveRange=function(p){this.removeSelectionMarkers(p.ranges),this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.$onMultiSelect=function(p){this.inMultiSelectMode||(this.inMultiSelectMode=!0,this.setStyle("ace_multiselect"),this.keyBinding.addKeyboardHandler(n.keyboardHandler),this.commands.setDefaultHandler("exec",this.$onMultiSelectExec),this.renderer.updateCursor(),this.renderer.updateBackMarkers())},this.$onSingleSelect=function(p){this.session.multiSelect.inVirtualMode||(this.inMultiSelectMode=!1,this.unsetStyle("ace_multiselect"),this.keyBinding.removeKeyboardHandler(n.keyboardHandler),this.commands.removeDefaultHandler("exec",this.$onMultiSelectExec),this.renderer.updateCursor(),this.renderer.updateBackMarkers(),this._emit("changeSelection"))},this.$onMultiSelectExec=function(p){var b=p.command,y=p.editor;if(y.multiSelect){if(b.multiSelectAction)b.multiSelectAction=="forEach"?d=y.forEachSelection(b,p.args):b.multiSelectAction=="forEachLine"?d=y.forEachSelection(b,p.args,!0):b.multiSelectAction=="single"?(y.exitMultiSelectMode(),d=b.exec(y,p.args||{})):d=b.multiSelectAction(y,p.args||{});else{var d=b.exec(y,p.args||{});y.multiSelect.addRange(y.multiSelect.toOrientedRange()),y.multiSelect.mergeOverlappingRanges()}return d}},this.forEachSelection=function(p,b,y){if(!this.inVirtualSelectionMode){var d=y&&y.keepOrder,v=y==!0||y&&y.$byLines,$=this.session,f=this.selection,u=f.rangeList,A=(d?f:u).ranges,m;if(!A.length)return p.exec?p.exec(this,b||{}):p(this,b||{});var h=f._eventRegistry;f._eventRegistry={};var C=new S($);this.inVirtualSelectionMode=!0;for(var L=A.length;L--;){if(v)for(;L>0&&A[L].start.row==A[L-1].end.row;)L--;C.fromOrientedRange(A[L]),C.index=L,this.selection=$.selection=C;var T=p.exec?p.exec(this,b||{}):p(this,b||{});!m&&T!==void 0&&(m=T),C.toOrientedRange(A[L])}C.detach(),this.selection=$.selection=f,this.inVirtualSelectionMode=!1,f._eventRegistry=h,f.mergeOverlappingRanges(),f.ranges[0]&&f.fromOrientedRange(f.ranges[0]);var E=this.renderer.$scrollAnimation;return this.onCursorChange(),this.onSelectionChange(),E&&E.from==E.to&&this.renderer.animateScrolling(E.from),m}},this.exitMultiSelectMode=function(){!this.inMultiSelectMode||this.inVirtualSelectionMode||this.multiSelect.toSingleRange()},this.getSelectedText=function(){var p="";if(this.inMultiSelectMode&&!this.inVirtualSelectionMode){for(var b=this.multiSelect.rangeList.ranges,y=[],d=0;d<b.length;d++)y.push(this.session.getTextRange(b[d]));var v=this.session.getDocument().getNewLineCharacter();p=y.join(v),p.length==(y.length-1)*v.length&&(p="")}else this.selection.isEmpty()||(p=this.session.getTextRange(this.getSelectionRange()));return p},this.$checkMultiselectChange=function(p,b){if(this.inMultiSelectMode&&!this.inVirtualSelectionMode){var y=this.multiSelect.ranges[0];if(this.multiSelect.isEmpty()&&b==this.multiSelect.anchor)return;var d=b==this.multiSelect.anchor?y.cursor==y.start?y.end:y.start:y.cursor;d.row!=b.row||this.session.$clipPositionToDocument(d.row,d.column).column!=b.column?this.multiSelect.toSingleRange(this.multiSelect.toOrientedRange()):this.multiSelect.mergeOverlappingRanges()}},this.findAll=function(p,b,y){if(b=b||{},b.needle=p||b.needle,b.needle==null){var d=this.selection.isEmpty()?this.selection.getWordRange():this.selection.getRange();b.needle=this.session.getTextRange(d)}this.$search.set(b);var v=this.$search.findAll(this.session);if(!v.length)return 0;var $=this.multiSelect;y||$.toSingleRange(v[0]);for(var f=v.length;f--;)$.addRange(v[f],!0);return d&&$.rangeList.rangeAtPoint(d.start)&&$.addRange(d,!0),v.length},this.selectMoreLines=function(p,b){var y=this.selection.toOrientedRange(),d=y.cursor==y.end,v=this.session.documentToScreenPosition(y.cursor);this.selection.$desiredColumn&&(v.column=this.selection.$desiredColumn);var $=this.session.screenToDocumentPosition(v.row+p,v.column);if(y.isEmpty())var u=$;else var f=this.session.documentToScreenPosition(d?y.end:y.start),u=this.session.screenToDocumentPosition(f.row+p,f.column);if(d){var A=M.fromPoints($,u);A.cursor=A.start}else{var A=M.fromPoints(u,$);A.cursor=A.end}if(A.desiredColumn=v.column,!this.selection.inMultiSelectMode)this.selection.addRange(y);else if(b)var m=y.cursor;this.selection.addRange(A),m&&this.selection.substractPoint(m)},this.transposeSelections=function(p){for(var b=this.session,y=b.multiSelect,d=y.ranges,v=d.length;v--;){var $=d[v];if($.isEmpty()){var f=b.getWordRange($.start.row,$.start.column);$.start.row=f.start.row,$.start.column=f.start.column,$.end.row=f.end.row,$.end.column=f.end.column}}y.mergeOverlappingRanges();for(var u=[],v=d.length;v--;){var $=d[v];u.unshift(b.getTextRange($))}p<0?u.unshift(u.pop()):u.push(u.shift());for(var v=d.length;v--;){var $=d[v],f=$.clone();b.replace($,u[v]),$.start.row=f.start.row,$.start.column=f.start.column}y.fromOrientedRange(y.ranges[0])},this.selectMore=function(p,b,y){var d=this.session,v=d.multiSelect,$=v.toOrientedRange();if(!($.isEmpty()&&($=d.getWordRange($.start.row,$.start.column),$.cursor=p==-1?$.start:$.end,this.multiSelect.addRange($),y))){var f=d.getTextRange($),u=i(d,f,p);u&&(u.cursor=p==-1?u.start:u.end,this.session.unfold(u),this.multiSelect.addRange(u),this.renderer.scrollCursorIntoView(null,.5)),b&&this.multiSelect.substractPoint($.cursor)}},this.alignCursors=function(){var p=this.session,b=p.multiSelect,y=b.ranges,d=-1,v=y.filter(function(_){if(_.cursor.row==d)return!0;d=_.cursor.row});if(!y.length||v.length==y.length-1){var $=this.selection.getRange(),f=$.start.row,u=$.end.row,A=f==u;if(A){var m=this.session.getLength(),h;do h=this.session.getLine(u);while(/[=:]/.test(h)&&++u<m);do h=this.session.getLine(f);while(/[=:]/.test(h)&&--f>0);f<0&&(f=0),u>=m&&(u=m-1)}var C=this.session.removeFullLines(f,u);C=this.$reAlignText(C,A),this.session.insert({row:f,column:0},C.join(`
`)+`
`),A||($.start.column=0,$.end.column=C[C.length-1].length),this.selection.setRange($)}else{v.forEach(function(_){b.substractPoint(_.cursor)});var L=0,T=1/0,E=y.map(function(_){var O=_.cursor,I=p.getLine(O.row),W=I.substr(O.column).search(/\S/g);return W==-1&&(W=0),O.column>L&&(L=O.column),W<T&&(T=W),W});y.forEach(function(_,O){var I=_.cursor,W=L-I.column,F=E[O]-T;W>F?p.insert(I,t.stringRepeat(" ",W-F)):p.remove(new M(I.row,I.column,I.row,I.column-W+F)),_.start.column=_.end.column=L,_.start.row=_.end.row=I.row,_.cursor=_.end}),b.fromOrientedRange(y[0]),this.renderer.updateCursor(),this.renderer.updateBackMarkers()}},this.$reAlignText=function(p,b){var y=!0,d=!0,v,$,f;return p.map(function(C){var L=C.match(/(\s*)(.*?)(\s*)([=:].*)/);return L?v==null?(v=L[1].length,$=L[2].length,f=L[3].length,L):(v+$+f!=L[1].length+L[2].length+L[3].length&&(d=!1),v!=L[1].length&&(y=!1),v>L[1].length&&(v=L[1].length),$<L[2].length&&($=L[2].length),f>L[3].length&&(f=L[3].length),L):[C]}).map(b?A:y?d?m:A:h);function u(C){return t.stringRepeat(" ",C)}function A(C){return C[2]?u(v)+C[2]+u($-C[2].length+f)+C[4].replace(/^([=:])\s+/,"$1 "):C[0]}function m(C){return C[2]?u(v+$-C[2].length)+C[2]+u(f)+C[4].replace(/^([=:])\s+/,"$1 "):C[0]}function h(C){return C[2]?u(v)+C[2]+u(f)+C[4].replace(/^([=:])\s+/,"$1 "):C[0]}}}).call(s.prototype);function c(p,b){return p.row==b.row&&p.column==b.column}k.onSessionChange=function(p){var b=p.session;b&&!b.multiSelect&&(b.$selectionMarkers=[],b.selection.$initRangeList(),b.multiSelect=b.selection),this.multiSelect=b&&b.multiSelect;var y=p.oldSession;y&&(y.multiSelect.off("addRange",this.$onAddRange),y.multiSelect.off("removeRange",this.$onRemoveRange),y.multiSelect.off("multiSelect",this.$onMultiSelect),y.multiSelect.off("singleSelect",this.$onSingleSelect),y.multiSelect.lead.off("change",this.$checkMultiselectChange),y.multiSelect.anchor.off("change",this.$checkMultiselectChange)),b&&(b.multiSelect.on("addRange",this.$onAddRange),b.multiSelect.on("removeRange",this.$onRemoveRange),b.multiSelect.on("multiSelect",this.$onMultiSelect),b.multiSelect.on("singleSelect",this.$onSingleSelect),b.multiSelect.lead.on("change",this.$checkMultiselectChange),b.multiSelect.anchor.on("change",this.$checkMultiselectChange)),b&&this.inMultiSelectMode!=b.selection.inMultiSelectMode&&(b.selection.inMultiSelectMode?this.$onMultiSelect():this.$onSingleSelect())};function g(p){p.$multiselectOnSessionChange||(p.$onAddRange=p.$onAddRange.bind(p),p.$onRemoveRange=p.$onRemoveRange.bind(p),p.$onMultiSelect=p.$onMultiSelect.bind(p),p.$onSingleSelect=p.$onSingleSelect.bind(p),p.$multiselectOnSessionChange=k.onSessionChange.bind(p),p.$checkMultiselectChange=p.$checkMultiselectChange.bind(p),p.$multiselectOnSessionChange(p),p.on("changeSession",p.$multiselectOnSessionChange),p.on("mousedown",a),p.commands.addCommands(n.defaultCommands),w(p))}function w(p){if(!p.textInput)return;var b=p.textInput.getElement(),y=!1;l.addListener(b,"keydown",function(v){var $=v.keyCode==18&&!(v.ctrlKey||v.shiftKey||v.metaKey);p.$blockSelectEnabled&&$?y||(p.renderer.setMouseCursor("crosshair"),y=!0):y&&d()},p),l.addListener(b,"keyup",d,p),l.addListener(b,"blur",d,p);function d(v){y&&(p.renderer.setMouseCursor(""),y=!1)}}k.MultiSelect=g,x("./config").defineOptions(s.prototype,"editor",{enableMultiselect:{set:function(p){g(this),p?this.on("mousedown",a):this.off("mousedown",a)},value:!0},enableBlockSelect:{set:function(p){this.$blockSelectEnabled=p},value:!0}})});ace.define("ace/mode/folding/fold_mode",["require","exports","module","ace/range"],function(x,k,H){"use strict";var R=x("../../range").Range,M=k.FoldMode=function(){};(function(){this.foldingStartMarker=null,this.foldingStopMarker=null,this.getFoldWidget=function(S,a,l){var t=S.getLine(l);return this.foldingStartMarker.test(t)?"start":a=="markbeginend"&&this.foldingStopMarker&&this.foldingStopMarker.test(t)?"end":""},this.getFoldWidgetRange=function(S,a,l){return null},this.indentationBlock=function(S,a,l){var t=/\S/,n=S.getLine(a),r=n.search(t);if(r!=-1){for(var e=l||n.length,i=S.getLength(),o=a,s=a;++a<i;){var c=S.getLine(a).search(t);if(c!=-1){if(c<=r){var g=S.getTokenAt(a,0);if(!g||g.type!=="string")break}s=a}}if(s>o){var w=S.getLine(s).length;return new R(o,e,s,w)}}},this.openingBracketBlock=function(S,a,l,t,n){var r={row:l,column:t+1},e=S.$findClosingBracket(a,r,n);if(e){var i=S.foldWidgets[e.row];return i==null&&(i=S.getFoldWidget(e.row)),i=="start"&&e.row>r.row&&(e.row--,e.column=S.getLine(e.row).length),R.fromPoints(r,e)}},this.closingBracketBlock=function(S,a,l,t,n){var r={row:l,column:t},e=S.$findOpeningBracket(a,r);if(e)return e.column++,r.column--,R.fromPoints(e,r)}}).call(M.prototype)});ace.define("ace/ext/error_marker",["require","exports","module","ace/line_widgets","ace/lib/dom","ace/range","ace/config"],function(x,k,H){"use strict";var R=x("../line_widgets").LineWidgets,M=x("../lib/dom"),S=x("../range").Range,a=x("../config").nls;function l(n,r,e){for(var i=0,o=n.length-1;i<=o;){var s=i+o>>1,c=e(r,n[s]);if(c>0)i=s+1;else if(c<0)o=s-1;else return s}return-(i+1)}function t(n,r,e){var i=n.getAnnotations().sort(S.comparePoints);if(i.length){var o=l(i,{row:r,column:-1},S.comparePoints);o<0&&(o=-o-1),o>=i.length?o=e>0?0:i.length-1:o===0&&e<0&&(o=i.length-1);var s=i[o];if(!(!s||!e)){if(s.row===r){do s=i[o+=e];while(s&&s.row===r);if(!s)return i.slice()}var c=[];r=s.row;do c[e<0?"unshift":"push"](s),s=i[o+=e];while(s&&s.row==r);return c.length&&c}}}k.showErrorMarker=function(n,r){var e=n.session;e.widgetManager||(e.widgetManager=new R(e),e.widgetManager.attach(n));var i=n.getCursorPosition(),o=i.row,s=e.widgetManager.getWidgetsAtRow(o).filter(function($){return $.type=="errorMarker"})[0];s?s.destroy():o-=r;var c=t(e,o,r),g;if(c){var w=c[0];i.column=(w.pos&&typeof w.column!="number"?w.pos.sc:w.column)||0,i.row=w.row,g=n.renderer.$gutterLayer.$annotations[i.row]}else{if(s)return;g={text:[a("Looks good!")],className:"ace_ok"}}n.session.unfold(i.row),n.selection.moveToPosition(i);var p={row:i.row,fixedWidth:!0,coverGutter:!0,el:M.createElement("div"),type:"errorMarker"},b=p.el.appendChild(M.createElement("div")),y=p.el.appendChild(M.createElement("div"));y.className="error_widget_arrow "+g.className;var d=n.renderer.$cursorLayer.getPixelPosition(i).left;y.style.left=d+n.renderer.gutterWidth-5+"px",p.el.className="error_widget_wrapper",b.className="error_widget "+g.className,b.innerHTML=g.text.join("<br>"),b.appendChild(M.createElement("div"));var v=function($,f,u){if(f===0&&(u==="esc"||u==="return"))return p.destroy(),{command:"null"}};p.destroy=function(){n.$mouseHandler.isMousePressed||(n.keyBinding.removeKeyboardHandler(v),e.widgetManager.removeLineWidget(p),n.off("changeSelection",p.destroy),n.off("changeSession",p.destroy),n.off("mouseup",p.destroy),n.off("change",p.destroy))},n.keyBinding.addKeyboardHandler(v),n.on("changeSelection",p.destroy),n.on("changeSession",p.destroy),n.on("mouseup",p.destroy),n.on("change",p.destroy),n.session.widgetManager.addLineWidget(p),p.el.onmousedown=n.focus.bind(n),n.renderer.scrollCursorIntoView(null,.5,{bottom:p.el.offsetHeight})},M.importCssString(`
    .error_widget_wrapper {
        background: inherit;
        color: inherit;
        border:none
    }
    .error_widget {
        border-top: solid 2px;
        border-bottom: solid 2px;
        margin: 5px 0;
        padding: 10px 40px;
        white-space: pre-wrap;
    }
    .error_widget.ace_error, .error_widget_arrow.ace_error{
        border-color: #ff5a5a
    }
    .error_widget.ace_warning, .error_widget_arrow.ace_warning{
        border-color: #F1D817
    }
    .error_widget.ace_info, .error_widget_arrow.ace_info{
        border-color: #5a5a5a
    }
    .error_widget.ace_ok, .error_widget_arrow.ace_ok{
        border-color: #5aaa5a
    }
    .error_widget_arrow {
        position: absolute;
        border: solid 5px;
        border-top-color: transparent!important;
        border-right-color: transparent!important;
        border-left-color: transparent!important;
        top: -5px;
    }
`,"error_marker.css",!1)});ace.define("ace/ace",["require","exports","module","ace/lib/dom","ace/range","ace/editor","ace/edit_session","ace/undomanager","ace/virtual_renderer","ace/worker/worker_client","ace/keyboard/hash_handler","ace/placeholder","ace/multi_select","ace/mode/folding/fold_mode","ace/theme/textmate","ace/ext/error_marker","ace/config","ace/loader_build"],function(x,k,H){"use strict";x("./loader_build")(k);var R=x("./lib/dom"),M=x("./range").Range,S=x("./editor").Editor,a=x("./edit_session").EditSession,l=x("./undomanager").UndoManager,t=x("./virtual_renderer").VirtualRenderer;x("./worker/worker_client"),x("./keyboard/hash_handler"),x("./placeholder"),x("./multi_select"),x("./mode/folding/fold_mode"),x("./theme/textmate"),x("./ext/error_marker"),k.config=x("./config"),k.edit=function(n,r){if(typeof n=="string"){var e=n;if(n=document.getElementById(e),!n)throw new Error("ace.edit can't find div #"+e)}if(n&&n.env&&n.env.editor instanceof S)return n.env.editor;var i="";if(n&&/input|textarea/i.test(n.tagName)){var o=n;i=o.value,n=R.createElement("pre"),o.parentNode.replaceChild(n,o)}else n&&(i=n.textContent,n.innerHTML="");var s=k.createEditSession(i),c=new S(new t(n),s,r),g={document:s,editor:c,onResize:c.resize.bind(c,null)};return o&&(g.textarea=o),c.on("destroy",function(){g.editor.container.env=null}),c.container.env=c.env=g,c},k.createEditSession=function(n,r){var e=new a(n,r);return e.setUndoManager(new l),e},k.Range=M,k.Editor=S,k.EditSession=a,k.UndoManager=l,k.VirtualRenderer=t,k.version=k.config.version});(function(){ace.require(["ace/ace"],function(x){x&&(x.config.init(!0),x.define=ace.define);var k=function(){return this}();!k&&typeof window<"u"&&(k=window),!k&&typeof self<"u"&&(k=self),k.ace||(k.ace=x);for(var H in x)x.hasOwnProperty(H)&&(k.ace[H]=x[H]);k.ace.default=k.ace,typeof ve=="object"&&typeof Se=="object"&&ve&&(ve.exports=k.ace)})})()});var ke=$e((Le,ye)=>{ace.define("ace/mode/ini_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(x,k,H){"use strict";var R=x("../lib/oop"),M=x("./text_highlight_rules").TextHighlightRules,S="\\\\(?:[\\\\0abtrn;#=:]|x[a-fA-F\\d]{4})",a=function(){this.$rules={start:[{token:"punctuation.definition.comment.ini",regex:"#.*",push_:[{token:"comment.line.number-sign.ini",regex:"$|^",next:"pop"},{defaultToken:"comment.line.number-sign.ini"}]},{token:"punctuation.definition.comment.ini",regex:";.*",push_:[{token:"comment.line.semicolon.ini",regex:"$|^",next:"pop"},{defaultToken:"comment.line.semicolon.ini"}]},{token:["keyword.other.definition.ini","text","punctuation.separator.key-value.ini"],regex:"\\b([a-zA-Z0-9_.-]+)\\b(\\s*)(=)"},{token:["punctuation.definition.entity.ini","constant.section.group-title.ini","punctuation.definition.entity.ini"],regex:"^(\\[)(.*?)(\\])"},{token:"punctuation.definition.string.begin.ini",regex:"'",push:[{token:"punctuation.definition.string.end.ini",regex:"'",next:"pop"},{token:"constant.language.escape",regex:S},{defaultToken:"string.quoted.single.ini"}]},{token:"punctuation.definition.string.begin.ini",regex:'"',push:[{token:"constant.language.escape",regex:S},{token:"punctuation.definition.string.end.ini",regex:'"',next:"pop"},{defaultToken:"string.quoted.double.ini"}]}]},this.normalizeRules()};a.metaData={fileTypes:["ini","conf"],keyEquivalent:"^~I",name:"Ini",scopeName:"source.ini"},R.inherits(a,M),k.IniHighlightRules=a});ace.define("ace/mode/folding/ini",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],function(x,k,H){"use strict";var R=x("../../lib/oop"),M=x("../../range").Range,S=x("./fold_mode").FoldMode,a=k.FoldMode=function(){};R.inherits(a,S),function(){this.foldingStartMarker=/^\s*\[([^\])]*)]\s*(?:$|[;#])/,this.getFoldWidgetRange=function(l,t,n){var r=this.foldingStartMarker,e=l.getLine(n),i=e.match(r);if(i){for(var o=i[1]+".",s=e.length,c=l.getLength(),g=n,w=n;++n<c;)if(e=l.getLine(n),!/^\s*$/.test(e)){if(i=e.match(r),i&&i[1].lastIndexOf(o,0)!==0)break;w=n}if(w>g){var p=l.getLine(w).length;return new M(g,s,w,p)}}}}.call(a.prototype)});ace.define("ace/mode/ini",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/ini_highlight_rules","ace/mode/folding/ini"],function(x,k,H){"use strict";var R=x("../lib/oop"),M=x("./text").Mode,S=x("./ini_highlight_rules").IniHighlightRules,a=x("./folding/ini").FoldMode,l=function(){this.HighlightRules=S,this.foldingRules=new a,this.$behaviour=this.$defaultBehaviour};R.inherits(l,M),function(){this.lineCommentStart=";",this.blockComment=null,this.$id="ace/mode/ini"}.call(l.prototype),k.Mode=l});(function(){ace.require(["ace/mode/ini"],function(x){typeof ye=="object"&&typeof Le=="object"&&ye&&(ye.exports=x)})})()});var Me=Ce(Ae(),1),De=Ce(ke(),1),We=({maxLines:x,minLines:k,fontSize:H})=>({editor:null,init(){this.editor=Me.default.edit(this.$refs.editor,{mode:"ace/mode/ini",readOnly:!0,maxLines:x,minLines:k,fontSize:H}),window.addEventListener("logContentUpdated",R=>{this.editor.session.setValue(R.detail.content)})},jumpToEnd(){this.editor.gotoLine(this.editor.session.doc.$lines.length)},jumpToStart(){this.editor.gotoLine(0)}});export{We as default};
/*! Bundled license information:

ace-builds/src-noconflict/ace.js:
  (*
  * based on code from:
  *
  * @license RequireJS text 0.25.0 Copyright (c) 2010-2011, The Dojo Foundation All Rights Reserved.
  * Available via the MIT or new BSD license.
  * see: http://github.com/jrburke/requirejs for details
  *)
  (*! @license
  ==========================================================================
  SproutCore -- JavaScript Application Framework
  copyright 2006-2009, Sprout Systems Inc., Apple Inc. and contributors.
  
  Permission is hereby granted, free of charge, to any person obtaining a
  copy of this software and associated documentation files (the "Software"),
  to deal in the Software without restriction, including without limitation
  the rights to use, copy, modify, merge, publish, distribute, sublicense,
  and/or sell copies of the Software, and to permit persons to whom the
  Software is furnished to do so, subject to the following conditions:
  
  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.
  
  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
  DEALINGS IN THE SOFTWARE.
  
  SproutCore and the SproutCore logo are trademarks of Sprout Systems, Inc.
  
  For more information about SproutCore, visit http://www.sproutcore.com
  
  
  ==========================================================================
  @license *)
*/
