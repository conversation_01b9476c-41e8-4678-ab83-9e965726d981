.filament-icon-picker .choices>.choices__list{padding-left:.5rem;padding-right:.5rem}.filament-icon-picker .choices>.choices__inner>.choices__list.choices__list--single{width:100%}.filament-icon-picker.filament-icon-picker-on_top .choices>.choices__list{visibility:visible;position:relative;padding-left:.5rem;padding-right:.5rem}.filament-icon-picker.filament-icon-picker-on_top .choices>.choices__inner{background-image:none;padding-left:.75rem;padding-right:.75rem}.filament-icon-picker.filament-icon-picker-on_top .choices>.choices__inner>.choices__list.choices__list--single{position:relative;padding-left:0;padding-right:0}.filament-icon-picker.filament-icon-picker-on_top .choices>.choices__inner>.choices__list.choices__list--single .choices__button{position:absolute;margin-right:.5rem}.filament-icon-picker .choices>.choices__list .choices__list{gap:.5rem;padding-bottom:.5rem}.filament-icon-picker .choices>.choices__list .choices__list .choices__item{border-radius:.5rem;border-width:1px;--tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.filament-icon-picker .choices>.choices__list .choices__list .choices__item:hover{cursor:pointer}.filament-icon-picker .choices>.choices__list .choices__list .choices__item.choices__item--disabled,.filament-icon-picker .choices>.choices__list .choices__list .choices__item.has-no-choices,.filament-icon-picker .choices>.choices__list .choices__list .choices__item.has-no-results{border-style:none;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.filament-icon-picker .choices>.choices__list .choices__list{display:grid}.\!h-16{height:4rem!important}.h-12{height:3rem}.w-12{width:3rem}.shrink-0{flex-shrink:0}.grow-0{flex-grow:0}.filament-icon-picker .choices>.choices__list .choices__list .choices__item.choices__item--disabled,.filament-icon-picker .choices>.choices__list .choices__list .choices__item.has-no-choices,.filament-icon-picker .choices>.choices__list .choices__list .choices__item.has-no-results{grid-column:1/-1}.filament-icon-picker[default="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[default="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[default="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[default="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[default="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[default="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[default="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[default="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[default="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[default="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[default="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[default="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}@media (min-width:640px){.filament-icon-picker[sm="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[sm="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[sm="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[sm="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[sm="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[sm="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[sm="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[sm="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[sm="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[sm="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[sm="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[sm="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}}@media (min-width:768px){.filament-icon-picker[md="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[md="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[md="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[md="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[md="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[md="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[md="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[md="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[md="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[md="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[md="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[md="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}}@media (min-width:1024px){.filament-icon-picker[lg="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[lg="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[lg="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[lg="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[lg="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[lg="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[lg="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[lg="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[lg="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[lg="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[lg="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[lg="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}}@media (min-width:1280px){.filament-icon-picker[xl="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[xl="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[xl="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[xl="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[xl="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[xl="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[xl="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[xl="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[xl="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[xl="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[xl="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[xl="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}}@media (min-width:1536px){.filament-icon-picker[\2xl="1"] .choices>.choices__list .choices__list{grid-template-columns:repeat(1,minmax(0,1fr))}.filament-icon-picker[\2xl="2"] .choices>.choices__list .choices__list{grid-template-columns:repeat(2,minmax(0,1fr))}.filament-icon-picker[\2xl="3"] .choices>.choices__list .choices__list{grid-template-columns:repeat(3,minmax(0,1fr))}.filament-icon-picker[\2xl="4"] .choices>.choices__list .choices__list{grid-template-columns:repeat(4,minmax(0,1fr))}.filament-icon-picker[\2xl="5"] .choices>.choices__list .choices__list{grid-template-columns:repeat(5,minmax(0,1fr))}.filament-icon-picker[\2xl="6"] .choices>.choices__list .choices__list{grid-template-columns:repeat(6,minmax(0,1fr))}.filament-icon-picker[\2xl="7"] .choices>.choices__list .choices__list{grid-template-columns:repeat(7,minmax(0,1fr))}.filament-icon-picker[\2xl="8"] .choices>.choices__list .choices__list{grid-template-columns:repeat(8,minmax(0,1fr))}.filament-icon-picker[\2xl="9"] .choices>.choices__list .choices__list{grid-template-columns:repeat(9,minmax(0,1fr))}.filament-icon-picker[\2xl="10"] .choices>.choices__list .choices__list{grid-template-columns:repeat(10,minmax(0,1fr))}.filament-icon-picker[\2xl="11"] .choices>.choices__list .choices__list{grid-template-columns:repeat(11,minmax(0,1fr))}.filament-icon-picker[\2xl="12"] .choices>.choices__list .choices__list{grid-template-columns:repeat(12,minmax(0,1fr))}}