<?php

namespace Tests\Feature;

use App\Enums\VehicleTypesCategories;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use App\Services\VehicleClassificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DriverApprovalServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private VehicleClassificationService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = app(VehicleClassificationService::class);
    }

    /** @test */
    public function service_provides_suggested_vehicle_types_for_driver_approval()
    {
        // Create test data
        $brand = VehicleBrand::create(['name_en' => 'Toyota', 'name_ar' => 'تويوتا']);
        $model = VehicleModel::create([
            'vehicle_brand_id' => $brand->id,
            'name_en' => 'Camry',
            'name_ar' => 'كامري',
        ]);

        $economyType = VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $comfortType = VehicleType::create([
            'name_en' => 'Comfort',
            'name_ar' => 'راحة',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $luxuryType = VehicleType::create([
            'name_en' => 'Luxury',
            'name_ar' => 'فاخر',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        // Create classification rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$brand->id],
            'models' => [$model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Create vehicle that matches the rule
        $vehicle = Vehicle::create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $economyType->id,
            'year' => 2020,
            'seat_number' => 4,
            'license_plate_number' => '1-12345-ليبيا',
            'image' => 'test.jpg',
        ]);

        // Test getSuggestedVehicleTypes method
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($vehicle);

        // Verify that suggested type is first
        $firstKey = array_key_first($suggestedTypes);
        $this->assertEquals($comfortType->id, $firstKey);

        // Verify that it's marked as suggested
        $this->assertStringContainsString('⭐', $suggestedTypes[$firstKey]);
        $this->assertStringContainsString('Suggested', $suggestedTypes[$firstKey]);

        // Verify other types are also available
        $this->assertArrayHasKey($economyType->id, $suggestedTypes);
        $this->assertArrayHasKey($luxuryType->id, $suggestedTypes);

        // Verify the suggested type is not duplicated
        $comfortCount = 0;
        foreach ($suggestedTypes as $typeId => $typeName) {
            if ($typeId === $comfortType->id) {
                $comfortCount++;
            }
        }
        $this->assertEquals(1, $comfortCount);
    }

    /** @test */
    public function service_handles_vehicle_without_classification_rules()
    {
        // Create test data without any classification rules
        $brand = VehicleBrand::create(['name_en' => 'Honda', 'name_ar' => 'هوندا']);
        $model = VehicleModel::create([
            'vehicle_brand_id' => $brand->id,
            'name_en' => 'Civic',
            'name_ar' => 'سيفيك',
        ]);

        $economyType = VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $comfortType = VehicleType::create([
            'name_en' => 'Comfort',
            'name_ar' => 'راحة',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $vehicle = Vehicle::create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $economyType->id,
            'year' => 2020,
            'seat_number' => 4,
            'license_plate_number' => '2-12345-ليبيا',
            'image' => 'test.jpg',
        ]);

        // Test getSuggestedVehicleTypes method
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($vehicle);

        // Should return all available types for the category
        $this->assertArrayHasKey($economyType->id, $suggestedTypes);
        $this->assertArrayHasKey($comfortType->id, $suggestedTypes);

        // Since no classification rules match, it should default to Economy as suggested
        $firstKey = array_key_first($suggestedTypes);
        $this->assertEquals($economyType->id, $firstKey);
        $this->assertStringContainsString('⭐', $suggestedTypes[$firstKey]);
        $this->assertStringContainsString('Suggested', $suggestedTypes[$firstKey]);
    }

    /** @test */
    public function service_handles_vehicle_without_model_gracefully()
    {
        $economyType = VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $comfortType = VehicleType::create([
            'name_en' => 'Comfort',
            'name_ar' => 'راحة',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        // Create a vehicle with model first, then test with null model
        $brand = VehicleBrand::create(['name_en' => 'Test Brand']);
        $model = VehicleModel::create([
            'vehicle_brand_id' => $brand->id,
            'name_en' => 'Test Model',
        ]);

        $vehicle = Vehicle::create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $economyType->id,
            'year' => 2020,
            'seat_number' => 4,
            'license_plate_number' => '3-12345-ليبيا',
            'image' => 'test.jpg',
        ]);

        // Manually set vehicle_model_id to null to test the service
        $vehicle->vehicle_model_id = null;

        // Test getSuggestedVehicleTypes method
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($vehicle);

        // Should return all available types as fallback
        $this->assertArrayHasKey($economyType->id, $suggestedTypes);
        $this->assertArrayHasKey($comfortType->id, $suggestedTypes);

        // No type should be marked as suggested since classification failed
        foreach ($suggestedTypes as $typeName) {
            $this->assertStringNotContainsString('⭐', $typeName);
            $this->assertStringNotContainsString('Suggested', $typeName);
        }
    }

    /** @test */
    public function service_can_be_resolved_from_container()
    {
        // Test that the service can be properly resolved from the Laravel container
        $service1 = app(VehicleClassificationService::class);
        $service2 = app(\App\Services\VehicleClassificationService::class);

        $this->assertInstanceOf(VehicleClassificationService::class, $service1);
        $this->assertInstanceOf(VehicleClassificationService::class, $service2);

        // Both should be the same instance (singleton behavior if configured)
        $this->assertEquals(get_class($service1), get_class($service2));
    }

    /** @test */
    public function service_integration_with_driver_approval_workflow()
    {
        // This test simulates the actual workflow that would happen in the driver approval process

        // Step 1: Create test data
        $brand = VehicleBrand::create(['name_en' => 'BMW', 'name_ar' => 'بي إم دبليو']);
        $model = VehicleModel::create([
            'vehicle_brand_id' => $brand->id,
            'name_en' => 'X5',
            'name_ar' => 'إكس 5',
        ]);

        $economyType = VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $luxuryType = VehicleType::create([
            'name_en' => 'Luxury',
            'name_ar' => 'فاخر',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        // Step 2: Create classification rule for luxury vehicles
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$brand->id],
            'models' => [$model->id],
            'min_year' => 2020,
            'max_year' => 2025,
            'seat_numbers' => [5, 7],
        ]);

        // Step 3: Create vehicle (simulating driver registration)
        $vehicle = Vehicle::create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $economyType->id, // Initially set to economy
            'year' => 2022,
            'seat_number' => 5,
            'license_plate_number' => '4-12345-ليبيا',
            'image' => 'test.jpg',
        ]);

        // Step 4: Admin reviews driver - service provides suggested types
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($vehicle);

        // Step 5: Verify luxury type is suggested first
        $firstKey = array_key_first($suggestedTypes);
        $this->assertEquals($luxuryType->id, $firstKey);
        $this->assertStringContainsString('Suggested', $suggestedTypes[$firstKey]);

        // Step 6: Admin accepts driver with suggested type (simulating form submission)
        $selectedTypeId = $firstKey; // Admin selects the suggested type
        $vehicle->update(['vehicle_type_id' => $selectedTypeId]);

        // Step 7: Verify final state
        $this->assertEquals($luxuryType->id, $vehicle->fresh()->vehicle_type_id);

        // Step 8: Verify auto-classification also works
        $vehicle->update(['vehicle_type_id' => $economyType->id]); // Reset
        $this->assertTrue($this->service->autoClassifyVehicle($vehicle));
        $this->assertEquals($luxuryType->id, $vehicle->fresh()->vehicle_type_id);
    }
}
