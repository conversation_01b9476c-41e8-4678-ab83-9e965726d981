<?php

use App\Models\Rider;
use App\Models\TripRating;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets rider trip_ratings', function () {
    $rider = Rider::factory()->create();
    $tripRatings = TripRating::factory()
        ->count(2)
        ->create([
            'rider_id' => $rider->id,
        ]);

    $response = $this->getJson(route('api.riders.trip-ratings.index', $rider));

    $response->assertOk()->assertSee($tripRatings[0]->trip_review);
});

test('it stores the rider trip_ratings', function () {
    $rider = Rider::factory()->create();
    $data = TripRating::factory()
        ->make([
            'rider_id' => $rider->id,
        ])
        ->toArray();

    $response = $this->postJson(
        route('api.riders.trip-ratings.store', $rider),
        $data
    );

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('trip_ratings', $data);

    $response->assertStatus(201)->assertJsonFragment($data);

    $tripRating = TripRating::latest('id')->first();

    $this->assertEquals($rider->id, $tripRating->rider_id);
});
