<?php

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets drivers list', function () {
    $drivers = Driver::factory()
        ->count(5)
        ->create();

    $response = $this->get(route('api.drivers.index'));

    $response->assertOk()->assertSee($drivers[0]->license_number);
});

test('it stores the driver', function () {
    $data = Driver::factory()
        ->make()
        ->toArray();

    $response = $this->postJson(route('api.drivers.store'), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('drivers', $data);

    $response->assertStatus(201)->assertJsonFragment($data);
});

test('it updates the driver', function () {
    $driver = Driver::factory()->create();

    $user = User::factory()->create();

    $data = [
        'license_number' => fake()->word(),
        'license_expiry' => fake()->word(),
        'national_id_number' => fake()->word(),
        'created_at' => fake()->dateTime(),
        'updated_at' => fake()->dateTime(),
        'user_id' => $user->id,
    ];

    $response = $this->putJson(route('api.drivers.update', $driver), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $data['id'] = $driver->id;

    $this->assertDatabaseHas('drivers', $data);

    $response->assertStatus(200)->assertJsonFragment($data);
});

test('it deletes the driver', function () {
    $driver = Driver::factory()->create();

    $response = $this->deleteJson(route('api.drivers.destroy', $driver));

    $this->assertModelMissing($driver);

    $response->assertNoContent();
});
