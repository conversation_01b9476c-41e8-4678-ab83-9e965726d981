<?php

namespace Tests\Feature\Api;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRuleNonOperationalPeriod;
use App\Models\PricingRules;
use App\Models\Rider;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class TripNonOperationalValidationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected $rider;

    protected $pricingRule;

    protected $dayCharge;

    protected function setUp(): void
    {
        parent::setUp();

        // Create user and rider
        $this->user = User::factory()->create(['email' => '<EMAIL>']);
        $this->rider = Rider::factory()->create(['user_id' => $this->user->id]);

        // Authenticate the user
        Sanctum::actingAs($this->user, [], 'web');

        // Create a pricing rule
        $this->pricingRule = PricingRules::create([
            'global_base_price' => 5.00,
            'global_price_per_km' => 2.00,
            'time_threshold_percentage' => 10.00,
        ]);

        // Create a day charge for Monday
        $this->dayCharge = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $this->pricingRule->id,
            'day' => 'Monday',
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_percentage_charge' => 10.0,
            'night_start_at' => '20:00',
            'night_end_at' => '05:00',
            'night_charge_type' => 'percentage',
            'night_percentage_charge' => 15.0,
        ]);

        // Mock Google Maps API responses
        Http::fake([
            'maps.googleapis.com/*' => Http::response([
                'routes' => [
                    [
                        'legs' => [
                            [
                                'distance' => ['value' => 5000], // 5km
                                'duration' => ['value' => 600], // 10 minutes
                            ],
                        ],
                        'overview_polyline' => ['points' => 'test_polyline_data'],
                    ],
                ],
                'status' => 'OK',
            ], 200),
        ]);
    }

    public function test_trip_creation_blocked_during_non_operational_hours()
    {
        // Set current time to Monday 13:00 (non-operational hours)
        Carbon::setTestNow(Carbon::parse('2025-01-06 13:00:00')); // Monday

        // Create non-operational period from 12:00 to 14:00
        PricingRuleNonOperationalPeriod::create([
            'day_charge_id' => $this->dayCharge->id,
            'start_at' => '12:00',
            'end_at' => '14:00',
        ]);

        $tripData = [
            'departure_location' => [
                'latitude' => 32.8872,
                'longitude' => 13.1913,
                'address' => 'Tripoli, Libya',
            ],
            'arrival_location' => [
                'latitude' => 32.9072,
                'longitude' => 13.2113,
                'address' => 'Benghazi, Libya',
            ],
            'vehicle_category' => 'passenger',
            'number_of_seats' => 4,
        ];

        $response = $this->postJson(route('api.trips.store'), $tripData);

        // Should return validation error
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['non_operational_period']);
        $response->assertJsonFragment([
            'non_operational_period' => ['Trip creation is not available during non-operational hours. Service is unavailable from 12:00 to 14:00.'],
        ]);
    }

    public function test_trip_creation_allowed_during_operational_hours()
    {
        // Set current time to Monday 10:00 (operational hours)
        Carbon::setTestNow(Carbon::parse('2025-01-06 10:00:00')); // Monday

        // Create non-operational period from 12:00 to 14:00
        PricingRuleNonOperationalPeriod::create([
            'day_charge_id' => $this->dayCharge->id,
            'start_at' => '12:00',
            'end_at' => '14:00',
        ]);

        $tripData = [
            'departure_location' => [
                'latitude' => 32.8872,
                'longitude' => 13.1913,
                'address' => 'Tripoli, Libya',
            ],
            'arrival_location' => [
                'latitude' => 32.9072,
                'longitude' => 13.2113,
                'address' => 'Benghazi, Libya',
            ],
            'vehicle_category' => 'passenger',
            'number_of_seats' => 4,
        ];

        $response = $this->postJson(route('api.trips.store'), $tripData);

        // Should succeed since 10:00 is not in non-operational period
        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'status',
                'distance',
            ],
            'message',
        ]);
        $response->assertJson([
            'status' => 'success',
            'message' => 'Trip created successfully',
        ]);
    }

    public function test_trip_creation_allowed_when_no_non_operational_periods_defined()
    {
        // Set current time to Monday 13:00
        Carbon::setTestNow(Carbon::parse('2025-01-06 13:00:00')); // Monday

        // Don't create any non-operational periods

        $tripData = [
            'departure_location' => [
                'latitude' => 32.8872,
                'longitude' => 13.1913,
                'address' => 'Tripoli, Libya',
            ],
            'arrival_location' => [
                'latitude' => 32.9072,
                'longitude' => 13.2113,
                'address' => 'Benghazi, Libya',
            ],
            'vehicle_category' => 'passenger',
            'number_of_seats' => 4,
        ];

        $response = $this->postJson(route('api.trips.store'), $tripData);

        // Should succeed since no non-operational periods are defined
        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'status',
                'distance',
            ],
            'message',
        ]);
        $response->assertJson([
            'status' => 'success',
            'message' => 'Trip created successfully',
        ]);
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset Carbon test time
        parent::tearDown();
    }
}
