<?php

use App\Models\Driver;
use App\Models\Trip;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets driver trips', function () {
    $driver = Driver::factory()->create();
    $trips = Trip::factory()
        ->count(2)
        ->create([
            'driver_id' => $driver->id,
        ]);

    $response = $this->getJson(route('api.drivers.trips.index', $driver));

    $response->assertOk()->assertSee($trips[0]->created_at);
});

test('it stores the driver trips', function () {
    $driver = Driver::factory()->create();
    $data = Trip::factory()
        ->make([
            'driver_id' => $driver->id,
        ])
        ->toArray();

    $response = $this->postJson(
        route('api.drivers.trips.store', $driver),
        $data
    );

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('trips', $data);

    $response->assertStatus(201)->assertJsonFragment($data);

    $trip = Trip::latest('id')->first();

    $this->assertEquals($driver->id, $trip->driver_id);
});
