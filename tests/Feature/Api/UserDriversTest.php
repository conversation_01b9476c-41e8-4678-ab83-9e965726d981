<?php

use App\Models\Driver;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets user drivers', function () {
    $user = User::factory()->create();
    $drivers = Driver::factory()
        ->count(2)
        ->create([
            'user_id' => $user->id,
        ]);

    $response = $this->getJson(route('api.users.drivers.index', $user));

    $response->assertOk()->assertSee($drivers[0]->license_number);
});

test('it stores the user drivers', function () {
    $user = User::factory()->create();
    $data = Driver::factory()
        ->make([
            'user_id' => $user->id,
        ])
        ->toArray();

    $response = $this->postJson(route('api.users.drivers.store', $user), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('drivers', $data);

    $response->assertStatus(201)->assertJsonFragment($data);

    $driver = Driver::latest('id')->first();

    $this->assertEquals($user->id, $driver->user_id);
});
