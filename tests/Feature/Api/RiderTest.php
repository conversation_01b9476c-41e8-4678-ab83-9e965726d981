<?php

use App\Models\Rider;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets riders list', function () {
    $riders = Rider::factory()
        ->count(5)
        ->create();

    $response = $this->get(route('api.riders.index'));

    $response->assertOk()->assertSee($riders[0]->created_at);
});

test('it stores the rider', function () {
    $data = Rider::factory()
        ->make()
        ->toArray();

    $response = $this->postJson(route('api.riders.store'), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('riders', $data);

    $response->assertStatus(201)->assertJsonFragment($data);
});

test('it updates the rider', function () {
    $rider = Rider::factory()->create();

    $user = User::factory()->create();

    $data = [
        'created_at' => fake()->dateTime(),
        'updated_at' => fake()->dateTime(),
        'user_id' => $user->id,
    ];

    $response = $this->putJson(route('api.riders.update', $rider), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $data['id'] = $rider->id;

    $this->assertDatabaseHas('riders', $data);

    $response->assertStatus(200)->assertJsonFragment($data);
});

test('it deletes the rider', function () {
    $rider = Rider::factory()->create();

    $response = $this->deleteJson(route('api.riders.destroy', $rider));

    $this->assertModelMissing($rider);

    $response->assertNoContent();
});
