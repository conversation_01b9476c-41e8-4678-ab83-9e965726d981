<?php

use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets driver vehicles', function () {
    $driver = Driver::factory()->create();
    $vehicle = Vehicle::factory()->create();

    $driver->vehicles()->attach($vehicle);

    $response = $this->getJson(route('api.drivers.vehicles.index', $driver));

    $response->assertOk()->assertSee($vehicle->license_plate_number);
});

test('it can attach vehicles to driver', function () {
    $driver = Driver::factory()->create();
    $vehicle = Vehicle::factory()->create();

    $response = $this->postJson(
        route('api.drivers.vehicles.store', [$driver, $vehicle])
    );

    $response->assertNoContent();

    $this->assertTrue(
        $driver
            ->vehicles()
            ->where('vehicles.id', $vehicle->id)
            ->exists()
    );
});

test('it can detach vehicles from driver', function () {
    $driver = Driver::factory()->create();
    $vehicle = Vehicle::factory()->create();

    $response = $this->deleteJson(
        route('api.drivers.vehicles.store', [$driver, $vehicle])
    );

    $response->assertNoContent();

    $this->assertFalse(
        $driver
            ->vehicles()
            ->where('vehicles.id', $vehicle->id)
            ->exists()
    );
});
