<?php

use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets vehicle drivers', function () {
    $vehicle = Vehicle::factory()->create();
    $driver = Driver::factory()->create();

    $vehicle->drivers()->attach($driver);

    $response = $this->getJson(route('api.vehicles.drivers.index', $vehicle));

    $response->assertOk()->assertSee($driver->license_number);
});

test('it can attach drivers to vehicle', function () {
    $vehicle = Vehicle::factory()->create();
    $driver = Driver::factory()->create();

    $response = $this->postJson(
        route('api.vehicles.drivers.store', [$vehicle, $driver])
    );

    $response->assertNoContent();

    $this->assertTrue(
        $vehicle
            ->drivers()
            ->where('drivers.id', $driver->id)
            ->exists()
    );
});

test('it can detach drivers from vehicle', function () {
    $vehicle = Vehicle::factory()->create();
    $driver = Driver::factory()->create();

    $response = $this->deleteJson(
        route('api.vehicles.drivers.store', [$vehicle, $driver])
    );

    $response->assertNoContent();

    $this->assertFalse(
        $vehicle
            ->drivers()
            ->where('drivers.id', $driver->id)
            ->exists()
    );
});
