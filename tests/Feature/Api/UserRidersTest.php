<?php

use App\Models\Rider;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets user riders', function () {
    $user = User::factory()->create();
    $riders = Rider::factory()
        ->count(2)
        ->create([
            'user_id' => $user->id,
        ]);

    $response = $this->getJson(route('api.users.riders.index', $user));

    $response->assertOk()->assertSee($riders[0]->created_at);
});

test('it stores the user riders', function () {
    $user = User::factory()->create();
    $data = Rider::factory()
        ->make([
            'user_id' => $user->id,
        ])
        ->toArray();

    $response = $this->postJson(route('api.users.riders.store', $user), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('riders', $data);

    $response->assertStatus(201)->assertJsonFragment($data);

    $rider = Rider::latest('id')->first();

    $this->assertEquals($user->id, $rider->user_id);
});
