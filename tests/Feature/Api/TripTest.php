<?php

use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\Zone;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets trips list', function () {
    $trips = Trip::factory()
        ->count(5)
        ->create();

    $response = $this->get(route('api.trips.index'));

    $response->assertOk()->assertSee($trips[0]->created_at);
});

test('it stores the trip', function () {
    $data = Trip::factory()
        ->make()
        ->toArray();

    $response = $this->postJson(route('api.trips.store'), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('trips', $data);

    $response->assertStatus(201)->assertJsonFragment($data);
});

test('it updates the trip', function () {
    $trip = Trip::factory()->create();

    $rider = Rider::factory()->create();
    $driver = Driver::factory()->create();
    $vehicle = Vehicle::factory()->create();
    $zone = Zone::factory()->create();
    $zone = Zone::factory()->create();

    $data = [
        'status' => fake()->word(),
        'departure_lat' => fake()->word(),
        'departure_lng' => fake()->word(),
        'arrival_lat' => fake()->word(),
        'arrival_lng' => fake()->word(),
        'distance' => fake()->randomFloat(2, 0, 9999),
        'estimated_departure_time' => fake()->word(),
        'actual_departure_time' => fake()->word(),
        'estimated_arrival_time' => fake()->word(),
        'actual_arrival_time' => fake()->word(),
        'pricing_breakdown' => fake()->word(),
        'created_at' => fake()->dateTime(),
        'updated_at' => fake()->dateTime(),
        'rider_id' => $rider->id,
        'driver_id' => $driver->id,
        'vehicle_id' => $vehicle->id,
        'departure_zone_id' => $zone->id,
        'arrival_zone_id' => $zone->id,
    ];

    $response = $this->putJson(route('api.trips.update', $trip), $data);

    unset($data['created_at']);
    unset($data['updated_at']);

    $data['id'] = $trip->id;

    $this->assertDatabaseHas('trips', $data);

    $response->assertStatus(200)->assertJsonFragment($data);
});

test('it deletes the trip', function () {
    $trip = Trip::factory()->create();

    $response = $this->deleteJson(route('api.trips.destroy', $trip));

    $this->assertModelMissing($trip);

    $response->assertNoContent();
});
