<?php

use App\Models\Driver;
use App\Models\DriverAvailability;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;

uses(RefreshDatabase::class, WithFaker::class);

beforeEach(function () {
    $this->withoutExceptionHandling();

    $user = User::factory()->create(['email' => '<EMAIL>']);

    Sanctum::actingAs($user, [], 'web');
});

test('it gets driver driver_availabilities', function () {
    $driver = Driver::factory()->create();
    $driverAvailabilities = DriverAvailability::factory()
        ->count(2)
        ->create([
            'driver_id' => $driver->id,
        ]);

    $response = $this->getJson(
        route('api.drivers.driver-availabilities.index', $driver)
    );

    $response->assertOk()->assertSee($driverAvailabilities[0]->notes);
});

test('it stores the driver driver_availabilities', function () {
    $driver = Driver::factory()->create();
    $data = DriverAvailability::factory()
        ->make([
            'driver_id' => $driver->id,
        ])
        ->toArray();

    $response = $this->postJson(
        route('api.drivers.driver-availabilities.store', $driver),
        $data
    );

    unset($data['created_at']);
    unset($data['updated_at']);

    $this->assertDatabaseHas('driver_availabilities', $data);

    $response->assertStatus(201)->assertJsonFragment($data);

    $driverAvailability = DriverAvailability::latest('id')->first();

    $this->assertEquals($driver->id, $driverAvailability->driver_id);
});
