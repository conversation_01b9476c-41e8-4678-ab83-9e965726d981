<?php

namespace Tests\Feature;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\VehicleTypesCategories;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use App\Services\VehicleClassificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleClassificationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    private VehicleClassificationService $service;
    private VehicleBrand $toyotaBrand;
    private VehicleModel $camryModel;
    private VehicleType $economyType;
    private VehicleType $comfortType;
    private VehicleType $luxuryType;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new VehicleClassificationService();
        
        // Create test data
        $this->toyotaBrand = VehicleBrand::create(['name_en' => 'Toyota', 'name_ar' => 'تويوتا']);
        $this->camryModel = VehicleModel::create([
            'vehicle_brand_id' => $this->toyotaBrand->id,
            'name_en' => 'Camry',
            'name_ar' => 'كامري'
        ]);
        
        // Create vehicle types
        $this->economyType = VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true
        ]);
        
        $this->comfortType = VehicleType::create([
            'name_en' => 'Comfort',
            'name_ar' => 'راحة',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true
        ]);
        
        $this->luxuryType = VehicleType::create([
            'name_en' => 'Luxury',
            'name_ar' => 'فاخر',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true
        ]);
    }

    /** @test */
    public function complete_vehicle_classification_workflow()
    {
        // Step 1: Create classification rules
        $comfortRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $comfortRule->qualifications()->create([
            'brands' => [$this->toyotaBrand->id],
            'models' => [$this->camryModel->id],
            'min_year' => 2018,
            'max_year' => 2022,
            'seat_numbers' => [4, 5]
        ]);
        
        $luxuryRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $luxuryRule->qualifications()->create([
            'brands' => [$this->toyotaBrand->id],
            'models' => [$this->camryModel->id],
            'min_year' => 2023,
            'max_year' => 2025,
            'seat_numbers' => [4, 5]
        ]);
        
        // Step 2: Test vehicle classification
        
        // Vehicle that should be classified as Comfort (2020 Toyota Camry)
        $comfortVehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id, // Start with economy
            'year' => 2020,
            'seat_number' => 4,
            'license_plate_number' => '1-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        $classifiedTypeId = $this->service->classifyVehicle($comfortVehicle);
        $this->assertEquals($this->comfortType->id, $classifiedTypeId);
        
        // Vehicle that should be classified as Luxury (2024 Toyota Camry)
        $luxuryVehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2024,
            'seat_number' => 5,
            'license_plate_number' => '2-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        $classifiedTypeId = $this->service->classifyVehicle($luxuryVehicle);
        $this->assertEquals($this->luxuryType->id, $classifiedTypeId);
        
        // Vehicle that doesn't match any rules (2015 Toyota Camry - too old)
        $economyVehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2015,
            'seat_number' => 4,
            'license_plate_number' => '3-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        $classifiedTypeId = $this->service->classifyVehicle($economyVehicle);
        $this->assertEquals($this->economyType->id, $classifiedTypeId); // Should default to Economy
        
        // Step 3: Test auto-classification
        $this->assertTrue($this->service->autoClassifyVehicle($comfortVehicle));
        $this->assertEquals($this->comfortType->id, $comfortVehicle->fresh()->vehicle_type_id);
        
        // Step 4: Test suggested vehicle types for driver approval
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($luxuryVehicle);
        
        // The luxury type should be first and marked as suggested
        $firstKey = array_key_first($suggestedTypes);
        $this->assertEquals($this->luxuryType->id, $firstKey);
        $this->assertStringContainsString('⭐', $suggestedTypes[$firstKey]);
        $this->assertStringContainsString('Suggested', $suggestedTypes[$firstKey]);
        
        // Other types should also be available
        $this->assertArrayHasKey($this->comfortType->id, $suggestedTypes);
        $this->assertArrayHasKey($this->economyType->id, $suggestedTypes);
    }

    /** @test */
    public function driver_approval_integration_with_classification()
    {
        // Create a driver with a vehicle
        $user = User::create([
            'name' => 'Test',
            'last_name' => 'Driver',
            'phone_number' => '+218912345678',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'type' => 'driver',
            'gender' => 'male'
        ]);
        
        $driver = Driver::create([
            'user_id' => $user->id,
            'id_number' => '123456789012',
            'global_status' => DriverGlobalStatus::in_progress
        ]);
        
        $vehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2021,
            'seat_number' => 4,
            'license_plate_number' => '4-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        $driver->vehicles()->attach($vehicle);
        
        // Create classification rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $rule->qualifications()->create([
            'brands' => [$this->toyotaBrand->id],
            'models' => [$this->camryModel->id],
            'min_year' => 2020,
            'max_year' => 2025,
            'seat_numbers' => [4]
        ]);
        
        // Test that the service suggests the correct type
        $suggestedTypes = $this->service->getSuggestedVehicleTypes($vehicle);
        $firstKey = array_key_first($suggestedTypes);
        $this->assertEquals($this->comfortType->id, $firstKey);
        
        // Simulate driver approval with the suggested type
        $vehicle->update(['vehicle_type_id' => $firstKey]);
        $driver->update(['global_status' => DriverGlobalStatus::active]);
        
        // Verify the final state
        $this->assertEquals(DriverGlobalStatus::active, $driver->fresh()->global_status);
        $this->assertEquals($this->comfortType->id, $vehicle->fresh()->vehicle_type_id);
    }

    /** @test */
    public function classification_handles_multiple_rules_correctly()
    {
        // Create overlapping rules with different priorities
        $rule1 = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $rule1->qualifications()->create([
            'brands' => [$this->toyotaBrand->id],
            'min_year' => 2018,
            'max_year' => 2025,
            'seat_numbers' => [4, 5]
        ]);
        
        $rule2 = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $rule2->qualifications()->create([
            'models' => [$this->camryModel->id],
            'min_year' => 2020,
            'max_year' => 2025,
            'seat_numbers' => [4]
        ]);
        
        // Vehicle that matches both rules
        $vehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2021,
            'seat_number' => 4,
            'license_plate_number' => '5-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        // Should return the first matching rule (Comfort)
        $classifiedTypeId = $this->service->classifyVehicle($vehicle);
        $this->assertEquals($this->comfortType->id, $classifiedTypeId);
    }

    /** @test */
    public function classification_works_with_partial_criteria()
    {
        // Create rule with only year and seat criteria (no brand/model restriction)
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger
        ]);
        
        $rule->qualifications()->create([
            'min_year' => 2022,
            'max_year' => 2025,
            'seat_numbers' => [6, 7, 8] // Luxury vehicles with more seats
        ]);
        
        // Vehicle with 6 seats from 2023 (any brand/model)
        $vehicle = Vehicle::create([
            'vehicle_model_id' => $this->camryModel->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2023,
            'seat_number' => 6,
            'license_plate_number' => '6-12345-ليبيا',
            'image' => 'test.jpg'
        ]);
        
        $classifiedTypeId = $this->service->classifyVehicle($vehicle);
        $this->assertEquals($this->luxuryType->id, $classifiedTypeId);
    }
}
