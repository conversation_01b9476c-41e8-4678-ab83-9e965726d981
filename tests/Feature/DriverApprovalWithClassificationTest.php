<?php

namespace Tests\Feature;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\VehicleTypesCategories;
use App\Filament\Resources\Panel\DriverResource;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationQualification;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class DriverApprovalWithClassificationTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    private Driver $driver;

    private Vehicle $vehicle;

    private VehicleType $economyType;

    private VehicleType $comfortType;

    private VehicleType $luxuryType;

    private VehicleBrand $brand;

    private VehicleModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);

        // Create test data
        $this->brand = VehicleBrand::factory()->create(['name_en' => 'Toyota']);
        $this->model = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Camry',
        ]);

        // Create vehicle types
        $this->economyType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->comfortType = VehicleType::factory()->create([
            'name_en' => 'Comfort',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->luxuryType = VehicleType::factory()->create([
            'name_en' => 'Luxury',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        // Create driver with in_progress status
        $this->driver = Driver::factory()->create([
            'global_status' => DriverGlobalStatus::in_progress,
        ]);

        // Create vehicle for the driver
        $this->vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2020,
            'seat_number' => 4,
        ]);

        // Associate vehicle with driver
        $this->driver->vehicles()->attach($this->vehicle);
    }

    /** @test */
    public function driver_approval_shows_suggested_vehicle_type_first()
    {
        $this->actingAs($this->admin);

        // Create classification rule that matches the vehicle
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        VehicleClassificationQualification::create([
            'rule_id' => $rule->id,
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Test the driver view page
        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->assertSuccessful()
            ->callAction('accept')
            ->assertActionHasForm('accept', [
                'vehicle_type_id' => null,
            ]);

        // The form should show the suggested type first
        // We can't easily test the exact order in Livewire, but we can verify the service works
        $classificationService = app(\App\Services\VehicleClassificationService::class);
        $options = $classificationService->getSuggestedVehicleTypes($this->vehicle);

        $firstKey = array_key_first($options);
        $this->assertEquals($this->comfortType->id, $firstKey);
        $this->assertStringContainsString('⭐', $options[$firstKey]);
        $this->assertStringContainsString('Suggested', $options[$firstKey]);
    }

    /** @test */
    public function driver_approval_updates_vehicle_type_correctly()
    {
        $this->actingAs($this->admin);

        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->callAction('accept', [
                'vehicle_type_id' => $this->luxuryType->id,
            ])
            ->assertSuccessful();

        // Check that driver status was updated
        $this->assertEquals(DriverGlobalStatus::active, $this->driver->fresh()->global_status);

        // Check that vehicle type was updated
        $this->assertEquals($this->luxuryType->id, $this->vehicle->fresh()->vehicle_type_id);
    }

    /** @test */
    public function driver_approval_works_without_classification_rules()
    {
        $this->actingAs($this->admin);

        // No classification rules exist
        $this->assertCount(0, VehicleClassificationRule::all());

        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->callAction('accept', [
                'vehicle_type_id' => $this->comfortType->id,
            ])
            ->assertSuccessful();

        // Check that driver and vehicle were updated correctly
        $this->assertEquals(DriverGlobalStatus::active, $this->driver->fresh()->global_status);
        $this->assertEquals($this->comfortType->id, $this->vehicle->fresh()->vehicle_type_id);
    }

    /** @test */
    public function driver_approval_handles_vehicle_without_model_gracefully()
    {
        $this->actingAs($this->admin);

        // Create vehicle without model
        $vehicleWithoutModel = Vehicle::factory()->create([
            'vehicle_model_id' => null,
            'vehicle_type_id' => $this->economyType->id,
        ]);

        $this->driver->vehicles()->detach();
        $this->driver->vehicles()->attach($vehicleWithoutModel);

        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->callAction('accept', [
                'vehicle_type_id' => $this->comfortType->id,
            ])
            ->assertSuccessful();

        // Should still work, just without classification
        $this->assertEquals(DriverGlobalStatus::active, $this->driver->fresh()->global_status);
        $this->assertEquals($this->comfortType->id, $vehicleWithoutModel->fresh()->vehicle_type_id);
    }

    /** @test */
    public function classification_service_is_used_during_approval_process()
    {
        $this->actingAs($this->admin);

        // Create classification rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        VehicleClassificationQualification::create([
            'rule_id' => $rule->id,
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        // Mock the service to verify it's called
        $mockService = $this->mock(\App\Services\VehicleClassificationService::class);
        $mockService->shouldReceive('getSuggestedVehicleTypes')
            ->once()
            ->with(\Mockery::type(Vehicle::class))
            ->andReturn([
                $this->luxuryType->id => '⭐ Luxury (Suggested)',
                $this->comfortType->id => 'Comfort',
                $this->economyType->id => 'Economy',
            ]);

        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->assertSuccessful();
    }

    /** @test */
    public function freight_vehicle_classification_works_in_approval()
    {
        // Create freight vehicle types
        $lightCoveredType = VehicleType::factory()->create([
            'name_en' => 'Light Covered Truck',
            'category' => VehicleTypesCategories::Freight,
            'is_covered' => true,
            'weight_category' => 'less_than_1000kg',
            'status' => true,
        ]);

        // Create freight vehicle
        $freightVehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $lightCoveredType->id,
            'is_covered' => true,
            'weight_category' => 'less_than_1000kg',
        ]);

        $this->driver->vehicles()->detach();
        $this->driver->vehicles()->attach($freightVehicle);

        // Create classification rule for freight
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $lightCoveredType->id,
            'category' => VehicleTypesCategories::Freight,
        ]);

        VehicleClassificationQualification::create([
            'rule_id' => $rule->id,
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2015,
            'max_year' => 2025,
            'is_covered' => true,
            'weight_category' => 'less_than_1000kg',
        ]);

        $this->actingAs($this->admin);

        Livewire::test(DriverResource\Pages\ViewDriver::class, [
            'record' => $this->driver->getRouteKey(),
        ])
            ->callAction('accept', [
                'vehicle_type_id' => $lightCoveredType->id,
            ])
            ->assertSuccessful();

        $this->assertEquals(DriverGlobalStatus::active, $this->driver->fresh()->global_status);
        $this->assertEquals($lightCoveredType->id, $freightVehicle->fresh()->vehicle_type_id);
    }
}
