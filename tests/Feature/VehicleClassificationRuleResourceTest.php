<?php

namespace Tests\Feature;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use App\Models\User;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class VehicleClassificationRuleResourceTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    private VehicleType $economyType;

    private VehicleType $comfortType;

    private VehicleType $lightCoveredType;

    private VehicleBrand $brand;

    private VehicleModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);

        // Create test data
        $this->brand = VehicleBrand::factory()->create(['name_en' => 'Toyota']);
        $this->model = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Camry',
        ]);

        $this->economyType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->comfortType = VehicleType::factory()->create([
            'name_en' => 'Comfort',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->lightCoveredType = VehicleType::factory()->create([
            'name_en' => 'Light Covered Truck',
            'category' => VehicleTypesCategories::Freight,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg,
            'status' => true,
        ]);
    }

    /** @test */
    public function admin_can_view_vehicle_classification_rules_index()
    {
        $this->actingAs($this->admin);

        // Create some test rules
        VehicleClassificationRule::factory()->count(3)->create();

        Livewire::test(VehicleClassificationRuleResource\Pages\ListVehicleClassificationRules::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords(VehicleClassificationRule::all());
    }

    /** @test */
    public function admin_can_create_passenger_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->comfortType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2018,
                        'max_year' => 2024,
                        'seat_numbers' => [4, 5],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger->value,
        ]);

        $this->assertDatabaseHas('vehicle_classification_qualifications', [
            'min_year' => 2018,
            'max_year' => 2024,
        ]);
    }

    /** @test */
    public function admin_can_create_freight_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Freight->value,
                'vehicle_type_id' => $this->lightCoveredType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2015,
                        'max_year' => 2025,
                        'is_covered' => true,
                        'weight_category' => 'less_than_1000kg',
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'vehicle_type_id' => $this->lightCoveredType->id,
            'category' => VehicleTypesCategories::Freight->value,
        ]);

        $this->assertDatabaseHas('vehicle_classification_qualifications', [
            'is_covered' => true,
            'weight_category' => 'less_than_1000kg',
        ]);
    }

    /** @test */
    public function admin_can_edit_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        $rule = VehicleClassificationRule::factory()->create([
            'vehicle_type_id' => $this->economyType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        Livewire::test(VehicleClassificationRuleResource\Pages\EditVehicleClassificationRule::class, [
            'record' => $rule->getRouteKey(),
        ])
            ->fillForm([
                'vehicle_type_id' => $this->comfortType->id,
            ])
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'id' => $rule->id,
            'vehicle_type_id' => $this->comfortType->id,
        ]);
    }

    /** @test */
    public function admin_can_delete_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        $rule = VehicleClassificationRule::factory()->create();

        Livewire::test(VehicleClassificationRuleResource\Pages\ListVehicleClassificationRules::class)
            ->callTableAction('delete', $rule)
            ->assertSuccessful();

        $this->assertSoftDeleted('vehicle_classification_rules', [
            'id' => $rule->id,
        ]);
    }

    /** @test */
    public function vehicle_type_options_are_filtered_by_category()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
            ])
            ->assertFormFieldExists('vehicle_type_id')
            ->assertFormFieldIsVisible('vehicle_type_id');
    }

    /** @test */
    public function qualification_fields_are_shown_based_on_category()
    {
        $this->actingAs($this->admin);

        // Test passenger category shows seat numbers
        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->economyType->id,
            ])
            ->assertFormFieldExists('qualifications.0.seat_numbers')
            ->assertFormFieldIsVisible('qualifications.0.seat_numbers');

        // Test freight category shows covered and weight fields
        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Freight->value,
                'vehicle_type_id' => $this->lightCoveredType->id,
            ])
            ->assertFormFieldExists('qualifications.0.is_covered')
            ->assertFormFieldExists('qualifications.0.weight_category')
            ->assertFormFieldIsVisible('qualifications.0.is_covered')
            ->assertFormFieldIsVisible('qualifications.0.weight_category');
    }

    /** @test */
    public function validation_prevents_invalid_year_ranges()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->economyType->id,
                'qualifications' => [
                    [
                        'min_year' => 2025,
                        'max_year' => 2020, // Invalid: max < min
                        'seat_numbers' => [4],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasFormErrors(['qualifications.0.max_year']);
    }

    /** @test */
    public function multiple_qualifications_can_be_added_to_single_rule()
    {
        $this->actingAs($this->admin);

        $secondBrand = VehicleBrand::factory()->create(['name_en' => 'Honda']);
        $secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $secondBrand->id,
            'name_en' => 'Accord',
        ]);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->comfortType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2018,
                        'max_year' => 2024,
                        'seat_numbers' => [4, 5],
                    ],
                    [
                        'brands' => [$secondBrand->id],
                        'models' => [$secondModel->id],
                        'min_year' => 2020,
                        'max_year' => 2024,
                        'seat_numbers' => [4],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $rule = VehicleClassificationRule::latest()->first();
        $this->assertCount(2, $rule->qualifications()->get());
    }
}
