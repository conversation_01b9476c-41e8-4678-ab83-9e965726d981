<?php

namespace Tests\Performance;

use App\Services\VehicleTrackingService;
use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use App\Enums\UserStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Enums\Drivers\DriverGlobalStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class VehicleTrackingPerformanceTest extends TestCase
{
    use RefreshDatabase;

    public function test_performance_with_10_vehicles()
    {
        $this->createVehicles(10);
        $this->assertQueryPerformance(10, 500); // 500ms max for 10 vehicles
    }

    public function test_performance_with_50_vehicles()
    {
        $this->createVehicles(50);
        $this->assertQueryPerformance(50, 1000); // 1s max for 50 vehicles
    }

    public function test_performance_with_100_vehicles()
    {
        $this->createVehicles(100);
        $this->assertQueryPerformance(100, 2000); // 2s max for 100 vehicles
    }

    public function test_memory_usage_with_large_dataset()
    {
        $memoryBefore = memory_get_usage(true);
        
        $this->createVehicles(100);
        
        $startTime = microtime(true);
        $vehicles = VehicleTrackingService::getOptimizedVehicleData();
        $endTime = microtime(true);
        
        $memoryAfter = memory_get_usage(true);
        $memoryUsed = $memoryAfter - $memoryBefore;
        
        // Assert memory usage is reasonable (less than 50MB for 100 vehicles)
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed, 'Memory usage should be less than 50MB');
        
        // Assert execution time
        $executionTime = ($endTime - $startTime) * 1000;
        $this->assertLessThan(2000, $executionTime, 'Query should complete within 2 seconds');
        
        // Assert data integrity
        $this->assertCount(100, $vehicles);
        $this->assertValidVehicleStructure($vehicles[0]);
    }

    public function test_query_count_optimization()
    {
        $this->createVehicles(20);
        
        // Enable query logging
        DB::enableQueryLog();
        
        $vehicles = VehicleTrackingService::getOptimizedVehicleData();
        
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        
        // Should use minimal queries due to eager loading
        $this->assertLessThan(10, $queryCount, 'Should use less than 10 queries for any number of vehicles');
        
        // Verify data completeness
        $this->assertNotEmpty($vehicles);
        foreach ($vehicles as $vehicle) {
            $this->assertValidVehicleStructure($vehicle);
        }
    }

    private function createVehicles(int $count): void
    {
        for ($i = 0; $i < $count; $i++) {
            $user = User::factory()->create([
                'status' => UserStatus::ONLINE
            ]);
            
            $driver = Driver::factory()->create([
                'user_id' => $user->id,
                'global_status' => DriverGlobalStatus::active,
                'location' => DB::raw("ST_GeomFromText('POINT(" . (13.1913 + $i * 0.001) . " " . (32.8872 + $i * 0.001) . ")', 4326)")
            ]);
            
            $brand = VehicleBrand::factory()->create();
            $model = VehicleModel::factory()->create(['vehicle_brand_id' => $brand->id]);
            $vehicleType = VehicleType::factory()->create();
            
            $vehicle = Vehicle::factory()->create([
                'vehicle_model_id' => $model->id,
                'vehicle_type_id' => $vehicleType->id,
                'global_status' => VehicleStatus::active,
                'status' => VehicleStatus::active,
            ]);

            $vehicle->drivers()->attach($driver);
        }
    }

    private function assertQueryPerformance(int $vehicleCount, int $maxTimeMs): void
    {
        $startTime = microtime(true);
        $vehicles = VehicleTrackingService::getOptimizedVehicleData();
        $endTime = microtime(true);
        
        $executionTime = ($endTime - $startTime) * 1000;
        
        $this->assertLessThan($maxTimeMs, $executionTime, 
            "Query should complete within {$maxTimeMs}ms for {$vehicleCount} vehicles. Actual: {$executionTime}ms");
        
        $this->assertCount($vehicleCount, $vehicles, "Should return all {$vehicleCount} vehicles");
        
        // Verify data structure
        if (!empty($vehicles)) {
            $this->assertValidVehicleStructure($vehicles[0]);
        }
    }

    private function assertValidVehicleStructure(array $vehicle): void
    {
        $this->assertArrayHasKey('id', $vehicle);
        $this->assertArrayHasKey('lat', $vehicle);
        $this->assertArrayHasKey('lng', $vehicle);
        $this->assertArrayHasKey('driver', $vehicle);
        $this->assertArrayHasKey('vehicle', $vehicle);
        $this->assertArrayHasKey('status', $vehicle);
        
        // Verify driver structure
        $this->assertIsArray($vehicle['driver']);
        $this->assertArrayHasKey('id', $vehicle['driver']);
        $this->assertArrayHasKey('name', $vehicle['driver']);
        $this->assertArrayHasKey('phone', $vehicle['driver']);
        
        // Verify vehicle structure
        $this->assertIsArray($vehicle['vehicle']);
        $this->assertArrayHasKey('model', $vehicle['vehicle']);
        $this->assertArrayHasKey('brand', $vehicle['vehicle']);
        $this->assertArrayHasKey('type', $vehicle['vehicle']);
    }
}
