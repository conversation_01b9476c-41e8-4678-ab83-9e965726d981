<?php

namespace Tests\Unit;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Services\Pricing\Factors\TimePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class TimePricingTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a global pricing rule
        \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        $this->globalRules = new GlobalPricingRules;
    }

    public function test_day_time_pricing_calculation()
    {
        // Create a global pricing rule again (since RefreshDatabase is used)
        $globalRule = \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        // Create a time pricing rule
        $timeRule = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $globalRule->id,
            'day' => 'Monday',
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_fixed_charge' => 0.0, // Not used for percentage
            'day_percentage_charge' => 10.0, // 10% of base fare
            'day_distance_charge_type' => 'percentage',
            'day_distance_fixed_charge' => 0.0, // Not used for percentage
            'day_distance_percentage_charge' => 5.0, // 5% of distance fare
            'night_start_at' => '18:00',
            'night_end_at' => '06:00',
            'night_charge_type' => 'fixed',
            'night_fixed_charge' => 10.0, // Fixed 10.0 additional
            'night_percentage_charge' => 0.0, // Not used for fixed
            'night_distance_charge_type' => 'fixed',
            'night_distance_fixed_charge' => 1.5, // Fixed 1.5 additional per km
            'night_distance_percentage_charge' => 0.0, // Not used for fixed
        ]);

        // Create the pricing factor with a Monday daytime
        $factor = new TimePricingFactor(Carbon::parse('2023-05-01 12:00:00')); // Monday at noon

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Day time pricing calculation test result', [
            'adjustment' => $adjustment,
            'time_rule' => $timeRule->toArray(),
        ]);

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(5.0, $adjustment['base_fare']); // 50 * 0.1 = 5.0
        $this->assertEquals(0.25, $adjustment['per_km']); // 5 * 0.05 = 0.25
        $this->assertEquals(7.5, $adjustment['total']); // 5.0 + (0.25 * 10) = 7.5

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Log the result for debugging
        Log::info('Day time pricing calculator test result', [
            'result' => $result->toArray(),
        ]);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values
        $expectedBaseFareAdjustment = $globalBaseFare * 0.1; // 10% of base fare
        $expectedDistanceFareAdjustment = $globalDistanceFare * 0.05; // 5% of distance fare
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }

    public function test_night_time_pricing_calculation()
    {
        // Create a global pricing rule again (since RefreshDatabase is used)
        $globalRule = \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        // Create a time pricing rule
        $timeRule = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $globalRule->id,
            'day' => 'Tuesday', // Use a different day
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_fixed_charge' => 0.0, // Not used for percentage
            'day_percentage_charge' => 10.0, // 10% of base fare
            'day_distance_charge_type' => 'percentage',
            'day_distance_fixed_charge' => 0.0, // Not used for percentage
            'day_distance_percentage_charge' => 5.0, // 5% of distance fare
            'night_start_at' => '18:00',
            'night_end_at' => '06:00',
            'night_charge_type' => 'fixed',
            'night_fixed_charge' => 10.0, // Fixed 10.0 additional
            'night_percentage_charge' => 0.0, // Not used for fixed
            'night_distance_charge_type' => 'fixed',
            'night_distance_fixed_charge' => 1.5, // Fixed 1.5 additional per km
            'night_distance_percentage_charge' => 0.0, // Not used for fixed
        ]);

        // Create the pricing factor with a Tuesday night time
        $factor = new TimePricingFactor(Carbon::parse('2023-05-02 20:00:00')); // Tuesday at 8 PM

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Night time pricing calculation test result', [
            'adjustment' => $adjustment,
            'time_rule' => $timeRule->toArray(),
        ]);

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(10.0, $adjustment['base_fare']); // Fixed 10.0
        $this->assertEquals(1.5, $adjustment['per_km']); // Fixed 1.5
        $this->assertEquals(25.0, $adjustment['total']); // 10.0 + (1.5 * 10) = 25.0

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Log the result for debugging
        Log::info('Night time pricing calculator test result', [
            'result' => $result->toArray(),
        ]);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values
        $expectedBaseFareAdjustment = 10.0; // Fixed 10.0
        $expectedDistanceFareAdjustment = 1.5; // Fixed 1.5
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }
}
