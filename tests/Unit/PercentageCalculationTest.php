<?php

namespace Tests\Unit;

use App\Models\VehicleType;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class PercentageCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->globalRules = new GlobalPricingRules();
    }

    public function test_vehicle_type_percentage_calculation()
    {
        // Create a vehicle type with percentage adjustment
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Percentage Type',
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'additional_base_fare' => 0.0, // Not used for percentage
            'additional_price_per_km' => 0.0, // Not used for percentage
            'base_fare_adjustment' => 20.0, // 20% of base fare
            'distance_fare_adjustment' => 15.0, // 15% of distance fare
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );
        
        // Log the adjustment for debugging
        Log::info('Percentage calculation test result', [
            'adjustment' => $adjustment,
            'vehicle_type' => $vehicleType->toArray(),
        ]);
        
        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(10.0, $adjustment['base_fare']); // 50 * 0.2 = 10
        $this->assertEquals(0.75, $adjustment['per_km']); // 5 * 0.15 = 0.75
        $this->assertEquals(17.5, $adjustment['total']); // 10 + (0.75 * 10) = 17.5
        $this->assertEquals('percentage', $adjustment['base_fare_adjustment_type']);
        $this->assertEquals('percentage', $adjustment['distance_fare_adjustment_type']);
    }
}
