<?php

namespace Tests\Unit;

use App\Models\VehicleType;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleTypePricingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test vehicle type pricing with fixed adjustment type
     */
    public function test_vehicle_type_pricing_with_fixed_adjustment(): void
    {
        // Create a vehicle type with fixed adjustment
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Vehicle Fixed',
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
            'additional_base_fare' => 10.0, // Fixed value for base fare
            'additional_price_per_km' => 2.0, // Fixed value for distance fare
            'base_fare_adjustment' => 0.0, // Not used for fixed type
            'distance_fare_adjustment' => 0.0, // Not used for fixed type
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules()
        );
        
        // Assert the results
        $this->assertEquals(10.0, $adjustment['base_fare']);
        $this->assertEquals(2.0, $adjustment['per_km']);
        $this->assertEquals(30.0, $adjustment['total']); // 10 + (2 * 10)
        $this->assertEquals('fixed', $adjustment['base_fare_adjustment_type']);
        $this->assertEquals('fixed', $adjustment['distance_fare_adjustment_type']);
    }
    
    /**
     * Test vehicle type pricing with percentage adjustment type
     */
    public function test_vehicle_type_pricing_with_percentage_adjustment(): void
    {
        // Create a vehicle type with percentage adjustment
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Vehicle Percentage',
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'additional_base_fare' => 0.0, // Not used for percentage type
            'additional_price_per_km' => 0.0, // Not used for percentage type
            'base_fare_adjustment' => 20.0, // 20% of base fare
            'distance_fare_adjustment' => 10.0, // 10% of distance fare
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules()
        );
        
        // Assert the results
        $this->assertEquals(10.0, $adjustment['base_fare']); // 50 * 0.2 = 10
        $this->assertEquals(0.5, $adjustment['per_km']); // 5 * 0.1 = 0.5
        $this->assertEquals(15.0, $adjustment['total']); // 10 + (0.5 * 10) = 15
        $this->assertEquals('percentage', $adjustment['base_fare_adjustment_type']);
        $this->assertEquals('percentage', $adjustment['distance_fare_adjustment_type']);
    }
    
    /**
     * Test vehicle type pricing with mixed adjustment types
     */
    public function test_vehicle_type_pricing_with_mixed_adjustment(): void
    {
        // Create a vehicle type with mixed adjustment types
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Vehicle Mixed',
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'percentage',
            'additional_base_fare' => 15.0, // Fixed value for base fare
            'additional_price_per_km' => 0.0, // Not used for percentage type
            'base_fare_adjustment' => 0.0, // Not used for fixed type
            'distance_fare_adjustment' => 25.0, // 25% of distance fare
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules()
        );
        
        // Assert the results
        $this->assertEquals(15.0, $adjustment['base_fare']); // Fixed 15
        $this->assertEquals(1.25, $adjustment['per_km']); // 5 * 0.25 = 1.25
        $this->assertEquals(27.5, $adjustment['total']); // 15 + (1.25 * 10) = 27.5
        $this->assertEquals('fixed', $adjustment['base_fare_adjustment_type']);
        $this->assertEquals('percentage', $adjustment['distance_fare_adjustment_type']);
    }
}
