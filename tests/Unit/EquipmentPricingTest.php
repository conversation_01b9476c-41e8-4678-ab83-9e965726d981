<?php

namespace Tests\Unit;

use App\Models\VehicleEquipment;
use App\Services\Pricing\Factors\EquipmentPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use App\Services\PricingService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EquipmentPricingTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test equipment pricing factor calculation
     */
    public function test_equipment_pricing_factor_calculation(): void
    {
        // Create test equipment
        $equipment1 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 1',
            'additional_fare' => 10.0,
            'status' => true,
        ]);

        $equipment2 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 2',
            'additional_fare' => 15.0,
            'status' => true,
        ]);

        // Create a collection of equipment
        $equipments = VehicleEquipment::whereIn('id', [$equipment1->id, $equipment2->id])->get();

        // Create the equipment pricing factor
        $factor = new EquipmentPricingFactor($equipments);

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules
        );

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(25.0, $adjustment['equipment_total']); // 10.0 + 15.0 = 25.0
        $this->assertCount(2, $adjustment['equipment']);
        $this->assertEquals($equipment1->id, $adjustment['equipment'][0]['id']);
        $this->assertEquals($equipment1->name_en, $adjustment['equipment'][0]['name']);
        $this->assertEquals(10.0, $adjustment['equipment'][0]['price']);
        $this->assertEquals($equipment2->id, $adjustment['equipment'][1]['id']);
        $this->assertEquals($equipment2->name_en, $adjustment['equipment'][1]['name']);
        $this->assertEquals(15.0, $adjustment['equipment'][1]['price']);
    }

    /**
     * Test equipment pricing is added to the final price
     */
    public function test_equipment_pricing_is_added_to_final_price(): void
    {
        // Create test equipment
        $equipment1 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 1',
            'additional_fare' => 10.0,
            'status' => true,
        ]);

        $equipment2 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 2',
            'additional_fare' => 15.0,
            'status' => true,
        ]);

        // Create a pricing calculator with global rules
        $calculator = new PricingCalculator(new GlobalPricingRules);

        // Add equipment pricing factor
        $equipments = VehicleEquipment::whereIn('id', [$equipment1->id, $equipment2->id])->get();
        $calculator->addFactor(new EquipmentPricingFactor($equipments));

        // Calculate the price
        $distance = 10.0;
        $result = $calculator->calculate($distance);

        // Get the base price without equipment
        $baseFare = $result->toArray()['base_fare'];
        $perKm = $result->toArray()['per_km'];
        $baseTotal = $baseFare + ($perKm * $distance);

        // Get the total price with equipment
        $totalPrice = $result->toArray()['total'];

        // Assert that the equipment price is added to the final price
        // Equipment price is added to the base fare, which affects the total price
        $this->assertEquals($baseTotal + 25.0, $totalPrice);

        // Check that the equipment adjustment is included in the result
        $adjustments = $result->getAdjustments();
        $this->assertArrayHasKey('equipment', $adjustments);
        $this->assertEquals(25.0, $adjustments['equipment']['equipment_total']);
    }

    /**
     * Test equipment pricing through PricingService
     */
    public function test_equipment_pricing_through_pricing_service(): void
    {
        // Create test equipment
        $equipment1 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 1',
            'additional_fare' => 10.0,
            'status' => true,
        ]);

        $equipment2 = VehicleEquipment::factory()->create([
            'name_en' => 'Test Equipment 2',
            'additional_fare' => 15.0,
            'status' => true,
        ]);

        // Calculate price without equipment
        $distance = 10.0;
        $priceWithoutEquipment = PricingService::calculatePrice(
            $distance,
            null, // areaId
            null, // vehicleTypeId
            null, // gender
            Carbon::now(), // startTime
            [] // equipmentIds - empty
        );

        // Calculate price with equipment
        $priceWithEquipment = PricingService::calculatePrice(
            $distance,
            null, // areaId
            null, // vehicleTypeId
            null, // gender
            Carbon::now(), // startTime
            [$equipment1->id, $equipment2->id] // equipmentIds
        );

        // Assert that the equipment price is added to the final price
        // Equipment price is added to the base fare, which affects the total price
        $this->assertEquals(
            $priceWithoutEquipment['total'] + 25.0,
            $priceWithEquipment['total']
        );

        // Check that the equipment adjustment is included in the result
        $this->assertArrayHasKey('adjustments', $priceWithEquipment);

        $equipmentAdjustment = null;
        foreach ($priceWithEquipment['adjustments'] as $adjustment) {
            if ($adjustment['type'] === 'equipment') {
                $equipmentAdjustment = $adjustment;
                break;
            }
        }

        $this->assertNotNull($equipmentAdjustment);
        $this->assertEquals(25.0, $equipmentAdjustment['amount']);
        $this->assertEquals(2, $equipmentAdjustment['equipment_count']);
    }

    /**
     * Test inactive equipment is not included in pricing
     */
    public function test_inactive_equipment_not_included_in_pricing(): void
    {
        // Create test equipment - one active, one inactive
        $activeEquipment = VehicleEquipment::factory()->create([
            'name_en' => 'Active Equipment',
            'additional_fare' => 10.0,
            'status' => true,
        ]);

        $inactiveEquipment = VehicleEquipment::factory()->create([
            'name_en' => 'Inactive Equipment',
            'additional_fare' => 15.0,
            'status' => false, // Inactive
        ]);

        // Calculate price with both equipment IDs
        $distance = 10.0;
        $price = PricingService::calculatePrice(
            $distance,
            null, // areaId
            null, // vehicleTypeId
            null, // gender
            Carbon::now(), // startTime
            [$activeEquipment->id, $inactiveEquipment->id] // equipmentIds - both active and inactive
        );

        // Check that only the active equipment is included in the result
        $equipmentAdjustment = null;
        foreach ($price['adjustments'] as $adjustment) {
            if ($adjustment['type'] === 'equipment') {
                $equipmentAdjustment = $adjustment;
                break;
            }
        }

        // Assert that only the active equipment is included
        $this->assertNotNull($equipmentAdjustment);
        $this->assertEquals(10.0, $equipmentAdjustment['amount']); // Only the active equipment price
        $this->assertEquals(1, $equipmentAdjustment['equipment_count']); // Only one equipment
    }
}
