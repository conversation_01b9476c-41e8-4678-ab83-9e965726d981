<?php

namespace Tests\Unit\Services;

use App\Models\Area;
use App\Services\AreaDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class AreaDetectionServiceTest extends TestCase
{
    use RefreshDatabase;

    private AreaDetectionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AreaDetectionService;
    }

    public function test_finds_area_by_coordinates_within_polygon()
    {
        // Create an area with a polygon
        $area = Area::factory()->create([
            'name_en' => 'Test Area',
            'name_ar' => 'منطقة اختبار',
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 13.0],
            ],
        ]);

        // Test a point inside the polygon
        $areaId = $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        $this->assertEquals($area->id, $areaId);
    }

    public function test_returns_nearest_area_for_coordinates_outside_all_polygons()
    {
        // Create an area with a polygon
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 13.0],
            ],
        ]);

        // Mock Google API to return no results
        Http::fake([
            'maps.googleapis.com/*' => Http::response([
                'status' => 'OK',
                'results' => [],
            ], 200),
        ]);

        // Test a point outside the polygon but still close enough to find the nearest area
        $areaId = $this->service->getAreaIdFromCoordinates(35.0, 15.0);

        // The service should return the nearest area ID
        $this->assertEquals($area->id, $areaId);
    }

    public function test_uses_google_maps_when_polygon_check_fails()
    {
        // Create an area that will match with Google's response
        $area = Area::factory()->create([
            'name_en' => 'Tripoli',
            'name_ar' => 'طرابلس',
            'is_active' => true,
        ]);

        // Mock Google API to return a result that matches our area
        Http::fake([
            'maps.googleapis.com/*' => Http::response([
                'status' => 'OK',
                'results' => [
                    [
                        'address_components' => [
                            [
                                'long_name' => 'Tripoli',
                                'short_name' => 'Tripoli',
                                'types' => ['locality', 'political'],
                            ],
                        ],
                    ],
                ],
            ], 200),
        ]);

        // Test coordinates that don't match any polygon but will match via Google
        $areaId = $this->service->getAreaIdFromCoordinates(32.8752, 13.1875);

        $this->assertEquals($area->id, $areaId);
    }

    public function test_caches_results()
    {
        // Create an area with a polygon
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 13.0],
            ],
        ]);

        // Clear the cache
        Cache::flush();

        // First call should hit the database
        $areaId1 = $this->service->getAreaIdFromCoordinates(32.5, 13.5);
        $this->assertEquals($area->id, $areaId1);

        // Delete the area to ensure the next call uses the cache
        $area->delete();

        // Second call should use the cache
        $areaId2 = $this->service->getAreaIdFromCoordinates(32.5, 13.5);
        $this->assertEquals($area->id, $areaId2);
    }

    public function test_handles_invalid_polygons()
    {
        // Create an area with an invalid polygon (less than 3 points)
        Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Mock Google API to return no results
        Http::fake([
            'maps.googleapis.com/*' => Http::response([
                'status' => 'OK',
                'results' => [],
            ], 200),
        ]);

        // Test a point - should return null since polygon is invalid
        $areaId = $this->service->getAreaIdFromCoordinates(32.0, 13.5);

        $this->assertNull($areaId);
    }

    public function test_handles_google_api_errors()
    {
        // Create an area with a polygon that won't match our test point
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 33.0, 'lng' => 13.0],
            ],
        ]);

        // Mock Google API to return an error
        Http::fake([
            'maps.googleapis.com/*' => Http::response([
                'status' => 'ERROR',
                'error_message' => 'API key invalid',
            ], 200),
        ]);

        // Test a point outside the polygon but still close enough to find the nearest area
        $areaId = $this->service->getAreaIdFromCoordinates(35.0, 15.0, true);

        // Even with Google API error, the service should still return the nearest area
        $this->assertEquals($area->id, $areaId);
    }
}
