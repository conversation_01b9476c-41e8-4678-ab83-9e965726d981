<?php

namespace Tests\Unit\Services\Pricing;

use App\Models\PricingRules;
use App\Services\PricingService;
use Carbon\Carbon;
use Tests\TestCase;

class TimeBasedOverchargeTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Create or update global pricing rules with a time threshold
        $globalRules = PricingRules::first();
        if (!$globalRules) {
            PricingRules::create([
                'global_base_price' => 3.00,
                'global_price_per_km' => 2.00,
                'time_threshold_percentage' => 20.00, // 20% threshold
            ]);
        } else {
            $globalRules->update([
                'time_threshold_percentage' => 20.00,
            ]);
        }
    }

    /**
     * Test that the time-based overcharge is calculated correctly.
     *
     * @return void
     */
    public function testTimeBasedOverchargeCalculation(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(45); // 50% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that time overcharge was applied
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(30, $pricingData['time_overcharge']['overcharge_percentage']);
        
        // Calculate expected subtotal with overcharge
        $subtotal = $pricingData['subtotal'];
        $expectedSubtotalWithOvercharge = $subtotal * 1.3; // 30% overcharge
        
        $this->assertEquals(round($expectedSubtotalWithOvercharge, 2), $pricingData['subtotal_with_overcharge']);
    }

    /**
     * Test that the time-based overcharge is applied to the final price.
     *
     * @return void
     */
    public function testTimeBasedOverchargeAppliedToFinalPrice(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(45); // 50% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that time overcharge was applied to the final price
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(30, $pricingData['time_overcharge']['overcharge_percentage']);
        
        // Calculate expected prices
        $subtotal = $pricingData['subtotal'];
        $expectedSubtotalWithOvercharge = $subtotal * 1.3; // 30% overcharge
        $expectedAdjustedPrice = $expectedSubtotalWithOvercharge + ($pricingData['equipment_total'] ?? 0);
        
        $this->assertEquals(round($expectedAdjustedPrice, 2), $pricingData['adjusted_price']);
        $this->assertEquals(round($expectedAdjustedPrice, 2), $pricingData['total']);
    }

    /**
     * Test that no overcharge is applied when the actual time is less than the estimated time.
     *
     * @return void
     */
    public function testNoOverchargeWhenActualTimeLessThanEstimated(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(25); // 16.67% shorter

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that no time overcharge was applied
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(0, $pricingData['time_overcharge']['overcharge_percentage']);
        $this->assertEquals($pricingData['subtotal'], $pricingData['subtotal_with_overcharge']);
    }

    /**
     * Test that no overcharge is applied when the time difference is within the threshold.
     *
     * @return void
     */
    public function testNoOverchargeWhenTimeDifferenceWithinThreshold(): void
    {
        // Set up test data
        $startTime = Carbon::now();
        $estimatedArrivalTime = $startTime->copy()->addMinutes(30);
        $actualArrivalTime = $startTime->copy()->addMinutes(35); // 16.67% longer

        // Calculate pricing
        $pricingData = PricingService::calculatePrice(
            5.0, // 5 km
            null, // No area ID
            null, // No vehicle type ID
            null, // No gender
            $startTime,
            [], // No equipment
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Assert that no time overcharge was applied
        $this->assertArrayHasKey('time_overcharge', $pricingData);
        $this->assertEquals(0, $pricingData['time_overcharge']['overcharge_percentage']);
        $this->assertEquals($pricingData['subtotal'], $pricingData['subtotal_with_overcharge']);
    }
}
