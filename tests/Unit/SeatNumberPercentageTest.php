<?php

namespace Tests\Unit;

use App\Models\PricingRuleSeatNumber;
use App\Services\Pricing\Factors\SeatCapacityPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class SeatNumberPercentageTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->globalRules = new GlobalPricingRules();
    }

    public function test_seat_number_percentage_calculation()
    {
        // Create a seat number pricing rule with percentage adjustment
        $seatRule = PricingRuleSeatNumber::create([
            'seats_number' => 4,
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'base_fare' => 0.0, // Not used for percentage
            'distance_fare' => 0.0, // Not used for percentage
            'base_fare_adjustment' => 25.0, // 25% of base fare
            'distance_fare_adjustment' => 20.0, // 20% of distance fare
        ]);

        // Create the pricing factor
        $factor = new SeatCapacityPricingFactor(4);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );
        
        // Log the adjustment for debugging
        Log::info('Seat number percentage calculation test result', [
            'adjustment' => $adjustment,
            'seat_rule' => $seatRule->toArray(),
        ]);
        
        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(12.5, $adjustment['base_fare']); // 50 * 0.25 = 12.5
        $this->assertEquals(1.0, $adjustment['per_km']); // 5 * 0.2 = 1.0
        $this->assertEquals(22.5, $adjustment['total']); // 12.5 + (1.0 * 10) = 22.5
        
        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);
        
        // Log the result for debugging
        Log::info('Seat number pricing calculator test result', [
            'result' => $result->toArray(),
        ]);
        
        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();
        
        // Calculate expected values
        $expectedBaseFareAdjustment = $globalBaseFare * 0.25; // 25% of base fare
        $expectedDistanceFareAdjustment = $globalDistanceFare * 0.2; // 20% of distance fare
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);
        
        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEquals($expectedTotal, $resultArray['total'], 'Total price does not match expected value', 0.01);
    }
}
