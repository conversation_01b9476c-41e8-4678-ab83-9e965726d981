<?php

namespace Tests\Unit;

use App\Models\VehicleType;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use Tests\TestCase;

class VehicleTypePricingDebugTest extends TestCase
{
    /**
     * Test vehicle type pricing with simulated problematic data
     */
    public function test_vehicle_type_pricing_with_problematic_data(): void
    {
        // Create a vehicle type with potentially problematic data
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Debug Vehicle Type',
            'base_fare_adjustment_type' => null, // This could be causing issues
            'distance_fare_adjustment_type' => null, // This could be causing issues
            'additional_base_fare' => 10.0,
            'additional_price_per_km' => 2.0,
            'base_fare_adjustment' => 0.0,
            'distance_fare_adjustment' => 0.0,
        ]);

        // Log the created vehicle type
        \Illuminate\Support\Facades\Log::info('Created vehicle type for debugging', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'base_fare_adjustment_type' => $vehicleType->base_fare_adjustment_type,
            'distance_fare_adjustment_type' => $vehicleType->distance_fare_adjustment_type,
            'additional_base_fare' => $vehicleType->additional_base_fare,
            'base_fare_adjustment' => $vehicleType->base_fare_adjustment,
            'additional_price_per_km' => $vehicleType->additional_price_per_km,
            'distance_fare_adjustment' => $vehicleType->distance_fare_adjustment,
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules
        );

        // Log the adjustment
        \Illuminate\Support\Facades\Log::info('Vehicle type adjustment result', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'adjustment' => $adjustment,
        ]);

        // Assert that the adjustment is not null
        $this->assertNotNull($adjustment);

        // Now test with fixed adjustment types
        $vehicleType->update([
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
        ]);

        // Refresh the model
        $vehicleType->refresh();

        // Log the updated vehicle type
        \Illuminate\Support\Facades\Log::info('Updated vehicle type with fixed types', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'base_fare_adjustment_type' => $vehicleType->base_fare_adjustment_type,
            'distance_fare_adjustment_type' => $vehicleType->distance_fare_adjustment_type,
            'additional_base_fare' => $vehicleType->additional_base_fare,
            'base_fare_adjustment' => $vehicleType->base_fare_adjustment,
            'additional_price_per_km' => $vehicleType->additional_price_per_km,
            'distance_fare_adjustment' => $vehicleType->distance_fare_adjustment,
        ]);

        // Create a new pricing factor with the updated vehicle type
        $factor = new VehicleTypePricingFactor($vehicleType->id);

        // Calculate the adjustment again
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules
        );

        // Log the adjustment
        \Illuminate\Support\Facades\Log::info('Vehicle type adjustment result with fixed types', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'adjustment' => $adjustment,
        ]);

        // Assert that the adjustment is not null
        $this->assertNotNull($adjustment);

        // Now test with percentage adjustment types
        $vehicleType->update([
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'base_fare_adjustment' => 20.0,
            'distance_fare_adjustment' => 10.0,
        ]);

        // Refresh the model
        $vehicleType->refresh();

        // Log the updated vehicle type
        \Illuminate\Support\Facades\Log::info('Updated vehicle type with percentage types', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'base_fare_adjustment_type' => $vehicleType->base_fare_adjustment_type,
            'distance_fare_adjustment_type' => $vehicleType->distance_fare_adjustment_type,
            'additional_base_fare' => $vehicleType->additional_base_fare,
            'base_fare_adjustment' => $vehicleType->base_fare_adjustment,
            'additional_price_per_km' => $vehicleType->additional_price_per_km,
            'distance_fare_adjustment' => $vehicleType->distance_fare_adjustment,
        ]);

        // Create a new pricing factor with the updated vehicle type
        $factor = new VehicleTypePricingFactor($vehicleType->id);

        // Calculate the adjustment again
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            new GlobalPricingRules
        );

        // Log the adjustment
        \Illuminate\Support\Facades\Log::info('Vehicle type adjustment result with percentage types', [
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'adjustment' => $adjustment,
        ]);

        // Assert that the adjustment is not null
        $this->assertNotNull($adjustment);
    }
}
