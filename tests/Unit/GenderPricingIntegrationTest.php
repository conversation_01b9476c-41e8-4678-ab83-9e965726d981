<?php

namespace Tests\Unit;

use App\Models\PricingRuleGender;
use App\Models\Trip;
use App\Models\User;
use App\Services\Pricing\Factors\GenderPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class GenderPricingIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a global pricing rule
        \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        $this->globalRules = new GlobalPricingRules;
    }

    public function test_gender_pricing_with_female_user()
    {
        // Create a gender pricing rule
        $genderRule = PricingRuleGender::create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0.0, // Not used for percentage
            'base_fare_percentage' => 15.0, // 15% of base fare
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare_fixed' => 2.0, // Fixed 2.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create a female user
        $femaleUser = User::factory()->create([
            'gender' => 'female',
        ]);

        // Create a rider for the user
        $femaleRider = \App\Models\Rider::factory()->create([
            'user_id' => $femaleUser->id,
        ]);

        // Create a trip with is_female = true
        $trip = Trip::factory()->create([
            'rider_id' => $femaleRider->id,
            'is_female' => true,
        ]);

        // Create the pricing factor
        $factor = new GenderPricingFactor($trip->is_female ? 'female' : 'male');

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Gender pricing calculation test result', [
            'adjustment' => $adjustment,
            'gender_rule' => $genderRule->toArray(),
            'trip' => $trip->toArray(),
        ]);

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(7.5, $adjustment['base_fare']); // 50 * 0.15 = 7.5
        $this->assertEquals(2.0, $adjustment['per_km']); // Fixed 2.0
        $this->assertEquals(27.5, $adjustment['total']); // 7.5 + (2.0 * 10) = 27.5
    }

    public function test_gender_pricing_with_male_user()
    {
        // Create a gender pricing rule
        $genderRule = PricingRuleGender::create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0.0, // Not used for percentage
            'base_fare_percentage' => 15.0, // 15% of base fare
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare_fixed' => 2.0, // Fixed 2.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create a male user
        $maleUser = User::factory()->create([
            'gender' => 'male',
        ]);

        // Create a rider for the user
        $maleRider = \App\Models\Rider::factory()->create([
            'user_id' => $maleUser->id,
        ]);

        // Create a trip with is_female = false
        $trip = Trip::factory()->create([
            'rider_id' => $maleRider->id,
            'is_female' => false,
        ]);

        // Create the pricing factor
        $factor = new GenderPricingFactor($trip->is_female ? 'female' : 'male');

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Gender pricing calculation test result', [
            'adjustment' => $adjustment,
            'gender_rule' => $genderRule->toArray(),
            'trip' => $trip->toArray(),
        ]);

        // Assert the results
        $this->assertNull($adjustment); // No adjustment for male users
    }
}
