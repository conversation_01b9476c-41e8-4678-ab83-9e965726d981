<?php

namespace Tests\Unit;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRulePeakHour;
use App\Services\Pricing\Factors\PeakHourPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class PeakHourPricingTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a global pricing rule
        \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        $this->globalRules = new GlobalPricingRules;
    }

    public function test_peak_hour_pricing_calculation()
    {
        // Create a global pricing rule again (since RefreshDatabase is used)
        $globalRule = \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        // Create a day charge for Monday
        $dayCharge = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $globalRule->id,
            'day' => 'Monday',
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_fixed_charge' => 0.0,
            'day_percentage_charge' => 10.0,
            'day_distance_charge_type' => 'percentage',
            'day_distance_fixed_charge' => 0.0,
            'day_distance_percentage_charge' => 5.0,
            'night_start_at' => '18:00',
            'night_end_at' => '06:00',
            'night_charge_type' => 'fixed',
            'night_fixed_charge' => 10.0,
            'night_percentage_charge' => 0.0,
            'night_distance_charge_type' => 'fixed',
            'night_distance_fixed_charge' => 1.5,
            'night_distance_percentage_charge' => 0.0,
        ]);

        // Create a peak hour rule for Monday morning rush hour
        $peakHour = PricingRulePeakHour::create([
            'day_charge_id' => $dayCharge->id,
            'peak_start_at' => '07:00',
            'peak_end_at' => '09:00',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0.0,
            'base_fare_percentage' => 20.0, // 20% additional during peak hours
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_fixed' => 0.0,
            'distance_fare_percentage' => 15.0, // 15% additional during peak hours
        ]);

        // Create the pricing factor with a Monday peak hour time
        $factor = new PeakHourPricingFactor(Carbon::parse('2023-05-01 08:00:00')); // Monday at 8 AM (peak hour)

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Peak hour pricing calculation test result', [
            'adjustment' => $adjustment,
            'peak_hour' => $peakHour->toArray(),
        ]);

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(10.0, $adjustment['base_fare']); // 50 * 0.2 = 10.0
        $this->assertEquals(0.75, $adjustment['per_km']); // 5 * 0.15 = 0.75
        $this->assertEquals(17.5, $adjustment['total']); // 10.0 + (0.75 * 10) = 17.5

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Log the result for debugging
        Log::info('Peak hour pricing calculator test result', [
            'result' => $result->toArray(),
        ]);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values
        $expectedBaseFareAdjustment = $globalBaseFare * 0.2; // 20% of base fare
        $expectedDistanceFareAdjustment = $globalDistanceFare * 0.15; // 15% of distance fare
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }

    public function test_non_peak_hour_pricing_calculation()
    {
        // Create a global pricing rule again (since RefreshDatabase is used)
        $globalRule = \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        // Create a day charge for Monday
        $dayCharge = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $globalRule->id,
            'day' => 'Monday',
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_fixed_charge' => 0.0,
            'day_percentage_charge' => 10.0,
            'day_distance_charge_type' => 'percentage',
            'day_distance_fixed_charge' => 0.0,
            'day_distance_percentage_charge' => 5.0,
            'night_start_at' => '18:00',
            'night_end_at' => '06:00',
            'night_charge_type' => 'fixed',
            'night_fixed_charge' => 10.0,
            'night_percentage_charge' => 0.0,
            'night_distance_charge_type' => 'fixed',
            'night_distance_fixed_charge' => 1.5,
            'night_distance_percentage_charge' => 0.0,
        ]);

        // Create a peak hour rule for Monday morning rush hour
        $peakHour = PricingRulePeakHour::create([
            'day_charge_id' => $dayCharge->id,
            'peak_start_at' => '07:00',
            'peak_end_at' => '09:00',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0.0,
            'base_fare_percentage' => 20.0, // 20% additional during peak hours
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_fixed' => 0.0,
            'distance_fare_percentage' => 15.0, // 15% additional during peak hours
        ]);

        // Create the pricing factor with a Monday non-peak hour time
        $factor = new PeakHourPricingFactor(Carbon::parse('2023-05-01 10:00:00')); // Monday at 10 AM (not peak hour)

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Log the adjustment for debugging
        Log::info('Non-peak hour pricing calculation test result', [
            'adjustment' => $adjustment,
            'peak_hour' => $peakHour->toArray(),
        ]);

        // Assert the results
        $this->assertNull($adjustment); // No adjustment for non-peak hours

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Log the result for debugging
        Log::info('Non-peak hour pricing calculator test result', [
            'result' => $result->toArray(),
        ]);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values (no peak hour adjustment)
        $expectedTotal = $globalBaseFare + ($globalDistanceFare * $distance);

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }
}
