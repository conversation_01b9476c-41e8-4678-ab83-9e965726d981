<?php

namespace Tests\Unit;

use App\Models\Area;
use App\Models\PricingRuleGender;
use App\Models\PricingRules;
use App\Models\PricingRuleSeatNumber;
use App\Models\VehicleType;
use App\Rules\PricingRuleConflictRule;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PricingRuleConflictRuleTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create global pricing rules
        PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        // Create an area with fixed pricing
        Area::create([
            'name_en' => 'Test Area',
            'name_ar' => 'منطقة اختبار',
            'is_active' => true,
            'polygon' => [
                [10.0, 10.0],
                [10.0, 11.0],
                [11.0, 11.0],
                [11.0, 10.0],
            ],
            'base_fare_adjustment_type' => 'fixed',
            'base_fare' => -20.0, // Negative adjustment
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare' => -2.0, // Negative adjustment
        ]);

        // Create a vehicle type with fixed pricing
        VehicleType::create([
            'name_en' => 'Test Vehicle',
            'name_ar' => 'مركبة اختبار',
            'base_fare_adjustment_type' => 'fixed',
            'additional_base_fare' => -15.0, // Negative adjustment
            'distance_fare_adjustment_type' => 'fixed',
            'additional_price_per_km' => -1.5, // Negative adjustment
        ]);

        // Create a seat number rule with fixed pricing
        PricingRuleSeatNumber::create([
            'seats_number' => 4,
            'base_fare_adjustment_type' => 'fixed',
            'base_fare' => -10.0, // Negative adjustment
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare' => -1.0, // Negative adjustment
        ]);

        // Create a gender rule with fixed pricing
        PricingRuleGender::create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'fixed',
            'base_fare_fixed' => -5.0, // Negative adjustment
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare_fixed' => -0.5, // Negative adjustment
        ]);
    }

    /** @test */
    public function it_detects_conflicts_when_global_base_price_is_reduced()
    {
        // Mock the PricingRuleConflictService to return conflicts
        $mockService = $this->createMock(\App\Services\PricingRuleConflictService::class);
        $mockService->method('checkForConflicts')
            ->willReturn([
                'areas' => [
                    [
                        'id' => 1,
                        'name' => 'Test Area',
                        'conflicts' => [
                            'base_fare' => [
                                'current_value' => -20.0,
                                'min_allowed' => -15.0,
                            ],
                        ],
                    ],
                ],
            ]);

        // Create a rule with the mock service
        $rule = new PricingRuleConflictRule('base', 5.0);
        // Set the mock service using reflection
        $reflectionClass = new \ReflectionClass($rule);
        $reflectionProperty = $reflectionClass->getProperty('conflictService');
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($rule, $mockService);

        // Test with any value - the mock will return conflicts
        $this->assertFalse($rule->passes('global_base_price', 30.0));
        $this->assertNotEmpty($rule->message());
    }

    /** @test */
    public function it_detects_conflicts_with_real_data_when_global_base_price_is_reduced()
    {
        // Update the global base price to a higher value
        PricingRules::first()->update([
            'global_base_price' => 50.0,
        ]);

        // Create a rule with the real service
        $rule = new PricingRuleConflictRule('base', 5.0);

        // Test with a value that would make the area's fixed base fare below -50% of the new global base price
        // Area has base_fare = -20.0, so minimum allowed would be -15.0 for a global base price of 30.0
        $this->assertFalse($rule->passes('global_base_price', 30.0));
        $this->assertNotEmpty($rule->message());

        // Test with a value that would not create conflicts
        // Area has base_fare = -20.0, so minimum allowed would be -25.0 for a global base price of 50.0
        $this->assertTrue($rule->passes('global_base_price', 50.0));
    }

    /** @test */
    public function it_detects_conflicts_when_global_price_per_km_is_reduced()
    {
        // Mock the PricingRuleConflictService to return conflicts
        $mockService = $this->createMock(\App\Services\PricingRuleConflictService::class);
        $mockService->method('checkForConflicts')
            ->willReturn([
                'areas' => [
                    [
                        'id' => 1,
                        'name' => 'Test Area',
                        'conflicts' => [
                            'distance_fare' => [
                                'current_value' => -2.0,
                                'min_allowed' => -1.5,
                            ],
                        ],
                    ],
                ],
            ]);

        // Create a rule with the mock service
        $rule = new PricingRuleConflictRule('distance', 50.0);
        // Set the mock service using reflection
        $reflectionClass = new \ReflectionClass($rule);
        $reflectionProperty = $reflectionClass->getProperty('conflictService');
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($rule, $mockService);

        // Test with any value - the mock will return conflicts
        $this->assertFalse($rule->passes('global_price_per_km', 3.0));
        $this->assertNotEmpty($rule->message());
    }

    /** @test */
    public function it_detects_conflicts_with_real_data_when_global_price_per_km_is_reduced()
    {
        // Update the global price per km to a higher value
        PricingRules::first()->update([
            'global_price_per_km' => 5.0,
        ]);

        // Create a rule with the real service
        $rule = new PricingRuleConflictRule('distance', 50.0);

        // Test with a value that would make the area's fixed distance fare below -50% of the new global price per km
        // Area has distance_fare = -2.0, so minimum allowed would be -1.5 for a global price per km of 3.0
        $this->assertFalse($rule->passes('global_price_per_km', 3.0));
        $this->assertNotEmpty($rule->message());

        // Test with a value that would not create conflicts
        // Area has distance_fare = -2.0, so minimum allowed would be -2.5 for a global price per km of 5.0
        $this->assertTrue($rule->passes('global_price_per_km', 5.0));
    }

    /** @test */
    public function it_passes_validation_when_no_conflicts_exist()
    {
        // Update all pricing components to use percentage adjustments instead of fixed
        Area::first()->update([
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_adjustment' => 10.0,
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment' => 10.0,
        ]);

        VehicleType::first()->update([
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_adjustment' => 10.0,
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment' => 10.0,
        ]);

        PricingRuleSeatNumber::first()->update([
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_adjustment' => 10.0,
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment' => 10.0,
        ]);

        PricingRuleGender::first()->update([
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_percentage' => 10.0,
            'distance_fare_adjustment_type' => 'percentage',
            'distance_fare_percentage' => 10.0,
        ]);

        // Create rules to validate
        $baseRule = new PricingRuleConflictRule('base', 5.0);
        $distanceRule = new PricingRuleConflictRule('distance', 50.0);

        // Test with any value since there are no fixed adjustments
        $this->assertTrue($baseRule->passes('global_base_price', 1.0));
        $this->assertTrue($distanceRule->passes('global_price_per_km', 1.0));
    }
}
