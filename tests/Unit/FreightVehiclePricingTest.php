<?php

namespace Tests\Unit;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Models\VehicleType;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class FreightVehiclePricingTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a global pricing rule
        \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);
        
        $this->globalRules = new GlobalPricingRules();
    }

    public function test_freight_vehicle_pricing_calculation()
    {
        // Create a freight vehicle type with fixed adjustment
        $freightVehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Freight Vehicle',
            'category' => VehicleTypesCategories::Freight,
            'weight_category' => WeightCategoryEnum::MoreThan1000kg,
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
            'additional_base_fare' => 20.0, // Fixed 20.0 additional
            'additional_price_per_km' => 3.0, // Fixed 3.0 additional per km
            'base_fare_adjustment' => 0.0, // Not used for fixed
            'distance_fare_adjustment' => 0.0, // Not used for fixed
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($freightVehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );
        
        // Log the adjustment for debugging
        Log::info('Freight vehicle pricing calculation test result', [
            'adjustment' => $adjustment,
            'vehicle_type' => $freightVehicleType->toArray(),
        ]);
        
        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(20.0, $adjustment['base_fare']); // Fixed 20.0
        $this->assertEquals(3.0, $adjustment['per_km']); // Fixed 3.0
        $this->assertEquals(50.0, $adjustment['total']); // 20.0 + (3.0 * 10) = 50.0
        
        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);
        
        // Log the result for debugging
        Log::info('Freight vehicle pricing calculator test result', [
            'result' => $result->toArray(),
        ]);
        
        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();
        
        // Calculate expected values
        $expectedBaseFareAdjustment = 20.0; // Fixed 20.0
        $expectedDistanceFareAdjustment = 3.0; // Fixed 3.0
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);
        
        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }

    public function test_freight_vehicle_pricing_with_percentage_adjustment()
    {
        // Create a freight vehicle type with percentage adjustment
        $freightVehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Freight Vehicle Percentage',
            'category' => VehicleTypesCategories::Freight,
            'weight_category' => WeightCategoryEnum::LessThan1000kg,
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'additional_base_fare' => 0.0, // Not used for percentage
            'additional_price_per_km' => 0.0, // Not used for percentage
            'base_fare_adjustment' => 25.0, // 25% of base fare
            'distance_fare_adjustment' => 20.0, // 20% of distance fare
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($freightVehicleType->id);
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );
        
        // Log the adjustment for debugging
        Log::info('Freight vehicle percentage pricing calculation test result', [
            'adjustment' => $adjustment,
            'vehicle_type' => $freightVehicleType->toArray(),
        ]);
        
        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(12.5, $adjustment['base_fare']); // 50 * 0.25 = 12.5
        $this->assertEquals(1.0, $adjustment['per_km']); // 5 * 0.2 = 1.0
        $this->assertEquals(22.5, $adjustment['total']); // 12.5 + (1.0 * 10) = 22.5
        
        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);
        
        // Log the result for debugging
        Log::info('Freight vehicle percentage pricing calculator test result', [
            'result' => $result->toArray(),
        ]);
        
        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();
        
        // Calculate expected values
        $expectedBaseFareAdjustment = $globalBaseFare * 0.25; // 25% of base fare
        $expectedDistanceFareAdjustment = $globalDistanceFare * 0.2; // 20% of distance fare
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);
        
        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }
}
