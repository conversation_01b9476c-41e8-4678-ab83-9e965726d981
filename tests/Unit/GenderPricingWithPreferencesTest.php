<?php

namespace Tests\Unit;

use App\Models\PricingRuleGender;
use App\Models\RiderPreferences;
use App\Models\Trip;
use App\Models\User;
use App\Services\Pricing\Factors\GenderPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use App\Services\TripPricingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class GenderPricingWithPreferencesTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a global pricing rule
        \App\Models\PricingRules::create([
            'id' => 1,
            'global_base_price' => 50.0,
            'global_price_per_km' => 5.0,
            'time_threshold_percentage' => 20.0,
        ]);

        $this->globalRules = new GlobalPricingRules;
    }

    public function test_gender_pricing_with_female_driver_preference()
    {
        // Create a female pricing rule
        $femalePricingRule = PricingRuleGender::factory()->create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
            'base_fare_fixed' => 15.0, // Fixed 15.0 additional
            'base_fare_percentage' => 0.0, // Not used for fixed
            'distance_fare_fixed' => 2.0, // Fixed 2.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create a male user
        $maleUser = User::factory()->create([
            'gender' => 'male',
        ]);

        // Create a rider for the user
        $maleRider = \App\Models\Rider::factory()->create([
            'user_id' => $maleUser->id,
        ]);

        // Create rider preferences with female driver preference
        $riderPreferences = RiderPreferences::create([
            'user_id' => $maleUser->id,
            'driver_gender' => 'female',
            'seats_number' => [4],
        ]);

        // Create a trip
        $trip = Trip::factory()->create([
            'rider_id' => $maleRider->id,
            'is_female' => true, // This should be ignored in the new logic
        ]);

        // Mock the TripPricingService
        $tripPricingService = new TripPricingService;

        // Create a mock data array
        $mockData = [
            'estimated_departure_time' => now(),
            'vehicle_category' => 'passenger',
        ];

        // Create a mock Google Maps data array
        $mockGoogleData = [
            'distance_value' => 10000, // 10 km
        ];

        // Create a mock departure area ID
        $mockDepartureAreaId = 1;

        // Test the gender determination logic
        $reflectionClass = new \ReflectionClass($tripPricingService);
        $reflectionMethod = $reflectionClass->getMethod('calculateAndSetTripPricing');
        $reflectionMethod->setAccessible(true);
        $reflectionMethod->invoke($tripPricingService, $trip, $mockGoogleData, $mockData, $mockDepartureAreaId);

        // Get the pricing breakdown
        $pricingBreakdown = json_decode($trip->pricing_breakdown, true);

        // Log the pricing breakdown for debugging
        Log::info('Pricing breakdown with female driver preference', [
            'pricing_breakdown' => $pricingBreakdown,
        ]);

        // Create the pricing factor directly to test
        $factor = new GenderPricingFactor('female');

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(15.0, $adjustment['base_fare']); // Fixed 15.0
        $this->assertEquals(2.0, $adjustment['per_km']); // Fixed 2.0
        $this->assertEquals(35.0, $adjustment['total']); // 15.0 + (2.0 * 10) = 35.0

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values
        $expectedBaseFareAdjustment = 15.0; // Fixed 15.0
        $expectedDistanceFareAdjustment = 2.0; // Fixed 2.0
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + $expectedAdjustedDistanceFare * $distance;

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }

    public function test_gender_pricing_with_male_driver_preference()
    {
        // Create a female pricing rule
        $femalePricingRule = PricingRuleGender::factory()->create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
            'base_fare_fixed' => 15.0, // Fixed 15.0 additional
            'base_fare_percentage' => 0.0, // Not used for fixed
            'distance_fare_fixed' => 2.0, // Fixed 2.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create a male pricing rule
        $malePricingRule = PricingRuleGender::factory()->create([
            'gender' => 'male',
            'base_fare_adjustment_type' => 'fixed',
            'distance_fare_adjustment_type' => 'fixed',
            'base_fare_fixed' => 5.0, // Fixed 5.0 additional
            'base_fare_percentage' => 0.0, // Not used for fixed
            'distance_fare_fixed' => 1.0, // Fixed 1.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create a female user
        $femaleUser = User::factory()->create([
            'gender' => 'female',
        ]);

        // Create a rider for the user
        $femaleRider = \App\Models\Rider::factory()->create([
            'user_id' => $femaleUser->id,
        ]);

        // Create rider preferences with male driver preference
        $riderPreferences = RiderPreferences::create([
            'user_id' => $femaleUser->id,
            'driver_gender' => 'male',
            'seats_number' => [4],
        ]);

        // Create a trip
        $trip = Trip::factory()->create([
            'rider_id' => $femaleRider->id,
            'is_female' => false, // This should be ignored in the new logic
        ]);

        // Mock the TripPricingService
        $tripPricingService = new TripPricingService;

        // Create a mock data array
        $mockData = [
            'estimated_departure_time' => now(),
            'vehicle_category' => 'passenger',
        ];

        // Create a mock Google Maps data array
        $mockGoogleData = [
            'distance_value' => 10000, // 10 km
        ];

        // Create a mock departure area ID
        $mockDepartureAreaId = 1;

        // Test the gender determination logic
        $reflectionClass = new \ReflectionClass($tripPricingService);
        $reflectionMethod = $reflectionClass->getMethod('calculateAndSetTripPricing');
        $reflectionMethod->setAccessible(true);
        $reflectionMethod->invoke($tripPricingService, $trip, $mockGoogleData, $mockData, $mockDepartureAreaId);

        // Get the pricing breakdown
        $pricingBreakdown = json_decode($trip->pricing_breakdown, true);

        // Log the pricing breakdown for debugging
        Log::info('Pricing breakdown with male driver preference', [
            'pricing_breakdown' => $pricingBreakdown,
        ]);

        // Create the pricing factor directly to test
        $factor = new GenderPricingFactor('male');

        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;

        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );

        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(5.0, $adjustment['base_fare']); // Fixed 5.0
        $this->assertEquals(1.0, $adjustment['per_km']); // Fixed 1.0
        $this->assertEquals(15.0, $adjustment['total']); // 5.0 + (1.0 * 10) = 15.0

        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);

        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();

        // Calculate expected values
        $expectedBaseFareAdjustment = 5.0; // Fixed 5.0
        $expectedDistanceFareAdjustment = 1.0; // Fixed 1.0
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + $expectedAdjustedDistanceFare * $distance;

        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEqualsWithDelta($expectedTotal, $resultArray['total'], 0.01, 'Total price does not match expected value');
    }
}
