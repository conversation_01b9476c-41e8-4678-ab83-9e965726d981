<?php

namespace Tests\Unit;

use App\Http\Requests\TripsRequests\TripStoreRequest;
use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRuleNonOperationalPeriod;
use App\Models\PricingRules;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Tests\TestCase;

class TripStoreRequestNonOperationalTest extends TestCase
{
    use RefreshDatabase;

    protected $pricingRule;

    protected $dayCharge;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a pricing rule
        $this->pricingRule = PricingRules::create([
            'global_base_price' => 5.00,
            'global_price_per_km' => 2.00,
            'time_threshold_percentage' => 10.00,
        ]);

        // Create a day charge for Monday
        $this->dayCharge = PricingRuleAdditionalDayCharge::create([
            'pricing_rule_id' => $this->pricingRule->id,
            'day' => 'Monday',
            'day_start_at' => '06:00',
            'day_end_at' => '18:00',
            'day_charge_type' => 'percentage',
            'day_percentage_charge' => 10.0,
            'night_start_at' => '20:00',
            'night_end_at' => '05:00',
            'night_charge_type' => 'percentage',
            'night_percentage_charge' => 15.0,
        ]);
    }

    public function test_trip_creation_allowed_during_operational_hours()
    {
        // Set current time to Monday 10:00 (operational hours)
        Carbon::setTestNow(Carbon::parse('2025-01-06 10:00:00')); // Monday

        // Create non-operational period from 12:00 to 14:00
        PricingRuleNonOperationalPeriod::create([
            'day_charge_id' => $this->dayCharge->id,
            'start_at' => '12:00',
            'end_at' => '14:00',
        ]);

        $request = new TripStoreRequest;
        $validator = Validator::make([], []);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('validateNonOperationalPeriods');
        $method->setAccessible(true);

        // Call the validation method
        $method->invoke($request, $validator);

        // Should not have any errors since 10:00 is not in non-operational period
        $this->assertFalse($validator->errors()->has('non_operational_period'));
    }

    public function test_trip_creation_blocked_during_non_operational_hours()
    {
        // Set current time to Monday 13:00 (non-operational hours)
        Carbon::setTestNow(Carbon::parse('2025-01-06 13:00:00')); // Monday

        // Create non-operational period from 12:00 to 14:00
        PricingRuleNonOperationalPeriod::create([
            'day_charge_id' => $this->dayCharge->id,
            'start_at' => '12:00',
            'end_at' => '14:00',
        ]);

        $request = new TripStoreRequest;
        $validator = Validator::make([], []);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('validateNonOperationalPeriods');
        $method->setAccessible(true);

        // Call the validation method
        $method->invoke($request, $validator);

        // Should have an error since 13:00 is in non-operational period
        $this->assertTrue($validator->errors()->has('non_operational_period'));
        $this->assertStringContainsString('Service is unavailable from 12:00 to 14:00',
            $validator->errors()->first('non_operational_period'));
    }

    public function test_trip_creation_blocked_during_midnight_spanning_non_operational_period()
    {
        // Set current time to Monday 23:00 (non-operational hours that span midnight)
        Carbon::setTestNow(Carbon::parse('2025-01-06 23:00:00')); // Monday

        // Create non-operational period from 22:00 to 06:00 (spans midnight)
        PricingRuleNonOperationalPeriod::create([
            'day_charge_id' => $this->dayCharge->id,
            'start_at' => '22:00',
            'end_at' => '06:00',
        ]);

        $request = new TripStoreRequest;
        $validator = Validator::make([], []);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('validateNonOperationalPeriods');
        $method->setAccessible(true);

        // Call the validation method
        $method->invoke($request, $validator);

        // Should have an error since 23:00 is in non-operational period
        $this->assertTrue($validator->errors()->has('non_operational_period'));
        $this->assertStringContainsString('Service is unavailable from 22:00 to 06:00',
            $validator->errors()->first('non_operational_period'));
    }

    public function test_trip_creation_allowed_when_no_non_operational_periods_defined()
    {
        // Set current time to Monday 13:00
        Carbon::setTestNow(Carbon::parse('2025-01-06 13:00:00')); // Monday

        // Don't create any non-operational periods

        $request = new TripStoreRequest;
        $validator = Validator::make([], []);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('validateNonOperationalPeriods');
        $method->setAccessible(true);

        // Call the validation method
        $method->invoke($request, $validator);

        // Should not have any errors since no non-operational periods are defined
        $this->assertFalse($validator->errors()->has('non_operational_period'));
    }

    public function test_trip_creation_allowed_when_no_day_configuration_exists()
    {
        // Set current time to Tuesday (no day configuration exists)
        Carbon::setTestNow(Carbon::parse('2025-01-07 13:00:00')); // Tuesday

        $request = new TripStoreRequest;
        $validator = Validator::make([], []);

        // Use reflection to access protected method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('validateNonOperationalPeriods');
        $method->setAccessible(true);

        // Call the validation method
        $method->invoke($request, $validator);

        // Should not have any errors since no day configuration exists
        $this->assertFalse($validator->errors()->has('non_operational_period'));
    }

    public function test_time_to_minutes_conversion()
    {
        $request = new TripStoreRequest;

        // Use reflection to access private method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('timeToMinutes');
        $method->setAccessible(true);

        // Test various time conversions
        $this->assertEquals(0, $method->invoke($request, '00:00'));
        $this->assertEquals(60, $method->invoke($request, '01:00'));
        $this->assertEquals(90, $method->invoke($request, '01:30'));
        $this->assertEquals(720, $method->invoke($request, '12:00'));
        $this->assertEquals(1439, $method->invoke($request, '23:59'));
    }

    public function test_is_time_in_non_operational_period_normal_period()
    {
        $request = new TripStoreRequest;

        // Use reflection to access private method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('isTimeInNonOperationalPeriod');
        $method->setAccessible(true);

        // Test normal period (12:00 to 14:00)
        $this->assertTrue($method->invoke($request, '12:00', '12:00', '14:00'));
        $this->assertTrue($method->invoke($request, '13:00', '12:00', '14:00'));
        $this->assertFalse($method->invoke($request, '14:00', '12:00', '14:00'));
        $this->assertFalse($method->invoke($request, '11:59', '12:00', '14:00'));
        $this->assertFalse($method->invoke($request, '15:00', '12:00', '14:00'));
    }

    public function test_is_time_in_non_operational_period_midnight_spanning()
    {
        $request = new TripStoreRequest;

        // Use reflection to access private method
        $reflection = new \ReflectionClass($request);
        $method = $reflection->getMethod('isTimeInNonOperationalPeriod');
        $method->setAccessible(true);

        // Test midnight spanning period (22:00 to 06:00)
        $this->assertTrue($method->invoke($request, '22:00', '22:00', '06:00'));
        $this->assertTrue($method->invoke($request, '23:00', '22:00', '06:00'));
        $this->assertTrue($method->invoke($request, '00:00', '22:00', '06:00'));
        $this->assertTrue($method->invoke($request, '05:59', '22:00', '06:00'));
        $this->assertFalse($method->invoke($request, '06:00', '22:00', '06:00'));
        $this->assertFalse($method->invoke($request, '12:00', '22:00', '06:00'));
        $this->assertFalse($method->invoke($request, '21:59', '22:00', '06:00'));
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset Carbon test time
        parent::tearDown();
    }
}
