<?php

namespace Tests\Unit;

use App\Models\PricingRuleGender;
use App\Services\Pricing\Factors\GenderPricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class GenderPricingTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->globalRules = new GlobalPricingRules();
    }

    public function test_gender_pricing_calculation()
    {
        // Create a gender pricing rule
        $genderRule = PricingRuleGender::create([
            'gender' => 'female',
            'base_fare_adjustment_type' => 'percentage',
            'base_fare_fixed' => 0.0, // Not used for percentage
            'base_fare_percentage' => 15.0, // 15% of base fare
            'distance_fare_adjustment_type' => 'fixed',
            'distance_fare_fixed' => 2.0, // Fixed 2.0 additional per km
            'distance_fare_percentage' => 0.0, // Not used for fixed
        ]);

        // Create the pricing factor
        $factor = new GenderPricingFactor('female');
        
        // Set up test values
        $baseFare = 50.0;
        $distanceFare = 5.0;
        $distance = 10.0;
        
        // Calculate the adjustment
        $adjustment = $factor->calculateAdjustment(
            $baseFare,
            $distanceFare,
            $distance,
            $this->globalRules
        );
        
        // Log the adjustment for debugging
        Log::info('Gender pricing calculation test result', [
            'adjustment' => $adjustment,
            'gender_rule' => $genderRule->toArray(),
        ]);
        
        // Assert the results
        $this->assertNotNull($adjustment);
        $this->assertEquals(7.5, $adjustment['base_fare']); // 50 * 0.15 = 7.5
        $this->assertEquals(2.0, $adjustment['per_km']); // Fixed 2.0
        $this->assertEquals(27.5, $adjustment['total']); // 7.5 + (2.0 * 10) = 27.5
        
        // Test with pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        $result = $calculator->calculate($distance);
        
        // Log the result for debugging
        Log::info('Gender pricing calculator test result', [
            'result' => $result->toArray(),
        ]);
        
        // Get the base fare and distance fare from global rules
        $globalBaseFare = $this->globalRules->getBaseFare();
        $globalDistanceFare = $this->globalRules->getDistancePerKm();
        
        // Calculate expected values
        $expectedBaseFareAdjustment = $globalBaseFare * 0.15; // 15% of base fare
        $expectedDistanceFareAdjustment = 2.0; // Fixed 2.0
        $expectedAdjustedBaseFare = $globalBaseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $globalDistanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);
        
        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEquals($expectedTotal, $resultArray['total'], 'Total price does not match expected value', 0.01);
    }
}
