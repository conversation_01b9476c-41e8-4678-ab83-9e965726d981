<?php

namespace Tests\Unit;

use App\Models\Driver;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class VehicleTrackingWidgetTest extends TestCase
{
    use RefreshDatabase;

    public function test_widget_loads_vehicle_data()
    {
        // Create test data with proper location
        $user = User::factory()->create([
            'status' => UserStatus::ONLINE,
        ]);
        $driver = Driver::factory()->create([
            'user_id' => $user->id,
            'global_status' => DriverGlobalStatus::active,
            'location' => DB::raw("ST_GeomFromText('POINT(13.1913 32.8872)', 4326)"),
        ]);

        $brand = VehicleBrand::factory()->create();
        $model = VehicleModel::factory()->create(['vehicle_brand_id' => $brand->id]);
        $vehicleType = VehicleType::factory()->create();

        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $vehicleType->id,
            'global_status' => VehicleStatus::active,
            'status' => VehicleStatus::online,
        ]);

        $vehicle->drivers()->attach($driver);

        // Test the widget
        $component = Livewire::test(VehiclesRealTimeMovement::class);

        $this->assertNotEmpty($component->get('vehicles'));
        $this->assertIsArray($component->get('vehicles'));
    }

    public function test_vehicle_tracking_service_returns_valid_data_structure()
    {
        // Create test data
        $user = User::factory()->create([
            'status' => UserStatus::ONLINE,
        ]);
        $driver = Driver::factory()->create([
            'user_id' => $user->id,
            'global_status' => DriverGlobalStatus::active,
            'location' => DB::raw("ST_GeomFromText('POINT(13.1913 32.8872)', 4326)"),
        ]);

        $brand = VehicleBrand::factory()->create();
        $model = VehicleModel::factory()->create(['vehicle_brand_id' => $brand->id]);
        $vehicleType = VehicleType::factory()->create();

        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $vehicleType->id,
            'global_status' => VehicleStatus::active,
            'status' => VehicleStatus::online,
        ]);

        $vehicle->drivers()->attach($driver);

        $vehicleData = VehicleTrackingService::getOptimizedVehicleData();

        $this->assertIsArray($vehicleData);

        if (! empty($vehicleData)) {
            $firstVehicle = $vehicleData[0];

            // Test required structure
            $this->assertArrayHasKey('id', $firstVehicle);
            $this->assertArrayHasKey('lat', $firstVehicle);
            $this->assertArrayHasKey('lng', $firstVehicle);
            $this->assertArrayHasKey('driver', $firstVehicle);
            $this->assertArrayHasKey('vehicle', $firstVehicle);
            $this->assertArrayHasKey('status', $firstVehicle);

            // Test driver structure
            $this->assertIsArray($firstVehicle['driver']);
            $this->assertArrayHasKey('id', $firstVehicle['driver']);
            $this->assertArrayHasKey('name', $firstVehicle['driver']);
            $this->assertArrayHasKey('phone', $firstVehicle['driver']);

            // Test vehicle structure
            $this->assertIsArray($firstVehicle['vehicle']);
            $this->assertArrayHasKey('model', $firstVehicle['vehicle']);
            $this->assertArrayHasKey('brand', $firstVehicle['vehicle']);
            $this->assertArrayHasKey('type', $firstVehicle['vehicle']);
        }
    }

    public function test_widget_polling_updates_vehicle_data()
    {
        // Create initial test data
        $user = User::factory()->create([
            'status' => UserStatus::ONLINE,
        ]);
        $driver = Driver::factory()->create([
            'user_id' => $user->id,
            'global_status' => DriverGlobalStatus::active,
            'location' => DB::raw("ST_GeomFromText('POINT(13.1913 32.8872)', 4326)"),
        ]);

        $brand = VehicleBrand::factory()->create();
        $model = VehicleModel::factory()->create(['vehicle_brand_id' => $brand->id]);
        $vehicleType = VehicleType::factory()->create();

        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $model->id,
            'vehicle_type_id' => $vehicleType->id,
            'global_status' => VehicleStatus::active,
            'status' => VehicleStatus::online,
        ]);

        $vehicle->drivers()->attach($driver);

        $component = Livewire::test(VehiclesRealTimeMovement::class);

        // Test initial load
        $initialVehicles = $component->get('vehicles');
        $this->assertNotEmpty($initialVehicles);

        // Simulate polling update
        $component->call('loadVehicles');

        // Verify data is still present after update
        $updatedVehicles = $component->get('vehicles');
        $this->assertNotEmpty($updatedVehicles);
        $this->assertIsArray($updatedVehicles);
    }

    public function test_vehicle_data_persistence_across_updates()
    {
        // Test that vehicle data structure remains consistent across polling updates
        $vehicleData = [
            'id' => 1,
            'lat' => 32.8872,
            'lng' => 13.1913,
            'driver' => [
                'id' => 1,
                'name' => 'Test Driver',
                'phone' => '+218 91-1234567',
                'rating' => 4.5,
                'gender' => 'male',
            ],
            'vehicle' => [
                'model' => 'Corolla',
                'brand' => 'Toyota',
                'type' => 'Economy',
                'rating' => 4.0,
            ],
            'status' => 'available',
            'current_trip' => null,
        ];

        // Test data validation
        $this->assertTrue($this->isValidVehicleDataStructure($vehicleData));

        // Test data cloning
        $clonedData = json_decode(json_encode($vehicleData), true);
        $this->assertEquals($vehicleData, $clonedData);
        $this->assertNotSame($vehicleData, $clonedData);
    }

    public function test_performance_with_multiple_vehicles()
    {
        $startTime = microtime(true);

        // Create multiple vehicles for performance testing
        for ($i = 0; $i < 50; $i++) {
            $user = User::factory()->create([
                'status' => UserStatus::ONLINE,
            ]);
            $driver = Driver::factory()->create([
                'user_id' => $user->id,
                'global_status' => DriverGlobalStatus::active,
                'location' => DB::raw("ST_GeomFromText('POINT(".(13.1913 + $i * 0.001).' '.(32.8872 + $i * 0.001).")', 4326)"),
            ]);

            $brand = VehicleBrand::factory()->create();
            $model = VehicleModel::factory()->create(['vehicle_brand_id' => $brand->id]);
            $vehicleType = VehicleType::factory()->create();

            $vehicle = Vehicle::factory()->create([
                'vehicle_model_id' => $model->id,
                'vehicle_type_id' => $vehicleType->id,
                'global_status' => VehicleStatus::active,
                'status' => VehicleStatus::online,
            ]);

            $vehicle->drivers()->attach($driver);
        }

        $vehicleData = VehicleTrackingService::getOptimizedVehicleData();

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Performance assertions
        $this->assertLessThan(2.0, $executionTime, 'Query should complete within 2 seconds for 50 vehicles');
        $this->assertCount(50, $vehicleData, 'Should return all 50 vehicles');
    }

    private function isValidVehicleDataStructure(array $vehicle): bool
    {
        return isset($vehicle['id']) &&
               isset($vehicle['driver']) &&
               isset($vehicle['vehicle']) &&
               is_array($vehicle['driver']) &&
               is_array($vehicle['vehicle']);
    }
}
