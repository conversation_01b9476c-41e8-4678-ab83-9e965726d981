<?php

namespace Tests\Unit;

use App\Models\VehicleType;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class PricingCalculatorPercentageTest extends TestCase
{
    use RefreshDatabase;

    protected $globalRules;

    protected function setUp(): void
    {
        parent::setUp();
        $this->globalRules = new GlobalPricingRules();
    }

    public function test_pricing_calculator_with_percentage_vehicle_type()
    {
        // Create a vehicle type with percentage adjustment
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Test Percentage Type',
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'additional_base_fare' => 0.0, // Not used for percentage
            'additional_price_per_km' => 0.0, // Not used for percentage
            'base_fare_adjustment' => 20.0, // 20% of base fare
            'distance_fare_adjustment' => 15.0, // 15% of distance fare
        ]);

        // Create the pricing factor
        $factor = new VehicleTypePricingFactor($vehicleType->id);
        
        // Create the pricing calculator
        $calculator = new PricingCalculator($this->globalRules);
        $calculator->addFactor($factor);
        
        // Calculate the price
        $distance = 10.0;
        $result = $calculator->calculate($distance);
        
        // Log the result for debugging
        Log::info('Pricing calculator percentage test result', [
            'result' => $result->toArray(),
            'vehicle_type' => $vehicleType->toArray(),
        ]);
        
        // Get the base fare and distance fare from global rules
        $baseFare = $this->globalRules->getBaseFare();
        $distanceFare = $this->globalRules->getDistancePerKm();
        
        // Calculate expected values
        $expectedBaseFareAdjustment = $baseFare * 0.2; // 20% of base fare
        $expectedDistanceFareAdjustment = $distanceFare * 0.15; // 15% of distance fare
        $expectedAdjustedBaseFare = $baseFare + $expectedBaseFareAdjustment;
        $expectedAdjustedDistanceFare = $distanceFare + $expectedDistanceFareAdjustment;
        $expectedTotal = $expectedAdjustedBaseFare + ($expectedAdjustedDistanceFare * $distance);
        
        // Assert the results
        $resultArray = $result->toArray();
        $this->assertEquals($expectedTotal, $resultArray['total'], 'Total price does not match expected value', 0.01);
        
        // Check the adjustments
        $adjustments = $result->getAdjustments();
        $this->assertArrayHasKey('vehicle_type_adjustment', $adjustments);
        $vehicleTypeAdjustment = $adjustments['vehicle_type_adjustment'];
        $this->assertEquals($expectedBaseFareAdjustment, $vehicleTypeAdjustment['base_fare'], 'Base fare adjustment does not match expected value', 0.01);
        $this->assertEquals($expectedDistanceFareAdjustment, $vehicleTypeAdjustment['per_km'], 'Distance fare adjustment does not match expected value', 0.01);
    }
}
