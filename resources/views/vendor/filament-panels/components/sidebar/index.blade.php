@props(['navigation'])

@php
    $openSidebarClasses =
        'fi-sidebar-open w-[--sidebar-width] translate-x-0 shadow-xl ring-1 ring-gray-950/5 dark:ring-white/10 rtl:-translate-x-0';
    $isRtl = __('filament-panels::layout.direction') === 'rtl';
@endphp

{{-- format-ignore-start --}}
<aside x-data="{}" @if (filament()->isSidebarCollapsibleOnDesktop() && !filament()->hasTopNavigation()) x-cloak
    x-bind:class="
            $store.sidebar.isOpen
                ? @js($openSidebarClasses . ' ' . 'lg:sticky')
                : '-translate-x-full rtl:translate-x-full lg:sticky lg:translate-x-0 rtl:lg:-translate-x-0'
" @else @if (filament()->hasTopNavigation()) x-cloak
    x-bind:class="$store.sidebar.isOpen ? @js($openSidebarClasses) : '-translate-x-full rtl:translate-x-full'" @elseif (filament()->isSidebarFullyCollapsibleOnDesktop()) x-cloak
            x-bind:class="$store.sidebar.isOpen ? @js($openSidebarClasses . ' ' . 'lg:sticky') : '-translate-x-full rtl:translate-x-full'"
            @else x-cloak="-lg" x-bind:class="
                                                                $store.sidebar.isOpen
                                                                    ? @js($openSidebarClasses . ' ' . 'lg:sticky')
                                                                    : 'w-[--sidebar-width] -translate-x-full rtl:translate-x-full lg:sticky'
" @endif @endif {{ $attributes->class([
    'fi-sidebar fixed inset-y-0 start-0 z-30 flex flex-col h-screen content-start bg-white transition-all dark:bg-gray-900 lg:z-0 lg:bg-transparent lg:shadow-none lg:ring-0 lg:transition-none dark:lg:bg-transparent',
    'lg:translate-x-0 rtl:lg:-translate-x-0' => !(
        filament()->isSidebarCollapsibleOnDesktop() ||
        filament()->isSidebarFullyCollapsibleOnDesktop() ||
        filament()->hasTopNavigation()
    ),
    'lg:-translate-x-full rtl:lg:translate-x-full' => filament()->hasTopNavigation(),
]) }}>
    <div class="overflow-x-clip">
        <header
            class="flex items-center h-16 px-6 bg-white fi-sidebar-header ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 lg:shadow-sm">

            <div @if (filament()->isSidebarCollapsibleOnDesktop()) x-show="$store.sidebar.isOpen"
                x-transition:enter="lg:transition lg:delay-100" x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100" @endif>
                @if ($homeUrl = filament()->getHomeUrl())
                    <a {{ \Filament\Support\generate_href_html($homeUrl) }}>
                        <x-filament-panels::logo />
                    </a>
                @else
                    <x-filament-panels::logo />
                @endif
            </div>
            <div x-show="!$store.sidebar.isOpen" x-transition:enter="lg:transition lg:delay-100"
                x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100">
                @if ($homeUrl = filament()->getHomeUrl())
                    <a {{ \Filament\Support\generate_href_html($homeUrl) }}>
                        <x-logo-small />
                    </a>
                @else
                    <x-logo-small />
                @endif
            </div>

            @if (filament()->isSidebarCollapsibleOnDesktop())
                <x-sidebar-toggle />
            @endif
        </header>
    </div>

    <nav class="flex flex-col flex-grow py-8 overflow-x-hidden overflow-y-auto fi-sidebar-nav gap-y-2" x-bind:class="
            $store.sidebar.isOpen
                ? 'px-4'
                : 'px-4'
        " style="scrollbar-gutter: stable">
        {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::SIDEBAR_NAV_START) }}
        <div x-data="{ sidebarVisible: true }" @toggle-sidebar.window="sidebarVisible = $event.detail.visible">
            <ul class="flex flex-col -mx-2 fi-sidebar-nav-groups gap-y-7" x-show="sidebarVisible">
                @foreach ($navigation as $group)
                    <x-filament-panels::sidebar.group :active="$group->isActive()" :collapsible="$group->isCollapsible()"
                        :icon="$group->getIcon()" :items="$group->getItems()" :label="$group->getLabel()"
                        :attributes="\Filament\Support\prepare_inherited_attributes($group->getExtraSidebarAttributeBag())" />
                @endforeach
            </ul>
        </div>

        <script>
            var collapsedGroups = JSON.parse(
                localStorage.getItem('collapsedGroups'),
            )

            if (collapsedGroups === null || collapsedGroups === 'null') {
                localStorage.setItem(
                    'collapsedGroups',
                    JSON.stringify(@js(collect($navigation)->filter(fn(\Filament\Navigation\NavigationGroup $group): bool => $group->isCollapsed())->map(fn(\Filament\Navigation\NavigationGroup $group): string => $group->getLabel())->values())),
                )
            }

            collapsedGroups = JSON.parse(
                localStorage.getItem('collapsedGroups'),
            )

            document
                .querySelectorAll('.fi-sidebar-group')
                .forEach((group) => {
                    if (
                        !collapsedGroups.includes(group.dataset.groupLabel)
                    ) {
                        return
                    }
                    // Alpine.js loads too slow, so attempt to hide a
                    // collapsed sidebar group earlier.
                    group.querySelector(
                        '.fi-sidebar-group-items',
                    ).style.display = 'none'
                    group
                        .querySelector('.fi-sidebar-group-collapse-button')
                        .classList.add('rotate-180')
                })
        </script>

        {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::SIDEBAR_NAV_END) }}
    </nav>
    {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::SIDEBAR_FOOTER) }}
</aside>