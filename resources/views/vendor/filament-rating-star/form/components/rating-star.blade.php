<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
  @php
      $id = $getId();
      $isDisabled = $isDisabled();
      $statePath = $getStatePath();
      $options = config('filament-rating-star.stars');
  @endphp

  <div class="ratings">
    <div class="stars mb-3">
      @foreach ($options as $key => $value)
            @php
                $shouldOptionBeDisabled = $isDisabled || $isOptionDisabled($key, $value);
                $uniqueId = $id . '-' . $key; // Unique ID for each input element
            @endphp
            <input
                @disabled($shouldOptionBeDisabled)
                id="{{ $uniqueId }}"
                name="{{ $id }}"
                type="radio"
                value="{{ $value }}"
                wire:loading.attr="disabled"
                {{ $applyStateBindingModifiers('wire:model') }}="{{ $statePath }}"
                class="star-{{ $value }}"
            />
            <label class="star-{{ $value }}" for="{{ $uniqueId }}"></label>
        @endforeach
        <span></span>
    </div>
  </div>

</x-dynamic-component>

<style>
.ratings {
  position: relative;
  z-index: 0;
}

.ratings textarea.form-control {
  height: 80px;
  font-size: 14px;
}

.ratings .stars {
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAABaCAYAAACv+ebYAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAABx0RVh0U29mdHdhcmUAQWRvYmUgRmlyZXdvcmtzIENTNXG14zYAAAAWdEVYdENyZWF0aW9uIFRpbWUAMDcvMDMvMTNJ3Rb7AAACnklEQVRoge2XwW3bMBSGPxa9NxtIGzTAW8DdRL7o3A0qb+BrdNIm9QAm0G7gbJBMwB5MoVJNUSRFIXGqHwhkmXr68hOPNH9ljOEt9OlNqBs4RlrrSmtdpdZ/Ti0EGnvtUoqTHFunBVCkuk6d6mbi83rggdteSa5THDeB3+UDO9z2inatXFum1roESuAReAB29vp15n2/gRfgZK+/gIuIXLxgrfUO+Bnzn0fom4ic+pvRVNuB/QrQ/RB6A7bwLjN8b985krO5MsKd0ElwJvgk1AteCPdCYWI5/SutddQxRUTU3DOzG4hd01EKqQnZuaLBITUh4F0CeLYm5CDw6PjuFTjaz9+BLwE1I8VO9StwAEoRaUSkseMHO+aqcWq2qwcdfQCOIvIy8dwDV/c/YL6zvWDbnQ3QuH5hltQEreM1dH/n6g28gT8eWLVUqqVKrb+vtGidFkCR6vp+0uLAba8k1/eRFh1ue0W7dv4sqpaSjGnR1Fy8YNWyY8W0aGpO/c1oqu3AKmlxCL0BW3iXGb637xzJ2VwZ4U7oJDgTfBLqBS+Ee6EQeMpULVFHUVOzPC3aNR2lkJotLbr0vtKiqWlMTcNaaXHQ0QfgaGqcaVG1jNLibGcbYyb/eDIlT6bjyZS+51JqtrS4gTfw/wzWqkKrKrU8fQPR6gKAmDKlPM3x1WkBFKmu0xxf3fZR5jnFdbzjv257JbmOdzx22yvadZzjW7e9ol27HWtVkjEtIubiB2u1Y8W0iJhTfzOe6uvAKmlxCL0FX+FdZvjevnMkd3Plgzuh0+A88EmoH7wM7oVC6AaiVdwuI2Z5WrRrOk4BNVtadOl9pUXENIhpWCstDjr6ABwR40yLaDVKi7Od7U1/Z0pzpjNngtNiaM2WFj8++A+motm0NTqjmwAAAABJRU5ErkJggg==") repeat-x 0 0;
  width: 150px;
}

.ratings .stars:before,
.ratings .stars:after {
  display: table;
  content: "";
}

.ratings .stars:after {
  clear: both;
}

.ratings .stars input[type=radio] {
  position: absolute;
  opacity: 0;
}

.ratings .stars input[type=radio].star-5:checked~span {
  width: 100%;
}

.ratings .stars input[type=radio].star-4:checked~span {
  width: 80%;
}

.ratings .stars input[type=radio].star-3:checked~span {
  width: 60%;
}

.ratings .stars input[type=radio].star-2:checked~span {
  width: 40%;
}

.ratings .stars input[type=radio].star-1:checked~span {
  width: 20%;
}

.ratings .stars label {
  display: block;
  width: 30px;
  height: 30px;
  margin: 0 !important;
  padding: 0 !important;
  text-indent: -99999rem;
  float: left;
  position: relative;
  z-index: 10;
  background: transparent !important;
  cursor: pointer;
}

.ratings .stars label:hover~span {
  background-position: 0 -30px;
}

.ratings .stars label.star-5:hover~span {
  width: 100% !important;
}

.ratings .stars label.star-4:hover~span {
  width: 80% !important;
}

.ratings .stars label.star-3:hover~span {
  width: 60% !important;
}

.ratings .stars label.star-2:hover~span {
  width: 40% !important;
}

.ratings .stars label.star-1:hover~span {
  width: 20% !important;
}

.ratings .stars span {
  display: block;
  width: 0;
  position: relative;
  top: 0;
  left: 0;
  height: 30px;
  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAABaCAYAAACv+ebYAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAABx0RVh0U29mdHdhcmUAQWRvYmUgRmlyZXdvcmtzIENTNXG14zYAAAAWdEVYdENyZWF0aW9uIFRpbWUAMDcvMDMvMTNJ3Rb7AAACnklEQVRoge2XwW3bMBSGPxa9NxtIGzTAW8DdRL7o3A0qb+BrdNIm9QAm0G7gbJBMwB5MoVJNUSRFIXGqHwhkmXr68hOPNH9ljOEt9OlNqBs4RlrrSmtdpdZ/Ti0EGnvtUoqTHFunBVCkuk6d6mbi83rggdteSa5THDeB3+UDO9z2inatXFum1roESuAReAB29vp15n2/gRfgZK+/gIuIXLxgrfUO+Bnzn0fom4ic+pvRVNuB/QrQ/RB6A7bwLjN8b985krO5MsKd0ElwJvgk1AteCPdCYWI5/SutddQxRUTU3DOzG4hd01EKqQnZuaLBITUh4F0CeLYm5CDw6PjuFTjaz9+BLwE1I8VO9StwAEoRaUSkseMHO+aqcWq2qwcdfQCOIvIy8dwDV/c/YL6zvWDbnQ3QuH5hltQEreM1dH/n6g28gT8eWLVUqqVKrb+vtGidFkCR6vp+0uLAba8k1/eRFh1ue0W7dv4sqpaSjGnR1Fy8YNWyY8W0aGpO/c1oqu3AKmlxCL0BW3iXGb637xzJ2VwZ4U7oJDgTfBLqBS+Ee6EQeMpULVFHUVOzPC3aNR2lkJotLbr0vtKiqWlMTcNaaXHQ0QfgaGqcaVG1jNLibGcbYyb/eDIlT6bjyZS+51JqtrS4gTfw/wzWqkKrKrU8fQPR6gKAmDKlPM3x1WkBFKmu0xxf3fZR5jnFdbzjv257JbmOdzx22yvadZzjW7e9ol27HWtVkjEtIubiB2u1Y8W0iJhTfzOe6uvAKmlxCL0FX+FdZvjevnMkd3Plgzuh0+A88EmoH7wM7oVC6AaiVdwuI2Z5WrRrOk4BNVtadOl9pUXENIhpWCstDjr6ABwR40yLaDVKi7Od7U1/Z0pzpjNngtNiaM2WFj8++A+motm0NTqjmwAAAABJRU5ErkJggg==") repeat-x 0 -60px;
  -webkit-transition: width 0.5s;
  -o-transition: width 0.5s;
  transition: width 0.5s;
}
</style>
