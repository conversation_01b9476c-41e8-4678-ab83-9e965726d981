<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-orange-600 to-orange-800 overflow-hidden shadow-lg rounded-lg">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold">Trips Report</h1>
                        <p class="text-orange-100 mt-1">Comprehensive analysis of trip volume, revenue, and performance
                        </p>
                    </div>
                    <div class="text-right">
                        <x-heroicon-o-map class="h-12 w-12 text-orange-200" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Form -->
        <div
            class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <x-heroicon-o-funnel class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Report Filters</h3>
                </div>

                {{ $this->form }}

                <div class="mt-6">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        @php $tripsData = $this->getTripsData(); @endphp
                        <span class="font-medium">{{ $tripsData['period_info'] ?? 'All time data' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Total Trips -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                            <x-heroicon-o-map class="h-8 w-8 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                Total Trips
                            </h3>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                {{ number_format($tripsData['total_trips']) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                            <x-heroicon-o-currency-dollar class="h-8 w-8 text-green-600 dark:text-green-400" />
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                Total Revenue
                            </h3>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">
                                {{ number_format($tripsData['total_revenue'], 2) }} LYD
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Trips -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                            <x-heroicon-o-check-circle class="h-8 w-8 text-green-600 dark:text-green-400" />
                        </div>
                        <div class="ml-4 flex-1">
                            <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                Completed Trips
                            </h3>
                            <p class="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">
                                {{ number_format($tripsData['status_distribution']['completed'] ?? 0) }}
                            </p>
                            @php
                                $completionRate = $tripsData['total_trips'] > 0
                                    ? round((($tripsData['status_distribution']['completed'] ?? 0) / $tripsData['total_trips']) * 100, 1)
                                    : 0;
                            @endphp
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                {{ $completionRate }}% completion rate
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Distribution -->
        <div
            class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <div class="flex items-center mb-6">
                    <x-heroicon-o-chart-pie class="h-6 w-6 text-gray-600 dark:text-gray-400 mr-3" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                        Trip Status Breakdown
                    </h3>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach(\App\Enums\Trips\TripStatus::cases() as $status)
                        @php
                            $count = $tripsData['status_distribution'][$status->value] ?? 0;
                            $revenue = $tripsData['revenue_by_status'][$status->value] ?? 0;
                            $percentage = $tripsData['total_trips'] > 0 ? round(($count / $tripsData['total_trips']) * 100, 1) : 0;
                            $revenuePercentage = $tripsData['total_revenue'] > 0 ? round(($revenue / $tripsData['total_revenue']) * 100, 1) : 0;
                        @endphp

                        @php
                            $bgColor = match ($status->getColor()) {
                                'success' => 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
                                'danger' => 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
                                'warning' => 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
                                'info' => 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
                                'gray' => 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700',
                                'orange' => 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800',
                                'amber' => 'bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800',
                                'teal' => 'bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800',
                                'purple' => 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800',
                                default => 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700'
                            };

                            $iconColor = match ($status->getColor()) {
                                'success' => 'text-green-600 dark:text-green-400',
                                'danger' => 'text-red-600 dark:text-red-400',
                                'warning' => 'text-yellow-600 dark:text-yellow-400',
                                'info' => 'text-blue-600 dark:text-blue-400',
                                'gray' => 'text-gray-600 dark:text-gray-400',
                                'orange' => 'text-orange-600 dark:text-orange-400',
                                'amber' => 'text-amber-600 dark:text-amber-400',
                                'teal' => 'text-teal-600 dark:text-teal-400',
                                'purple' => 'text-purple-600 dark:text-purple-400',
                                default => 'text-gray-600 dark:text-gray-400'
                            };

                            $progressColor = match ($status->getColor()) {
                                'success' => '#10b981',
                                'danger' => '#ef4444',
                                'warning' => '#f59e0b',
                                'info' => '#3b82f6',
                                'gray' => '#6b7280',
                                'orange' => '#f97316',
                                'amber' => '#f59e0b',
                                'teal' => '#14b8a6',
                                'purple' => '#8b5cf6',
                                default => '#6b7280'
                            };
                        @endphp

                        <div class="border rounded-lg p-4 hover:shadow-md transition-shadow {{ $bgColor }}">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <x-dynamic-component :component="$status->getIcon()"
                                        class="h-5 w-5 mr-3 {{ $iconColor }}" />
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $status->getLabel() }}
                                    </span>
                                </div>
                                <div class="text-right">
                                    <div class="text-xl font-bold text-gray-900 dark:text-white">
                                        {{ number_format($count) }}
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-2 mb-3">
                                <div class="flex justify-between">
                                    <span class="text-xs text-gray-600 dark:text-gray-400">Trips:</span>
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ $percentage }}%
                                    </span>
                                </div>

                                <div class="flex justify-between">
                                    <span class="text-xs text-gray-600 dark:text-gray-400">Revenue:</span>
                                    <span class="text-sm font-semibold text-gray-900 dark:text-white">
                                        {{ number_format($revenue, 2) }} LYD
                                    </span>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mb-2">
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full transition-all duration-300"
                                        style="width: {{ $percentage }}%; background-color: {{ $progressColor }}"></div>
                                </div>
                            </div>

                            <div class="flex justify-between items-center text-xs">
                                <span class="text-gray-600 dark:text-gray-400">
                                    {{ $percentage }}% of total
                                </span>
                                @if($count > 0)
                                    <span class="font-medium {{ $iconColor }}">
                                        {{ $revenuePercentage }}% revenue
                                    </span>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Additional Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Trip Performance -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <x-heroicon-o-chart-bar class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                            Trip Performance
                        </h4>
                    </div>
                    @php
                        $completedTrips = $tripsData['status_distribution']['completed'] ?? 0;
                        $canceledTrips = $tripsData['status_distribution']['canceled'] ?? 0;
                        $completionRate = $tripsData['total_trips'] > 0 ? round(($completedTrips / $tripsData['total_trips']) * 100, 1) : 0;
                        $cancellationRate = $tripsData['total_trips'] > 0 ? round(($canceledTrips / $tripsData['total_trips']) * 100, 1) : 0;
                        $averageRevenue = $completedTrips > 0 ? round($tripsData['revenue_by_status']['completed'] / $completedTrips, 2) : 0;
                    @endphp
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Completion Rate:</span>
                            <span class="font-semibold text-green-600">{{ $completionRate }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Cancellation Rate:</span>
                            <span class="font-semibold text-red-600">{{ $cancellationRate }}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Avg. Revenue per Trip:</span>
                            <span
                                class="font-semibold text-gray-900 dark:text-white">{{ number_format($averageRevenue, 2) }}
                                LYD</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Distance & Time -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <x-heroicon-o-clock class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                            Distance & Time Metrics
                        </h4>
                    </div>
                    @php
                        $averageDistance = \App\Models\Trip::whereNotNull('distance')->avg('distance');
                        $totalDistance = \App\Models\Trip::whereNotNull('distance')->sum('distance');
                    @endphp
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Total Distance:</span>
                            <span
                                class="font-semibold text-gray-900 dark:text-white">{{ number_format($totalDistance, 1) }}
                                km</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">Average Distance:</span>
                            <span
                                class="font-semibold text-gray-900 dark:text-white">{{ number_format($averageDistance, 1) }}
                                km</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>