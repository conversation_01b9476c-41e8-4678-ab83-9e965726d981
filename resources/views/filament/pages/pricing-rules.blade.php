<x-filament-panels::page x-data="{ tabId: 1 }">
    <div class="mb-6">
        @livewire('pricing-rules.global-pricing-view')
    </div>
    <x-filament::tabs label="Content tabs">
        <!-- Tab Items -->
        <x-filament::tabs.item icon="mdi-alpha-g-circle" alpine-active="tabId === 1" x-on:click="tabId = 1">
            Global Rules
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="mdi-map-search" alpine-active="tabId === 2" x-on:click="tabId = 2">
            Area
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="mdi-clock-time-eight" alpine-active="tabId === 3" x-on:click="tabId = 3">
            Day-Time
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="mdi-train-car" alpine-active="tabId === 4" x-on:click="tabId = 4">
            Vehicle Type
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="mdi-gender-female" alpine-active="tabId === 5" x-on:click="tabId = 5">
            Gender
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="heroicon-o-squares-plus" alpine-active="tabId === 6" x-on:click="tabId = 6">
            Equipment
        </x-filament::tabs.item>

        <x-filament::tabs.item icon="mdi-seat-passenger" alpine-active="tabId === 7" x-on:click="tabId = 7">
            Seat Capacity
        </x-filament::tabs.item>
    </x-filament::tabs>

    <!-- Tab Contents -->
    <div x-show="tabId === 1">
        @livewire('pricing-rules.global-pricing-rules')
    </div>

    <div x-show="tabId === 2">
        @livewire('pricing-rules.area-specific-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>
    <div x-show="tabId === 3">

        <div>
            @livewire('pricing-rules.global-time')
        </div>

        <br>
        @livewire('pricing-rules.time-based-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>

    <div x-show="tabId === 4">
        @livewire('pricing-rules.vehicle-type-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>

    <div x-show="tabId === 5">
        @livewire('pricing-rules.gender-based-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>

    <div x-show="tabId === 6">
        @livewire('pricing-rules.equipment-based-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>

    <div x-show="tabId === 7">
        @livewire('pricing-rules.seat-capacity-pricing')
        <div class="flex items-center p-4">
            <x-heroicon-o-information-circle class="w-6 h-6 mr-4 text-primary" />
            <p class="text-lg font-bold text-gray-700 dark:text-white">
                The entered value will be calculated along with the global price and any other applicable conditions.
            </p>
        </div>
    </div>
</x-filament-panels::page>