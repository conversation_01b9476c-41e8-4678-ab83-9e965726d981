<div x-data="{
        map: null,
        polygon: null,
        initialize() {
            this.map = new google.maps.Map(this.$refs.map, {
                center: { lat: {{ $polygon[0]['lat'] ?? 0 }}, lng: {{ $polygon[0]['lng'] ?? 0 }} },
                zoom: 12,
            });

            if ({{ count($polygon) }} > 0) {
                const paths = {{ json_encode($polygon) }}.map(point => ({ lat: point.lat, lng: point.lng }));

             this.polygon = new google.maps.Polygon({
                paths:paths,

                strokeColor: '#dd9f2b',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#dd9f2b',  // Using the same color as in PolygonMapField
                fillOpacity: 0.35
            });
                this.polygon.setMap(this.map);
            }
        }
    }" x-init="initialize()">
    {{--
    <pre>{{ json_encode($polygon, JSON_PRETTY_PRINT) }}</pre> --}}

    <div x-ref="map" style="height: 600px; width: 100%;"></div>
</div>

@assets
<script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}"></script>
@endassets
