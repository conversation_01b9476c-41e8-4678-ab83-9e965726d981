<div
    x-data="{
        map: null,
        directionsService: null,
        directionsRenderer: null,
        initialize() {
            this.map = new google.maps.Map(this.$refs.map, {
                center: { lat: {{ $departure['lat'] ?? 0 }}, lng: {{ $departure['lng'] ?? 0 }} },
                zoom: 12,
            });
            this.directionsService = new google.maps.DirectionsService();
            this.directionsRenderer = new google.maps.DirectionsRenderer();
            this.directionsRenderer.setMap(this.map);
            this.calculateAndDisplayRoute();
        },
        calculateAndDisplayRoute() {
            this.directionsService.route(
                {
                    origin: { lat: {{ $departure['lat'] ?? 0 }}, lng: {{ $departure['lng'] ?? 0 }} },
                    destination: { lat: {{ $arrival['lat'] ?? 0 }}, lng: {{ $arrival['lng'] ?? 0 }} },
                    travelMode: google.maps.TravelMode.DRIVING,
                },
                (response, status) => {
                    if (status === 'OK') {
                        this.directionsRenderer.setDirections(response);
                    } else {
                        alert('Directions request failed due to ' + status);
                    }
                }
            );
        }
    }"
    x-init="initialize()"
>
    <div x-ref="map" style="height: 400px; width: 100%;"></div>
</div>

@assets
<script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}"></script>
@endassets
