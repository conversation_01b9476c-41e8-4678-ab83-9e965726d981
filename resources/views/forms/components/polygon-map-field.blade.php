<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
    <div wire:ignore x-data="polygonMapField({
            state: $wire.$entangle('{{ $getStatePath() }}'),
            existingPolygons: {{ json_encode($getExistingPolygons() ?? []) }},
            defaultLat: {{ $getDefaultLat() }},
            defaultLng: {{ $getDefaultLng() }},
            defaultZoom: {{ $getDefaultZoom() }},
            drawingColor: '{{ $getDrawingColor() }}',
            existingPolygonsColor: '{{ $getExistingPolygonsColor() }}',
            fillOpacity: {{ $getFillOpacity() }},
        })" x-init="init()" class="w-full">
        <input id="{{ $getId() }}" name="{{ $getName() }}" {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}" type="hidden" />

        <div class="flex flex-col space-y-2">
            <div x-ref="map" class="w-full h-96 rounded-lg border border-gray-300">
            </div>

            <div x-show="hasOverlap"
                class="p-3 text-sm font-medium text-red-600 !bg-red-100 border border-red-400 rounded-md shadow-sm">
                <span class="flex items-center">
                    <svg class="w-5 h-5 mr-2 text-red-600" fill="currentColor" viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span style="color: #e53e3e;">Warning: Your polygon <strong>overlaps with existing areas</strong>.
                        Please reposition.</span>
                </span>
            </div>
        </div>
    </div>
</x-dynamic-component>

@pushOnce('scripts')
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ config('filament-google-maps.keys.web_key') }}&libraries=drawing,geometry&callback=initPolygonMapFields&v=weekly"></script>
    <script>
        // Global initialization function that will be called when Google Maps API is loaded
        function initPolygonMapFields() {
            document.dispatchEvent(new CustomEvent('google-maps-loaded'));
        }

        // Handle API loading errors
        window.gm_authFailure = function () {
            document.dispatchEvent(new CustomEvent('google-maps-error', {
                detail: { message: 'Google Maps API key authentication failed' }
            }));
        };


        document.addEventListener('alpine:init', () => {
            Alpine.data('polygonMapField', ({
                state,
                existingPolygons,
                defaultLat,
                defaultLng,
                defaultZoom,
                drawingColor,
                existingPolygonsColor,
                fillOpacity,
                mapsLoaded,
            }) => ({
                map: null,
                drawingManager: null,
                currentPolygon: null,
                existingPolygonObjects: [],
                hasOverlap: false,
                isEditMode: false,

                init() {
                    // Check if state exists to determine if we're in edit mode
                    this.isEditMode = state && (
                        (typeof state === 'string' && state.length > 0 && state !== '[]') ||
                        (Array.isArray(state) && state.length > 0) ||
                        (state.initialValue && state.initialValue !== '[]')
                    );

                    // Check if Google Maps is already loaded
                    if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                        this.mapsLoaded = true;
                        this.initializeMap();
                    } else {
                        // Wait for Google Maps to load
                        document.addEventListener('google-maps-loaded', () => {
                            this.mapsLoaded = true;
                            this.initializeMap();
                        });

                        // Listen for Google Maps errors
                        document.addEventListener('google-maps-error', (event) => {
                            console.error('Google Maps error event received', event.detail);
                        });

                        // Fallback in case the callback doesn't work
                        setTimeout(() => {
                            if (!this.mapsLoaded) {
                                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                                    this.initializeMap();
                                } else {
                                    console.error('Google Maps failed to load after timeout');
                                }
                            }
                        }, 5000);
                    }
                },

                initializeMap() {
                    try {
                        // Create the map
                        this.map = new google.maps.Map(this.$refs.map, {
                            center: { lat: defaultLat, lng: defaultLng },
                            zoom: defaultZoom,
                            mapTypeId: google.maps.MapTypeId.ROADMAP,
                            mapTypeControl: true,
                            scrollwheel: true,
                            fullscreenControl: true
                        });

                        // Create the drawing manager with appropriate configuration based on edit mode
                        this.drawingManager = new google.maps.drawing.DrawingManager({
                            drawingMode: this.isEditMode ? null : google.maps.drawing.OverlayType.POLYGON,
                            drawingControl: false, // Hide built-in drawing controls
                            polygonOptions: {
                                fillColor: drawingColor,
                                fillOpacity: fillOpacity,
                                strokeWeight: 2,
                                strokeColor: drawingColor,
                                clickable: true,
                                editable: true,
                                draggable: false,
                                zIndex: 1
                            }
                        });

                        // Set the drawing manager on the map in create mode only
                        if (!this.isEditMode) {
                            this.drawingManager.setMap(this.map);
                        }

                        this.addExistingPolygons();

                        // Add listeners for polygon complete
                        google.maps.event.addListener(this.drawingManager, 'polygoncomplete', (polygon) => {
                            // Clear any existing polygon
                            this.removeCurrentPolygon();

                            this.currentPolygon = polygon;

                            // Make the polygon editable but not draggable
                            this.currentPolygon.setOptions({
                                editable: true,
                                draggable: false
                            });

                            // Check for overlaps
                            this.checkForOverlaps();

                            // Update the state with the polygon coordinates
                            this.updateStateFromPolygon(polygon);

                            // Add listeners for polygon changes
                            google.maps.event.addListener(polygon.getPath(), 'set_at', () => {
                                this.checkForOverlaps();
                                this.updateStateFromPolygon(polygon);
                            });

                            google.maps.event.addListener(polygon.getPath(), 'insert_at', () => {
                                this.checkForOverlaps();
                                this.updateStateFromPolygon(polygon);
                            });

                            google.maps.event.addListener(polygon, 'dragend', () => {
                                this.checkForOverlaps();
                                this.updateStateFromPolygon(polygon);
                            });

                            // Disable further drawing after one polygon is created
                            this.drawingManager.setDrawingMode(null);
                            // Remove drawing manager from map to prevent creating new polygons
                            if (this.isEditMode) {
                                this.drawingManager.setMap(null);
                            }
                        });

                        // If there's already a polygon in the state, draw it
                        if (this.isEditMode) {
                            this.drawPolygonFromState();
                        }
                    } catch (error) {
                        console.error('Error initializing map:', error);
                        this.mapLoadError = 'Error initializing map: ' + error.message;
                    }
                },

                addExistingPolygons() {
                    if (!existingPolygons || !Array.isArray(existingPolygons)) {
                        return;
                    }

                    // Clear any existing polygons from the map
                    this.existingPolygonObjects.forEach(polygon => {
                        polygon.setMap(null);
                    });
                    this.existingPolygonObjects = [];

                    existingPolygons.forEach(polygonCoords => {
                        // convert polygonCoords string to array of objects
                        if (typeof polygonCoords === 'string') {
                            try {
                                polygonCoords = JSON.parse(polygonCoords);
                            } catch (e) {
                                console.error('Invalid existing polygon coordinates:', polygonCoords);
                                return;
                            }
                        }

                        if (Array.isArray(polygonCoords) && polygonCoords.length > 0) {
                            const paths = polygonCoords.map(coord => ({
                                lat: parseFloat(coord.lat),
                                lng: parseFloat(coord.lng)
                            }));

                            const polygon = new google.maps.Polygon({
                                paths: paths,
                                strokeColor: existingPolygonsColor,
                                strokeOpacity: 0.8,
                                strokeWeight: 2,
                                fillColor: existingPolygonsColor,
                                fillOpacity: fillOpacity,
                                map: this.map
                            });

                            this.existingPolygonObjects.push(polygon);
                        }
                    });
                },

                removeCurrentPolygon() {
                    if (this.currentPolygon) {
                        // Remove all event listeners explicitly
                        google.maps.event.clearInstanceListeners(this.currentPolygon);
                        if (this.currentPolygon.getPath) {
                            google.maps.event.clearInstanceListeners(this.currentPolygon.getPath());
                        }
                        // Remove from map
                        this.currentPolygon.setMap(null);
                        this.currentPolygon = null;
                    }
                },

                drawPolygonFromState() {
                    if (!state) {
                        return;
                    }

                    let coordinates = state;

                    // Handle case when state is a string
                    if (typeof state === 'string') {
                        try {
                            coordinates = JSON.parse(state);
                        } catch (e) {
                            console.error('Invalid state format:', state);
                            return;
                        }
                    }

                    // Handle case when state has initialValue property (from Alpine.js entangle)
                    if (state.initialValue) {
                        try {
                            coordinates = JSON.parse(state.initialValue);
                        } catch (e) {
                            coordinates = state.initialValue;
                            if (typeof coordinates === 'string') {
                                try {
                                    coordinates = JSON.parse(coordinates);
                                } catch (e) {
                                    console.error('Invalid initialValue format:', state.initialValue);
                                    return;
                                }
                            }
                        }
                    }

                    // Ensure coordinates is an array
                    if (!Array.isArray(coordinates) || coordinates.length === 0) {
                        return;
                    }

                    const paths = coordinates.map(coord => ({
                        lat: parseFloat(coord.lat),
                        lng: parseFloat(coord.lng)
                    }));

                    // Clear any existing polygon
                    this.removeCurrentPolygon();

                    // Create the new polygon
                    this.currentPolygon = new google.maps.Polygon({
                        paths: paths,
                        strokeColor: drawingColor,
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: drawingColor,
                        fillOpacity: fillOpacity,
                        editable: true,
                        draggable: false,
                        map: this.map
                    });

                    // Add listeners for polygon editing
                    google.maps.event.addListener(this.currentPolygon, 'dragend', () => {
                        this.checkForOverlaps();
                        this.updateStateFromPolygon();
                    });

                    google.maps.event.addListener(this.currentPolygon.getPath(), 'insert_at', () => {
                        this.checkForOverlaps();
                        this.updateStateFromPolygon();
                    });

                    google.maps.event.addListener(this.currentPolygon.getPath(), 'remove_at', () => {
                        this.checkForOverlaps();
                        this.updateStateFromPolygon();
                    });

                    google.maps.event.addListener(this.currentPolygon.getPath(), 'set_at', () => {
                        this.checkForOverlaps();
                        this.updateStateFromPolygon();
                    });

                    // Center the map on the polygon
                    if (paths.length > 0) {
                        const bounds = new google.maps.LatLngBounds();
                        paths.forEach(point => bounds.extend(point));
                        this.map.fitBounds(bounds);
                    }

                    // In edit mode, we don't want to allow creating new polygons
                    if (this.isEditMode) {
                        this.drawingManager.setMap(null);
                    }
                },

                startDrawing() {
                    if (!this.mapsLoaded || !this.drawingManager) {
                        console.error('Cannot start drawing - map not loaded');
                        return;
                    }

                    // Clear any existing polygon
                    this.removeCurrentPolygon();

                    // Reset state - use a proper deep clone for state objects
                    if (typeof state === 'object' && state !== null && !Array.isArray(state)) {
                        // For Alpine.js entangled objects
                        this.state = [];
                    } else {
                        // For simple arrays or other values
                        this.state = [];
                    }

                    this.hasOverlap = false;

                    // Start drawing mode
                    this.drawingManager.setMap(this.map);
                    this.drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                    this.isEditMode = false;

                    // Update the hidden state input to ensure it's actually cleared
                    this.updateEmptyState();
                },

                clearDrawing() {
                    if (!this.mapsLoaded) {
                        return;
                    }

                    // Remove the current polygon completely
                    this.removeCurrentPolygon();

                    // Reset state completely - handling both primitive and object states
                    if (typeof state === 'object' && state !== null && !Array.isArray(state)) {
                        // For Alpine.js entangled objects
                        this.state = [];
                    } else {
                        // For simple arrays or other values
                        this.state = [];
                    }

                    this.hasOverlap = false;

                    // Reset drawing mode based on whether we're in edit mode
                    if (this.isEditMode) {
                        // In edit mode, we're now completely clearing the polygon
                        this.isEditMode = false;
                        this.drawingManager.setMap(this.map);
                        this.drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                    } else {
                        // In create mode, just reset to drawing mode
                        this.drawingManager.setMap(this.map);
                        this.drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                    }

                    // Update the hidden state input to ensure it's actually cleared
                    this.updateEmptyState();
                },

                updateEmptyState() {
                    // Ensure the input is properly updated with an empty state
                    const stateInput = document.getElementById('{{ $getId() }}');
                    if (stateInput) {
                        stateInput.value = '[]';
                        // Dispatch events to ensure Livewire/Alpine picks up the change
                        stateInput.dispatchEvent(new Event('input', { bubbles: true }));
                        stateInput.dispatchEvent(new Event('change', { bubbles: true }));

                        // Force Alpine/Livewire to update if using entangled state
                        if (typeof state === 'object' && state !== null && typeof state.__livewire !== 'undefined') {
                            $wire.set('{{ $getStatePath() }}', []);
                        }
                    }
                },

                updateStateFromPolygon(polygon = this.currentPolygon) {
                    if (!polygon) {
                        this.updateEmptyState();
                        return;
                    }

                    const path = polygon.getPath();
                    const coordinates = [];

                    for (let i = 0; i < path.getLength(); i++) {
                        const point = path.getAt(i);
                        coordinates.push({
                            lat: point.lat(),
                            lng: point.lng()
                        });
                    }

                    // Update the state with deep clone to ensure reactivity
                    this.state = JSON.parse(JSON.stringify(coordinates));
                    this.checkForOverlaps();

                    // Update the hidden input value
                    const stateInput = document.getElementById('{{ $getId() }}');
                    if (stateInput) {
                        const jsonValue = JSON.stringify(coordinates);
                        stateInput.value = jsonValue;

                        // Dispatch events to trigger validation
                        stateInput.dispatchEvent(new Event('input', { bubbles: true }));
                        stateInput.dispatchEvent(new Event('change', { bubbles: true }));

                        // Directly update Livewire if using entangled state
                        if (typeof state === 'object' && state !== null && typeof state.__livewire !== 'undefined') {
                            $wire.set('{{ $getStatePath() }}', coordinates);
                        }
                    }
                },

                checkForOverlaps() {
                    if (!this.currentPolygon || this.existingPolygonObjects.length === 0) {
                        this.hasOverlap = false;
                        return;
                    }

                    for (let i = 0; i < this.existingPolygonObjects.length; i++) {
                        const existingPolygon = this.existingPolygonObjects[i];

                        if (this.polygonsOverlap(this.currentPolygon, existingPolygon)) {
                            this.hasOverlap = true;
                            return;
                        }
                    }

                    this.hasOverlap = false;
                },

                polygonsOverlap(polygon1, polygon2) {
                    // Check if any point of polygon1 is inside polygon2
                    const path1 = polygon1.getPath();
                    for (let i = 0; i < path1.getLength(); i++) {
                        const point = path1.getAt(i);
                        if (google.maps.geometry.poly.containsLocation(point, polygon2)) {
                            return true;
                        }
                    }

                    // Check if any point of polygon2 is inside polygon1
                    const path2 = polygon2.getPath();
                    for (let i = 0; i < path2.getLength(); i++) {
                        const point = path2.getAt(i);
                        if (google.maps.geometry.poly.containsLocation(point, polygon1)) {
                            return true;
                        }
                    }

                    // Check if any edges intersect
                    for (let i = 0; i < path1.getLength(); i++) {
                        const a1 = path1.getAt(i);
                        const a2 = path1.getAt((i + 1) % path1.getLength());

                        for (let j = 0; j < path2.getLength(); j++) {
                            const b1 = path2.getAt(j);
                            const b2 = path2.getAt((j + 1) % path2.getLength());

                            if (this.lineSegmentsIntersect(
                                a1.lat(), a1.lng(), a2.lat(), a2.lng(),
                                b1.lat(), b1.lng(), b2.lat(), b2.lng()
                            )) {
                                return true;
                            }
                        }
                    }

                    return false;
                },

                lineSegmentsIntersect(a1Lat, a1Lng, a2Lat, a2Lng, b1Lat, b1Lng, b2Lat, b2Lng) {
                    // Calculate direction of the lines
                    const dxa = a2Lat - a1Lat;
                    const dya = a2Lng - a1Lng;
                    const dxb = b2Lat - b1Lat;
                    const dyb = b2Lng - b1Lng;

                    // Calculate determinant
                    const det = dxa * dyb - dya * dxb;

                    // If lines are parallel, they don't intersect
                    if (Math.abs(det) < 1e-10) {
                        return false;
                    }

                    // Calculate the point of intersection
                    const t = ((b1Lat - a1Lat) * dyb - (b1Lng - a1Lng) * dxb) / det;
                    const u = ((b1Lat - a1Lat) * dya - (b1Lng - a1Lng) * dxa) / det;

                    // Check if the intersection is within both line segments
                    return t >= 0 && t <= 1 && u >= 0 && u <= 1;
                }
            }));
        });
    </script>
@endPushOnce