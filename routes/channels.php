<?php

use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use Illuminate\Support\Facades\Broadcast;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('driver-location.{driverId}', function ($user, $driverId) {
    // Check if user is the driver
    $isDriver = Driver::where('id', $driverId)
        ->where('user_id', $user->id)
        ->exists();

    $isAssignedRider = Trip::where('driver_id', $driverId)
        ->whereHas('rider', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->whereIn('status', ['assigned', 'driver_arriving', 'driver_arrived', 'in_progress'])
        ->exists();

    return $isDriver || $isAssignedRider;
});

// Broadcast::channel('find-driver.{riderId}', function ($user, $riderId) {
//     $rider = Rider::find($riderId);

//     return $rider && $rider->user_id === $user->id;
// });

Broadcast::channel('ride-request.{driverId}', function ($user, $driverId) {
    $driver = Driver::find($driverId);

    return $driver && $driver->user_id === $user->id;
});

Broadcast::channel('trip.{tripId}', function ($user, $tripId) {
    $trip = Trip::find($tripId);

    return $trip && (
        $trip->rider->user_id === $user->id ||
        ($trip->driver && $trip->driver->user_id === $user->id)
    );
});
