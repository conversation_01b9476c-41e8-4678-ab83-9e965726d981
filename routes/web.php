<?php

use App\Http\Middleware\GoogleMapsCsp;
use Illuminate\Support\Facades\Route;

// Apply GoogleMapsCsp middleware to all admin panel routes
Route::middleware([GoogleMapsCsp::class])->group(function () {
    // The admin panel routes are handled by Filament
});

// Add this route for the shared trip view
Route::get('/trips/shared/{shareToken}', [App\Http\Controllers\SharedTripController::class, 'show'])->name('trips.shared');
