; All logging should be output on pid 1 fd 1 (stdout of tini)
; Run everything foreground and kill supervisord if either fail

[supervisord]
nodaemon=true
logfile=/dev/stdout
logfile_maxbytes=0
loglevel=warn
user=root
pidfile=/run/supervisord.pid

[program:php-fpm]
command=/bin/sh -c "echo 'starting php-fpm'; php-fpm --nodaemonize"
autostart=true
priority=5
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true

[program:nginx]
command=/bin/sh -c "echo 'starting nginx'; nginx -g 'daemon off;'"
autostart=true
priority=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
redirect_stderr=true

[program:queue_worker]
command=/bin/sh -c "php /var/www/html/artisan queue:work"
autostart=true
autorestart=true
priority=10
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:reverb]
command=/bin/sh -c "php artisan reverb:start --debug"
autostart=true
autorestart=true
priority=20
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0






