APP_NAME="ZTS"
APP_ENV=production
APP_KEY="base64:94ISYsaoJbxt3/yjqozovqDZJ3qSEyay+lGDlfrdim0="
APP_DEBUG=false
DEBUGBAR_ENABLED=false
APP_TIMEZONE="UTC"
APP_URL="https://zts-qa.k8s-v2.satoripop.io"

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=************
DB_PORT=5432
DB_DATABASE=zts-qa_db
DB_USERNAME=zts_usr
DB_PASSWORD="873m2%CIBe"

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=reverb
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_STORE=redis
CACHE_PREFIX=

MEMCACHED_HOST=************

REDIS_CLIENT=phpredis
REDIS_HOST=************
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=29fa88a21bc15b
MAIL_PASSWORD=3b22863fcf3bcc
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

REVERB_APP_ID=527674
REVERB_APP_KEY=vxnqth7yo6uvqt4iwc6a
REVERB_APP_SECRET=7sqom4uvwnamwvpi1hsa
REVERB_HOST="zts-qa-reverb.k8s-v2.satoripop.io"
REVERB_PORT=443
REVERB_SCHEME=https
REVERB_SERVER_HOST=0.0.0.0
REVERB_SCALING_ENABLED=true

VITE_REVERB_APP_KEY=vxnqth7yo6uvqt4iwc6a
VITE_REVERB_HOST="zts-qa-reverb.k8s-v2.satoripop.io"
VITE_REVERB_PORT=443
VITE_REVERB_SCHEME=https

GOOGLE_MAPS_API_KEY=AIzaSyCuiw7shEGM2iXXCAXNBcdGdLJ2045terk

DRIVER_SEARSH_FIRST_RADIUS_FROM=0
DRIVER_SEARSH_FIRST_RADIUS_TO=5
FIRST_RADIUS_TIMEOUT=60
DRIVER_SEARSH_SECONDE_RADIUS_FROM=5
DRIVER_SEARSH_SECONDE_RADIUS_TO=8
SECONDE_RADIUS_TIMEOUT=30
DRIVER_SEARSH_THIRD_RADIUS_FROM=8
DRIVER_SEARSH_THIRD_RADIUS_TO=10
THIRD_RADIUS_TIMEOUT=30

MAX_CANCELLATIONS="4"