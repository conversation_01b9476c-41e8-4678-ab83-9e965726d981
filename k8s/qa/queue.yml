apiVersion: apps/v1
kind: Deployment
metadata:
  name: zts-qa-queue
  labels:
    app: zts-qa-queue
  namespace: zts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zts-qa-queue
  template:
    metadata:
      labels:
        app: zts-qa-queue
    spec:
      volumes:
        - name: pv-storage
          persistentVolumeClaim:
            claimName: zts-qa-pvc
      containers:
        - name: zts-qa-queue
          image: nexus.satoripop.io:8083/repository/projets/zts-qa:latest
          command: ["/bin/sh"]
          args:
            - -c
            - >
              su -s /bin/bash www-data -c 'php /var/www/html/artisan queue:work'
          volumeMounts:
            - name: pv-storage
              mountPath: "/var/www/html/storage"
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: zts-qa
          resources:
            requests:
              memory: "512Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "2000m"
          securityContext:
            capabilities:
              add:
                - NET_ADMIN
                - SYS_ADMIN
                - SYS_PTRACE
            privileged: true
      imagePullSecrets:
        - name: nexus.satoripop.io