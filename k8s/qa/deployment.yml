apiVersion: apps/v1
kind: Deployment
metadata:
  name: zts-qa
  labels: 
    app: zts-qa
  namespace: zts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zts-qa
  template:
    metadata:
      labels:
        app: zts-qa
    spec:
      volumes:
      - name: pv-storage
        persistentVolumeClaim:
          claimName: zts-qa-pvc
      containers:
      - name: zts-qa
        image: nexus.satoripop.io:8083/repository/projets/zts-qa:latest
        command: ["/bin/sh"]
        args:
        - -ce
        - |
          /bin/bash <<'EOF'
          false | cp -avir /tmpstorage/* /var/www/html/storage/
          chown -R www-data:www-data /var/www/html/storage
          php artisan op:cl
          supervisord
          tail -f /dev/null
          EOF
        volumeMounts:
        - mountPath: "/var/www/html/storage"
          name: pv-storage
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: zts-qa
      imagePullSecrets:
        - name: nexus.satoripop.io