apiVersion: batch/v1
kind: CronJob
metadata:
  name: zts-test-cron
  namespace: zts
spec:
  schedule: '* * * * *'
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: zts-test-cron
            k8s-app: zts-test
        spec:
          volumes:
            - name: pv-storage
              persistentVolumeClaim:
                claimName: zts-test-pvc
          containers:
            - name: zts-test-cron
              image: nexus.satoripop.io:8083/repository/projets/zts-test:latest
              command:
                - /bin/sh
              args:
                - -c
                - >
                  su -s /bin/bash www-data -c 'php /var/www/html/artisan schedule:run >> /dev/null 2>&1'
              volumeMounts:
                - name: pv-storage
                  mountPath: "/var/www/html/storage"
              envFrom:
                - configMapRef:
                    name: zts-test

              securityContext:
                capabilities:
                  add:
                    - NET_ADMIN
                    - SYS_ADMIN
                    - SYS_PTRACE
                privileged: true
              imagePullPolicy: Always
          restartPolicy: Never
          imagePullSecrets:
            - name: nexus.satoripop.io
          schedulerName: default-scheduler
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1