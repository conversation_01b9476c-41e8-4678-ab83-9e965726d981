apiVersion: apps/v1
kind: Deployment
metadata:
  name: zts-test
  labels: 
    app: zts-test
  namespace: zts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zts-test
  template:
    metadata:
      labels:
        app: zts-test
    spec:
      volumes:
      - name: pv-storage
        persistentVolumeClaim:
          claimName: zts-test-pvc
      containers:
      - name: zts-test
        image: nexus.satoripop.io:8083/repository/projets/zts-test:latest
        command: ["/bin/sh"]
        args:
        - -ce
        - |
          /bin/bash <<'EOF'
          false | cp -avir /tmpstorage/* /var/www/html/storage/
          chown -R www-data:www-data /var/www/html/storage
          php artisan op:cl
          supervisord
          tail -f /dev/null
          EOF
        volumeMounts:
        - mountPath: "/var/www/html/storage"
          name: pv-storage
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        envFrom:
        - configMapRef:
            name: zts-test
      imagePullSecrets:
        - name: nexus.satoripop.io