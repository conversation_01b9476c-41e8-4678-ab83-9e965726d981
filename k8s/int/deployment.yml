apiVersion: apps/v1
kind: Deployment
metadata:
  name: zts-int
  labels: 
    app: zts-int
  namespace: zts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zts-int
  template:
    metadata:
      labels:
        app: zts-int
    spec:
      volumes:
      - name: pv-storage
        persistentVolumeClaim:
          claimName: zts-int-pvc
      containers:
      - name: zts-int
        image: nexus.satoripop.io:8083/repository/projets/zts-int:latest
        command: ["/bin/sh"]
        args:
        - -ce
        - |
          /bin/bash <<'EOF'
          false | cp -avir /tmpstorage/* /var/www/html/storage/
          chown -R www-data:www-data /var/www/html/storage
          php artisan op:cl
          supervisord
          tail -f /dev/null
          EOF
        volumeMounts:
        - mountPath: "/var/www/html/storage"
          name: pv-storage
        imagePullPolicy: Always
        ports:
        - containerPort: 80
        env:
        - name: APP_LOCALE
          value: "en"
        - name: APP_FALLBACK_LOCALE
          value: "en"
        - name: APP_FAKER_LOCALE
          value: "en_US"
        envFrom:
        - configMapRef:
            name: zts-int
      imagePullSecrets:
        - name: nexus.satoripop.io