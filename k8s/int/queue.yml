apiVersion: apps/v1
kind: Deployment
metadata:
  name: zts-int-queue
  labels:
    app: zts-int-queue
  namespace: zts
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zts-int-queue
  template:
    metadata:
      labels:
        app: zts-int-queue
    spec:
      volumes:
        - name: pv-storage
          persistentVolumeClaim:
            claimName: zts-int-pvc
      containers:
        - name: zts-int-queue
          image: nexus.satoripop.io:8083/repository/projets/zts-int:latest
          command: ["/bin/sh"]
          args:
            - -c
            - >
              su -s /bin/bash www-data -c 'php /var/www/html/artisan queue:work'
          volumeMounts:
            - name: pv-storage
              mountPath: "/var/www/html/storage"
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: zts-int
          resources:
            requests:
              memory: "512Mi"
              cpu: "1000m"
            limits:
              memory: "2048Mi"
              cpu: "2000m"
          securityContext:
            capabilities:
              add:
                - NET_ADMIN
                - SYS_ADMIN
                - SYS_PTRACE
            privileged: true
      imagePullSecrets:
        - name: nexus.satoripop.io