apiVersion: v1
kind: PersistentVolume
metadata:
  name: zts-int-pv 
  namespace: zts
spec:
  capacity:
    storage: 5Gi 
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain 
  nfs: 
    path: /NFSShares/zts-int
    server: 192.168.1.1
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: zts-int-pvc
  namespace: zts
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: 5Gi