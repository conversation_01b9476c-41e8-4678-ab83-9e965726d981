apiVersion: v1
kind: Service
metadata:
 name: zts-int
 namespace: zts
spec:
 selector:
   app: zts-int
 ports:
 - name: http
   protocol: TCP
   port: 80
---
apiVersion: v1
kind: Service
metadata:
  name: zts-int-reverb
  namespace: zts
spec:
  selector:
    app: zts-int
  ports:
    - name: reverb
      protocol: TCP
      port: 8080
      targetPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: zts-int-webhook
  namespace: zts
  annotations:
    acme.cert-manager.io/http01-edit-in-place: 'true'
    cert-manager.io/cluster-issuer: letsencrypt-dns01-issuer
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'

    nginx.ingress.kubernetes.io/server-snippets: |
      location / {
        proxy_set_header Upgrade $http_upgrade;
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
        }
            
spec:
  tls:
    - hosts:
        - zts-int.k8s-v2.satoripop.io

      secretName: zts-int-tls
  rules:
    - host: zts-int.k8s-v2.satoripop.io
      http:
        paths:
          - path: /api
            pathType: Prefix
            backend:
              service:
                name: zts-int
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: zts-int
  namespace: zts
  annotations:
    acme.cert-manager.io/http01-edit-in-place: 'true'
    cert-manager.io/cluster-issuer: letsencrypt-dns01-issuer
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
    #nginx.ingress.kubernetes.io/satisfy: "any"
    #nginx.ingress.kubernetes.io/whitelist-source-range: "*************/32,*************/32,************/32,************/32,**********/16"
    #nginx.ingress.kubernetes.io/auth-type: basic
    #nginx.ingress.kubernetes.io/auth-secret: basic-auth
    #nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - aaa'

    nginx.ingress.kubernetes.io/server-snippets: |
      location / {
        proxy_set_header Upgrade $http_upgrade;
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
        }
            
spec:
  tls:
    - hosts:
        - zts-int.k8s-v2.satoripop.io

      secretName: zts-int-tls
  rules:
    - host: zts-int.k8s-v2.satoripop.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: zts-int
                port:
                  number: 80
---                  
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: zts-int-reverb
  namespace: zts
  annotations:
    acme.cert-manager.io/http01-edit-in-place: 'true'
    cert-manager.io/cluster-issuer: letsencrypt-dns01-issuer
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/force-ssl-redirect: 'true'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '3600'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '3600'
    #nginx.ingress.kubernetes.io/satisfy: "any"
    #nginx.ingress.kubernetes.io/whitelist-source-range: "*************/32,*************/32,************/32,************/32,**********/16"
    #nginx.ingress.kubernetes.io/auth-type: basic
    #nginx.ingress.kubernetes.io/auth-secret: basic-auth
    #nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required - aaa'

    nginx.ingress.kubernetes.io/server-snippets: |
      location / {
        proxy_set_header Upgrade $http_upgrade;
        proxy_http_version 1.1;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-For $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header Connection "upgrade";
        proxy_cache_bypass $http_upgrade;
        add_header 'Content-Security-Policy' 'upgrade-insecure-requests';
        }
       
spec:
  tls:
    - hosts:
        - zts-int-reverb.k8s-v2.satoripop.io

      secretName: zts-int-reverb-tls
  rules:
    - host: zts-int-reverb.k8s-v2.satoripop.io
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: zts-int-reverb
                port:
                  number: 8080                  