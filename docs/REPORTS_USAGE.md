# Reports Resource Documentation

## Overview

The Reports resource provides comprehensive analytics and reporting functionality for the ZTS (Zaho Transportation System) admin panel. It includes detailed reports for Drivers, Riders, and Trips with CSV export capabilities.

## Features

### 📊 Report Types

1. **Drivers Report**
   - New driver registrations with growth percentage
   - Driver distribution by status (pending, active, blocked, etc.)
   - Performance metrics and ratings
   - Vehicle assignments and trip history

2. **Riders Report**
   - New rider registrations with growth percentage
   - Rider distribution by status (pending, active, blocked, etc.)
   - Trip activity and spending patterns
   - Gender distribution and engagement metrics

3. **Trips Report**
   - Total trip volume and revenue analysis
   - Trip breakdown by status with revenue share
   - Performance metrics (completion rate, cancellation rate)
   - Distance and time analytics

### 🔍 Filtering Options

- **Weekly**: Current week data
- **Monthly**: Current month data
- **Total**: All-time data
- **Custom Range**: User-defined date range

### 📥 Export Functionality

All reports support CSV export with detailed data including:
- Complete user information
- Transaction history
- Performance metrics
- Timestamps and location data

## Usage

### Accessing Reports

1. Navigate to the admin panel
2. Go to **Analytics > Reports** in the sidebar
3. Choose from the available report types:
   - Drivers Report
   - Riders Report
   - Trips Report

### Generating Reports

1. Select your desired report type
2. Choose the time period (Weekly/Monthly/Total/Custom)
3. For custom ranges, select start and end dates
4. Click "Update Report" to refresh data
5. Use "Export CSV" to download detailed data

### Understanding Metrics

#### Drivers Report
- **Total Drivers**: Number of drivers in selected period
- **Growth Rate**: Percentage change compared to previous period
- **Active Drivers**: Drivers with active status
- **Status Distribution**: Breakdown by driver status with percentages

#### Riders Report
- **Total Riders**: Number of riders in selected period
- **Growth Rate**: Percentage change compared to previous period
- **Active Riders**: Riders with active status
- **Trip Activity**: Engagement metrics and spending patterns

#### Trips Report
- **Total Trips**: Number of trips in selected period
- **Total Revenue**: Sum of all trip revenues
- **Completed Trips**: Successfully completed trips
- **Status Breakdown**: Distribution by trip status with revenue share

## Technical Implementation

### File Structure
```
app/Filament/Resources/Panel/ReportResource.php
app/Filament/Resources/Panel/ReportResource/Pages/
├── ListReports.php
├── DriversReport.php
├── RidersReport.php
└── TripsReport.php

app/Filament/Exports/
├── DriversReportExporter.php
├── RidersReportExporter.php
└── TripsReportExporter.php

resources/views/filament/resources/report-resource/pages/
├── list-reports.blade.php
├── drivers-report.blade.php
├── riders-report.blade.php
└── trips-report.blade.php
```

### Key Features
- **Filament Export Actions**: Built-in CSV export functionality
- **Real-time Filtering**: Dynamic date range selection
- **Growth Calculations**: Automatic percentage calculations
- **Status Distribution**: Visual progress bars and percentages
- **Revenue Analytics**: Comprehensive financial metrics

### Dependencies
- Laravel Filament v3.x
- Filament Export Actions
- Job Batches (for export processing)
- Database Notifications

## Troubleshooting

### Common Issues

1. **Export not working**
   - Ensure job batches table exists
   - Check queue configuration
   - Verify export permissions

2. **Data not showing**
   - Check date range selection
   - Verify database relationships
   - Clear application cache

3. **Performance issues**
   - Consider adding database indexes
   - Optimize query performance
   - Use appropriate chunk sizes for exports

### Commands
```bash
# Clear caches
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Check migrations
php artisan migrate:status

# Process export jobs
php artisan queue:work
```

## Security Considerations

- Reports are only accessible to admin users
- Export files are stored securely
- User data is properly sanitized in exports
- Access logs are maintained for audit purposes

## Future Enhancements

- Real-time dashboard widgets
- Scheduled report generation
- Email report delivery
- Advanced filtering options
- Chart visualizations
- PDF export support
