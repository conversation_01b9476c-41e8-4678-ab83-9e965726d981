FROM nexus.satoripop.io:8083/repository/php-images/php-8.3-nginx-node20:latest
LABEL maintainer="<EMAIL>"
USER www-data
COPY --chown=www-data . .

ENV COMPOSER_MEMORY_LIMIT=-1

USER root
#COPY NGINX CONF (LARAVEL)
RUN cp k8s/nginx.conf /etc/nginx/nginx.conf
RUN apk add --no-cache  freetype-dev libjpeg-turbo-dev libwebp-dev libpng-dev
RUN docker-php-ext-configure gd --with-freetype --with-webp --with-jpeg && docker-php-ext-install gd

# Install supervisor and other necessary packages
#RUN apk add --no-cache supervisor

# Create directories for Supervisor
RUN cp k8s/default.ini /etc/supervisor.d/default.ini

# Copy the Supervisor configuration file

# Set proper permissions for Supervisor directories

USER www-data
#START TO CHANGE BY DEVELOPPER
RUN cp k8s/qa/.env.k8s-qa .env
RUN composer install
#RUN composer require plan2net/webp

RUN php artisan migrate --force
#RUN php artisan db:seed --class=AdminPanelUserSeeder --force
#RUN php artisan db:seed --class=ShieldSeeder --force
RUN npm install --no-dev
RUN npm run build
RUN php artisan storage:link
RUN php artisan optimize

#END TO CHANGE BY DEVELOPPER

RUN chmod -R 777 storage
RUN chown -R www-data.www-data storage

USER root
RUN mkdir /tmpstorage
RUN cp -arv storage/* /tmpstorage/

EXPOSE 80
EXPOSE 8080

# Start Supervisor
