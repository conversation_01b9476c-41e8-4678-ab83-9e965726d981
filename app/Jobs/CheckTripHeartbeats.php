<?php

namespace App\Jobs;

use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use App\Http\Controllers\Api\RidersTripController;
use App\Models\Trip;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class CheckTripHeartbeats implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private RidersTripController $tripController;

    public function __construct()
    {
        $this->tripController = App::make(RidersTripController::class);
    }

    public function handle()
    {
        Log::info('Starting trip heartbeat check');

        // Using the consolidated checkAssignedTrips method that handles multiple statuses
        $this->checkAssignedTrips();

        // Comment out individual status checks since they're now handled in checkAssignedTrips
        // $this->checkDriverArrivingTrips();
        // $this->checkDriverArrivedTrips();
        // $this->checkOnTripTrips();

        Log::info('Completed trip heartbeat check');
    }

    private function checkAssignedTrips()
    {
        $trips = Trip::whereIn('status', [
            TripStatus::assigned->value,
            TripStatus::driver_arrived->value,
            TripStatus::driver_arriving->value,
        ])
            ->with(['driver', 'rider'])
            ->get();

        foreach ($trips as $trip) {
            // Skip if missing driver or rider
            if (! $trip->driver || ! $trip->rider) {
                continue;
            }

            $now = Carbon::now();

            // Get heartbeats
            $driverHeartbeat = $trip->driver->last_heartbeat ? Carbon::parse($trip->driver->last_heartbeat) : null;
            $riderHeartbeat = $trip->rider->last_heartbeat ? Carbon::parse($trip->rider->last_heartbeat) : null;

            // Calculate buffer (ETA + 100%)
            $etaBuffer = $this->calculateTripDurationBuffer($trip);

            if ($driverHeartbeat && $riderHeartbeat) {
                $driverHeartbeatAge = abs(num: $now->diffInSeconds($driverHeartbeat));
                $riderHeartbeatAge = abs($now->diffInSeconds($riderHeartbeat));

                Log::info('Trip heartbeat check - both exist', [
                    'trip_id' => $trip->id,
                    'driver_heartbeat_age' => $driverHeartbeatAge,
                    'rider_heartbeat_age' => $riderHeartbeatAge,
                    'eta_buffer' => $etaBuffer,
                ]);

                // Check if both heartbeats exceed the buffer
                if ($driverHeartbeatAge > $etaBuffer && $riderHeartbeatAge > $etaBuffer) {
                    // Both have connection issues, cancel based on who disconnected first/longer
                    $cancelledBy = $driverHeartbeatAge > $riderHeartbeatAge ? 'driver' : 'rider';
                    $userId = $cancelledBy === 'driver' ? $trip->driver->user_id : $trip->rider->user_id;

                    Log::info('Both heartbeats exceeded buffer', [
                        'trip_id' => $trip->id,
                        'cancelled_by' => $cancelledBy,
                        'driver_heartbeat_age' => $driverHeartbeatAge,
                        'rider_heartbeat_age' => $riderHeartbeatAge,
                    ]);

                    $this->cancelTrip(
                        $trip,
                        $userId,
                        $cancelledBy,
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );

                    continue;
                }
                // Check if only driver heartbeat exceeds buffer
                elseif ($driverHeartbeatAge > $etaBuffer) {
                    Log::info('Only driver heartbeat exceeded buffer', [
                        'trip_id' => $trip->id,
                        'driver_heartbeat_age' => $driverHeartbeatAge,
                        'buffer' => $etaBuffer,
                    ]);

                    $this->cancelTrip(
                        $trip,
                        $trip->driver->user_id,
                        'driver',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );

                    continue;
                }
                // Check if only rider heartbeat exceeds buffer
                elseif ($riderHeartbeatAge > $etaBuffer) {
                    Log::info('Only rider heartbeat exceeded buffer', [
                        'trip_id' => $trip->id,
                        'rider_heartbeat_age' => $riderHeartbeatAge,
                        'buffer' => $etaBuffer,
                    ]);

                    $this->cancelTrip(
                        $trip,
                        $trip->rider->user_id,
                        'rider',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );

                    continue;
                }
                // Both heartbeats are within buffer, trip continues normally
            }
            // Only driver heartbeat exists
            elseif ($driverHeartbeat && ! $riderHeartbeat) {
                $driverHeartbeatAge = abs($now->diffInSeconds($driverHeartbeat));

                Log::info('Only driver heartbeat exists', [
                    'trip_id' => $trip->id,
                    'driver_heartbeat_age' => $driverHeartbeatAge,
                    'buffer' => $etaBuffer,
                ]);

                if ($driverHeartbeatAge > $etaBuffer) {
                    // Driver heartbeat exists but exceeds buffer
                    $this->cancelTrip(
                        $trip,
                        $trip->driver->user_id,
                        'driver',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );
                } else {
                    // Driver heartbeat is good, but rider has no heartbeat
                    $this->cancelTrip(
                        $trip,
                        $trip->rider->user_id,
                        'rider',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );
                }

                continue;
            }
            // Only rider heartbeat exists
            elseif (! $driverHeartbeat && $riderHeartbeat) {
                $riderHeartbeatAge = abs($now->diffInSeconds($riderHeartbeat));

                Log::info('Only rider heartbeat exists', [
                    'trip_id' => $trip->id,
                    'rider_heartbeat_age' => $riderHeartbeatAge,
                    'buffer' => $etaBuffer,
                ]);

                if ($riderHeartbeatAge > $etaBuffer) {
                    // Rider heartbeat exists but exceeds buffer
                    $this->cancelTrip(
                        $trip,
                        $trip->rider->user_id,
                        'rider',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );
                } else {
                    // Rider heartbeat is good, but driver has no heartbeat
                    $this->cancelTrip(
                        $trip,
                        $trip->driver->user_id,
                        'driver',
                        'lost_connection',
                        CancellationStage::afterAssigned
                    );
                }

                continue;
            }
            // Neither heartbeat exists
            else {
                Log::info('No heartbeats found for trip', ['trip_id' => $trip->id]);

                // Default to canceling as driver when both are missing
                $this->cancelTrip(
                    $trip,
                    $trip->driver->user_id,
                    'driver',
                    'lost_connection',
                    CancellationStage::afterAssigned
                );

                continue;
            }
        }
    }

    private function calculateTripDurationBuffer(Trip $trip)
    {
        // Define fallback buffers by status (in seconds)
        $fallbackBuffers = [
            TripStatus::assigned->value => 600,        // 10 minutes for assigned
            TripStatus::driver_arriving->value => 600, // 10 minutes for driver_arriving
            TripStatus::driver_arrived->value => 300,  // 5 minutes for driver_arrived
        ];

        // Default fallback
        $defaultBuffer = 600; // 10 minutes

        // If we have both departure and arrival times, calculate the trip duration
        if ($trip->estimated_departure_time && $trip->estimated_arrival_time) {
            $etd = Carbon::parse($trip->estimated_departure_time);
            $eta = Carbon::parse($trip->estimated_arrival_time);

            // Calculate trip duration and double it (ETA + 100%)
            $tripDuration = $etd->diffInSeconds($eta);

            return $tripDuration * 2;
        }

        // If we don't have both times, use fallback based on status
        return $fallbackBuffers[$trip->status->value] ?? $defaultBuffer;
    }

    private function cancelTrip(Trip $trip, $userId, $cancelledBy, $reason, CancellationStage $stage)
    {
        try {
            Log::info('Auto-cancelling trip due to heartbeat timeout', [
                'trip_id' => $trip->id,
                'cancelled_by' => $cancelledBy,
                'stage' => $stage->value,
                'reason' => $reason,
            ]);

            $result = $this->tripController->cancelTrip(
                $trip->id,
                $userId,
                $cancelledBy,
                'other'
            );

            Log::info('Trip auto-cancelled successfully', [
                'trip_id' => $trip->id,
                'result' => $result,
            ]);

            return $result;
        } catch (\Exception $e) {
            Log::error('Failed to auto-cancel trip', [
                'trip_id' => $trip->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'message' => 'Failed to cancel trip: '.$e->getMessage(),
            ];
        }
    }
}
