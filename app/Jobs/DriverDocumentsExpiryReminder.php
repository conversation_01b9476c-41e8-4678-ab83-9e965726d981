<?php

namespace App\Jobs;

use App\Models\Driver;
use App\Models\User;
use App\Notifications\DriverDocumentsExpiryNotification;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DriverDocumentsExpiryReminder implements ShouldBeUnique, ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting DriverDocumentsExpiryReminder job');

        $admins = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->get();

        Log::info('Found admin users for notifications', ['count' => $admins->count()]);

        $now = Carbon::now();
        $threeMonthsDate = $now->copy()->addMonths(3)->toDateString();
        $twoWeeksDate = $now->copy()->addWeeks(2)->toDateString();
        $todayDate = $now->toDateString();

        Log::info('Checking for license expiry dates', [
            'three_months_date' => $threeMonthsDate,
            'two_weeks_date' => $twoWeeksDate,
            'today_date' => $todayDate,
            'current_date' => $now->toDateString(),
        ]);

        // Query for drivers with licenses expiring in exactly 3 months, 2 weeks, or today
        $query = Driver::whereHas('documents', function ($query) use ($threeMonthsDate, $twoWeeksDate, $todayDate) {
            $query->whereDate('license_expiry', '=', $threeMonthsDate)
                ->orWhereDate('license_expiry', '=', $twoWeeksDate)
                ->orWhereDate('license_expiry', '=', $todayDate);
        })
            ->whereIn('global_status', ['active', 'blocked'])
            ->with(['documents', 'user']);

        $totalDrivers = $query->count();
        Log::info('Found drivers with expiring licenses', ['count' => $totalDrivers]);

        $query->chunkById(100, function ($drivers) use ($now, $admins, $totalDrivers) {
            Log::info('Processing chunk of drivers', ['chunk_size' => $drivers->count(), 'total' => $totalDrivers]);

            foreach ($drivers as $driver) {
                if (! $driver->documents || ! $driver->documents->license_expiry) {
                    Log::warning('Driver has no documents or license expiry date', [
                        'driver_id' => $driver->id,
                        'has_documents' => (bool) $driver->documents,
                    ]);

                    continue;
                }

                $licenseExpiry = Carbon::parse($driver->documents->license_expiry);
                Log::info('Checking driver license', [
                    'driver_id' => $driver->id,
                    'driver_name' => $driver->user->name.' '.$driver->user->last_name,
                    'license_expiry' => $licenseExpiry->toDateString(),
                    'is_future' => $licenseExpiry->isFuture(),
                ]);

                if ($licenseExpiry->isFuture() || $licenseExpiry->isToday()) {
                    $daysUntilExpiry = $now->diffInDays($licenseExpiry, false);

                    if ($licenseExpiry->isToday()) {
                        $timeframe = 'today';
                        $color = 'danger';
                        $message = "URGENT: The driver {$driver->user->name} {$driver->user->last_name} ".
                                  "has their license document expiring TODAY ({$licenseExpiry->toDateString()}). ".
                                  'Immediate action required.';
                    } else {
                        $timeframe = $daysUntilExpiry > 14 ? '3 months' : '2 weeks';
                        $color = $daysUntilExpiry > 14 ? 'warning' : 'danger';
                        $message = "The driver {$driver->user->name} {$driver->user->last_name} ".
                                  "has their license document expiring on {$licenseExpiry->toDateString()} ".
                                  "(exactly {$timeframe} from now).";
                    }

                    Log::info('Sending expiry notification', [
                        'driver_id' => $driver->id,
                        'days_until_expiry' => $daysUntilExpiry,
                        'timeframe' => $timeframe,
                        'color' => $color,
                    ]);

                    foreach ($admins as $admin) {
                        Log::info('Notifying admin', [
                            'admin_id' => $admin->id,
                            'admin_name' => $admin->name,
                        ]);

                        $admin->notifyNow(new DriverDocumentsExpiryNotification(
                            'Driver license soon expires',
                            $message,
                            $color
                        ));
                    }
                } else {
                    Log::info('License already expired, skipping notification', [
                        'driver_id' => $driver->id,
                        'license_expiry' => $licenseExpiry->toDateString(),
                    ]);
                }
            }
        });

        Log::info('Completed DriverDocumentsExpiryReminder job');
    }
}
