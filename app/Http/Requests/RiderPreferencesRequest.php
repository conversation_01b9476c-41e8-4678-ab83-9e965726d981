<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RiderPreferencesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'driver_gender' => ['required', 'string', Rule::in(['male', 'female', 'both'])],
            'seats_number' => ['required', 'array', Rule::in(['2', '4', '6'])],
            'car_equipments' => ['sometimes', 'array', 'exists:vehicle_equipment,id'],
            'car_types' => ['required', 'array', 'exists:vehicle_types,id'],
        ];
    }
}
