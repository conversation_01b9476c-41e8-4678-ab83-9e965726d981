<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['sometimes', 'string'],
            'last_name' => ['sometimes', 'string'],
            'email' => [
                'sometimes',
                'string',
                'email',
                Rule::unique('users', 'email')->ignore(Auth::id()),
            ],
            'otp' => ['required_with:email', 'string', 'digits:4'],
            'gender' => ['sometimes', Rule::in(['female', 'male', 'both'])],
            'address' => ['sometimes', 'array'],
            'driver_gender' => ['sometimes', 'string', Rule::in(['male', 'female', 'both'])],
            'id_number' => ['sometimes', 'string',
                Rule::when($this->document_type === 'passport', ['regex:/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8}$/']),
                Rule::when($this->document_type === 'national_id_number', [
                    'nullable',
                    'numeric',
                    'digits:12',
                    'regex:/^[12]\d{11}$/',
                    function ($attribute, $value, $fail) {
                        $birthYearDigits = (int) substr($value, 1, 4);
                        $currentYear = date('Y');

                        $age = $currentYear - $birthYearDigits;

                        if ($age < 18 || $age > 100) {
                            $fail('The birth year in the National ID must indicate an age between 18 and 100 years.');
                        }
                    },
                ]),
                Rule::when($this->document_type === 'personal_id_number', ['regex:/^\d{1,15}$/']),
            ],
            'rider_gender' => ['sometimes', Rule::in(['female', 'male', 'both'])],
            'document_type' => ['sometimes', Rule::in(['passport', 'national_id_number', 'personal_id_number'])],
            'rejectionSubmit' => ['sometimes', 'string'],
        ];
    }
}
