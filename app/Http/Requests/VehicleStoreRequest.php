<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VehicleStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'vehicle_model_id' => ['required'],
            'year' => ['required'],
            'color' => ['required'],
            'license_plate_number' => [
                'required',
                'string',
                Rule::unique('vehicles', 'license_plate_number'),
            ],
            'insurance_company' => ['nullable', 'string'],
            'insurance_policy_number' => ['nullable', 'string'],
            'insurance_expiry_date' => ['nullable', 'date'],
            'registration_expiry_date' => ['nullable', 'date'],
        ];
    }
}
