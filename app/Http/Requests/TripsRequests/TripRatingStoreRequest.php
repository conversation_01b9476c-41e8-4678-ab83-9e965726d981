<?php

namespace App\Http\Requests\TripsRequests;

use Illuminate\Foundation\Http\FormRequest;

class TripRatingStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'trip_id' => ['required'],
            'rider_id' => ['required'],
            'driver_id' => ['required'],
            'rider_to_driver_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_to_car_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_review' => ['nullable', 'string'],
            'driver_review' => ['nullable', 'string'],
            'driver_to_rider_rating' => ['required', 'integer', 'min:1', 'max:5'],
        ];
    }
}
