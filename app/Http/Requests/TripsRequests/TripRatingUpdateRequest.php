<?php

namespace App\Http\Requests\TripsRequests;

use Illuminate\Foundation\Http\FormRequest;

class TripRatingUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'trip_id' => ['sometimes'],
            'rider_id' => ['sometimes'],
            'driver_id' => ['sometimes'],
            'rider_to_driver_rating' => ['sometimes', 'integer', 'min:1', 'max:5'],
            'rider_to_car_rating' => ['sometimes', 'integer', 'min:1', 'max:5'],
            'rider_review' => ['sometimes', 'string'],
            'driver_review' => ['sometimes', 'string'],
            'driver_to_rider_rating' => ['sometimes', 'integer', 'min:1', 'max:5'],
        ];
    }
}
