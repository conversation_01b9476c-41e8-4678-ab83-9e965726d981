<?php

namespace App\Http\Requests\TripsRequests;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRuleNonOperationalPeriod;
use App\Services\GoogleMapsService;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Validator;

class TripStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'departure_location.latitude' => ['required', 'numeric'],
            'departure_location.longitude' => ['required', 'numeric'],
            'departure_location.address' => ['required', 'string'],
            'arrival_location.latitude' => ['required', 'numeric'],
            'arrival_location.longitude' => ['required', 'numeric'],
            'arrival_location.address' => ['required', 'string'],
            'rider_notes' => ['sometimes', 'string', 'max:100'],
            'vehicle_category' => ['required', 'string', Rule::in(['passenger', 'freight'])],
            'vehicle_equipments' => ['sometimes', 'array'],
            'number_of_seats' => [
                'required_if:vehicle_category,passenger',
                'integer', Rule::in([2, 4, 6]),
            ],
            'weight_category' => [
                'required_if:vehicle_category,freight',
                'string', Rule::in(['less_than_1000kg', 'more_than_1000kg']),
            ],
            'is_covered' => [
                'required_if:vehicle_category,freight',
                'boolean',
            ],
            // 'is_female' => [
            //     'sometimes',
            //     'boolean',
            //     Rule::in(
            //         auth()->check() &&
            //         auth()->user()->rider &&
            //         auth()->user()->gender === \App\Enums\GenderEnum::female
            //             ? [true, false]
            //             : [false]
            //     ),
            // ],

        ];

        return $rules;
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateTripDistance($validator);
            $this->validateNonOperationalPeriods($validator);
        });
    }

    /**
     * Validate that the trip distance is between 1KM and 200KM.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateTripDistance($validator)
    {
        $departureLocation = request('departure_location');
        $arrivalLocation = request('arrival_location');

        if (! $departureLocation || ! $arrivalLocation) {
            return;
        }

        $origin = [
            'lat' => $departureLocation['latitude'],
            'lng' => $departureLocation['longitude'],
        ];

        $destination = [
            'lat' => $arrivalLocation['latitude'],
            'lng' => $arrivalLocation['longitude'],
        ];

        // Get the distance from Google Maps
        $mapsService = app(GoogleMapsService::class);
        $googleData = $mapsService->getDistanceAndTime($origin, $destination);
        $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : 0;

        // Validate the distance
        if ($distance < 1) {
            $validator->errors()->add(
                'distance',
                'The trip distance must be at least 1 kilometer.'
            );
        } elseif ($distance > 200) {
            $validator->errors()->add(
                'distance',
                'The trip distance cannot exceed 200 kilometers.'
            );
        }
    }

    /**
     * Validate that the trip is not being created during non-operational periods.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    protected function validateNonOperationalPeriods($validator)
    {
        // Get current time
        $currentTime = now();
        $currentDayOfWeek = $currentTime->format('l'); // e.g., 'Monday'
        $currentTimeString = $currentTime->format('H:i');

        // Get the day charge configuration for the current day
        $dayCharge = PricingRuleAdditionalDayCharge::where('day', $currentDayOfWeek)->first();

        if (! $dayCharge) {
            // No day configuration found, allow the trip
            return;
        }

        // Get all non-operational periods for this day
        $nonOperationalPeriods = PricingRuleNonOperationalPeriod::where('day_charge_id', $dayCharge->id)->get();

        if ($nonOperationalPeriods->isEmpty()) {
            // No non-operational periods defined, allow the trip
            return;
        }

        // Check if current time falls within any non-operational period
        foreach ($nonOperationalPeriods as $period) {
            if ($this->isTimeInNonOperationalPeriod($currentTimeString, $period->start_at, $period->end_at)) {
                $validator->errors()->add(
                    'non_operational_period',
                    'Trip creation is not available during non-operational hours. Service is unavailable from '.
                    Carbon::parse($period->start_at)->format('H:i').' to '.
                    Carbon::parse($period->end_at)->format('H:i').'.'
                );

                return; // Stop checking other periods once we find a match
            }
        }
    }

    /**
     * Check if a given time falls within a non-operational period.
     *
     * @param  string  $currentTime  Current time in H:i format
     * @param  string  $startTime  Period start time in H:i format
     * @param  string  $endTime  Period end time in H:i format
     */
    private function isTimeInNonOperationalPeriod(string $currentTime, string $startTime, string $endTime): bool
    {
        // Convert times to minutes for easier comparison
        $currentMinutes = $this->timeToMinutes($currentTime);
        $startMinutes = $this->timeToMinutes($startTime);
        $endMinutes = $this->timeToMinutes($endTime);

        // Check if the period spans midnight (end time is earlier than start time)
        if ($endMinutes <= $startMinutes) {
            // Period spans midnight (e.g., 22:00 to 06:00)
            // Current time is in the period if it's after start OR before end
            return $currentMinutes >= $startMinutes || $currentMinutes < $endMinutes;
        } else {
            // Normal period (e.g., 14:00 to 16:00)
            // Current time is in the period if it's between start and end
            return $currentMinutes >= $startMinutes && $currentMinutes < $endMinutes;
        }
    }

    /**
     * Convert time string (H:i) to minutes since midnight.
     */
    private function timeToMinutes(string $time): int
    {
        [$hours, $minutes] = explode(':', $time);

        return (int) $hours * 60 + (int) $minutes;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'number_of_seats.prohibited_if' => 'Number of seats is only applicable for passenger vehicles, not for freight vehicles.',
            'weight_category.prohibited_if' => 'Weight category is only applicable for freight vehicles, not for passenger vehicles.',
            'is_covered.prohibited_if' => 'The is_covered field is only applicable for freight vehicles, not for passenger vehicles.',
        ];
    }
}
