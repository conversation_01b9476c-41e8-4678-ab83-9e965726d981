<?php

namespace App\Http\Requests;

use App\Enums\VehicleColors;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class VehicleUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'vehicle_photo' => 'sometimes|image|mimes:jpeg,png,jpg|max:5120',
            'vehicle_type' => ['sometimes', Rule::in(['passenger', 'freight'])],
            'license_plate_number' => ['sometimes', 'string', 'regex:/^(?!.*--)[\d-]{1,30}$/'],
            'share_benefits' => ['sometimes'],
            'seats_number' => [
                'sometimes',
                'integer', Rule::in([2, 4, 6]),
            ],
            'vehicle_model' => ['sometimes', 'integer', 'exists:vehicle_models,id'],
            'year' => ['sometimes', 'regex:/^\d{4}$/'],
            'vehicle_color' => ['sometimes', 'string', Rule::in(array_column(VehicleColors::cases(), 'value'))],
            'vehicle_equipments' => ['sometimes', 'string', function ($attribute, $value, $fail) {
                $vehicleEquipments = json_decode($value, true);

                if (! is_array($vehicleEquipments)) {
                    return $fail('The vehicle_equipments format is invalid.');
                }

                foreach ($vehicleEquipments as $equipmentId) {
                    if (! is_numeric($equipmentId)) {
                        return $fail('Each vehicle equipment must be numeric.');
                    }
                    $exists = DB::table('vehicle_equipment')->where('id', $equipmentId)->exists();
                    if (! $exists) {
                        return $fail("The vehicle equipment with ID $equipmentId does not exist.");
                    }
                }
            }],
            'insurance' => ['sometimes', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'technical_inspection' => ['sometimes',  'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'roaming_permit' => ['sometimes',  'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'insurance_expiry' => ['sometimes', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'technical_inspection_expiry' => ['sometimes', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'roaming_permit_expiry' => ['sometimes', 'date', 'after_or_equal:'.now()->addMonths(4)->toDateString()],
            'is_covered' => ['sometimes', 'string'],
            'weight_category' => ['sometimes', 'string', Rule::in(['under_1000', 'over_1000'])],
        ];
        // dd($rules);

        return $rules;
    }
}
