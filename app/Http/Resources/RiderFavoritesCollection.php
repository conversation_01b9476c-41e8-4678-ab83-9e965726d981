<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RiderFavoritesCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [];

        $data = $this->collection->map(function ($item) {
            if ($this->collection[0] instanceof \App\Models\Address) {
                return [
                    'id' => $item->id,
                    'address' => $item->address ?? null,
                    'full_address' => $item->full_address ?? null,
                    // 'postal_address' => $item->postal_address ?? null,
                    'latitude' => $item->latitude ?? null,
                    'longitude' => $item->longitude ?? null,
                ];
            } elseif ($this->collection[0] instanceof \App\Models\Trip) {
                return [
                    'id' => $item->id,
                    'departureAddress' => [
                        'address' => $item->departureAddress->address,
                        'full_address' => $item->departureAddress->full_address,
                        'label' => $item->departureAddress->label->label ?? null,
                    ],
                    'arrivalAddress' => [
                        'address' => $item->arrivalAddress->address,
                        'full_address' => $item->arrivalAddress->full_address,
                        'label' => $item->arrivalAddress->label->label ?? null,
                    ],
                ];
            }
        });

        return [
            'data' => $data,
            'meta' => [
                'current_page' => $this->currentPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'next_page_url' => $this->nextPageUrl(),
                'prev_page_url' => $this->previousPageUrl(),
            ],
        ];
    }
}
