<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RiderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'driver_gender' => $this->driver_gender,
            'seats_number' => $this->seats_number,
            'car_equipments' => $this->carEquipments->map(function ($car_equipment) {
                return [
                    'id' => $car_equipment->id,
                    'name' => $car_equipment->name,
                ];
            }),
            'car_types' => $this->carTypes->map(function ($car_type) {
                return [
                    'id' => $car_type->id,
                    'name' => $car_type->name,
                    'additional_base_fare' => $car_type->additional_base_fare,
                    'additional_price_per_km' => $car_type->additional_price_per_km,
                ];
            }),
        ];
    }
}
