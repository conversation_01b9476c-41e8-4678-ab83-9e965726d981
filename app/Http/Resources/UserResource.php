<?php

namespace App\Http\Resources;

use App\Enums\Drivers\DriverGlobalStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $coverPicture = env('APP_URL', '/').'/storage/'.$this->cover_picture;

        $data = [
            'id' => $this->id,
            'name' => $this->name,
            'last_name' => $this->last_name,
            'phone_number' => $this->phone_number,
            'phone_verified_at' => $this->phone_verified_at,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'cover_picture' => ! empty($this->cover_picture)
                ? env('APP_URL', '/').'/storage/'.$this->cover_picture
                : env('APP_URL', '/').'/images/avatar.png',
            'type' => $this->type,
            'full_name' => $this->full_name,
            'gender' => $this->gender,
        ];

        if ($this->relationLoaded('address')) {
            $firstAddress = $this->address->first();
            $data['address'] = $firstAddress ? [
                'address' => $firstAddress->address ?? null,
                'full_address' => $firstAddress->full_address ?? null,
                // 'postal_address' => $firstAddress->postal_address ?? null,
                // 'type' => $firstAddress->type ?? null,
                // 'latitude' => $firstAddress->latitude ?? null,
                // 'longitude' => $firstAddress->longitude ?? null,
                // 'is_favorite' => $firstAddress->is_favorite ?? null,
            ] : null;
        }

        if ($this->type === 'passenger') {

            $data['rider'] = [
                'id' => $this->rider->id,
                'user_id' => $this->rider->user_id,
                'global_status' => $this->rider->global_status,
            ];

        } else {
            $vehicle = $this->driver->vehicles()->exists()
            ? $this->driver->vehicles()->orderBy('created_at')->first()
            : null;

            $data['driver'] = [
                'id' => $this->driver->id,
                'user_id' => $this->driver->user_id,
                'id_number' => $this->driver->id_number,
                'document_type' => $this->driver->documents ? $this->driver->documents->document_type : null,
                'rider_gender' => $this->driver->rider_gender,
                'global_status' => $this->driver->global_status,
                'rejected_fields' => $this->driver->global_status->value === DriverGlobalStatus::rejected->value
                    ? $this->driver->rejection_reason_columns
                    : null,
                'license_expiry' => $this->driver->documents ? $this->driver->documents->license_expiry : null,
            ];

            if ($vehicle) {
                $data['driver']['vehicle'] = [
                    'id' => $vehicle->id ?? null,
                    'category' => $vehicle->vehicleType->category ?? null,
                    'image' => $vehicle->image
                        ? env('APP_URL', '/').'/storage/'.$vehicle->image
                        : env('APP_URL', '/').'/images/vehicle.jpg',
                    'license_plate_number' => $vehicle->license_plate_number ?? null,
                    'seat_number' => $vehicle->seat_number ?? null,
                    'vehicle_brand_id' => optional($vehicle->vehicleModel->vehicleBrand)->id,
                    'vehicle_model_id' => $vehicle->vehicle_model_id ?? null,
                    'year' => $vehicle->year ?? null,
                    'color' => $vehicle->color ?? null,
                    'is_covred' => ($vehicle->vehicleType && isset($vehicle->vehicleType->category) && $vehicle->vehicleType->category->value === 'freight')
                        ? $vehicle->vehicleType->is_covered
                        : null,
                    'weight_category' => $vehicle->vehicleType->weight_category ?? null,
                    // 'vehicle_equipment' => ($vehicle->vehicleType && isset($vehicle->vehicleType->category) && $vehicle->vehicleType->category->value === 'passenger')
                    //     ? $vehicle->vehicleEquipments()
                    //     : null,
                    'vehicle_documents' => [
                        'insurance_expiry' => optional($vehicle->documents)->insurance_expiry,
                        'technical_inspection_expiry' => optional($vehicle->documents)->technical_inspection_expiry,
                        'roaming_permit_expiry' => optional($vehicle->documents)->roaming_permit_expiry,
                    ],
                ];
            }

        }

        return $data;
    }
}
