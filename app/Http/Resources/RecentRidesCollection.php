<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RecentRidesCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request)
    {
        return $this->collection->map(function ($item) {
            // dd($item);

            // return [
            //     'departure_address' => [
            //         'id' => $item->departureAddress->id,
            //         'address' => $item->departureAddress->address,
            //         'full_address' => $item->departureAddress->full_address,
            //         'is_favorite' => $item->departureAddress->is_favorite,
            //     ],
            //     'arrival_address' => [
            //         'id' => $item->arrivalAddress->id,
            //         'address' => $item->arrivalAddress->address,
            //         'full_address' => $item->arrivalAddress->full_address,
            //         'is_favorite' => $item->arrivalAddress->is_favorite,
            //     ],
            // ];
        })->toArray();
    }
}
