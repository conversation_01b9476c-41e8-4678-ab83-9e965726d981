<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class VehicleCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'brand' => $vehicle->vehicleModel->vehicleBrand->name_en ?? null,
                    'model' => $vehicle->vehicleModel->name_en ?? null,
                    'plate_number' => $vehicle->license_plate_number ?? null,
                    'average_vehicle_rating' => $vehicle->average_vehicle_rating ?? null,
                    'image' => $vehicle->image
                        ? env('APP_URL', '/').'/storage/'.$vehicle->image
                        : env('APP_URL', '/').'/images/vehicle.jpg',
                ];
            }),
        ];
    }
}
