<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class RiderCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray($request)
    {
        $addresses = $this->collection->map(function ($item) {

            return [
                'address' => $item->address,
                'full_address' => $item->full_address,
                // 'postal_address' => $item->postal_address ?? null,
                'latitude' => $item->latitude,
                'longitude' => $item->longitude,
                'is_favorite' => $item->is_favorite,
                'address_label' => $item->label ? $item->label->label : null,
                'address_icon' => $item->label ? $item->label->icon : null,
                'icon_svg' => $item->label ? svg($item->label->icon ?? 'heroicon-c-home')->toHtml() : null,
            ];
        });

        $pagination = [
            'current_page' => $this->currentPage(),
            'per_page' => $this->perPage(),
            'total' => $this->total(),
            'first_page_url' => $this->url(1),
            'last_page_url' => $this->url($this->lastPage()),
            'next_page_url' => $this->hasMorePages() ? $this->nextPageUrl() : null,
            'prev_page_url' => $this->onFirstPage() ? null : $this->previousPageUrl(),
        ];

        // Return the custom data and pagination format
        return [
            'addresses' => $addresses,
            'addressesPagination' => $pagination,
        ];
    }
}
