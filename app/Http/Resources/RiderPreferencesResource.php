<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RiderPreferencesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'driver_gender' => $this->resource['driver_gender'],
            'car_brands' => $this->resource['car_brands'] ?? null,
            'seats_number' => $this->resource['seats_number'],
            'car_equipments' => $this->resource['car_equipments'] ? $this->resource['car_equipments']->map(function ($equipment) {
                return [
                    'id' => $equipment->id,
                    'name_en' => $equipment->name_en,
                    'name_ar' => $equipment->name_ar,
                    'icon' => svg($equipment->icon ?? 'heroicon-o-question-mark-circle')->toHtml(),
                ];
            }) : null,
            'car_types' => $this->resource['car_types'] ? $this->resource['car_types']->map(function ($type) {
                return [
                    'id' => $type->id,
                    'name_en' => $type->name_en,
                    'name_ar' => $type->name_ar,
                    'image' => ! empty($type->image) ? env('APP_URL').'/storage/'.$type->image : null,
                    'description_ar' => $type->description_ar,
                    'description_en' => $type->description_en,
                    'category' => $type->category,
                    'is_covered' => $type->is_covered,
                    'weight_category' => $type->weight_category,
                ];
            }) : null,
        ];
    }
}
