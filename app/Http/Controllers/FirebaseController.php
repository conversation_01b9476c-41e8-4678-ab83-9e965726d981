<?php

namespace App\Http\Controllers;

use App\Http\Responses\ApiResponse;
use Exception;
use Illuminate\Http\Request;
use <PERSON>reait\Firebase\Factory;

class FirebaseController extends Controller
{
    /**
     * Handle the incoming request.
     */

    // configuration of the firebase admin php sdk
    public function __invoke(Request $request)
    {
        $path = storage_path('firebase/RakebFirebase.json');

        if (! file_exists($path)) {
            return ApiResponse::error(null, 'An error occured when trying to fetch firebase configuration file.', 404, null);
        }
        try {
            $factory = (new Factory)
                ->withServiceAccount($path);
            //     ->withDatabaseUri(env('FIREBASE_DATABASE_URL'));

            // $database = $factory->createDatabase();

            // $data = $database->getReference('data');

            // return $data->getValue();
        } catch (Exception $error) {
            return ApiResponse::error(null, 'An error occured when trying to connect to firebase.', $error->getCode(), $error->getMessage());
        }

    }
}
