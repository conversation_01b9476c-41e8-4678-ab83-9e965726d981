<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class MapController extends Controller
{
    public function getDistanceInfo(Request $request)
    {
        $origin = $request->input('origin'); // e.g. "40.6655101,-73.8918897"
        $destination = $request->input('destination'); // e.g. "40.6905615,-73.9976592"

        $response = Http::get('https://maps.googleapis.com/maps/api/distancematrix/json', [
            'origins' => $origin,
            'destinations' => $destination,
            'key' => config('services.google_maps.key'),
        ]);
        // dd($response);

        return response()->json($response->json());
    }
}
