<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DriverCollection;
use App\Http\Resources\DriverResource;
use App\Models\User;
use Illuminate\Http\Request;

class UsersDriverController extends Controller
{
    public function index(Request $request, User $user): DriverCollection
    {
        $search = $request->get('search', '');

        $drivers = $this->getSearchQuery($search, $user)
            ->latest()
            ->paginate();

        return new DriverCollection($drivers);
    }

    public function store(Request $request, User $user): DriverResource
    {
        $validated = $request->validate([
            'license_number' => ['nullable', 'string'],
            'license_expiry' => ['nullable', 'string'],
            'national_id_number' => ['nullable', 'string'],
        ]);

        $driver = $user->drivers()->create($validated);

        return new DriverResource($driver);
    }

    public function getSearchQuery(string $search, User $user)
    {
        return $user->drivers()->where('license_number', 'like', "%{$search}%");
    }
}
