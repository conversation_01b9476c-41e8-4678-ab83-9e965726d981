<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\Trip;
use App\Models\TripRating;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class TripRatingController extends Controller
{
    public function riderReview(Request $request)
    {
        $validated = $request->validate([
            'trip_id' => ['required', 'exists:trips,id'],
            'rider_id' => ['required', 'exists:riders,id'],
            'driver_id' => ['required', 'exists:drivers,id'],
            'traffic_lights_exceeded' => ['required', 'boolean'],
            'speed_exceeded' => ['required', 'boolean'],
            'driver_behavior' => ['required', Rule::in(['excellent', 'good', 'poor'])],
            'rider_to_driver_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_to_car_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_review' => ['nullable', 'string'],
        ]);

        $trip = Trip::with('tripRatings')->find($validated['trip_id']);

        if (! $trip || $trip->rider_id !== Auth::user()->rider->id) {
            return response()->json(['message' => 'Trip not found or unauthorized'], 404);
        }

        $ratingData = [
            'trip_id' => $validated['trip_id'],
            'rider_id' => $validated['rider_id'],
            'driver_id' => $validated['driver_id'],
            'traffic_lights_exceeded' => $validated['traffic_lights_exceeded'],
            'speed_exceeded' => $validated['speed_exceeded'],
            'driver_behavior' => $validated['driver_behavior'],
            'rider_to_driver_rating' => $validated['rider_to_driver_rating'],
            'rider_to_car_rating' => $validated['rider_to_car_rating'],
            'rider_review' => $validated['rider_review'],
        ];

        $existingRating = $trip->tripRatings()
            ->where('driver_id', $validated['driver_id'])
            ->first();

        if ($existingRating) {
            $existingRating->update($ratingData);
            $message = 'Rider review updated successfully';
        } else {
            $ratingData['driver_to_rider_rating'] = 0;
            $existingRating = TripRating::create($ratingData);
            $message = 'Rider review created successfully';
        }

        return ApiResponse::success($existingRating, $message, 201);
    }
}
