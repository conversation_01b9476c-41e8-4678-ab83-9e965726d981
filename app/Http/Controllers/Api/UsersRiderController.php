<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\Trip;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;

class UsersRiderController extends Controller
{
    /**
     * Generate a shareable link for the rider's current trip
     */
    public function shareLocation($tripId)
    {
        // Get authenticated user and their active trip
        $user = Auth::user();
        $rider = $user->rider;

        if (! $rider) {
            return ApiResponse::error(null, 'User is not a rider', 403);
        }

        // Find the rider's active trip
        $trip = Trip::find($tripId);

        if (! $trip) {
            return ApiResponse::error(null, 'No active trip found', 404);
        }

        // Generate or retrieve share token
        if (! $trip->share_token) {
            $trip->share_token = Str::random(32);
            $trip->save();
        }

        // Generate a signed URL that expires in 24 hours
        // Using a regular signed route instead of temporary signed route
        // to ensure it can be used multiple times within the expiry period
        $shareUrl = URL::signedRoute(
            'trips.shared',
            ['shareToken' => $trip->share_token, 'expiry_time' => now()->addHours(24)->timestamp]
        );

        return ApiResponse::success([
            'share_url' => $shareUrl,
            'trip_id' => $trip->id,
            'expires_at' => now()->addHours(24)->toIso8601String(),
        ], 'Trip share link generated successfully');
    }
}
