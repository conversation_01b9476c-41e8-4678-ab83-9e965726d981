<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\RiderPreferencesRequest;
use App\Http\Resources\RecentRidesCollection;
use App\Http\Resources\RiderCollection;
use App\Http\Resources\RiderResource;
use App\Http\Responses\ApiResponse;
use App\Models\AddressLabel;
use App\Models\Rider;
use App\Models\RiderPreferences;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RiderController extends Controller
{
    /**
     * Home
     *
     * @response array{userAddresses : RiderCollection,recent : RecentRidesCollection , message: string, status: integer,error : string}
     */
    public function home()
    {
        $user = Auth::user()->load(['rider' => function ($query) {
            return $query->withTrashed();
        }]);

        if ($user->type !== 'passenger') {
            return ApiResponse::error('', 'Unothorized.', 403, null);
        }

        $addresses = $user->address()
            ->where('address_label_id', '!=', null)
            ->with('label')
            ->paginate(10);

        $addressesArray = $addresses->items();

        usort($addressesArray, function ($a, $b) {
            $priority = ['home' => 1, 'work' => 2, 'school' => 3];

            $aLabel = strtolower($a->label->label ?? '');
            $bLabel = strtolower($b->label->label ?? '');

            $aPriority = $priority[$aLabel] ?? 9999;
            $bPriority = $priority[$bLabel] ?? 9999;

            return $aPriority <=> $bPriority;
        });
        $addresses->setCollection(collect($addressesArray));

        if (! $user->rider) {
            return ApiResponse::error(null, "Rider doesn't exist", 404);
        }

        $recentAddresses = $user->rider->trips()
            ->select('id', 'trip_location_id')
            ->latest()
            ->take(5)
            ->with('tripLocation')
            ->get();

        $data = [
            'userAddresses' => new RiderCollection($addresses),
            'recent' => new RecentRidesCollection($recentAddresses),
        ];

        return ApiResponse::success($data, 'User data has been loaded successfully.', 200, null);
    }

    /**
     * Add new favorite address
     *
     * Adds a new address to the rider's favorite addresses list.
     *
     * @response array{data :array , message: string, status: integer,error : string}
     **/
    public function addFavoriteAddress(Request $request)
    {
        $rider = Auth::user()->load('rider');
        if ($rider->type !== 'passenger') {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $validated = $request->validate([
            'address' => 'required|string',
            'full_address' => 'nullable|string',
            'postal_address' => 'nullable|json',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        $address = $rider->address()->create([
            'address' => $validated['address'],
            'full_address' => $validated['full_address'] ?? null,
            'postal_address' => $validated['postal_address'] ?? null,
            'latitude' => $validated['latitude'],
            'longitude' => $validated['longitude'],
            'is_favorite' => true,
        ]);

        if ($address) {
            return ApiResponse::success($address, 'address added to favorite successfully', 201, null);
        } else {
            return ApiResponse::success(null, 'faild to added to favorite', 500, null);
        }

    }

    /**
     * Rider preferences
     *
     * @response array{data : RiderResource , message: string, status: integer,error : string}
     **/
    public function preferences(RiderPreferencesRequest $request)
    {
        $validated = $request->validated();

        $preference = RiderPreferences::updateOrCreate(
            ['user_id' => Auth::user()->id],
            [
                'driver_gender' => Auth::user()->gender == 'female' ? $validated['driver_gender'] : 'both',
                'seats_number' => $validated['seats_number'],
            ]
        );

        $preference->carEquipments()->sync($validated['car_equipments']);
        $preference->carEquipments()->sync(
            collect($validated['car_equipments'])->mapWithKeys(fn ($id) => [$id => ['created_at' => now(), 'updated_at' => now()]])
        );

        $preference->carTypes()->sync(
            collect($validated['car_types'])->mapWithKeys(fn ($id) => [$id => ['created_at' => now(), 'updated_at' => now()]])
        );

        $preference->load(['carTypes', 'carEquipments']);

        $message = $preference->wasRecentlyCreated
        ? 'Rider preferences created successfully.'
        : 'Rider preferences updated successfully.';

        return ApiResponse::success(new RiderResource($preference), $message, 200, null);
    }

    /**
     * Rider heartbeat
     *
     * @response array{data : null , message: 'Rider heartbeat updated', status: 200,error : string}
     **/
    public function heartbeat(Request $request)
    {
        $user = Auth::user();

        if (! $user->rider) {
            return ApiResponse::error(null, 'User is not a rider', 403);
        }

        // Update rider's heartbeat
        $user->rider->update(['last_heartbeat' => now()]);

        Log::info('Rider heartbeat updated', [
            'rider_id' => $user->rider->id,
            'timestamp' => now(),
        ]);

        return ApiResponse::success(null, 'Heartbeat updated');
    }

    public function addLabel(Request $request)
    {
        $validated = $request->validate([
            'address_id' => 'required|numeric|exists:addresses,id',
            'label' => 'required|string',
            'icone' => 'required|string',
        ]);

        if (! Auth::user()->address()->where('id', $validated['address_id'])->exists()) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        $addressLabel = AddressLabel::where('address_id', $validated['address_id'])->first();
        if ($addressLabel) {
            $addressLabel->update([
                'label' => $validated['label'],
                'icon' => $validated['icone'],
            ]);

            return ApiResponse::success($validated['label'], 'Label updated successfully', 200, null);
        }

        AddressLabel::create([
            'label' => $validated['label'],
            'icon' => $validated['icone'],
            'address_id' => $validated['address_id'],
        ]);

        return ApiResponse::success($validated['label'], 'Label created successfully', 201, null);
    }

    public function removeLabel(Request $request)
    {
        $validated = $request->validate([
            'address_id' => 'required|numeric|exists:addresses,id',
        ]);

        if (! Auth::user()->address()->where('id', $validated['address_id'])->exists()) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        AddressLabel::where('address_id', $validated['address_id'])->delete();

        return ApiResponse::success(null, 'Label removed successfully', 200, null);
    }
}
