<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TripRatingCollection;
use App\Http\Resources\TripRatingResource;
use App\Models\Trip;
use App\Models\TripRating;
use Illuminate\Http\Request;

class TripsTripRatingController extends Controller
{
    public function index(Request $request, Trip $trip): TripRatingCollection
    {
        $search = $request->get('search', '');

        $tripRatings = $this->getSearchQuery($search, $trip)
            ->latest()
            ->paginate();

        return new TripRatingCollection($tripRatings);
    }

    public function store(Request $request, Trip $trip): TripRatingResource
    {
        $validated = $request->validate([
            'rider_id' => ['required'],
            'driver_id' => ['required'],
            'rider_to_driver_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_to_car_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'rider_review' => ['nullable', 'string'],
            'driver_review' => ['nullable', 'string'],
            'driver_to_rider_rating' => ['required', 'integer', 'min:1', 'max:5'],
        ]);

        $validated['trip_id'] = $trip->id;
        $tripRating = TripRating::create($validated);

        return new TripRatingResource($tripRating);
    }

    public function getSearchQuery(string $search, Trip $trip)
    {
        return $trip
            ->tripRatings()
            ->where('rider_review', 'like', "%{$search}%");
    }
}
