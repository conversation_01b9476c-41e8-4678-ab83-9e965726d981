<?php

namespace App\Http\Controllers\Api;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\GenderEnum;
use App\Enums\RiderGlobalStatus;
use App\Enums\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\UserResource;
use App\Http\Responses\ApiResponse;
use App\Models\Driver;
use App\Models\RiderPreferences;
use App\Models\TripCancellation;
use App\Models\User;
use Ichtrojan\Otp\Otp;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * User profile
     *
     * @response UserResource
     */
    public function me(Request $request)
    {
        $user = $this->LoadTrashed($request->user());
        $userType = $user->rider()->exists() ? 'rider' : 'driver';

        if ($user->driver) {
            $user->driver->load('documents');
        }
        $user->load(['address' => function ($query) {
            $query->orderBy('created_at', 'asc')->first();
        }]);

        $weekStart = Carbon::now()->startOfWeek(Carbon::SATURDAY);
        $weekEnd = clone $weekStart;
        $weekEnd->addDays(6)->endOfDay();
        $maxCancellations = (int) env('MAX_CANCELLATIONS');

        $cancellationsCount = TripCancellation::where('user_id', $user->id)
            ->where('cancelled_by', $userType)
            ->where('counts_towards_limit', true)
            ->whereBetween('cancelled_at', [$weekStart, $weekEnd])
            ->count();

        $remainingCancellations = $maxCancellations - $cancellationsCount;

        return (new UserResource($user))->additional([
            'meta' => [
                'cancellations' => [
                    'total' => $cancellationsCount,
                    'remaining' => max(0, $remainingCancellations),
                    'max_allowed' => $maxCancellations,
                    'week_start' => $weekStart->toDateString(),
                    'week_end' => $weekEnd->toDateString(),
                ],
            ],
        ]);
    }

    /**
     * Update user profile
     *
     * @response UserResource
     */
    public function patch(UserUpdateRequest $request)
    {
        $validated = $request->validated();
        $user = $this->LoadTrashed(Auth::user());

        if (isset($validated['otp']) && isset($validated['email'])) {

            $otpInstance = new Otp;
            $isValidOtp = $otpInstance->validate($validated['email'], $validated['otp']);

            if (! $isValidOtp->status) {
                return ApiResponse::error(null, 'Invalid OTP or it has expired.', 400, null);
            }
            $user->email_verified_at = now();
            $user->save();
        }
        if ($request->has('id_number') && ($user->driver != null)) {

            // Note: Here, we are checking the uniqueness of the document type.
            if (Driver::withTrashed()
                ->where('id_number', $validated['id_number'])
                ->where('id', '!=', $user->driver->id)
                ->exists()) {
                return ApiResponse::error(null, $validated['document_type'].' Already Exists.', 422, null);
            }

            if ($validated['document_type'] === 'national_id_number') {
                $firstNumber = substr($validated['id_number'], 0, 1);

                if ($validated['gender'] === 'female') {
                    $validated['rider_gender'] = 'female';
                }

                if ($firstNumber === '1' && $validated['gender'] === 'female') {
                    return ApiResponse::error(null, 'National ID number cannot start with 1 when the driver\'s gender is female', 422, null);
                } elseif ($firstNumber === '2' && $validated['gender'] === 'male') {
                    return ApiResponse::error(null, 'National ID number cannot start with 2 when the driver\'s gender is male', 422, null);
                }
            }
            $user->driver->update([
                'id_number' => $validated['id_number'],
                'rider_gender' => $validated['rider_gender'],
            ]);
            $user->driver->documents()->updateOrCreate(
                ['driver_id' => $user->driver->id],
                ['document_type' => $validated['document_type']]
            );

            $validated = Arr::except($validated, ['id_number', 'rider_gender', 'document_type']);
        }

        if ($request->has('driver_gender') && ($user->rider != null)) {

            RiderPreferences::updateOrCreate([
                'user_id' => $user->id,
            ], [
                'seats_number' => [],
                'driver_gender' => $validated['gender'] === GenderEnum::female->value ? $validated['driver_gender'] : 'both',
            ]);
        }
        // Handle address updates
        if (array_key_exists('address', $validated)) {
            $request->validate([
                'address' => 'required|array',
                'address.address' => 'nullable|string',
                'address.full_address' => 'nullable|string',
                'address.postal_address' => 'nullable|array',
                'address.latitude' => 'nullable|numeric',
                'address.longitude' => 'nullable|numeric',
            ]);
            $address = $user->address()->orderBy('created_at', 'asc')->first();
            if (! $address) {
                $user->address()->create($validated['address']);
            } else {
                $address->update($validated['address']);
            }
            $validatedWithoutAddress = Arr::except($validated, ['address', 'driver_gender']);
            $user->update($validatedWithoutAddress);
        } else {
            $validUserInputs = Arr::except($validated, ['driver_gender']);
            $user->update($validUserInputs);
        }

        if ($user->rider != null && $user->rider->deleted_at === null && $user->rider->global_status->value != RiderGlobalStatus::blocked->value) {
            $user->rider->update([
                'global_status' => RiderGlobalStatus::active->value,
                'previous_global_status' => RiderGlobalStatus::pending->value,
            ]);

        } elseif ($user->rider != null && $user->rider->global_status->value === RiderGlobalStatus::blocked->value) {
            $user->rider->update([
                'previous_global_status' => RiderGlobalStatus::active->value,
            ]);
        }

        if (isset($validated['rejectionSubmit']) && $validated['rejectionSubmit'] === 'true' && $user->driver != null) {
            if ($user->driver->deleted_at === null && $user->driver->global_status->value === DriverGlobalStatus::rejected->value) {
                $user->driver->update([
                    'global_status' => DriverGlobalStatus::in_progress->value,
                    'previous_global_status' => DriverGlobalStatus::rejected->value]);
            }
        }
        $userType = ($user->driver != null) ? 'driver' : 'rider';

        return ApiResponse::success(new UserResource($user->load('address')), "$userType data has been updated successfully", 200, null);
    }

    /**
     * Upload user profile picture
     *
     * @response UserResource
     */
    public function picture(Request $request)
    {
        if ($request->file('photo')->isValid()) {

            $request->validate(['photo' => 'required|mimes:jpeg,png|min:1|max:5120']); // 5 mb

            $path = $request->file('photo')->store('pictures', 'public');

            $user = $this->LoadTrashed(Auth::user());

            $user->update(['cover_picture' => $path]);

            return ApiResponse::success(new UserResource($user), 'Profile picture uploaded successfully.', 200, null);
        } else {
            return ApiResponse::error('null', 'An error occured while uploading the image.', 500, null);
        }
    }

    public function LoadTrashed($user)
    {
        if ($user->type === 'driver') {
            $user->load(['driver' => function ($query) {
                $query->withTrashed();
            }]);
        } elseif ($user->type === 'passenger') {
            $user->load(['rider' => function ($query) {
                $query->withTrashed();
            }]);
        }

        return $user;
    }

    /**
     * Update user status
     *
     * @response array{data : 'online' , message: 'User status updated successfully', status: integer,error : string}
     */
    public function status(Request $request)
    {
        $validated = $request->validate([
            'status' => [
                'required',
                'string',
                Rule::in([UserStatus::ONLINE->value, UserStatus::OFFLINE->value, UserStatus::BUSY->value]),
            ],
        ]);

        $currentStatus = Auth::user()->status->value;

        if (Auth::user()->rider()->exists()) {
            if ($validated['status'] === UserStatus::OFFLINE->value) {
                // could not be bad request it's unprocessed req
                return ApiResponse::error(null, 'Status change denied: A rider cannot go offline.', 422, null);
            }
        }
        // if ($currentStatus === $validated['status']) {
        //     return ApiResponse::error(null, 'User status already set to '.$validated['status'], 422, null); // unproccesssed request khater it violate the buissnes logic
        // }

        if ($currentStatus === UserStatus::OFFLINE->value && $validated['status'] === UserStatus::BUSY->value ||
         $currentStatus === UserStatus::BUSY->value && $validated['status'] === UserStatus::OFFLINE->value) {
            return ApiResponse::error(null, 'User status cannot be changed directly from offline to busy or vice versa', 422, null); //  unproccesssed request khater it violate the buissnes logic
        }
        Auth::user()->status = $validated['status'];
        Auth::user()->save();

        return ApiResponse::success(Auth::user()->status, 'User status updated successfully', 200, null);
    }
}
