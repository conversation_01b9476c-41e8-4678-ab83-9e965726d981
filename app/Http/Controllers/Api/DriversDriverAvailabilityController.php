<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DriverAvailabilityCollection;
use App\Http\Resources\DriverAvailabilityResource;
use App\Models\Driver;
use Illuminate\Http\Request;

class DriversDriverAvailabilityController extends Controller
{
    public function index(
        Request $request,
        Driver $driver
    ): DriverAvailabilityCollection {
        $search = $request->get('search', '');

        $driverAvailabilities = $this->getSearchQuery($search, $driver)
            ->latest()
            ->paginate();

        return new DriverAvailabilityCollection($driverAvailabilities);
    }

    public function store(
        Request $request,
        Driver $driver
    ): DriverAvailabilityResource {
        $validated = $request->validate([
            'status' => ['required'],
            'start_time' => ['required'],
            'end_time' => ['required'],
            'notes' => ['nullable', 'string'],
        ]);

        $driverAvailability = $driver
            ->driverAvailabilities()
            ->create($validated);

        return new DriverAvailabilityResource($driverAvailability);
    }

    public function getSearchQuery(string $search, Driver $driver)
    {
        return $driver
            ->driverAvailabilities()
            ->where('notes', 'like', "%{$search}%");
    }
}
