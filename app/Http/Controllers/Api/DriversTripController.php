<?php

namespace App\Http\Controllers\Api;

use App\Enums\Trips\TripStatus;
use App\Enums\UserStatus;
use App\Events\TripEvents\CompleteTrip;
use App\Events\TripEvents\StartTrip;
use App\Http\Controllers\Controller;
use App\Http\Resources\TripCollection;
use App\Http\Resources\TripResource;
use App\Http\Responses\ApiResponse;
use App\Models\Trip;
use App\Models\TripRating;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DriversTripController extends Controller
{
    public function tripList(Request $request)
    {
        $validated = $request->validate([
            'From' => 'sometimes|string',
            'To' => 'sometimes|string',
            'Day' => 'sometimes|string',
            'Year' => 'sometimes|string',
        ]);

        $user = Auth::user();

        if (! $user->driver) {
            return ApiResponse::error('', 'Unauthorized access', 403);
        }

        $query = $user->driver->trips()
            ->whereIn('status', [TripStatus::canceled->value, TripStatus::completed->value]);

        // Handle Year filter

        if (! empty($validated['Year'])) {
            $query->whereYear('created_at', $validated['Year']);
        }

        // Handle Day filter (specific date)
        elseif (! empty($validated['Day'])) {
            $query->whereDate('created_at', Carbon::parse($validated['Day'])->toDateString());
        }
        // Handle From and To date range
        elseif (! empty($validated['From']) && ! empty($validated['To'])) {
            if (Carbon::parse($validated['From']) > Carbon::parse($validated['To'])) {
                return ApiResponse::error('', 'From date cannot be greater than To date', 400);
            }

            $query->whereBetween('created_at', [
                Carbon::parse($validated['From'])->startOfDay(),
                Carbon::parse($validated['To'])->endOfDay(),
            ]);
        }

        // Handle only From date (from that date until now)
        elseif (! empty($validated['From']) && empty($validated['To'])) {
            $query->where('created_at', '>=', Carbon::parse($validated['From'])->startOfDay());
        }

        // Handle only To date (until that date)
        elseif (empty($validated['From']) && ! empty($validated['To'])) {
            $query->where('created_at', '<=', Carbon::parse($validated['To'])->endOfDay());
        } else {
            return ApiResponse::error('', 'Please provide a date filter (Year, Day, From/To)', 400);
        }

        $trips = $query->paginate(10);

        return ApiResponse::success(new TripCollection($trips), 'Driver trips retrieved successfully', 200);
    }

    public function trip($tripId)
    {

        $trip = Trip::find($tripId);

        if (! $trip) {
            return ApiResponse::error(null, 'Trip not found', 404);
        }

        if (Auth::user()->driver->id != $trip->driver_id) {
            return ApiResponse::error(null, 'Unauthorized access', 403);
        }

        // Fix: Get the rating properly from the collection
        if ($trip->tripRatings()->exists()) {
            $tripRating = $trip->tripRatings()->first();
            $trip['rating'] = $tripRating ? $tripRating->rider_to_driver_rating : null;
        }

        $trip['total_price'] = json_decode($trip->pricing_breakdown, true);
        $trip['total_price'] = $trip['total_price']['total'] ?? null;

        $data = $trip
            ->load(
                'driver.user',
                'vehicle.vehicleModel.vehicleBrand',
                'tripLocation',
                'vehicleType',
            );

        return ApiResponse::success(new TripResource($data), 'Rider ride retrieved successfully', 200);
    }

    // move the ride request response to this controller
    public function startTrip(array $data): void
    {
        try {
            $trip = Trip::where('id', $data['trip_id'])->first();

            Log::info('this is the data :', [$trip]);
            // Get drop-off coordinates from trip location
            $pickup_lng = $trip->tripLocation->departure_lng;
            $pickup_lat = $trip->tripLocation->departure_lat;

            // Get current driver coordinates
            $driver_lng = $data['lng'];
            $driver_lat = $data['lat'];

            // Check if driver is within 50 meters of drop-off point using PostGIS
            $distance = DB::select("
                SELECT ST_Distance(
                    ST_Transform(ST_SetSRID(ST_MakePoint($driver_lng, $driver_lat), 4326), 3857),
                    ST_Transform(ST_SetSRID(ST_MakePoint($pickup_lng, $pickup_lat), 4326), 3857)
                ) as distance
            ")[0]->distance;
            Log::info('this is the distance between the driver and the pickup point  :', [$distance]);
            // if ($distance > 50 || $trip->status->value !== TripStatus::driver_arrived->value) {
            //     Log::info('Driver attempted to start trip too far from pickup point', [
            //         'trip_id' => $trip->id,
            //         'distance' => $distance,
            //         'trip_status' => $trip->status->value,
            //         'distance_to_pickup' => $distance,
            //         'driver_location' => ['lng' => $driver_lng, 'lat' => $driver_lat],
            //         'dropoff_location' => ['lng' => $pickup_lng, 'lat' => $pickup_lat],
            //     ]);

            //     broadcast(new StartTrip($trip, false, 'Driver attempted to start trip too far from pickup point'));

            //     return;
            // }

            DB::transaction(function () use ($trip) {
                // Update trip status
                $trip->update([
                    'status' => TripStatus::on_trip->value,
                    'actual_departure_time' => now(),
                ]);

                // Broadcast trip start's to say to tell rider
                broadcast(new StartTrip($trip, true, 'Trip started successfully'));
                $trip->update(['driver_started_at' => now()]);
                Log::info('Trip started successfully', [
                    'trip_id' => $trip->id,
                    'driver_id' => $trip->driver_id,
                    'rider_id' => $trip->rider_id,
                ]);
            });

            return;

        } catch (\Exception $e) {
            Log::error('Error starting trip', [
                'error' => $e->getMessage(),
                'trip_id' => $data['trip_id'] ?? 'unknown',
            ]);
        }
    }

    public function CompleteTrip(array $data): void
    {
        try {
            $trip = Trip::where('id', $data['trip_id'])->first();

            // Get drop-off coordinates from trip location
            $dropoff_lng = $trip->tripLocation->arrival_lng;
            $dropoff_lat = $trip->tripLocation->arrival_lat;

            // Get current driver coordinates
            $driver_lng = $data['lng'];
            $driver_lat = $data['lat'];

            // Check if driver is within 50 meters of drop-off point using PostGIS
            $distance = DB::select("
                SELECT ST_Distance(
                    ST_Transform(ST_SetSRID(ST_MakePoint($driver_lng, $driver_lat), 4326), 3857),
                    ST_Transform(ST_SetSRID(ST_MakePoint($dropoff_lng, $dropoff_lat), 4326), 3857)
                ) as distance
            ")[0]->distance;
            Log::info('this is the distance between the driver and the dropoff point  :', [$distance]);

            // if ($distance > 50 || $trip->status->value !== TripStatus::on_trip->value) {
            //     Log::info('Driver attempted to end trip too far from drop-off point', [
            //         'trip_id' => $trip->id,
            //         'trip_status' => $trip->status->value,
            //         'distance_to_dropoff' => $distance,
            //         'driver_location' => ['lng' => $driver_lng, 'lat' => $driver_lat],
            //         'dropoff_location' => ['lng' => $dropoff_lng, 'lat' => $dropoff_lat],
            //     ]);

            //     broadcast(new CompleteTrip($trip, false, 'Driver attempted to end trip too far from drop-off point'));

            //     return;
            // }

            DB::transaction(function () use ($trip) {
                // Update trip status and arrival time
                $trip->update([
                    'status' => TripStatus::completed->value,
                    'actual_arrival_time' => now(),
                ]);

                // Update driver and rider status to available
                $trip->driver->user->update([
                    'status' => UserStatus::ONLINE->value,
                ]);

                $trip->rider->user->update([
                    'status' => UserStatus::ONLINE->value,
                ]);

                broadcast(new CompleteTrip($trip, true, 'Trip completed successfully'));

                Log::info('Trip completed successfully', [
                    'trip_id' => $trip->id,
                    'trip_status' => $trip->status->value,
                    'driver_id' => $trip->driver_id,
                    'rider_id' => $trip->rider_id,
                    'duration' => $trip->actual_arrival_time->diffInMinutes($trip->actual_departure_time),
                ]);
                TripRating::create([
                    'trip_id' => $trip->id,
                    'rider_id' => $trip->rider_id,
                    'driver_id' => $trip->driver_id,
                    'rider_to_driver_rating' => 0,
                    'rider_to_car_rating' => 0,
                    'driver_to_rider_rating' => 0,
                ]);
            });
        } catch (\Exception $e) {
            Log::error('Error ending trip', [
                'error' => $e->getMessage(),
                'trip_id' => $data['trip_id'] ?? 'unknown',
            ]);
        }
    }

    public function driverReview(Request $request)
    {
        $validated = $request->validate([
            'trip_id' => ['required', 'exists:trips,id'],
            'rider_id' => ['required', 'exists:riders,id'],
            'driver_id' => ['required', 'exists:drivers,id'],
            'driver_to_rider_rating' => ['required', 'integer', 'min:1', 'max:5'],
            'driver_review' => ['nullable', 'string'],
        ]);

        $trip = Trip::with('tripRatings')->find($validated['trip_id']);

        if (! $trip || $trip->driver_id !== Auth::user()->driver->id) {
            return response()->json(['message' => 'Trip not found or unauthorized'], 404);
        }

        $ratingData = [
            'trip_id' => $validated['trip_id'],
            'rider_id' => $validated['rider_id'],
            'driver_id' => $validated['driver_id'],
            'driver_to_rider_rating' => $validated['driver_to_rider_rating'],
            'driver_review' => $validated['driver_review'],
        ];

        $existingRating = $trip->tripRatings()
            ->where('rider_id', $validated['rider_id'])
            ->first();

        if ($existingRating) {
            $existingRating->update($ratingData);
            $message = 'Driver review updated successfully';
        } else {
            $ratingData['rider_to_driver_rating'] = 0;
            $ratingData['rider_to_car_rating'] = 0;
            $existingRating = TripRating::create($ratingData);
            $message = 'Driver review created successfully';
        }

        return ApiResponse::success($existingRating, $message, 201);
    }
}
