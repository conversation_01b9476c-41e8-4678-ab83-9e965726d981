<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehicleController extends Controller
{
    // public function picture(Request $request)
    // {
    //     if ($request->file('photo')->isValid()) {

    //         $request->validate(['photo' => 'required|mimes:jpg,jpeg,png|min:1|max:10240']);

    //         $path = $request->file('photo')->store('pictures', 'public');

    //         $user = Auth::user();
    //         $user->update(['cover_picture' => $path]);

    //         if ($user->type === 'driver') {
    //             $user->load('driver');
    //         } elseif ($user->type === 'passenger') {
    //             $user->load('rider');
    //         }

    //         return ApiResponse::success(new UserResource($user), 'Profile picture uploaded successfully.', 200, null);
    //     } else {
    //         return ApiResponse::error('null', 'An error occured while uploading the image.', 500, null);
    //     }
    // }

    public function destroy(Request $request, Vehicle $vehicle): Response
    {
        $vehicle->delete();

        return response()->noContent();
    }
}
