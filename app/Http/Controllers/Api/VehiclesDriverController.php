<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\DriverCollection;
use App\Models\Driver;
use App\Models\Vehicle;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class VehiclesDriverController extends Controller
{
    public function index(Request $request, Vehicle $vehicle): DriverCollection
    {
        $search = $request->get('search', '');

        $drivers = $this->getSearchQuery($search, $vehicle)
            ->latest()
            ->paginate();

        return new DriverCollection($drivers);
    }

    public function store(
        Request $request,
        Vehicle $vehicle,
        Driver $driver
    ): Response {
        $vehicle->drivers()->syncWithoutDetaching([$driver->id]);

        return response()->noContent();
    }

    public function destroy(
        Request $request,
        Vehicle $vehicle,
        Driver $driver
    ): Response {
        $vehicle->drivers()->detach($driver);

        return response()->noContent();
    }

    public function getSearchQuery(string $search, Vehicle $vehicle)
    {
        return $vehicle
            ->drivers()
            ->whereHas('user', function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
    }
}
