<?php

namespace App\Http\Controllers\Api;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Http\Responses\ApiResponse;
use App\Models\Auth\OtpBlock;
use App\Models\Auth\OtpRequest;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\User;
use App\Notifications\VerifyEmailNotification;
use App\Services\AuthService;
use App\Services\OtpRequestService;
use Ichtrojan\Otp\Otp;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    public function __construct(
        private readonly AuthService $service,
        private readonly OtpRequestService $otpRequestService
    ) {}

    /**
     * Login with phone number
     *
     * Initiates the login process by sending an OTP to the provided phone number.
     * If the user is new, they will be registered automatically.
     *
     * @bodyParam phone_number string required The user's phone number in international format. Example: +218929262034
     * @bodyParam type string required User type, either 'driver' or 'passenger'. Example: passenger
     *
     * @response {
     *   "otp": "8234",
     *   "message": "OTP has been sent to your phone number.",
     *   "registration_step": "home"
     * }
     */
    public function login(Request $request, OtpRequestService $otpService): JsonResponse
    {
        $credentials = $request->validate([
            'phone_number' => ['required', 'regex:/^\S+$/', 'phone:INTERNATIONAL,mobile'],
            'type' => ['required', 'in:driver,passenger'],
        ]);

        // Check if the user is blocked for SMS OTP (patch API)
        $blockMessage = $otpService->checkAndEnforceLimits($credentials['phone_number'], 'patch');

        if ($blockMessage) {
            // Ensure $blockMessage is an array before accessing offsets
            if (is_array($blockMessage)) {
                $date = [
                    'hours' => $blockMessage['hours'] ?? null,
                    'minutes' => $blockMessage['minutes'] ?? null,
                    'seconds' => $blockMessage['seconds'] ?? null,
                ];

                return ApiResponse::error(
                    null,
                    $blockMessage['message'],
                    429,
                    $date
                );
            }
        }

        $existingUser = User::where('phone_number', $credentials['phone_number'])->first();

        if ($existingUser && $existingUser->type !== $credentials['type']) {
            return ApiResponse::error(
                null,
                "User is already registered as a {$existingUser->type}, cannot register as a {$credentials['type']}.",
                422
            );
        }

        $user = User::firstOrCreate(
            ['phone_number' => $credentials['phone_number']],
            [
                'type' => $credentials['type'],
                'password' => Hash::make(Str::random(10)),
            ]
        );

        if ($user->wasRecentlyCreated) {
            if ($credentials['type'] === 'driver') {
                Driver::create(['user_id' => $user->id]);
            } elseif ($credentials['type'] === 'passenger') {
                Rider::create(['user_id' => $user->id]);
            }
        }

        // Generate OTP
        $otpInstance = new Otp;
        $otp = $otpInstance->generate($credentials['phone_number'], 'numeric', 4, 5);

        // Record the OTP request
        $otpService->recordRequest($credentials['phone_number'], 'patch');

        // Determine registration step
        $step = 'register';

        if ($credentials['type'] === 'driver') {
            $user = $user->load(['driver' => function ($query) {
                $query->withTrashed();
            }]);

            $hasVehicle = $user->driver->vehicles()->exists();
            $hasDocuments = $user->driver->documents()->first() ?? null;

            if ($hasVehicle) {
                $step = $user->driver->global_status->value === DriverGlobalStatus::pending->value ? 'pending_verification' : 'home';
            } elseif (! $hasVehicle && ($hasDocuments && $hasDocuments->document_front != null)) {
                $step = 'car_form';
            } elseif ($hasDocuments && $hasDocuments->document_front === null && ! empty($user->name)) {
                $step = 'driver_documents';
            }
        } else {
            if (! empty($user->name)) {
                $step = 'home';
            }
        }

        return response()->json([
            'otp' => $otp->token,
            'message' => 'OTP has been sent to your phone number.',
            'registration_step' => $step,
        ]);
    }

    public function otp(Request $request)
    {
        $otpData = $request->validate([
            'phone_number' => ['required', 'phone:INTERNATIONAL,mobile'],
            'otp' => ['required', 'numeric'],
        ]);
        $otpInstance = new Otp;
        $isValidOtp = $otpInstance->validate($otpData['phone_number'], $otpData['otp']);

        if (! $isValidOtp->status) {
            return response()->json(['message' => 'Invalid OTP or it has expired.'], 400);
        }

        $user = User::wherePhoneNumber($otpData['phone_number'])->firstOrFail();

        if (! $user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        if (is_null($user->phone_verified_at)) {
            $user->phone_verified_at = now();
            $user->save();
        }

        if ($user->type === 'passenger') {
            $user->load(['rider' => function ($query) {
                $query->withTrashed();
            }]);
            $user->rider->last_heartbeat = now();
            $user->rider->save();
        } elseif ($user->type === 'driver') {
            $user->load(['driver' => function ($query) {
                $query->withTrashed();
            }, 'driver.documents']);
            $user->driver->last_heartbeat = now();
            $user->driver->save();
        }
 
        $tokens = $this->service->generateTokens($user);
        $user->load(['address' => function ($query) {
            $query->orderBy('created_at', 'asc')->first();
        }]);

        // Return directly instead of using sendResponseWithTokens
        return response()->json([
            'message' => 'Phone OTP validated successfully.',
            'user' => new UserResource($user->load('address')),
            'accessToken' => $tokens['accessToken'],
            'refreshToken' => $tokens['refreshToken'],
        ]);
    }

    /**
     * Refresh 'Access token'
     */
    public function refresh(Request $request)
    {

        $request->validate([
            'refresh_token' => 'required',
            'user_id' => ['required', 'numeric'],
        ]);

        $token = PersonalAccessToken::findToken($request->refresh_token);

        if (! $token || ($token->expires_at && $token->expires_at->isPast())) {
            return ApiResponse::error(null, 'Invalid or expired refresh token', 401);
        }

        if ($request->user_id != $token->tokenable_id) {
            return ApiResponse::error(null, 'Unauthorized', 401);
        }

        $user = User::where('id', $token->tokenable_id)->first();

        if (! $user) {
            return ApiResponse::error(null, 'User not found', 404);
        }

        $tokens = $this->service->generateTokens($user);

        return response()->json([
            'accessToken' => $tokens['accessToken'],
            'refreshToken' => $tokens['refreshToken'],
            'message' => 'Tokens refreshed successfully',
        ], 200);
    }

    public function sendResponseWithTokens(array $tokens, $body = []): JsonResponse
    {
        $rtExpireTime = config('sanctum.rt_expiration');
        // $cookie = cookie('refreshToken', $tokens['refreshToken'], $rtExpireTime, secure: true);

        return response()->json(array_merge($body, [
            'accessToken' => $tokens['accessToken'],
            'refreshToken' => $tokens['refreshToken'],
        ]));
        // ]))->withCookie($cookie);
    }

    /**
     * Logout user
     *
     * @response array{data: null, message: 'User logged out successfully',status: 200,  errors: null}
     */
    public function logout(Request $request)
    {
        if (Auth::check()) {
            $tokenId = $request->user()->currentAccessToken()->id;

            Auth::user()->tokens()
                ->whereIn('id', [$tokenId, ($tokenId + 1)])->delete();

            // deleting the user attempts
            $this->clearOtpData(Auth::user());

            $cookie = cookie()->forget('refreshToken');

            return ApiResponse::success(null, 'User logged out successfully.', 200, null);
        }

        return ApiResponse::error(null, 'User must be logged in to log out', 401, null);
    }

    protected function clearOtpData($user)
    {
        // Delete OTP requests and blocks for the user's phone number
        OtpRequest::where('identifier', $user->phone_number)->delete();
        OtpBlock::where('identifier', $user->phone_number)->delete();

        // If the user has an email, delete OTP requests and blocks for the email
        if ($user->email) {
            OtpRequest::where('identifier', $user->email)->delete();
            OtpBlock::where('identifier', $user->email)->delete();
        }
    }

    /**
     * Verify email
     *
     * @response array{data: null, message: 'OTP has been sent to your email.', status: 200, errors: null}
     */
    public function verify_email(Request $request, OtpRequestService $otpService)
    {
        $user = Auth::user();

        $validated = $request->validate([
            'email' => ['required', 'string', 'email', Rule::unique('users', 'email')->ignore(Auth::user())],
            'name' => ['required', 'string', 'max:30'],
        ]);

        // Check if the user is blocked for email OTP (verify_email API)
        $blockMessage = $otpService->checkAndEnforceLimits($validated['email'], 'verify_email');
        if ($blockMessage) {
            // Ensure $blockMessage is an array before accessing offsets
            if (is_array($blockMessage)) {
                $date = [
                    'hours' => $blockMessage['hours'] ?? null,
                    'minutes' => $blockMessage['minutes'] ?? null,
                    'seconds' => $blockMessage['seconds'] ?? null,
                ];

                return ApiResponse::error(
                    null,
                    $blockMessage['message'],
                    429,
                    $date
                );
            }
        }

        try {
            // Generate OTP
            $otpInstance = new Otp;
            $otp = $otpInstance->generate($validated['email'], 'numeric', 4, 5);

            // Update user's email
            $user->update(['email' => $validated['email']]);

            // Send OTP notification
            $notification = new VerifyEmailNotification($otp->token, $validated['name']);
            $user->notifyNow($notification);

            // Record the OTP request
            $otpService->recordRequest($validated['email'], 'verify_email');

            return ApiResponse::success(['otp' => $otp->token], 'OTP has been sent to your email.', 200, null);
        } catch (\Exception $e) {
            return ApiResponse::error(null, 'Something went wrong while sending the verification OTP.', 400, $e->getMessage());
        }
    }

    /**
     * Set fcm-token
     *
     * @response array{data : null, message: 'FCM token has been updated successfully',status: 200, errors: null}
     */
    public function register_fcm(Request $request)
    {
        // Validate incoming request data
        $validated = $request->validate([
            'fcm_token' => ['required', 'string'],
        ]);

        try {
            $user = Auth::user();

            $user->fcm_token = $validated['fcm_token'];
            $user->save();

            return ApiResponse::success(null, 'FCM token has been updated successfully.', 200);

        } catch (\Exception $e) {
            return ApiResponse::error(null, 'Failed to update FCM token: '.$e->getMessage(), 500);
        }
    }
}
