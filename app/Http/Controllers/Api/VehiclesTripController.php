<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\TripCollection;
use App\Http\Resources\TripResource;
use App\Models\Vehicle;
use Illuminate\Http\Request;

class VehiclesTripController extends Controller
{
    public function index(Request $request, Vehicle $vehicle): TripCollection
    {
        $search = $request->get('search', '');

        $trips = $this->getSearchQuery($search, $vehicle)
            ->latest()
            ->paginate();

        return new TripCollection($trips);
    }

    public function store(Request $request, Vehicle $vehicle): TripResource
    {
        $validated = $request->validate([
            'status' => ['required'],
            'rider_id' => ['required'],
            'driver_id' => ['required'],
            'departure_area_id' => ['required'],
            'arrival_area_id' => ['required'],
            'departure_lat' => ['required'],
            'departure_lng' => ['required'],
            'arrival_lat' => ['required'],
            'arrival_lng' => ['required'],
            'distance' => ['required'],
            'estimated_departure_time' => ['nullable', 'date'],
            'actual_departure_time' => ['nullable', 'date'],
            'estimated_arrival_time' => ['nullable', 'date'],
            'actual_arrival_time' => ['nullable', 'date'],
            'pricing_breakdown' => ['nullable'],
        ]);

        $validated['pricing_breakdown'] = json_encode(
            $validated['pricing_breakdown'],
            true
        );

        $trip = $vehicle->trips()->create($validated);

        return new TripResource($trip);
    }

    public function getSearchQuery(string $search, Vehicle $vehicle)
    {
        return $vehicle->trips()->where('created_at', 'like', "%{$search}%");
    }
}
