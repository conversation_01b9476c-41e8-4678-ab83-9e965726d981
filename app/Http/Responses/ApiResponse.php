<?php

namespace App\Http\Responses;

use Illuminate\Http\JsonResponse;

class ApiResponse extends JsonResponse
{
    public function __construct($data = null, $message = 'Success', $statusCode = 200, $error = null)
    {
        $response = [
            'status' => $statusCode >= 200 && $statusCode < 300 ? 'success' : 'error',
            'message' => $message,
            'data' => $data,
            'errors' => $error,
        ];

        parent::__construct($response, $statusCode);
    }

    public static function success($data = null, $message = 'Success', $statusCode = 200, $error = null)
    {
        return new static($data, $message, $statusCode , $error);
    }

    public static function error($data = null, $message = 'Error', $statusCode = 400, $error = null)
    {
        return new static($data, $message, $statusCode,$error);
    }

    public static function successExtraPayload($data, $extra, $message = 'Success', $statusCode = 200, $error = null)
    {
        return $response = [
            'status' => $statusCode >= 200 && $statusCode < 300 ? 'success' : 'error',
            'message' => $message,
            'data' => $data,
            'extra_data' => $extra,
            'errors' => $error,
        ];
    }
}
