<?php

namespace App\Rules;

use App\Models\User;
use Illuminate\Contracts\Validation\Rule;

class UniqueTransformedPhoneNumber implements Rule
{
    protected $ignoreId;

    public function __construct($ignoreId = null)
    {
        $this->ignoreId = $ignoreId;
    }

    public function passes($attribute, $value)
    {
        $transformed = '+218'.preg_replace('/[^0-9]/', '', $value);
        $query = User::where('phone_number', $transformed);

        if ($this->ignoreId) {
            $query->where('id', '!=', $this->ignoreId);
        }

        return ! $query->exists();
    }

    public function message()
    {
        return __('The phone number has already been taken.');
    }
}
