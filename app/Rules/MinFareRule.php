<?php

namespace App\Rules;

use App\Models\PricingRules;
use Illuminate\Contracts\Validation\Rule;

class MinFareRule implements Rule
{
    protected string $type; // 'base' or 'distance'
    protected ?string $message = null;

    public function __construct(string $type)
    {
        $this->type = $type;
    }

    public function passes($attribute, $value): bool
    {
        $pricingRule = PricingRules::first();

        if ($this->type === 'base') {
            $basePrice = $pricingRule?->global_base_price ?? 0;
        } else {
            $basePrice = $pricingRule?->global_price_per_km ?? 0;
        }

        $minValue = round(-($basePrice / 2), 2);

        if ($value < $minValue) {
            $this->message = "The {$this->type} fare must be at least {$minValue} LYD.";
            return false;
        }

        return true;
    }

    public function message(): string
    {
        return $this->message;
    }
}
