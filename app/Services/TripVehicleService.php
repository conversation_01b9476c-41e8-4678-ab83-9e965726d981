<?php

namespace App\Services;

use App\Models\TripVehicleType;

class TripVehicleService
{
    /**
     * Create a new trip vehicle type
     *
     * @param  int  $tripId  Trip ID
     * @param  array  $data  Vehicle type data
     * @return TripVehicleType The created trip vehicle type
     */
    public function createTripVehicleType(int $tripId, array $data): TripVehicleType
    {
        $vehicleTypeData = [
            'trip_id' => $tripId,
            'vehicle_category' => $data['vehicle_category'],
            'vehicle_equipments' => isset($data['vehicle_equipments']) ? implode(',', $data['vehicle_equipments']) : null,
        ];

        // Only set seat_number for passenger vehicles
        if ($data['vehicle_category'] === 'passenger') {
            $vehicleTypeData['seat_number'] = $data['number_of_seats'] ?? null;
        }

        // Only set weight_category and is_covered for freight vehicles
        if ($data['vehicle_category'] === 'freight') {
            $vehicleTypeData['weight_category'] = $data['weight_category'] ?? null;
            $vehicleTypeData['is_covered'] = $data['is_covered'] ?? null;
        }

        return TripVehicleType::create($vehicleTypeData);
    }

    /**
     * Update an existing trip vehicle type
     *
     * @param  TripVehicleType  $tripVehicleType  The trip vehicle type to update
     * @param  array  $data  Updated vehicle type data
     * @return TripVehicleType The updated trip vehicle type
     */
    public function updateTripVehicleType(TripVehicleType $tripVehicleType, array $data): TripVehicleType
    {
        $updateData = [];

        if (isset($data['vehicle_category'])) {
            $updateData['vehicle_category'] = $data['vehicle_category'];
        }

        if (isset($data['vehicle_equipments'])) {
            $updateData['vehicle_equipments'] = implode(',', $data['vehicle_equipments']);
        }

        // Update seat_number for passenger vehicles
        if (isset($data['vehicle_category']) && $data['vehicle_category'] === 'passenger') {
            if (isset($data['number_of_seats'])) {
                $updateData['seat_number'] = $data['number_of_seats'];
            }
        }

        // Update weight_category and is_covered for freight vehicles
        if (isset($data['vehicle_category']) && $data['vehicle_category'] === 'freight') {
            if (isset($data['weight_category'])) {
                $updateData['weight_category'] = $data['weight_category'];
            }
            if (isset($data['is_covered'])) {
                $updateData['is_covered'] = $data['is_covered'];
            }
        }

        // Only update if there are changes
        if (! empty($updateData)) {
            $tripVehicleType->update($updateData);
        }

        return $tripVehicleType->fresh();
    }
}
