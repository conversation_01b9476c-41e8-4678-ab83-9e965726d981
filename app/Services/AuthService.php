<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * @return array{
 *     accessToken: string,
 *     refreshToken: string,
 * }
 */
class AuthService
{
    public function generateTokens($user): array
    {
        $atExpireTime = now()->addMinutes(config('sanctum.expiration'));
        $rtExpireTime = now()->addMinutes(config('sanctum.rt_expiration'));

        // This will make the user's old tokens disappear every time they log in and a new token is created.
        // This ensures that a user can have only one active session at a time.
        $user->tokens()->delete();

        $accessToken = $user->createToken('auth-token', ['*'], $atExpireTime);
        $refreshToken = $user->createToken('refresh_token', ['*'], $rtExpireTime);

        return [
            'accessToken' => $accessToken->plainTextToken,
            'refreshToken' => $refreshToken->plainTextToken,
        ];
    }
}
