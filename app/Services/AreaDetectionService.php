<?php

namespace App\Services;

use App\Models\Area;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Throwable;

class AreaDetectionService
{
    /**
     * Cache TTL in seconds (1 hour)
     */
    private const CACHE_TTL = 3600;

    /**
     * Get area ID from coordinates
     *
     * Only returns an area ID if coordinates are exactly within a defined area polygon.
     * Does not use any fallback logic.
     *
     * @param  float  $latitude  The latitude coordinate
     * @param  float  $longitude  The longitude coordinate
     * @return int|null The area ID if found, null otherwise
     */
    public function getAreaIdFromCoordinates(float $latitude, float $longitude): ?int
    {
        // Check cache first
        $cacheKey = "area_coordinates_{$latitude}_{$longitude}";
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        // Try to find in our database using exact polygon check
        $area = $this->findAreaByCoordinates($latitude, $longitude);

        if ($area) {
            // Cache the result
            Cache::put($cacheKey, $area->id, self::CACHE_TTL);

            return $area->id;
        }

        // Cache the null result to prevent repeated lookups
        Cache::put($cacheKey, null, self::CACHE_TTL);

        return null;
    }

    /**
     * Find area by coordinates using polygon check
     *
     * @param  float  $latitude  The latitude coordinate
     * @param  float  $longitude  The longitude coordinate
     * @return Area|null The area if found, null otherwise
     */
    private function findAreaByCoordinates(float $latitude, float $longitude): ?Area
    {
        try {
            $areas = Area::select('id', 'polygon', 'name_en', 'is_active')->get();

            foreach ($areas as $area) {
                $polygon = is_string($area->polygon) ? json_decode($area->polygon, true) : $area->polygon;

                // Skip areas with invalid polygons
                if (! is_array($polygon) || count($polygon) < 3) {
                    Log::warning("Invalid polygon for area {$area->id}", [
                        'area_id' => $area->id,
                        'area_name' => $area->name_en,
                    ]);

                    continue;
                }

                if ($this->pointInPolygon($latitude, $longitude, $polygon)) {
                    return $area;
                }
            }

            return null;
        } catch (Throwable $e) {
            Log::error('Failed to find area by coordinates', [
                'latitude' => $latitude,
                'longitude' => $longitude,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Check if a point is inside a polygon using the ray-casting algorithm
     *
     * @param  float  $latitude  The latitude coordinate
     * @param  float  $longitude  The longitude coordinate
     * @param  array  $polygon  The polygon to check against
     * @return bool True if the point is inside the polygon, false otherwise
     */
    private function pointInPolygon(float $latitude, float $longitude, array $polygon): bool
    {
        $x = $longitude;
        $y = $latitude;
        $inside = false;
        $n = count($polygon);

        for ($i = 0, $j = $n - 1; $i < $n; $j = $i++) {
            // Ensure polygon points have the expected structure
            if (! isset($polygon[$i]['lng'], $polygon[$i]['lat'], $polygon[$j]['lng'], $polygon[$j]['lat'])) {
                Log::warning('Invalid polygon point structure', [
                    'point_i' => $polygon[$i] ?? null,
                    'point_j' => $polygon[$j] ?? null,
                ]);

                return false;
            }

            $xi = $polygon[$i]['lng'];
            $yi = $polygon[$i]['lat'];
            $xj = $polygon[$j]['lng'];
            $yj = $polygon[$j]['lat'];

            // Avoid division by zero
            if (abs($yj - $yi) < 0.000001) {
                continue;
            }

            $intersect = (($yi > $y) != ($yj > $y))
                && ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi);

            if ($intersect) {
                $inside = ! $inside;
            }
        }

        return $inside;
    }
}
