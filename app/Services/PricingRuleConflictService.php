<?php

namespace App\Services;

use App\Enums\Payments\PaymentTypeEnum;
use App\Models\Area;
use App\Models\PricingRuleGender;
use App\Models\PricingRuleSeatNumber;
use App\Models\VehicleType;

class PricingRuleConflictService
{
    /**
     * Check for conflicts between global pricing rules and component-specific pricing adjustments
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    public function checkForConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $conflicts = [];
        $conflictChecks = [
            'areas' => $this->checkAreaConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'vehicle_types' => $this->checkVehicleTypeConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'seat_numbers' => $this->checkSeatNumberConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'gender_rules' => $this->checkGenderConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'equipment' => $this->checkEquipmentConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
            'day_time_configs' => $this->checkDayTimeConflicts($newGlobalBasePrice, $newGlobalPricePerKm),
        ];

        foreach ($conflictChecks as $key => $checkResults) {

            if (! empty($checkResults)) {
                $conflicts[$key] = $checkResults;
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in area pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkAreaConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $areas = Area::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($areas as $area) {
            $areaConflict = $this->checkComponentConflicts(
                $area,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($areaConflict)) {
                $conflicts[] = [
                    'id' => $area->id,
                    'name' => $area->name_en,
                    'conflicts' => $areaConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in vehicle type pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkVehicleTypeConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $vehicleTypes = VehicleType::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($vehicleTypes as $vehicleType) {
            $vehicleTypeConflict = $this->checkComponentConflicts(
                $vehicleType,
                $minBaseValue,
                $minDistanceValue,
                'additional_base_fare',
                'additional_price_per_km',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($vehicleTypeConflict)) {
                $conflicts[] = [
                    'id' => $vehicleType->id,
                    'name' => $vehicleType->name_en,
                    'conflicts' => $vehicleTypeConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in seat number pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkSeatNumberConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $seatNumbers = PricingRuleSeatNumber::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($seatNumbers as $seatNumber) {
            $seatNumberConflict = $this->checkComponentConflicts(
                $seatNumber,
                $minBaseValue,
                $minDistanceValue,
                'base_fare',
                'distance_fare',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($seatNumberConflict)) {
                $conflicts[] = [
                    'id' => $seatNumber->id,
                    'seats_number' => $seatNumber->seats_number,
                    'conflicts' => $seatNumberConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in gender pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkGenderConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $genderRules = PricingRuleGender::where(function ($query) {
            $query->where('base_fare_adjustment_type', PaymentTypeEnum::fixed)
                ->orWhere('distance_fare_adjustment_type', PaymentTypeEnum::fixed);
        })->get();

        $conflicts = [];

        foreach ($genderRules as $genderRule) {
            $genderConflict = $this->checkComponentConflicts(
                $genderRule,
                $minBaseValue,
                $minDistanceValue,
                'base_fare_fixed',
                'distance_fare_fixed',
                'base_fare_adjustment_type',
                'distance_fare_adjustment_type'
            );

            if (! empty($genderConflict)) {
                $conflicts[] = [
                    'id' => $genderRule->id,
                    'gender' => $genderRule->gender->value,
                    'conflicts' => $genderConflict,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in equipment pricing
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkEquipmentConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);

        $equipment = \App\Models\VehicleEquipment::where('additional_fare', '<', $minBaseValue)
            ->where('status', true)
            ->get();

        $conflicts = [];

        foreach ($equipment as $item) {
            if ((float) $item->additional_fare < $minBaseValue) {
                $conflicts[] = [
                    'id' => $item->id,
                    'name' => $item->name_en,
                    'conflicts' => [
                        'base_fare' => [
                            'current_value' => $item->additional_fare,
                            'min_allowed' => $minBaseValue,
                        ],
                    ],
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check for conflicts in day-time configurations
     *
     * @param  float  $newGlobalBasePrice  The new global base price
     * @param  float  $newGlobalPricePerKm  The new global price per km
     * @return array Array of conflicts found
     */
    private function checkDayTimeConflicts(float $newGlobalBasePrice, float $newGlobalPricePerKm): array
    {
        $minBaseValue = $this->calculateMinValue($newGlobalBasePrice);
        $minDistanceValue = $this->calculateMinValue($newGlobalPricePerKm);

        $conflicts = [];

        // Check day charges
        $dayCharges = \App\Models\PricingRuleAdditionalDayCharge::where(function ($query) use ($minBaseValue, $minDistanceValue) {
            $query->where(function ($q) use ($minBaseValue) {
                $q->where('day_charge_type', 'fixed')
                    ->where('day_fixed_charge', '<', $minBaseValue);
            })->orWhere(function ($q) use ($minBaseValue) {
                $q->where('night_charge_type', 'fixed')
                    ->where('night_fixed_charge', '<', $minBaseValue);
            })->orWhere(function ($q) use ($minDistanceValue) {
                $q->where('day_distance_charge_type', 'fixed')
                    ->where('day_distance_fixed_charge', '<', $minDistanceValue);
            })->orWhere(function ($q) use ($minDistanceValue) {
                $q->where('night_distance_charge_type', 'fixed')
                    ->where('night_distance_fixed_charge', '<', $minDistanceValue);
            });
        })->get();

        foreach ($dayCharges as $dayCharge) {
            $dayConflicts = [];

            if ($dayCharge->day_charge_type === 'fixed' && (float) $dayCharge->day_fixed_charge < $minBaseValue) {
                $dayConflicts['day_base_fare'] = [
                    'current_value' => $dayCharge->day_fixed_charge,
                    'min_allowed' => $minBaseValue,
                ];
            }

            if ($dayCharge->night_charge_type === 'fixed' && (float) $dayCharge->night_fixed_charge < $minBaseValue) {
                $dayConflicts['night_base_fare'] = [
                    'current_value' => $dayCharge->night_fixed_charge,
                    'min_allowed' => $minBaseValue,
                ];
            }

            if ($dayCharge->day_distance_charge_type === 'fixed' && (float) $dayCharge->day_distance_fixed_charge < $minDistanceValue) {
                $dayConflicts['day_distance_fare'] = [
                    'current_value' => $dayCharge->day_distance_fixed_charge,
                    'min_allowed' => $minDistanceValue,
                ];
            }

            if ($dayCharge->night_distance_charge_type === 'fixed' && (float) $dayCharge->night_distance_fixed_charge < $minDistanceValue) {
                $dayConflicts['night_distance_fare'] = [
                    'current_value' => $dayCharge->night_distance_fixed_charge,
                    'min_allowed' => $minDistanceValue,
                ];
            }

            if (! empty($dayConflicts)) {
                $conflicts[] = [
                    'id' => $dayCharge->id,
                    'name' => ucfirst($dayCharge->day).' Time Config',
                    'conflicts' => $dayConflicts,
                ];
            }
        }

        // Check peak hours
        $peakHours = \App\Models\PricingRulePeakHour::where(function ($query) use ($minBaseValue, $minDistanceValue) {
            $query->where(function ($q) use ($minBaseValue) {
                $q->where('base_fare_adjustment_type', 'fixed')
                    ->where('base_fare_fixed', '<', $minBaseValue);
            })->orWhere(function ($q) use ($minDistanceValue) {
                $q->where('distance_fare_adjustment_type', 'fixed')
                    ->where('distance_fare_fixed', '<', $minDistanceValue);
            });
        })->get();

        foreach ($peakHours as $peakHour) {
            $peakConflicts = [];

            if ($peakHour->base_fare_adjustment_type === 'fixed' && (float) $peakHour->base_fare_fixed < $minBaseValue) {
                $peakConflicts['base_fare'] = [
                    'current_value' => $peakHour->base_fare_fixed,
                    'min_allowed' => $minBaseValue,
                ];
            }

            if ($peakHour->distance_fare_adjustment_type === 'fixed' && (float) $peakHour->distance_fare_fixed < $minDistanceValue) {
                $peakConflicts['distance_fare'] = [
                    'current_value' => $peakHour->distance_fare_fixed,
                    'min_allowed' => $minDistanceValue,
                ];
            }

            if (! empty($peakConflicts)) {
                $conflicts[] = [
                    'id' => $peakHour->id,
                    'name' => 'Peak Hour ('.$peakHour->peak_start_at.'-'.$peakHour->peak_end_at.')',
                    'conflicts' => $peakConflicts,
                ];
            }
        }

        return $conflicts;
    }

    /**
     * Check component for base and distance fare conflicts
     *
     * @param  mixed  $component  The component to check
     * @param  float  $minBaseValue  Minimum allowed base value
     * @param  float  $minDistanceValue  Minimum allowed distance value
     * @param  string  $baseField  Field name for base fare
     * @param  string  $distanceField  Field name for distance fare
     * @param  string  $baseTypeField  Field name for base fare adjustment type
     * @param  string  $distanceTypeField  Field name for distance fare adjustment type
     * @return array Component conflicts
     */
    private function checkComponentConflicts(
        $component,
        float $minBaseValue,
        float $minDistanceValue,
        string $baseField,
        string $distanceField,
        string $baseTypeField,
        string $distanceTypeField
    ): array {
        $conflicts = [];

        if (
            $component->{$baseTypeField}?->value === 'fixed' &&
            (float) $component->{$baseField} < $minBaseValue
        ) {
            $conflicts['base_fare'] = [
                'current_value' => $component->{$baseField},
                'min_allowed' => $minBaseValue,
            ];
        }

        if (
            $component->{$distanceTypeField}?->value === 'fixed' &&
            (float) $component->{$distanceField} < $minDistanceValue
        ) {
            $conflicts['distance_fare'] = [
                'current_value' => $component->{$distanceField},
                'min_allowed' => $minDistanceValue,
            ];
        }

        return $conflicts;
    }

    /**
     * Calculate the minimum value based on the global price
     *
     * @param  float  $globalPrice  The global price
     * @return float The minimum value (-50% of global price)
     */
    private function calculateMinValue(float $globalPrice): float
    {
        $minValue = round(-($globalPrice / 2), 2);

        return $minValue;
    }
}
