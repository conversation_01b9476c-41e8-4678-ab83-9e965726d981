<?php

namespace App\Services;

use App\Models\Auth\OtpBlock;
use App\Models\Auth\OtpRequest;

class OtpRequestService
{
    private const SHORT_BLOCK_DURATION = 30; // 30 minutes

    private const LONG_BLOCK_DURATION = 1440; // 24 hours

    private const BLOCK_THRESHOLD = 3; // Number of attempts to trigger a block

    /**
     * Check and enforce OTP request limits.
     */
    public function checkAndEnforceLimits(string $identifier, string $type)
    {

        // Check if the user is currently blocked

        $block = OtpBlock::where('identifier', $identifier)

            ->where('blocked_until', '>', now())

            ->first();

        if ($block) {

            // User is already blocked—don’t update anything, just return the message

            return $this->getBlockMessage($block);

        }

        // Get the most recent block (even if expired) to determine the request counting period and block type

        $latestBlock = OtpBlock::where('identifier', $identifier)

            ->orderBy('blocked_until', 'desc')

            ->first();

        // Count requests made after the last block expired (or in the last 24 hours if no block exists)

        $requestStartTime = $latestBlock ? $latestBlock->blocked_until : now()->subHours(24);

        $recentRequests = OtpRequest::where('identifier', $identifier)

            ->where('type', $type)

            ->where('created_at', '>', $requestStartTime)

            ->count();

        // No previous block: Block for 1 minute after 3 attempts

        if (! $latestBlock && $recentRequests >= self::BLOCK_THRESHOLD) {

            $this->createOrUpdateBlock($identifier, '30_min_block', self::SHORT_BLOCK_DURATION);

            // return 'Too many attempts. Please try again after 30 minutes.';
            return [
                'message' => 'Too many attempts. Please try again after 30 minutes.',
                'hours' => 0,
                'minutes' => 30,
                'seconds' => 0,
            ];

        }

        // Previous block exists: Alternate block duration based on the last block type

        if ($latestBlock && $recentRequests >= self::BLOCK_THRESHOLD) {

            if ($latestBlock->type === '30_min_block') {

                // Last block was 1 minute, so block for 3 minutes

                $this->createOrUpdateBlock($identifier, '1440_min_block', self::LONG_BLOCK_DURATION);

                return [
                    'message' => 'Too many attempts. Please try again after 24 hours.',
                    'hours' => 24,
                    'minutes' => 0,
                    'seconds' => 0,
                ];

            } else {

                // Last block was 3 minutes, so block for 1 minute

                $this->createOrUpdateBlock($identifier, '30_min_block', self::SHORT_BLOCK_DURATION);

                return [
                    'message' => 'Too many attempts. Please try again after 30 minutes.',
                    'hours' => 0,
                    'minutes' => 30,
                    'seconds' => 0,
                ];
            }

        }

        return null; // No block applied

    }

    /**
     * Create or update a block record with a new blocked_until timestamp.
     */
    private function createOrUpdateBlock(string $identifier, string $blockType, int $minutes): void
    {

        OtpBlock::updateOrCreate(

            ['identifier' => $identifier, 'type' => $blockType], // Ensure 'type' matches DB schema

            ['blocked_until' => now()->addMinutes($minutes)]

        );

    }

    /**
     * Generate a message for a blocked user.
     */
    private function getBlockMessage(OtpBlock $block)
    {
        $remainingSeconds = now()->diffInSeconds($block->blocked_until);

        // Convert seconds to minutes and seconds
        $minutes = floor($remainingSeconds / 60);
        $seconds = $remainingSeconds % 60;

        if ($minutes > 0) {
            if ($seconds > 0) {
                return [
                    'message' => "You are blocked until {$block->blocked_until}. Please wait {$minutes} minutes and {$seconds} seconds.",
                    'hours' => $minutes && $minutes / 60 >= 1 ? floor($minutes / 60) : 0,
                    'minutes' => $minutes ?: null,
                    'seconds' => $seconds,
                ];
            } else {
                return [
                    'message' => "You are blocked until {$block->blocked_until}. Please wait {$minutes} minutes.",
                    'hours' => $minutes ? $minutes / 60 : null,
                    'minutes' => $minutes ?: null,
                    'seconds' => null,
                ];
            }
        } else {
            return [
                'message' => "You are blocked until {$block->blocked_until}. Please wait {$seconds} seconds.",
                'hours' => null,
                'minutes' => null,
                'seconds' => $seconds,
            ];
        }
    }

    /**
     * Record a new OTP request.
     */
    public function recordRequest(string $identifier, string $type): void
    {

        OtpRequest::create([

            'identifier' => $identifier,

            'type' => $type,

        ]);

    }
}
