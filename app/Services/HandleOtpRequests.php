<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class HandleOtpRequests
{
    public function handle(string $identifier, string $type = 'otp'): ?JsonResponse
    {
        // Vérifier si l'utilisateur est bloqué

        $firstBlockDuration = (int) env('OTP_BLOCK_DURATION_FIRST', 30);
        $secondBlockDuration = (int) env('OTP_BLOCK_DURATION_SECOND', 1440);

        $lockedUntil = Cache::get("locked_until:$identifier");

        if ($lockedUntil && Carbon::parse($lockedUntil)->isFuture()) {
            return response()->json([
                'message' => 'Your account is locked. Please try again after '.Carbon::parse($lockedUntil)->diffForHumans(),
                'locked_until' => $lockedUntil,
            ], 403);
        }

        // Clé pour les tentatives et le comptage des blocages
        $attemptKey = "{$type}_attempts:$identifier";
        $blockCountKey = "block_count:$identifier";

        $blockCount = Cache::get($blockCountKey, 0);

        if (RateLimiter::tooManyAttempts($attemptKey, 3)) {
            if ($blockCount == 0) {
                $lockDuration = $firstBlockDuration;
                Cache::increment($blockCountKey);
            } else {
                $lockDuration = $secondBlockDuration;
                Cache::put($blockCountKey, 0); // Remise à zéro pour recommencer le cycle
            }

            // Appliquer le blocage
            Cache::put("locked_until:$identifier", now()->addMinutes($lockDuration), now()->addMinutes($lockDuration));

            // Réinitialiser les tentatives
            RateLimiter::clear($attemptKey);

            return response()->json([
                'message' => $lockDuration === 3
                    ? 'You have been blocked for 24 hours due to multiple violations.'
                    : 'You have exceeded the maximum attempts. Please try again after 30 minute.',
                'locked_until' => Cache::get("locked_until:$identifier"),
            ], 429);
        }

        return null; // Pas de blocage, continuer le processus
    }
}
