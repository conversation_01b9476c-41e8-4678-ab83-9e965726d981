<?php

namespace App\Services;

use App\Models\TripLocation;

class TripLocationService
{
    /**
     * Create a new trip location
     *
     * @param  int  $tripId  Trip ID
     * @param  array  $data  Location data
     * @return TripLocation The created trip location
     */
    public function createTripLocation(int $tripId, array $data): TripLocation
    {
        $locationData = [
            'trip_id' => $tripId,
            ...$this->extractLocationData($data),
            ...$this->extractOptionalFields($data, ['address', 'polyline']),
        ];

        return TripLocation::create($locationData);
    }

    /**
     * Update an existing trip location
     *
     * @param  TripLocation  $tripLocation  The trip location to update
     * @param  array  $data  Updated location data
     * @return TripLocation The updated trip location
     */
    public function updateTripLocation(TripLocation $tripLocation, array $data): TripLocation
    {
        $updateData = $this->buildUpdateData($data);

        if (!empty($updateData)) {
            $tripLocation->update($updateData);
            return $tripLocation->fresh();
        }

        return $tripLocation;
    }

    /**
     * Get coordinates from trip location
     *
     * @param  TripLocation  $tripLocation  The trip location
     * @return array Origin and destination coordinates
     */
    public function getCoordinates(TripLocation $tripLocation): array
    {
        return [
            'origin' => [
                'lat' => $tripLocation->departure_lat,
                'lng' => $tripLocation->departure_lng,
            ],
            'destination' => [
                'lat' => $tripLocation->arrival_lat,
                'lng' => $tripLocation->arrival_lng,
            ],
        ];
    }

    /**
     * Extract location data from input array
     *
     * @param  array  $data
     * @return array
     */
    private function extractLocationData(array $data): array
    {
        return [
            'departure_address' => $data['departure_location']['address'] ?? 'Unknown address',
            'arrival_address' => $data['arrival_location']['address'] ?? 'Unknown address',
            'departure_lat' => $data['departure_location']['latitude'] ?? null,
            'departure_lng' => $data['departure_location']['longitude'] ?? null,
            'arrival_lat' => $data['arrival_location']['latitude'] ?? null,
            'arrival_lng' => $data['arrival_location']['longitude'] ?? null,
        ];
    }

    /**
     * Extract optional fields from data array
     *
     * @param  array  $data
     * @param  array  $fields
     * @return array
     */
    private function extractOptionalFields(array $data, array $fields): array
    {
        return array_filter(
            array_intersect_key($data, array_flip($fields)),
            fn($value) => $value !== null
        );
    }

    /**
     * Build update data array for trip location
     *
     * @param  array  $data
     * @return array
     */
    private function buildUpdateData(array $data): array
    {
        $updateData = [];

        // Handle direct address updates
        $this->addDirectAddressUpdates($updateData, $data);
        
        // Handle location-based updates (coordinates + addresses)
        $this->addLocationBasedUpdates($updateData, $data);

        return $updateData;
    }

    /**
     * Add direct address updates to update data
     *
     * @param  array  &$updateData
     * @param  array  $data
     * @return void
     */
    private function addDirectAddressUpdates(array &$updateData, array $data): void
    {
        if (isset($data['departure_address'])) {
            $updateData['departure_address'] = $data['departure_address'];
        }

        if (isset($data['arrival_address'])) {
            $updateData['arrival_address'] = $data['arrival_address'];
        }
    }

    /**
     * Add location-based updates (coordinates and addresses) to update data
     *
     * @param  array  &$updateData
     * @param  array  $data
     * @return void
     */
    private function addLocationBasedUpdates(array &$updateData, array $data): void
    {
        $locations = [
            'departure' => $data['departure_location'] ?? [],
            'arrival' => $data['arrival_location'] ?? [],
        ];

        foreach ($locations as $type => $location) {
            if (empty($location)) {
                continue;
            }

            $latKey = "{$type}_lat";
            $lngKey = "{$type}_lng";
            $addressKey = "{$type}_address";

            // Update coordinates if provided
            if (isset($location['latitude'])) {
                $updateData[$latKey] = $location['latitude'];
            }

            if (isset($location['longitude'])) {
                $updateData[$lngKey] = $location['longitude'];
            }

            // Update address if coordinates are being updated and address isn't already set
            if ((isset($location['latitude']) || isset($location['longitude'])) 
                && !isset($updateData[$addressKey]) 
                && isset($location['address'])) {
                $updateData[$addressKey] = $location['address'];
            }

            // Handle standalone address updates from location object
            if (!isset($updateData[$addressKey]) && isset($location['address'])) {
                $updateData[$addressKey] = $location['address'];
            }
        }
    }
}