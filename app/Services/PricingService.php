<?php

namespace App\Services;

use App\Models\VehicleEquipment;
use App\Models\VehicleType;
use App\Services\Pricing\Factors\AreaPricingFactor;
use App\Services\Pricing\Factors\EquipmentPricingFactor;
use App\Services\Pricing\Factors\GenderPricingFactor;
use App\Services\Pricing\Factors\PeakHourPricingFactor;
use App\Services\Pricing\Factors\SeatCapacityPricingFactor;
use App\Services\Pricing\Factors\TimeOverchargeFactor;
use App\Services\Pricing\Factors\TimePricingFactor;
use App\Services\Pricing\Factors\VehicleTypePricingFactor;
use App\Services\Pricing\GlobalPricingRules;
use App\Services\Pricing\PricingCalculator;
use Carbon\Carbon;

class PricingService
{
    /**
     * Calculate price for multiple vehicle types
     *
     * @param  array  $googleData  Distance and duration data from Google Maps
     * @param  array  $vehicleTypeIds  Array of vehicle type IDs
     * @param  string|null  $gender  User's gender
     * @param  array  $equipmentIds  Array of equipment IDs
     * @param  Carbon|null  $startTime  Trip start time
     * @param  int|null  $areaId  Departure area ID
     * @param  Carbon|null  $estimatedArrivalTime  Estimated arrival time
     * @param  Carbon|null  $actualArrivalTime  Actual arrival time
     * @return array Array of pricing breakdown for each vehicle type
     */
    public static function calculatePrices(
        array $googleData,
        array $vehicleTypeIds,
        ?string $gender,
        array $equipmentIds = [],
        ?Carbon $startTime = null,
        ?int $areaId = null,
        ?Carbon $estimatedArrivalTime = null,
        ?Carbon $actualArrivalTime = null
    ): array {
        $result = [];

        if (empty($vehicleTypeIds)) {
            return $result;
        }

        $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : 0;
        $startTime ??= now();

        // Debug array to store gender-specific pricing for each vehicle type
        $genderPricingDebug = [];

        foreach ($vehicleTypeIds as $vehicleTypeId) {
            $vehicleType = VehicleType::find($vehicleTypeId);
            // Calculate base price without gender adjustment
            $basePrice = self::calculatePrice(
                $distance,
                $areaId,
                $vehicleTypeId,
                null, // No gender adjustment
                $startTime,
                $equipmentIds,
                $estimatedArrivalTime,
                $actualArrivalTime
            );

            // Calculate price with gender adjustment
            $genderPrice = self::calculatePrice(
                $distance,
                $areaId,
                $vehicleTypeId,
                $gender,
                $startTime,
                $equipmentIds,
                $estimatedArrivalTime,
                $actualArrivalTime
            );

            $genderPricingDebug[$vehicleTypeId] = [
                'vehicle_name' => $vehicleType->name ?? "Vehicle Type {$vehicleTypeId}",
                'gender' => $gender,
                'base_price' => [
                    'base_fare' => $basePrice['base_fare'] ?? 0,
                    'distance_fare' => $basePrice['distance_fare'] ?? 0,
                    'total' => $basePrice['total'] ?? 0,
                ],
                'gender_adjusted_price' => [
                    'base_fare' => $genderPrice['base_fare'] ?? 0,
                    'distance_fare' => $genderPrice['distance_fare'] ?? 0,
                    'total' => $genderPrice['total'] ?? 0,
                ],
                'gender_adjustment' => $genderPrice['gender_adjustment'] ?? null,
                'difference' => [
                    'base_fare' => ($genderPrice['base_fare'] ?? 0) - ($basePrice['base_fare'] ?? 0),
                    'distance_fare' => ($genderPrice['distance_fare'] ?? 0) - ($basePrice['distance_fare'] ?? 0),
                    'total' => ($genderPrice['total'] ?? 0) - ($basePrice['total'] ?? 0),
                ],
            ];
        }

        // Store the pricing data in the result array
        foreach ($vehicleTypeIds as $vehicleTypeId) {
            // Use the gender price data from the debug array
            $result[$vehicleTypeId] = $genderPricingDebug[$vehicleTypeId]['gender_adjusted_price'] ?? [];

            // Ensure distance is always included
            if (isset($result[$vehicleTypeId]) && ! isset($result[$vehicleTypeId]['distance'])) {
                $result[$vehicleTypeId]['distance'] = $distance;
            }
        }

        return $result;
    }

    /**
     * Calculate price for a specific trip configuration
     *
     * @param  float  $distance  Distance in kilometers
     * @param  int|null  $areaId  Departure area ID
     * @param  int|null  $vehicleTypeId  Vehicle type ID
     * @param  string|null  $gender  User's gender
     * @param  Carbon  $startTime  Trip start time
     * @param  array  $equipmentIds  Array of equipment IDs
     * @param  Carbon|null  $estimatedArrivalTime  Estimated arrival time
     * @param  Carbon|null  $actualArrivalTime  Actual arrival time
     * @return array Pricing breakdown
     */
    public static function calculatePrice(
        float $distance,
        ?int $areaId = null,
        ?int $vehicleTypeId = null,
        ?string $gender = null,
        ?Carbon $startTime = null,
        array $equipmentIds = [],
        ?Carbon $estimatedArrivalTime = null,
        ?Carbon $actualArrivalTime = null
    ): array {
        // Use current time if not provided
        $startTime ??= now();

        // Initialize pricing calculator with global rules
        $calculator = new PricingCalculator(new GlobalPricingRules);

        // Add vehicle details to result if provided
        $vehicleType = null;
        if ($vehicleTypeId) {
            $vehicleType = VehicleType::where('id', $vehicleTypeId)->first();
            if ($vehicleType) {
                $calculator->addVehicleTypeDetails($vehicleType);

                // Get the seat capacity from the vehicle type or use the default
                $seatCapacity = null;

                // Try to get the seat capacity from the trip vehicle type
                if ($vehicleType) {
                    $tripVehicleType = \App\Models\TripVehicleType::where('trip_id', request()->route('trip'))->first();
                    if ($tripVehicleType && $tripVehicleType->seat_number) {
                        $seatCapacity = $tripVehicleType->seat_number;
                    }
                }

                // If we couldn't get it from the trip, check the request
                if (! $seatCapacity && request()->has('number_of_seats')) {
                    $seatCapacity = request()->input('number_of_seats');
                }

                // If we still don't have it, use the default
                $seatCapacity = $seatCapacity ?: 4;

                $calculator->addFactor(new SeatCapacityPricingFactor($seatCapacity));

            }
        }

        // Add pricing factors
        // Always add area information to the result, even if not used for pricing
        if ($areaId !== null) {
            $area = \App\Models\Area::find($areaId);
            if ($area) {
                $calculator->result->setAreaInfo([
                    'id' => $area->id,
                    'name' => $area->name_en,
                    'is_active' => $area->is_active,
                    'has_pricing_config' => ($area->base_fare_adjustment_type !== null &&
                                             $area->distance_fare_adjustment_type !== null),
                ]);

                // Only add area pricing factor if area is active and has valid pricing config
                if ($area->is_active &&
                    $area->base_fare_adjustment_type !== null &&
                    $area->distance_fare_adjustment_type !== null) {
                    $calculator->addFactor(new AreaPricingFactor($areaId));
                }
            }
        }

        $calculator->addFactor(new VehicleTypePricingFactor($vehicleTypeId));
        $calculator->addFactor(new GenderPricingFactor($gender));
        $calculator->addFactor(new TimePricingFactor($startTime));
        $calculator->addFactor(new PeakHourPricingFactor($startTime));

        // checks for active equipment when calculating prices
        if (! empty($equipmentIds)) {
            $equipments = VehicleEquipment::whereIn('id', $equipmentIds)
                ->where('status', true)
                ->get();
            $calculator->addFactor(new EquipmentPricingFactor($equipments));
        }

        // Add time overcharge if applicable
        if ($estimatedArrivalTime && $actualArrivalTime) {
            $calculator->addFactor(new TimeOverchargeFactor(
                $startTime,
                $estimatedArrivalTime,
                $actualArrivalTime
            ));
        }

        // Calculate the final price
        $result = $calculator->calculate($distance);

        // Apply rounding rules
        $result->applyRounding();

        // Use simplified format for frontend, or full format for backend/debugging
        return $result->toArray();
    }
}
