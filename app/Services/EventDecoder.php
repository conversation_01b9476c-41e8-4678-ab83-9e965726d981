<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * @return array{
 *     accessToken: string,
 *     refreshToken: string,
 * }
 */
class EventDecoder
{
    public static function Decoder(\Laravel\Reverb\Events\MessageReceived $event, string|array $eventName): bool|array
    {

        try {
            $message = json_decode($event->message);

            if (! isset($message->event)) {
                Log::warning('Received message with no event name');

                return ['no_event'];
            }

            $eventMatched = is_array($eventName)
                ? in_array($message->event, $eventName)
                : $message->event === $eventName;

            if (isset($message->event) && $eventMatched) {
                $data = is_string($message->data) ? json_decode($message->data) : $message->data;
            } else {
                $data = ['skiping_event'];
            }
            if (! empty($data)) {
                $EventData = [];
                foreach ($data as $key => $value) {
                    $EventData[$key] = $value;
                }

                return $EventData;
            }

            return ['no_data'];
        } catch (\JsonException $e) {
            Log::error('Failed to decode WebSocket message', [
                'error' => $e->getMessage(),
                'message' => $event->message,
            ]);

            throw new \InvalidArgumentException('Invalid message format');
        }

    }
}
