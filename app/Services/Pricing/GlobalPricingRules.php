<?php

namespace App\Services\Pricing;

use App\Models\PricingRules;
use Illuminate\Support\Facades\Log;

class GlobalPricingRules
{
    /**
     * @var float
     */
    private $baseFare;

    /**
     * @var float
     */
    private $distancePerKm;

    /**
     * @var float
     */
    private $timeThresholdPercentage;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->loadGlobalRules();
    }

    /**
     * Load global pricing rules from the database
     */
    private function loadGlobalRules(): void
    {
        // Fetch global pricing rules
        $globalRules = PricingRules::first();

        // Use default values if global rules not found
        if (! $globalRules) {
            Log::warning('Global pricing rules not found, using default values');
            $this->baseFare = 5.00; // Default base fare
            $this->distancePerKm = 5.00; // Default per km rate
            $this->timeThresholdPercentage = 20.00; // Default time threshold percentage

            // Create global pricing rules with default values
            try {
                $globalRules = PricingRules::create([
                    'global_base_price' => $this->baseFare,
                    'global_price_per_km' => $this->distancePerKm,
                    'time_threshold_percentage' => $this->timeThresholdPercentage,
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to create default global pricing rules', [
                    'error' => $e->getMessage(),
                ]);
            }
        } else {

            $this->baseFare = $globalRules->global_base_price;
            $this->distancePerKm = $globalRules->global_price_per_km;
            $this->timeThresholdPercentage = $globalRules->time_threshold_percentage ?? 20.00;
        }
    }

    /**
     * Get the base fare
     */
    public function getBaseFare(): float
    {
        return (float) $this->baseFare;
    }

    /**
     * Get the distance per km rate
     */
    public function getDistancePerKm(): float
    {
        return (float) $this->distancePerKm;
    }

    /**
     * Get the time threshold percentage
     */
    public function getTimeThresholdPercentage(): float
    {
        return (float) $this->timeThresholdPercentage;
    }
}
