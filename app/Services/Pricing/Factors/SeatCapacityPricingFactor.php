<?php

namespace App\Services\Pricing\Factors;

use App\Models\PricingRuleSeatNumber;
use App\Services\Pricing\GlobalPricingRules;

class SeatCapacityPricingFactor extends AbstractPricingFactor
{
    /**
     * @var int|null
     */
    private $seatCapacity;

    /**
     * @var PricingRuleSeatNumber|null
     */
    private $seatCapacityRule;

    /**
     * Constructor
     */
    public function __construct(?int $seatCapacity)
    {
        $this->seatCapacity = $seatCapacity;

        // Only support exact seat capacities: 2, 4, or 6
        if (in_array($this->seatCapacity, [2, 4, 6], true)) {
            $this->seatCapacityRule = PricingRuleSeatNumber::where('seats_number', $this->seatCapacity)->first();
        }
    }

    /**
     * Get the factor name
     */
    public function getName(): string
    {
        return 'seat_capacity_adjustment';
    }

    /**
     * Check if this factor is applicable
     */
    public function isApplicable(): bool
    {
        return $this->seatCapacityRule !== null;
    }

    /**
     * Calculate the seat capacity price adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->seatCapacityRule) {
            return null;
        }

        $baseFareAdjustmentType = is_object($this->seatCapacityRule->base_fare_adjustment_type)
            ? $this->seatCapacityRule->base_fare_adjustment_type->value
            : ($this->seatCapacityRule->base_fare_adjustment_type ?? 'fixed');

        $distanceFareAdjustmentType = is_object($this->seatCapacityRule->distance_fare_adjustment_type)
            ? $this->seatCapacityRule->distance_fare_adjustment_type->value
            : ($this->seatCapacityRule->distance_fare_adjustment_type ?? 'fixed');

        $baseFareValue = ($baseFareAdjustmentType === 'fixed')
            ? $this->seatCapacityRule->base_fare
            : $this->seatCapacityRule->base_fare_adjustment;

        $distanceFareValue = ($distanceFareAdjustmentType === 'fixed')
            ? $this->seatCapacityRule->distance_fare
            : $this->seatCapacityRule->distance_fare_adjustment;

        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            (float) $baseFareValue
        );

        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            (float) $distanceFareValue
        );

        $totalAdjustment = $baseFareAdjustment + ($distanceFareAdjustment * $distance);

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
            'seats_number' => $this->seatCapacityRule->seats_number,
        ];
    }
}
