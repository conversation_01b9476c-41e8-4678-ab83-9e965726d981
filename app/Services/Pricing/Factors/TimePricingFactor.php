<?php

namespace App\Services\Pricing\Factors;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Services\Pricing\GlobalPricingRules;
use Carbon\Carbon;

class TimePricingFactor extends AbstractPricingFactor
{
    private Carbon $startTime;

    private ?PricingRuleAdditionalDayCharge $timeRule = null;

    private string $dayOfWeek;

    private string $timeOfDay;

    public function __construct(?Carbon $startTime = null)
    {
        $this->startTime = $startTime ?? now();
        $this->dayOfWeek = $this->startTime->format('l');
        $this->determineTimeOfDay();
        $this->loadTimeRule();
    }

    private function determineTimeOfDay(): void
    {
        $dayTimeSettings = PricingRuleAdditionalDayCharge::where('day', $this->dayOfWeek)->first();

        if (! $dayTimeSettings) {
            $this->timeOfDay = 'day';

            return;
        }

        $currentTime = $this->startTime->format('H:i');
        $dayStartAt = $dayTimeSettings->day_start_at;
        $dayEndAt = $dayTimeSettings->day_end_at;

        if ($dayStartAt <= $dayEndAt) {
            $this->timeOfDay = ($currentTime >= $dayStartAt && $currentTime < $dayEndAt) ? 'day' : 'night';
        } else {
            $this->timeOfDay = ($currentTime >= $dayStartAt || $currentTime < $dayEndAt) ? 'day' : 'night';
        }
    }

    private function loadTimeRule(): void
    {
        $this->timeRule = PricingRuleAdditionalDayCharge::where('day', $this->dayOfWeek)->first();
    }

    public function getName(): string
    {
        return 'time_adjustment';
    }

    public function isApplicable(): bool
    {
        return $this->timeRule !== null;
    }

    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->timeRule) {
            return null;
        }

        $baseFareAdjustmentType = $this->timeOfDay === 'day'
            ? $this->timeRule->day_charge_type
            : $this->timeRule->night_charge_type;

        $baseFareAdjustmentValue = $baseFareAdjustmentType === 'fixed'
            ? ($this->timeOfDay === 'day'
                ? $this->timeRule->day_fixed_charge
                : $this->timeRule->night_fixed_charge)
            : ($this->timeOfDay === 'day'
                ? $this->timeRule->day_percentage_charge
                : $this->timeRule->night_percentage_charge);

        $distanceFareAdjustmentType = $this->timeOfDay === 'day'
            ? $this->timeRule->day_distance_charge_type
            : $this->timeRule->night_distance_charge_type;

        $distanceFareAdjustmentValue = $distanceFareAdjustmentType === 'fixed'
            ? ($this->timeOfDay === 'day'
                ? $this->timeRule->day_distance_fixed_charge
                : $this->timeRule->night_distance_fixed_charge)
            : ($this->timeOfDay === 'day'
                ? $this->timeRule->day_distance_percentage_charge
                : $this->timeRule->night_distance_percentage_charge);

        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            (float) $baseFareAdjustmentValue
        );

        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            (float) $distanceFareAdjustmentValue
        );

        $totalAdjustment = $baseFareAdjustment + $distanceFareAdjustment * $distance;

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
        ];
    }
}
