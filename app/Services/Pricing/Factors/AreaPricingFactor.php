<?php

namespace App\Services\Pricing\Factors;

use App\Models\Area;
use App\Services\Pricing\GlobalPricingRules;

class AreaPricingFactor extends AbstractPricingFactor
{
    private ?int $areaId;

    private ?Area $area;

    public function __construct(?int $areaId)
    {
        $this->areaId = $areaId;
        $this->area = $areaId ? Area::find($areaId) : null;
    }

    public function getName(): string
    {
        return 'area_adjustment';
    }

    public function isApplicable(): bool
    {
        if ($this->area === null || ! $this->area->is_active) {
            return false;
        }

        return $this->area->base_fare_adjustment_type !== null &&
               $this->area->distance_fare_adjustment_type !== null;
    }

    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->area) {
            return null;
        }

        $baseFareAdjustmentType = $this->getAdjustmentType($this->area->base_fare_adjustment_type);
        $distanceFareAdjustmentType = $this->getAdjustmentType($this->area->distance_fare_adjustment_type);

        $baseFareValue = ($baseFareAdjustmentType === 'fixed')
            ? $this->area->base_fare
            : $this->area->base_fare_adjustment;

        $distanceFareValue = ($distanceFareAdjustmentType === 'fixed')
            ? $this->area->distance_fare
            : $this->area->distance_fare_adjustment;

        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            (float) $baseFareValue
        );

        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            (float) $distanceFareValue
        );

        $totalAdjustment = $baseFareAdjustment + ($distanceFareAdjustment * $distance);

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
            'area_id' => $this->area->id,
            'area_name' => $this->area->name_en,
            'base_fare_adjustment_type' => $baseFareAdjustmentType,
            'distance_fare_adjustment_type' => $distanceFareAdjustmentType,
            'base_fare_value' => $baseFareValue,
            'distance_fare_value' => $distanceFareValue,
        ];
    }

    private function getAdjustmentType($adjustmentType): string
    {
        return is_object($adjustmentType) ? $adjustmentType->value : $adjustmentType;
    }
}
