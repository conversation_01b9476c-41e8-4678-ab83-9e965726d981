<?php

namespace App\Services\Pricing\Factors;

use App\Models\PricingRuleGender;
use App\Services\Pricing\GlobalPricingRules;

class GenderPricingFactor extends AbstractPricingFactor
{
    /**
     * @var string|null
     */
    private $gender;

    /**
     * @var PricingRuleGender|null
     */
    private $genderRule;

    /**
     * Constructor
     */
    public function __construct(?string $gender)
    {
        // Ensure we always have a valid gender string
        if (! in_array($gender, ['male', 'female'])) {
            $gender = 'male'; // Default to male if invalid or null
        }

        $this->gender = $gender;

        // Get the gender rule from the database
        $this->genderRule = PricingRuleGender::where('gender', $this->gender)->first();

        // If no gender rule is found, log a warning
        if (! $this->genderRule) {
            \Illuminate\Support\Facades\Log::warning('No gender pricing rule found in database', [
                'gender' => $this->gender,
                'all_rules' => PricingRuleGender::all()->toArray(),
            ]);
        }

    }

    /**
     * Get the factor name
     */
    public function getName(): string
    {
        return 'gender_adjustment';
    }

    /**
     * Check if this factor is applicable
     */
    public function isApplicable(): bool
    {
        return $this->genderRule !== null;
    }

    /**
     * Calculate the gender price adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->genderRule) {
            \Illuminate\Support\Facades\Log::warning('No gender rule found for gender', [
                'gender' => $this->gender,
            ]);

            return null;
        }

        // Get the adjustment type values, handling both string and enum
        $baseFareAdjustmentType = is_object($this->genderRule->base_fare_adjustment_type)
            ? $this->genderRule->base_fare_adjustment_type->value
            : $this->genderRule->base_fare_adjustment_type;

        $distanceFareAdjustmentType = is_object($this->genderRule->distance_fare_adjustment_type)
            ? $this->genderRule->distance_fare_adjustment_type->value
            : $this->genderRule->distance_fare_adjustment_type;

        // Determine which value to use based on adjustment type
        $baseFareAdjustmentValue = $baseFareAdjustmentType === 'fixed'
            ? (float) $this->genderRule->base_fare_fixed
            : (float) $this->genderRule->base_fare_percentage;

        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            $baseFareAdjustmentValue
        );

        // Determine which value to use based on adjustment type
        $distanceFareAdjustmentValue = $distanceFareAdjustmentType === 'fixed'
            ? (float) $this->genderRule->distance_fare_fixed
            : (float) $this->genderRule->distance_fare_percentage;

        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            $distanceFareAdjustmentValue
        );

        // Calculate the total adjustment
        $totalAdjustment = $baseFareAdjustment + $distanceFareAdjustment * $distance;

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
        ];
    }
}
