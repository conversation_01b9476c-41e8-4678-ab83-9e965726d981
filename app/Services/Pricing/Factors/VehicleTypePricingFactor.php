<?php

namespace App\Services\Pricing\Factors;

use App\Models\VehicleType;
use App\Services\Pricing\GlobalPricingRules;

class VehicleTypePricingFactor extends AbstractPricingFactor
{
    /**
     * @var int|null
     */
    private $vehicleTypeId;

    /**
     * @var VehicleType|null
     */
    private $vehicleType;

    /**
     * Constructor
     */
    public function __construct(?int $vehicleTypeId)
    {
        $this->vehicleTypeId = $vehicleTypeId;
        if ($this->vehicleTypeId) {
            $this->vehicleType = VehicleType::find($this->vehicleTypeId);
        }
    }

    /**
     * Get the factor name
     */
    public function getName(): string
    {
        return 'vehicle_type_adjustment';
    }

    /**
     * Check if this factor is applicable
     */
    public function isApplicable(): bool
    {
        return $this->vehicleType !== null;
    }

    /**
     * Calculate the vehicle type price adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->vehicleType) {
            return null;
        }

        // Get the adjustment type values, handling both string, enum, and null values
        // Default to 'fixed' if the adjustment type is null
        $baseFareAdjustmentType = $this->vehicleType->base_fare_adjustment_type ?? 'fixed';

        // If it's an object (enum), get its value
        if (is_object($baseFareAdjustmentType)) {
            $baseFareAdjustmentType = $baseFareAdjustmentType->value;
        }
        // If it's null or empty, default to 'fixed'
        elseif (empty($baseFareAdjustmentType)) {
            $baseFareAdjustmentType = 'fixed';
        }

        // Same for distance fare adjustment type
        $distanceFareAdjustmentType = $this->vehicleType->distance_fare_adjustment_type ?? 'fixed';

        // If it's an object (enum), get its value
        if (is_object($distanceFareAdjustmentType)) {
            $distanceFareAdjustmentType = $distanceFareAdjustmentType->value;
        }
        // If it's null or empty, default to 'fixed'
        elseif (empty($distanceFareAdjustmentType)) {
            $distanceFareAdjustmentType = 'fixed';
        }

        // Determine the base fare value based on adjustment type
        $baseFareValue = ($baseFareAdjustmentType === 'fixed')
            ? $this->vehicleType->additional_base_fare
            : $this->vehicleType->base_fare_adjustment;

        // Determine the distance fare value based on adjustment type
        $distanceFareValue = ($distanceFareAdjustmentType === 'fixed')
            ? $this->vehicleType->additional_price_per_km
            : $this->vehicleType->distance_fare_adjustment;

        // Calculate the base fare adjustment
        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            (float) $baseFareValue
        );

        // Calculate the distance fare adjustment
        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            (float) $distanceFareValue
        );
        
        $totalAdjustment = $baseFareAdjustment + ($distanceFareAdjustment * $distance);

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
            'vehicle_type_id' => $this->vehicleType->id,
            'vehicle_type_name' => $this->vehicleType->name_en,
            'base_fare_adjustment_type' => $baseFareAdjustmentType,
            'distance_fare_adjustment_type' => $distanceFareAdjustmentType,
        ];
    }
}
