<?php

namespace App\Services\Pricing\Factors;

use App\Services\Pricing\GlobalPricingRules;
use Illuminate\Database\Eloquent\Collection;

class EquipmentPricingFactor extends AbstractPricingFactor
{
    /**
     * @var Collection
     */
    private $equipments;

    /**
     * Constructor
     */
    public function __construct(Collection $equipments)
    {
        $this->equipments = $equipments;
    }

    /**
     * Get the factor name
     */
    public function getName(): string
    {
        return 'equipment';
    }

    /**
     * Check if this factor is applicable
     */
    public function isApplicable(): bool
    {
        return $this->equipments->isNotEmpty();
    }

    /**
     * Calculate the equipment price adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if ($this->equipments->isEmpty()) {
            return null;
        }

        // Map equipment details
        $equipmentDetails = $this->equipments->map(function ($equipment) {
            return [
                'id' => $equipment->id,
                'name' => $equipment->name_en,
                'price' => (float) $equipment->additional_fare,
            ];
        })->toArray();

        // Calculate total additional fare - ensuring we get values for active equipment only
        $additionalFare = $this->equipments->sum('additional_fare') ?? 0.0;

        return [
            'equipment' => $equipmentDetails,
            'equipment_total' => (float) $additionalFare,
            'base_fare' => (float) $additionalFare, // Explicitly add to base fare
        ];
    }
}
