<?php

namespace App\Services\Pricing\Factors;

abstract class AbstractPricingFactor implements PricingFactorInterface
{
    /**
     * Apply price adjustment based on type and value
     */
    protected function applyAdjustment(
        float $originalValue,
        $adjustmentType, // Removed type hint to allow for null values
        float $adjustmentValue
    ): float {
        // Handle string, enum, and null values for adjustment type
        // Default to 'fixed' if the adjustment type is null or invalid
        $adjustmentTypeValue = 'fixed'; // Default value

        if (is_object($adjustmentType) && method_exists($adjustmentType, 'value')) {
            $adjustmentTypeValue = $adjustmentType->value;
        } elseif (is_string($adjustmentType) && in_array($adjustmentType, ['fixed', 'percentage'])) {
            $adjustmentTypeValue = $adjustmentType;
        } else {
            // If adjustment type is null or invalid, log a warning
            \Illuminate\Support\Facades\Log::warning('Invalid adjustment type, defaulting to fixed', [
                'adjustment_type' => $adjustmentType,
                'is_object' => is_object($adjustmentType),
                'class' => is_object($adjustmentType) ? get_class($adjustmentType) : 'not an object',
            ]);
        }

        $result = 0;
        if ($adjustmentTypeValue === 'percentage') {
            // For percentage adjustments, we calculate the adjustment amount
            // The formula is: original_value * (adjustment_value / 100)
            // For example, if original_value is 100 and adjustment_value is 10 (10%),
            // the result is 10, which is the adjustment amount, not the new value
            $result = $originalValue * ($adjustmentValue / 100);

        } else {
            // For fixed adjustments, we return the fixed value directly
            $result = $adjustmentValue;
        }

        return $result;
    }
}
