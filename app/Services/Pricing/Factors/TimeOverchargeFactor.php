<?php

namespace App\Services\Pricing\Factors;

use App\Services\Pricing\GlobalPricingRules;
use Carbon\Carbon;

class TimeOverchargeFactor extends AbstractPricingFactor
{
    /**
     * @var Carbon
     */
    private $startTime;

    /**
     * @var Carbon
     */
    private $estimatedArrivalTime;

    /**
     * @var Carbon
     */
    private $actualArrivalTime;

    /**
     * Constructor
     */
    public function __construct(
        Carbon $startTime,
        Carbon $estimatedArrivalTime,
        Carbon $actualArrivalTime
    ) {
        $this->startTime = $startTime;
        $this->estimatedArrivalTime = $estimatedArrivalTime;
        $this->actualArrivalTime = $actualArrivalTime;
    }

    /**
     * Get the factor name
     */
    public function getName(): string
    {
        return 'time_overcharge';
    }

    /**
     * Check if this factor is applicable
     */
    public function isApplicable(): bool
    {
        return $this->estimatedArrivalTime && $this->actualArrivalTime;
    }

    /**
     * Calculate the time overcharge adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        // Calculate durations using absolute values to handle any direction of time difference
        $estimatedDuration = abs($this->estimatedArrivalTime->diffInSeconds($this->startTime));
        $actualDuration = abs($this->actualArrivalTime->diffInSeconds($this->startTime));

        // Default values
        $timeDifference = 0;
        $overcharge = 0;
        $overchargeApplied = 'no';

        // Prevent division by zero
        if ($estimatedDuration > 0) {
            $timeDifference = ($actualDuration - $estimatedDuration) / $estimatedDuration;

            // Get the threshold from global rules (convert from percentage to decimal)
            $threshold = $globalRules->getTimeThresholdPercentage() / 100;

            // Apply overcharge if time difference exceeds threshold and is positive
            // (we only apply overcharge if the trip takes longer than expected)
            if ($timeDifference > 0 && $timeDifference > $threshold) {
                $overcharge = $timeDifference - $threshold;
                $overchargeApplied = 'yes';
            }
        }

        return [
            'estimated_duration' => $estimatedDuration,
            'actual_duration' => $actualDuration,
            'time_difference_percentage' => round($timeDifference * 100, 2),
            'threshold_percentage' => round($globalRules->getTimeThresholdPercentage(), 2),
            'overcharge_percentage' => round($overcharge * 100, 2),
            'global_rules_found' => 'yes',
            'threshold_source' => 'global_rules',
            'overcharge_applied' => $overchargeApplied,
            'overcharge_value' => $overcharge,
        ];
    }
}
