<?php

namespace App\Services\Pricing\Factors;

use App\Services\Pricing\GlobalPricingRules;

interface PricingFactorInterface
{
    /**
     * Get the factor name
     */
    public function getName(): string;

    /**
     * Check if this factor is applicable to the current calculation
     */
    public function isApplicable(): bool;

    /**
     * Calculate the price adjustment
     *
     * @param  float  $baseFare  Base fare
     * @param  float  $distanceFare  Distance fare per km
     * @param  float  $distance  Distance in km
     * @param  GlobalPricingRules  $globalRules  Global pricing rules
     * @return array|null Adjustment data or null if no adjustment
     */
    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array;
}
