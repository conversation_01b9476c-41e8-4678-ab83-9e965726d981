<?php

namespace App\Services\Pricing\Factors;

use App\Models\PricingRuleAdditionalDayCharge;
use App\Models\PricingRulePeakHour;
use App\Services\Pricing\GlobalPricingRules;
use Carbon\Carbon;

class PeakHourPricingFactor extends AbstractPricingFactor
{
    private Carbon $startTime;

    private ?PricingRulePeakHour $peakHourRule = null;

    private string $dayOfWeek;

    public function __construct(?Carbon $startTime = null)
    {
        $this->startTime = $startTime ?? now();
        $this->dayOfWeek = $this->startTime->format('l');
        $this->loadPeakHourRule();
    }

    private function loadPeakHourRule(): void
    {
        $dayCharge = PricingRuleAdditionalDayCharge::where('day', $this->dayOfWeek)->first();

        if (! $dayCharge) {
            return;
        }

        $currentTime = $this->startTime->format('H:i');

        $this->peakHourRule = PricingRulePeakHour::where('day_charge_id', $dayCharge->id)
            ->where(function ($query) use ($currentTime) {
                $query->where(function ($q) use ($currentTime) {
                    $q->whereRaw('peak_start_at <= peak_end_at')
                        ->whereRaw('? >= peak_start_at', [$currentTime])
                        ->whereRaw('? <= peak_end_at', [$currentTime]);
                })->orWhere(function ($q) use ($currentTime) {
                    $q->whereRaw('peak_start_at > peak_end_at')
                        ->where(function ($sq) use ($currentTime) {
                            $sq->whereRaw('? >= peak_start_at', [$currentTime])
                                ->orWhereRaw('? <= peak_end_at', [$currentTime]);
                        });
                });
            })
            ->first();
    }

    public function getName(): string
    {
        return 'peak_hour_adjustment';
    }

    public function isApplicable(): bool
    {
        return $this->peakHourRule !== null;
    }

    public function calculateAdjustment(
        float $baseFare,
        float $distanceFare,
        float $distance,
        GlobalPricingRules $globalRules
    ): ?array {
        if (! $this->peakHourRule) {
            return null;
        }

        $baseFareAdjustmentType = $this->peakHourRule->base_fare_adjustment_type;
        $distanceFareAdjustmentType = $this->peakHourRule->distance_fare_adjustment_type;

        $baseFareAdjustmentValue = $baseFareAdjustmentType === 'fixed'
            ? (float) $this->peakHourRule->base_fare_fixed
            : (float) $this->peakHourRule->base_fare_percentage;

        $distanceFareAdjustmentValue = $distanceFareAdjustmentType === 'fixed'
            ? (float) $this->peakHourRule->distance_fare_fixed
            : (float) $this->peakHourRule->distance_fare_percentage;

        $baseFareAdjustment = $this->applyAdjustment(
            $baseFare,
            $baseFareAdjustmentType,
            $baseFareAdjustmentValue
        );

        $distanceFareAdjustment = $this->applyAdjustment(
            $distanceFare,
            $distanceFareAdjustmentType,
            $distanceFareAdjustmentValue
        );

        $totalAdjustment = $baseFareAdjustment + $distanceFareAdjustment * $distance;

        return [
            'base_fare' => $baseFareAdjustment,
            'per_km' => $distanceFareAdjustment,
            'total' => $totalAdjustment,
        ];
    }
}
