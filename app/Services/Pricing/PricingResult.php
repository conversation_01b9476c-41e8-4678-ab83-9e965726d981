<?php

namespace App\Services\Pricing;

class PricingResult
{
    /**
     * @var array
     */
    private $data = [
        'base_fare' => 0,
        'per_km' => 0,
        'distance' => 0,
        'subtotal' => 0,
        'total' => 0,
    ];

    /**
     * @var array
     */
    private $adjustments = [];

    /**
     * Set the base fare
     */
    public function setBaseFare(float $baseFare): self
    {
        $this->data['base_fare'] = (float) $baseFare;

        return $this;
    }

    /**
     * Set the per km rate
     */
    public function setPerKm(float $perKm): self
    {
        $this->data['per_km'] = (float) $perKm;

        return $this;
    }

    /**
     * Set the distance
     */
    public function setDistance(float $distance): self
    {
        $this->data['distance'] = (float) $distance;

        return $this;
    }

    /**
     * Set the subtotal
     */
    public function setSubtotal(float $subtotal): self
    {
        $this->data['subtotal'] = (float) $subtotal;

        return $this;
    }

    /**
     * Set the subtotal with overcharge
     */
    public function setSubtotalWithOvercharge(float $subtotalWithOvercharge): self
    {
        $this->data['subtotal_with_overcharge'] = round($subtotalWithOvercharge, 2);

        return $this;
    }

    /**
     * Set the original price
     */
    public function setOriginalPrice(float $originalPrice): self
    {
        $this->data['original_price'] = round($originalPrice, 2);

        return $this;
    }

    /**
     * Set the adjusted price
     */
    public function setAdjustedPrice(float $adjustedPrice): self
    {
        $this->data['adjusted_price'] = round($adjustedPrice, 2);

        return $this;
    }

    /**
     * Set the total price
     */
    public function setTotal(float $total): self
    {
        $this->data['total'] = $total;

        return $this;
    }

    /**
     * Set vehicle type details
     */
    public function setVehicleType(array $vehicleType): self
    {
        $this->data['vehicle_type'] = $vehicleType;

        return $this;
    }

    /**
     * Set area information
     */
    public function setAreaInfo(array $areaInfo): self
    {
        $this->data['area_info'] = $areaInfo;

        return $this;
    }

    /**
     * Add a pricing adjustment
     */
    public function addAdjustment(string $key, array $adjustment): self
    {
        $this->adjustments[$key] = $adjustment;

        return $this;
    }

    /**
     * Get all adjustments
     */
    public function getAdjustments(): array
    {
        return $this->adjustments;
    }

    /**
     * Get the subtotal with overcharge
     */
    public function getSubtotalWithOvercharge(): float
    {
        return $this->data['subtotal_with_overcharge'] ?? $this->data['subtotal'];
    }

    /**
     * Apply custom rounding rules for the final price
     */
    public function applyRounding(): self
    {
        $totalPrice = $this->data['total'];

        // Apply custom rounding rules:
        // - Values ≥ 0.5 are rounded up (e.g., 10.6 → 11)
        // - Values < 0.5 are rounded down (e.g., 10.2 → 10)
        $integerPart = floor($totalPrice);
        $decimalPart = $totalPrice - $integerPart;

        if ($decimalPart >= 0.5) {
            // Round up
            $roundedPrice = ceil($totalPrice);
        } else {
            // Round down
            $roundedPrice = floor($totalPrice);
        }

        $this->data['total'] = $roundedPrice;

        return $this;
    }

    /**
     * Convert the result to an array with all details
     */
    public function toArray(): array
    {
        // Ensure distance is always set
        $distance = isset($this->data['distance']) ? $this->data['distance'] : 0;

        $pricing = [
            'base_fare' => round($this->data['base_fare'], 2),
            'per_km' => round($this->data['per_km'], 2),
            'distance' => round($distance, 2),
            'subtotal' => round($this->data['subtotal'], 2),
            'total' => round($this->data['total'], 2),
            'currency' => 'LYD',
            'adjustments' => [],
        ];

        // Add vehicle type information if available
        if (isset($this->data['vehicle_type'])) {
            $pricing['vehicle_type'] = [
                'id' => $this->data['vehicle_type']['id'],
                'name' => $this->data['vehicle_type']['name'],
            ];
        }

        // Add area information if available
        if (isset($this->data['area_info'])) {
            $pricing['area_info'] = $this->data['area_info'];
        }

        // Include all adjustments in the pricing breakdown
        foreach ($this->adjustments as $key => $adjustment) {
            $adjustmentData = [
                'type' => $key,
                'amount' => round($adjustment['total'] ?? 0, 2),
            ];

            // Add specific details based on adjustment type
            if ($key === 'area_adjustment' && isset($adjustment['base_fare']) && isset($adjustment['per_km'])) {
                $adjustmentData['base_fare'] = round($adjustment['base_fare'], 2);
                $adjustmentData['per_km'] = round($adjustment['per_km'], 2);
            } elseif ($key === 'vehicle_type_adjustment' && isset($adjustment['base_fare']) && isset($adjustment['per_km'])) {
                $adjustmentData['base_fare'] = round($adjustment['base_fare'], 2);
                $adjustmentData['per_km'] = round($adjustment['per_km'], 2);
            } elseif ($key === 'time_adjustment' && isset($adjustment['base_fare'])) {
                $adjustmentData['base_fare'] = round($adjustment['base_fare'], 2);
                $adjustmentData['reason'] = 'Time-based pricing';
            } elseif ($key === 'gender_adjustment' && isset($adjustment['base_fare'])) {
                $adjustmentData['base_fare'] = round($adjustment['base_fare'], 2);
                $adjustmentData['reason'] = 'Gender-based pricing';
            } elseif ($key === 'equipment' && isset($adjustment['equipment_total'])) {
                $adjustmentData['equipment_count'] = count($adjustment['equipment'] ?? []);
                $adjustmentData['amount'] = (float) $adjustment['equipment_total'];
                $adjustmentData['reason'] = 'Additional equipment';
            } elseif ($key === 'time_overcharge' && isset($adjustment['overcharge_percentage'])) {
                $adjustmentData['overcharge_percentage'] = $adjustment['overcharge_percentage'];
                $adjustmentData['reason'] = 'Time threshold exceeded';
            }

            $pricing['adjustments'][] = $adjustmentData;
        }

        // Always include a time_overcharge entry, even if not present in adjustments
        $hasTimeOvercharge = false;
        foreach ($pricing['adjustments'] as $adjustment) {
            if ($adjustment['type'] === 'time_overcharge') {
                $hasTimeOvercharge = true;
                break;
            }
        }

        if (! $hasTimeOvercharge) {
            $pricing['adjustments'][] = [
                'type' => 'time_overcharge',
                'amount' => 0,
                'overcharge_percentage' => 0,
                'reason' => 'No time overcharge',
            ];
        }

        // Add original and adjusted prices
        if (isset($this->data['original_price'])) {
            $pricing['original_price'] = round($this->data['original_price'], 2);
        }

        if (isset($this->data['adjusted_price'])) {
            $pricing['adjusted_price'] = round($this->data['adjusted_price'], 2);
        }

        return $pricing;
    }

    /**
     * Convert the result to a simplified array for frontend display
     */
    public function toSimplifiedArray(): array
    {
        // Only include the essential data needed for the frontend
        $result = [
            'price' => $this->data['total'],
            'distance' => $this->data['distance'],
            'currency' => 'LYD', // Add currency if needed
        ];

        // Add vehicle type if available
        if (isset($this->data['vehicle_type'])) {
            $result['vehicle_type'] = $this->data['vehicle_type'];
        }

        return $result;
    }
}
