<?php

namespace App\Services\Pricing;

use App\Models\VehicleType;
use App\Services\Pricing\Factors\PricingFactorInterface;

class PricingCalculator
{
    /**
     * @var GlobalPricingRules
     */
    private $globalRules;

    /**
     * @var array
     */
    private $factors = [];

    /**
     * @var PricingResult
     */
    public $result;

    /**
     * @var VehicleType|null
     */
    private $vehicleType = null;

    /**
     * Constructor
     */
    public function __construct(GlobalPricingRules $globalRules)
    {
        $this->globalRules = $globalRules;
        $this->result = new PricingResult;
    }

    /**
     * Add a pricing factor to the calculator
     */
    public function addFactor(PricingFactorInterface $factor): self
    {
        $this->factors[] = $factor;

        return $this;
    }

    /**
     * Add vehicle type details to the result
     */
    public function addVehicleTypeDetails(VehicleType $vehicleType): self
    {
        $this->vehicleType = $vehicleType;
        $this->result->setVehicleType([
            'id' => $vehicleType->id,
            'name' => $vehicleType->name_en,
            'category' => $vehicleType->category,
            'image' => ! empty($vehicleType->image)
                ? rtrim(env('APP_URL', '/'), '/').'/storage/'.ltrim($vehicleType->image, '/')
                : null,
        ]);

        return $this;
    }

    /**
     * Calculate the final price
     */
    public function calculate(float $distance): PricingResult
    {
        // Ensure distance is at least 0.01 to avoid division by zero issues
        $distance = max($distance, 0.01);

        // Store initial values in the result
        $baseFare = $this->globalRules->getBaseFare();
        $distanceFare = $this->globalRules->getDistancePerKm();

        $this->result->setBaseFare($baseFare);
        $this->result->setPerKm($distanceFare);
        $this->result->setDistance($distance);

        // Track the adjusted base fare and distance fare
        $adjustedBaseFare = $baseFare;
        $adjustedDistanceFare = $distanceFare;

        // Process each pricing factor
        foreach ($this->factors as $factor) {
            $factorName = $factor->getName();

            if ($factor->isApplicable()) {
                $adjustment = $factor->calculateAdjustment($baseFare, $distanceFare, $distance, $this->globalRules);

                if ($adjustment) {
                    $this->result->addAdjustment($factorName, $adjustment);

                    // Update the adjusted base fare and distance fare
                    if (isset($adjustment['base_fare']) &&
                        ($factorName === 'area_adjustment' ||
                         $factorName === 'vehicle_type_adjustment' ||
                         $factorName === 'gender_adjustment' ||
                         $factorName === 'time_adjustment' ||
                         $factorName === 'peak_hour_adjustment' ||
                         $factorName === 'seat_capacity_adjustment')) {

                        $adjustedBaseFare += $adjustment['base_fare'];
                    }

                    if (isset($adjustment['per_km']) &&
                        ($factorName === 'area_adjustment' ||
                         $factorName === 'vehicle_type_adjustment' ||
                         $factorName === 'gender_adjustment' ||
                         $factorName === 'time_adjustment' ||
                         $factorName === 'peak_hour_adjustment' ||
                         $factorName === 'seat_capacity_adjustment')) {

                        $adjustedDistanceFare += $adjustment['per_km'];
                    }
                }
            }
        }

        // Calculate subtotal without overcharge using the adjusted values
        $subtotal = $adjustedBaseFare + ($adjustedDistanceFare * $distance);
        $this->result->setSubtotal($subtotal);

        // Get equipment total if available
        $equipmentTotal = 0;
        if (isset($this->result->getAdjustments()['equipment'])) {
            $equipmentTotal = $this->result->getAdjustments()['equipment']['equipment_total'] ?? 0;

            // Add equipment total to the adjusted base fare
            // According to the pricing rules, equipment charges are added to B
            $adjustedBaseFare += $equipmentTotal;

            // Recalculate subtotal with equipment
            $subtotal = $adjustedBaseFare + ($adjustedDistanceFare * $distance);
            $this->result->setSubtotal($subtotal);
        }

        // Apply time overcharge if present
        $subtotalWithOvercharge = $subtotal;
        if (isset($this->result->getAdjustments()['time_overcharge'])) {
            $overcharge = $this->result->getAdjustments()['time_overcharge']['overcharge_value'] ?? 0;

            $subtotalWithOvercharge = $adjustedBaseFare + ($adjustedDistanceFare * $distance) * (1 + $overcharge);

            $this->result->setSubtotalWithOvercharge($subtotalWithOvercharge);
        } else {
            $this->result->setSubtotalWithOvercharge($subtotal);
        }

        // Calculate final total
        $totalPrice = $subtotalWithOvercharge;

        // Equipment is already added to the base fare, so we don't need to add it again
        // This is according to the pricing rules: "Additional charges for special equipment
        // are added to B at the end, after adjustments for day, area, vehicle types,
        // number of seats, and gender, but before applying the time adjustment."

        $totalPrice = max($totalPrice, 0.01); // Prevent zero or negative prices

        $this->result->setOriginalPrice($subtotal);
        $this->result->setAdjustedPrice($totalPrice);
        $this->result->setTotal($totalPrice);

        return $this->result;
    }
}
