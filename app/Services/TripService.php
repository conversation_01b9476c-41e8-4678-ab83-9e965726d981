<?php

namespace App\Services;

use App\Enums\Trips\TripStatus;
use App\Models\Area;
use App\Models\Trip;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class TripService
{
    protected $googleMapsService;

    protected $areaDetectionService;

    protected $tripPricingService;

    protected $tripLocationService;

    protected $tripVehicleService;

    public function __construct(
        GoogleMapsService $googleMapsService,
        AreaDetectionService $areaDetectionService,
        TripPricingService $tripPricingService,
        TripLocationService $tripLocationService,
        TripVehicleService $tripVehicleService
    ) {
        $this->googleMapsService = $googleMapsService;
        $this->areaDetectionService = $areaDetectionService;
        $this->tripPricingService = $tripPricingService;
        $this->tripLocationService = $tripLocationService;
        $this->tripVehicleService = $tripVehicleService;
    }

    /**
     * Create a new trip
     *
     * @param  array  $validated  Validated request data
     * @param  int  $riderId  Rider ID
     * @return Trip The created trip
     */
    public function createTrip(array $validated, int $riderId, bool $is_female): Trip
    {
        // Get coordinates
        $origin = [
            'lat' => $validated['departure_location']['latitude'],
            'lng' => $validated['departure_location']['longitude'],
        ];
        $destination = [
            'lat' => $validated['arrival_location']['latitude'],
            'lng' => $validated['arrival_location']['longitude'],
        ];

        // Get distance and time information
        $googleData = $this->googleMapsService->getDistanceAndTime($origin, $destination);
        $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : 0;

        // Detect areas
        $departureAreaId = $this->detectArea($origin['lat'], $origin['lng']);
        $arrivalAreaId = $this->detectArea($destination['lat'], $destination['lng']);
        // Create the trip
        $tripData = Arr::except($validated, [
            'vehicle_category',
            'number_of_seats',
            'weight_category',
            'is_covered',
            'vehicle_equipments',
            'departure_location',
            'arrival_location',
            'address',
            'polyline',
        ]);

        $trip = Trip::create(array_merge([
            'rider_id' => $riderId,
            'status' => TripStatus::pending->value,
            'is_favorite' => false,
            'distance' => $distance,
            'departure_area_id' => $departureAreaId,
            'arrival_area_id' => $arrivalAreaId,
            'rider_notes' => isset($validated['rider_notes']) && $validated['rider_notes'] !== '' ? $validated['rider_notes'] : null,
            'is_female' => $is_female,
        ], $tripData));

        // Create vehicle type for trip
        $vehicleType = $this->tripVehicleService->createTripVehicleType($trip->id, $validated);
        $tripLocation = $this->tripLocationService->createTripLocation($trip->id, $validated);
        $trip->update([
            'trip_location_id' => $tripLocation->id,
            'vehicle_type_id' => $vehicleType->id,
        ]);

        // Calculate and set trip times
        $this->setTripTimes($trip, $origin, $destination, $googleData, $validated);

        // Calculate pricing
        $this->tripPricingService->calculateAndSetTripPricing(
            $trip,
            $googleData,
            $validated,
            $departureAreaId
        );

        return $trip->fresh();
    }

    /**
     * Update an existing trip
     *
     * @param  Trip  $trip  The trip to update
     * @param  array  $validated  Validated request data
     * @param  int  $userId  User ID
     * @return Trip The updated trip
     */
    public function updateTrip(Trip $trip, array $validated, int $userId): Trip
    {
        // Get the current trip location data
        $tripLocation = $trip->tripLocation;
        $locationChanged = false;
        $pricingRelatedFieldsChanged = false;

        // Check if departure or arrival location has changed
        $departureLocationChanged = isset($validated['departure_location']) &&
            ($validated['departure_location']['latitude'] != $tripLocation->departure_lat ||
             $validated['departure_location']['longitude'] != $tripLocation->departure_lng);

        $arrivalLocationChanged = isset($validated['arrival_location']) &&
            ($validated['arrival_location']['latitude'] != $tripLocation->arrival_lat ||
             $validated['arrival_location']['longitude'] != $tripLocation->arrival_lng);

        // // Check if other pricing-related fields have changed
        $vehicleEquipmentsChanged = isset($validated['vehicle_equipments']);
        $vehicleCategoryChanged = isset($validated['vehicle_category']);
        $numberSeatsChanged = isset($validated['number_of_seats']);
        $weightCategoryChanged = isset($validated['weight_category']);
        $isCoveredChanged = isset($validated['is_covered']);
        $isFemaleChanged = isset($validated['is_female']) && $validated['is_female'] != $trip->is_female;
        $departureTimeChanged = isset($validated['estimated_departure_time']) &&
                               $validated['estimated_departure_time'] != $trip->estimated_departure_time;

        // Determine if any pricing-related field has change

        // Check if we can recalculate pricing
        $canRecalculatePricing = $this->canRecalculatePricing($trip);

        // Remove direct pricing_breakdown updates - pricing should always be calculated by the system

        if ($canRecalculatePricing) {
            $locationChanged = $departureLocationChanged || $arrivalLocationChanged;

            // Get the updated coordinates (or use existing ones if not changed)
            $origin = [
                'lat' => $validated['departure_location']['latitude'] ?? $tripLocation->departure_lat,
                'lng' => $validated['departure_location']['longitude'] ?? $tripLocation->departure_lng,
            ];

            $destination = [
                'lat' => $validated['arrival_location']['latitude'] ?? $tripLocation->arrival_lat,
                'lng' => $validated['arrival_location']['longitude'] ?? $tripLocation->arrival_lng,
            ];

            // Update trip with new location information and recalculate pricing
            $this->updateTripLocation($trip, $origin, $destination, $departureLocationChanged, $arrivalLocationChanged);

            // Update trip location if needed
            if ($locationChanged) {
                $this->tripLocationService->updateTripLocation($trip->tripLocation, $validated);
            }

            // Update vehicle type information if needed
            if ($vehicleCategoryChanged || $vehicleEquipmentsChanged || $numberSeatsChanged ||
                $weightCategoryChanged || $isCoveredChanged) {
                $this->updateTripVehicleType($trip, $validated);
            }

            // Update other trip fields
            $filtered = Arr::except($validated, [
                'vehicle_equipments',
                'number_of_seats',
                'weight_category',
                'is_covered',
                'departure_location',
                'arrival_location',
                'pricing_breakdown',
            ]);

            if (! empty($filtered)) {
                $trip->update($filtered);
            }
        } else {
            // If location has changed but we can't recalculate pricing, still update the location
            if ($departureLocationChanged || $arrivalLocationChanged) {
                $this->tripLocationService->updateTripLocation($trip->tripLocation, $validated);

            }

            // Update the trip with other validated data
            $filtered = Arr::except($validated, [
                'vehicle_equipments',
                'number_of_seats',
                'weight_category',
                'is_covered',
                'departure_location',
                'arrival_location',
                'pricing_breakdown',
            ]);

            if (! empty($filtered)) {
                $trip->update($filtered);
            }
        }

        return $trip->fresh();
    }

    /**
     * Check if trip pricing can be recalculated
     *
     * @param  Trip  $trip  The trip to check
     * @return bool Whether pricing can be recalculated
     */
    public function canRecalculatePricing(Trip $trip): bool
    {
        // Convert the status to string for comparison
        $status = $trip->status->value;

        $canRecalculate = ($status === TripStatus::pending->value ||
                          $status === TripStatus::dispatched->value) &&
                          $trip->driver_id === null;

        return $canRecalculate;
    }

    /**
     * Detect area from coordinates
     *
     * @param  float  $latitude  Latitude
     * @param  float  $longitude  Longitude
     * @return int|null Area ID if found, regardless of pricing usability
     */
    protected function detectArea(float $latitude, float $longitude): ?int
    {
        $areaId = $this->areaDetectionService->getAreaIdFromCoordinates(
            $latitude,
            $longitude
        );

        if ($areaId) {
            $area = Area::find($areaId);

            if (! $area) {
                return null;
            }

            // Still return ID even if area is not suitable for pricing
            if (! $area->is_active ||
                $area->base_fare_adjustment_type === null ||
                $area->distance_fare_adjustment_type === null) {
                // Do nothing, return area ID anyway
            }
        }

        return $areaId;
    }

    /**
     * Update trip location and related data
     *
     * @param  Trip  $trip  The trip to update
     * @param  array  $origin  Origin coordinates
     * @param  array  $destination  Destination coordinates
     * @param  bool  $departureLocationChanged  Whether departure location changed
     * @param  bool  $arrivalLocationChanged  Whether arrival location changed
     */
    protected function updateTripLocation(
        Trip $trip,
        array $origin,
        array $destination,
        bool $departureLocationChanged,
        bool $arrivalLocationChanged
    ): void {
        // Get new distance and time information from Google Maps
        $googleData = $this->googleMapsService->getDistanceAndTime($origin, $destination);

        // Always update the distance when either location changes
        $distance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : $trip->distance;

        // Update departure area if needed
        $departureAreaId = $trip->departure_area_id;
        if ($departureLocationChanged) {
            $departureAreaId = $this->detectArea($origin['lat'], $origin['lng']);
            $trip->update(['departure_area_id' => $departureAreaId]);

        }

        // Update arrival area if needed
        $arrivalAreaId = $trip->arrival_area_id;
        if ($arrivalLocationChanged) {
            $arrivalAreaId = $this->detectArea($destination['lat'], $destination['lng']);
            $trip->update(['arrival_area_id' => $arrivalAreaId]);

        }

        // Set the departure time, defaulting to the existing one or now
        $departureTime = $trip->estimated_departure_time ?? now();

        // Calculate estimated arrival time
        $estimatedArrivalTime = $this->calculateEstimatedArrivalTime($departureTime, $googleData, $distance);

        // Calculate actual arrival time
        $actualArrivalTime = $this->calculateActualArrivalTime($googleData, $distance);

        // Get the vehicle type and equipment information
        $vehicleTypeId = $trip->vehicle_type_id;
        $equipments = $trip->vehicleType->vehicle_equipments ?
            explode(',', $trip->vehicleType->vehicle_equipments) :
            [];

        // Store the old pricing information
        $oldPricing = json_decode($trip->pricing_breakdown, true);

        // Recalculate pricing with the new information
        $updatedPricing = $this->tripPricingService->calculateTripPricing(
            $distance,
            $departureAreaId,
            $vehicleTypeId,
            $trip->is_female,
            $departureTime,
            $equipments,
            $estimatedArrivalTime,
            $actualArrivalTime
        );

        // Calculate price difference if old pricing exists
        if ($oldPricing && isset($oldPricing['total']) && isset($updatedPricing['total'])) {
            $priceDifference = $updatedPricing['total'] - $oldPricing['total'];

            // Add old pricing and price difference to the updated pricing
            $updatedPricing['old_price'] = $oldPricing['total'];
            $updatedPricing['price_difference'] = $priceDifference;
        } else {
            // If old pricing doesn't exist, set default values
            $updatedPricing['old_price'] = $updatedPricing['total'] ?? 0;
            $updatedPricing['price_difference'] = 0;

            // Log the missing pricing
            Log::warning('Old pricing not found or invalid', [
                'trip_id' => $trip->id,
                'old_pricing' => $oldPricing,
                'updated_pricing' => $updatedPricing,
            ]);
        }

        // Update trip with new distance, times, and pricing
        $trip->update([
            'distance' => $distance,
            'estimated_arrival_time' => $estimatedArrivalTime,
            'actual_arrival_time' => $actualArrivalTime,
            'pricing_breakdown' => json_encode($updatedPricing),
        ]);

        $trip->refresh();

    }

    /**
     * Set trip times based on Google Maps data
     *
     * @param  Trip  $trip  The trip to update
     * @param  array  $origin  Origin coordinates
     * @param  array  $destination  Destination coordinates
     * @param  array  $googleData  Google Maps data
     * @param  array  $validated  Validated request data
     */
    protected function setTripTimes(
        Trip $trip,
        array $origin,
        array $destination,
        array $googleData,
        array $validated
    ): void {
        // Set the estimated departure time, defaulting to now if not provided
        $departureTime = isset($validated['estimated_departure_time'])
            ? Carbon::parse($validated['estimated_departure_time'])
            : now();

        // Get distance, time, and traffic information in a single API call
        $travelData = $this->googleMapsService->getDistanceAndTime($origin, $destination, true);
        $distance = $trip->distance;

        // Calculate estimated arrival time
        $estimatedArrivalTime = $this->calculateEstimatedArrivalTime($departureTime, $travelData, $distance);

        // Calculate actual arrival time
        $actualArrivalTime = $this->calculateActualArrivalTime($travelData, $distance);

        // Update trip with estimated and actual times
        $trip->update([
            'estimated_departure_time' => $departureTime,
            'estimated_arrival_time' => $estimatedArrivalTime,
            'actual_departure_time' => now(), // Set actual departure time to now
            'actual_arrival_time' => $actualArrivalTime,
        ]);
    }

    /**
     * Calculate estimated arrival time
     *
     * @param  Carbon  $departureTime  Departure time
     * @param  array|null  $googleData  Google Maps data
     * @param  float  $distance  Distance in kilometers
     * @return Carbon Estimated arrival time
     */
    protected function calculateEstimatedArrivalTime(
        Carbon $departureTime,
        ?array $googleData,
        float $distance
    ): Carbon {
        if ($googleData && isset($googleData['duration_value'])) {
            $durationInSeconds = $googleData['duration_value'];

            return $departureTime->copy()->addSeconds($durationInSeconds);
        } else {
            // Fallback: Use a default duration based on distance (assuming 50 km/h average speed)
            $estimatedDurationInSeconds = ($distance * 1000 / 50) * 3.6; // Convert km to m, then calculate seconds

            return $departureTime->copy()->addSeconds($estimatedDurationInSeconds);
        }
    }

    /**
     * Calculate actual arrival time
     *
     * @param  array|null  $googleData  Google Maps data
     * @param  float  $distance  Distance in kilometers
     * @return Carbon Actual arrival time
     */
    protected function calculateActualArrivalTime(?array $googleData, float $distance): Carbon
    {
        if ($googleData && (isset($googleData['duration_in_traffic_value']) || isset($googleData['duration_value']))) {
            // Use duration_in_traffic if available, otherwise use regular duration
            $durationWithTraffic = $googleData['duration_in_traffic_value'] ?? $googleData['duration_value'];

            return now()->addSeconds($durationWithTraffic);
        } else {
            // Fallback: Use a default duration based on distance (assuming 50 km/h average speed)
            $estimatedDurationInSeconds = ($distance * 1000 / 50) * 3.6; // Convert km to m, then calculate seconds
            // Add 20% to account for traffic
            $actualDurationInSeconds = $estimatedDurationInSeconds * 1.2;

            return now()->addSeconds($actualDurationInSeconds);
        }
    }

    /**
     * Validate trip distance
     *
     * @param  float  $distance  Distance in kilometers
     * @return string|null Error message if validation fails
     */
    public function validateTripDistance(float $distance): ?string
    {
        if ($distance < 1) {
            return 'The trip distance must be at least 1 kilometer.';
        }

        if ($distance > 200) {
            return 'The trip distance cannot exceed 200 kilometers.';
        }

        return null;
    }

    /**
     * Update trip vehicle type and related information
     *
     * @param  Trip  $trip  The trip to update
     * @param  array  $validated  Validated request data
     */
    protected function updateTripVehicleType(Trip $trip, array $validated): void
    {
        // Get the trip vehicle type
        $tripVehicleType = $trip->vehicleType;

        if (! $tripVehicleType) {
            Log::error('Trip vehicle type not found', [
                'trip_id' => $trip->id,
            ]);

            return;
        }

        // Update the trip vehicle type
        $this->tripVehicleService->updateTripVehicleType($tripVehicleType, $validated);

        // Get the equipment IDs
        $equipmentIds = $validated['vehicle_equipments'] ??
            ($tripVehicleType->vehicle_equipments ? explode(',', $tripVehicleType->vehicle_equipments) : []);

        // Log the vehicle type update
        Log::info('Trip vehicle type updated', [
            'trip_id' => $trip->id,
            'vehicle_type_id' => $trip->vehicle_type_id,
            'equipment_ids' => $equipmentIds,
        ]);
    }

    /**
     * Calculate pricing for edit request
     */
    public function calculateEditRequestPricing(Trip $trip, array $validated, array $googleData): array
    {
        // Get the new distance in kilometers
        $newDistance = isset($googleData['distance_value']) ? $googleData['distance_value'] / 1000 : $trip->distance;

        // Get departure area ID (using existing one since departure hasn't changed)
        $departureAreaId = $trip->departure_area_id;

        // Calculate new arrival area ID
        $arrivalAreaId = $this->detectArea(
            $validated['arrival_location']['latitude'],
            $validated['arrival_location']['longitude']
        );

        // Get current pricing
        $currentPricing = json_decode($trip->pricing_breakdown, true);

        // Calculate pricing using existing trip parameters
        $pricingData = $this->tripPricingService->calculateTripPricing(
            $newDistance,
            $departureAreaId,
            $trip->vehicle_type_id,
            $trip->is_female,
            $trip->estimated_departure_time,
            $trip->vehicleType->vehicle_equipments ? explode(',', $trip->vehicleType->vehicle_equipments) : [],
            $trip->estimated_arrival_time,
            $trip->actual_arrival_time
        );

        // Calculate price difference
        $priceDifference = $pricingData['total'] - $currentPricing['total'];

        // Add old pricing and price difference to the pricing data
        $pricingData['old_price'] = $currentPricing['total'];
        $pricingData['price_difference'] = $priceDifference;

        return [
            'new_total' => $pricingData['total'],
            'current_total' => $currentPricing['total'],
            'difference' => $priceDifference,
            'breakdown' => $pricingData,
            'currency' => $currentPricing['currency'] ?? 'LYD',
        ];
    }
}
