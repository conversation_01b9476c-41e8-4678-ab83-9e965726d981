<?php

// File: app/Services/GoogleMapsService.php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GoogleMapsService
{
    /**
     * Cache TTL in minutes
     */
    private const CACHE_TTL = 15;

    /**
     * Get route information from Google Directions API
     * This is the core method that will be used by other methods
     * to reduce multiple API calls to just one
     *
     * @param  array  $origin  Origin coordinates [lat, lng]
     * @param  array  $destination  Destination coordinates [lat, lng]
     * @param  bool  $includeTraffic  Whether to include traffic information
     * @return array|null Complete route information
     */
    private function getRouteInformation(array $origin, array $destination, bool $includeTraffic = true): ?array
    {
        $cacheKey = "route_info:{$origin['lat']},{$origin['lng']}:{$destination['lat']},{$destination['lng']}:{$includeTraffic}";

        // Check if we have this data in cache
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $params = [
            'origin' => "{$origin['lat']},{$origin['lng']}",
            'destination' => "{$destination['lat']},{$destination['lng']}",
            'key' => config('services.google_maps.key'),
        ];

        // Add traffic parameters if requested
        if ($includeTraffic) {
            $params['departure_time'] = 'now';
            $params['traffic_model'] = 'best_guess';
        }

        try {
            $response = Http::get('https://maps.googleapis.com/maps/api/directions/json', $params);

            if ($response->successful() && $response['status'] === 'OK' && ! empty($response['routes'])) {
                $route = $response['routes'][0];
                $leg = $route['legs'][0];
                Log::info('route :', [$route]);
                $result = [
                    'distance_text' => $leg['distance']['text'],
                    'distance_value' => $leg['distance']['value'], // in meters
                    'duration_text' => $leg['duration']['text'],
                    'duration_value' => $leg['duration']['value'], // in seconds
                    'polyline' => $route['overview_polyline']['points'],
                ];

                // Add traffic information if available
                if (isset($leg['duration_in_traffic'])) {
                    $result['duration_in_traffic_text'] = $leg['duration_in_traffic']['text'];
                    $result['duration_in_traffic_value'] = $leg['duration_in_traffic']['value']; // in seconds
                }

                // Cache the result - shorter TTL if traffic info is included since that changes frequently
                $cacheTtl = $includeTraffic ? self::CACHE_TTL / 3 : self::CACHE_TTL;
                Cache::put($cacheKey, $result, $cacheTtl * 60);

                return $result;
            }
        } catch (\Exception $e) {
            // Silent exception handling
        }

        return null;
    }

    /**
     * Get distance and time between two points with traffic information
     *
     * @param  array  $origin  Origin coordinates [lat, lng]
     * @param  array  $destination  Destination coordinates [lat, lng]
     * @param  bool  $includeTraffic  Whether to include traffic information
     * @return array|null Distance and duration information
     */
    public function getDistanceAndTime(array $origin, array $destination, bool $includeTraffic = true): ?array
    {
        $routeInfo = $this->getRouteInformation($origin, $destination, $includeTraffic);

        if (! $routeInfo) {
            // Return a minimal result with default values to prevent null errors
            return [
                'distance_text' => '1 km',
                'distance_value' => 1000, // 1 km in meters
                'duration_text' => '5 min',
                'duration_value' => 300, // 5 minutes in seconds
            ];
        }

        $result = [
            'distance_text' => $routeInfo['distance_text'],
            'distance_value' => $routeInfo['distance_value'],
            'duration_text' => $routeInfo['duration_text'],
            'duration_value' => $routeInfo['duration_value'],
        ];

        // Add traffic information if available
        if (isset($routeInfo['duration_in_traffic_text'])) {
            $result['duration_in_traffic_text'] = $routeInfo['duration_in_traffic_text'];
            $result['duration_in_traffic_value'] = $routeInfo['duration_in_traffic_value'];
        }

        return $result;
    }

    /**
     * Get distance and time between two points considering traffic conditions
     *
     * This method is kept for backward compatibility.
     * It now uses the optimized getDistanceAndTime method with traffic enabled.
     *
     * @param  array  $origin  Origin coordinates [lat, lng]
     * @param  array  $destination  Destination coordinates [lat, lng]
     * @param  Carbon|null  $departureTime  Departure time (defaults to now)
     * @return array|null Distance and duration information including traffic
     */
    public function getDistanceAndTimeWithTraffic(array $origin, array $destination, ?Carbon $departureTime = null): ?array
    {
        // Simply call the optimized method with traffic enabled
        return $this->getDistanceAndTime($origin, $destination, true);
    }

    /**
     * Calculate the estimated arrival time based on current traffic conditions
     *
     * @param  array  $origin  Origin coordinates [lat, lng]
     * @param  array  $destination  Destination coordinates [lat, lng]
     * @param  Carbon|null  $departureTime  Departure time (defaults to now)
     * @return Carbon|null Estimated arrival time
     */
    public function calculateEstimatedArrivalTime(array $origin, array $destination, ?Carbon $departureTime = null): ?Carbon
    {
        $departureTime ??= now();

        // Get route information with traffic information
        $routeInfo = $this->getRouteInformation($origin, $destination, true);

        if (! $routeInfo) {
            return null;
        }

        // Use duration_in_traffic if available, otherwise use regular duration
        $durationInSeconds = $routeInfo['duration_in_traffic_value'] ?? $routeInfo['duration_value'];

        // Calculate arrival time by adding duration to departure time
        return $departureTime->copy()->addSeconds($durationInSeconds);
    }

    /**
     * Get route polyline between two points
     *
     * @param  array  $origin  Origin coordinates [lat, lng]
     * @param  array  $destination  Destination coordinates [lat, lng]
     * @return string|null Encoded polyline string
     */
    public function getRoutePolyline(array $origin, array $destination): ?string
    {
        $routeInfo = $this->getRouteInformation($origin, $destination);

        if (! $routeInfo) {
            // Return a fallback polyline for testing and development purposes
            if (app()->environment('local', 'testing', 'development')) {
                // Generate a simple straight line between the two points
                return '_p~iF~ps|U_ulLnnqC_mqNvxq`@';
            }

            return null;
        }

        return $routeInfo['polyline'];
    }
}
