<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TripVehicleType extends Model
{
    protected $table = 'trip_vehicle_type';

    protected $fillable = [
        'trip_id',
        'vehicle_category',
        'seat_number',
        'vehicle_equipments',
        'is_covered',
        'weight_category',
    ];

    public function trip()
    {
        return $this->belongsTo(Trip::class);
    }
}
