<?php

namespace App\Models;

use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use <PERSON><PERSON>\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser
{
    use HasApiTokens;
    use HasFactory;
    use HasRoles;
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'last_name',
        'phone_number',
        'phone_verified_at',
        'email',
        'email_verified_at',
        'password',
        'cover_picture',
        'remember_token',
        'type',
        'full_name',
        'gender',
        'status',
        'blocking_reason',
        'fcm_token',
        'old_phone_number',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = ['password', 'remember_token'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {

        return [
            // 'gender' => 'string',
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'gender' => \App\Enums\GenderEnum::class,
            'status' => \App\Enums\UserStatus::class,
        ];
    }

    public function routeNotificationForFcm()
    {
        return $this->fcm_token;
    }

    public function getPhoneNumberFormattedAttribute()
    {
        if (strpos($this->phone_number, '+218') === 0) {
            $number = substr($this->phone_number, 4);

            return '+218 '.substr_replace($number, '-', 2, 0);  // Insert a hyphen after the first two digits and add +218 prefix
        }

        return $this->phone_number;
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($this->type === 'admin') {
            return true;
        }

        $allowedDomains = ['@zaho.ly', '@satoripop.com'];
        foreach ($allowedDomains as $domain) {
            if (str_ends_with($this->email, $domain)) {
                return true;
            }
        }

        return false;
    }

    public function rider()
    {
        return $this->hasOne(Rider::class);
    }

    public function driver()
    {
        return $this->hasOne(Driver::class);
    }

    public function preferences()
    {
        return $this->hasOne(RiderPreferences::class);
    }

    public function address()
    {
        return $this->morphMany(Address::class, 'addressable');
    }
}
