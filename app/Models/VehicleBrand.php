<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleBrand extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function vehicleModels()
    {
        return $this->hasMany(VehicleModel::class);
    }

    public function vehicles()
    {
        return $this->hasManyThrough(Vehicle::class, VehicleModel::class);
    }

    public function setNameEnAttribute($value)
    {
        $this->attributes['name_en'] = ucfirst(strtolower($value));  // Capitalizes the first letter and ensures the rest are lowercase
    }
}
