<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleEquipment extends Model
{
    use HasFactory;

    protected $fillable = ['name_en', 'name_ar', 'icon', 'status', 'additional_fare'];

    public function vehicles()
    {
        return $this->belongsToMany(Vehicle::class, 'vehicle_equipment_links');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 0);
    }

    public function riderPreferences()
    {
        return $this->belongsToMany(RiderPreferences::class, 'preference_vehicle_equipments');
    }

    public function setNameEnAttribute($value)
    {
        $this->attributes['name_en'] = ucfirst(strtolower($value));  // Capitalizes the first letter and ensures the rest are lowercase
    }
}
