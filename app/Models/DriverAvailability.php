<?php

namespace App\Models;

use App\Enums\Drivers\DriverAvailabilities;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DriverAvailability extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'status' => DriverAvailabilities::class,
    ];

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }
}
