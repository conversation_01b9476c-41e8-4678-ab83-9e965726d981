<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EditRideRequest extends Model
{
    protected $fillable = [
        'trip_id',
        'polyline',
        'departure_lat',
        'departure_lng',
        'arrival_lat',
        'arrival_lng',
        'departure_address',
        'arrival_address',
        'departure_area_id',
        'arrival_area_id',
        'distance',
        'estimated_duration',
        'previous_pricing_data',
        'new_pricing',
    ];

    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    public function rider(): BelongsTo
    {
        return $this->belongsTo(Rider::class);
    }

    public function departureArea(): BelongsTo
    {
        return $this->belongsTo(Area::class, 'departure_area_id');
    }

    public function arrivalArea(): BelongsTo
    {
        return $this->belongsTo(Area::class, 'arrival_area_id');
    }
}
