<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRuleAdditionalDayCharge extends Model
{
    use HasFactory;

    protected $fillable = [
        'pricing_rule_id',
        'day',
        'day_start_at',
        'day_end_at',
        'day_charge_type',
        'night_start_at',
        'night_end_at',
        'night_charge_type',
        'day_fixed_charge',
        'day_percentage_charge',
        'night_fixed_charge',
        'night_percentage_charge',
        'non_operational_start_at',
        'non_operational_end_at',
        'day_distance_charge_type',
        'day_distance_fixed_charge',
        'day_distance_percentage_charge',
        'night_distance_charge_type',
        'night_distance_fixed_charge',
        'night_distance_percentage_charge',
    ];

    public function peakHours()
    {
        return $this->hasMany(PricingRulePeakHour::class, 'day_charge_id');
    }

    public function nonOperationalPeriods()
    {
        return $this->hasMany(PricingRuleNonOperationalPeriod::class, 'day_charge_id');
    }

    /**
     * Get the calculated value of the day charge.
     */
    public function getDayChargeValueAttribute(): string
    {
        return match ($this->day_charge_type) {
            'fixed' => $this->day_fixed_charge ? number_format($this->day_fixed_charge, 2).' LYD' : '-',
            'percentage' => $this->day_percentage_charge ? "{$this->day_percentage_charge}%" : '-',
            default => '-',
        };
    }

    /**
     * Get the calculated value of the night charge.
     */
    public function getNightChargeValueAttribute(): string
    {
        return match ($this->night_charge_type) {
            'fixed' => $this->night_fixed_charge ? number_format($this->night_fixed_charge, 2).' LYD' : '-',
            'percentage' => $this->night_percentage_charge ? "{$this->night_percentage_charge}%" : '-',
            default => '-',
        };
    }

    /**
     * Get the calculated non-operational time value (optional).
     */
    public function getNonOperationalTimeAttribute(): string
    {
        if ($this->non_operational_start_at && $this->non_operational_end_at) {
            return "{$this->non_operational_start_at} - {$this->non_operational_end_at}";
        }

        return '-';
    }

    public function getDayDistanceChargeValueAttribute(): string
    {
        return match ($this->day_distance_charge_type) {
            'fixed' => $this->day_distance_fixed_charge ? number_format($this->day_distance_fixed_charge, 2).' LYD' : '-',
            'percentage' => $this->day_distance_percentage_charge ? "{$this->day_distance_percentage_charge}%" : '-',
            default => '-',
        };
    }

    /**
     * Get the calculated value of the night distance charge.
     */
    public function getNightDistanceChargeValueAttribute(): string
    {
        return match ($this->night_distance_charge_type) {
            'fixed' => $this->night_distance_fixed_charge ? number_format($this->night_distance_fixed_charge, 2).' LYD' : '-',
            'percentage' => $this->night_distance_percentage_charge ? "{$this->night_distance_percentage_charge}%" : '-',
            default => '-',
        };
    }
}
