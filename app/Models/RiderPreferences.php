<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiderPreferences extends Model
{
    /** @use HasFactory<\Database\Factories\RiderPreferencesFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'driver_gender',
        'seats_number',
    ];

    protected $casts = [
        'seats_number' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function carTypes()
    {
        return $this->belongsToMany(VehicleType::class, 'preference_vehicle_type');
    }

    public function carEquipments()
    {
        return $this->belongsToMany(VehicleEquipment::class, 'preference_vehicle_equipments');
    }
}
