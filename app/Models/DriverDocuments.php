<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DriverDocuments extends Model
{
    protected $fillable = [
        'document_front',
        'document_back',
        'license_front',
        'license_back',
        'registration_card',
        'document_type',
        'license_expiry',
    ];
    public function driver()
    {
        return $this->belongsTo(Driver::class, 'driver_id');
    }
}
