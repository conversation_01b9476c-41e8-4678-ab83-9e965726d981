<?php

namespace App\Models;

use App\Enums\VehicleTypesCategories;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VehicleClassificationRule extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'category' => VehicleTypesCategories::class,
    ];

    public function vehicleType()
    {
        return $this->belongsTo(VehicleType::class);
    }

    public function qualifications(): HasMany
    {
        return $this->hasMany(VehicleClassificationQualification::class, 'rule_id');
    }
}
