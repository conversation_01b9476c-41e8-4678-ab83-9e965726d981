<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VehicleDocuments extends Model
{
    protected $fillable = [
        'vehicle_id',
        'insurance',
        'technical_inspection',
        'roaming_permit',
        'insurance_expiry',
        'technical_inspection_expiry',
        'roaming_permit_expiry',
    ];

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }
}
