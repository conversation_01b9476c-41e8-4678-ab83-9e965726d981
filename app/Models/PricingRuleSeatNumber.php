<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRuleSeatNumber extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'base_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
        'distance_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
    ];

    public function setBaseFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['base_fare_adjustment_type'] = $value;
    }

    public function setDistanceFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['distance_fare_adjustment_type'] = $value;

    }

    public function getBaseFareValueAttribute(): string
    {
        return match ($this->base_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->base_fare_adjustment ? "{$this->base_fare_adjustment}%" : '-',
            'fixed' => $this->base_fare ? number_format($this->base_fare, 2).' LYD' : '-', // Use `base_fare` for fixed
            default => '-',
        };
    }

    public function getDistanceFareValueAttribute(): string
    {
        return match ($this->distance_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->distance_fare_adjustment ? "{$this->distance_fare_adjustment}%" : '-',
            'fixed' => $this->distance_fare ? number_format($this->distance_fare, 2).' LYD' : '-', // Use `distance_fare` for fixed
            default => '-',
        };
    }
}
