<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRulePeakHour extends Model
{
    use HasFactory;

    protected $fillable = [
        'peak_start_at',
        'peak_end_at',
        'base_fare_adjustment_type',
        'base_fare_fixed',
        'base_fare_percentage',
        'distance_fare_adjustment_type',
        'distance_fare_fixed',
        'distance_fare_percentage',
        'day_charge_id'
    ];

    public function dayCharge()
    {
        return $this->belongsTo(PricingRuleAdditionalDayCharge::class, 'day_charge_id');
    }
}