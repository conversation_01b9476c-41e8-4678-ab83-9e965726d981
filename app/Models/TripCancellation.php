<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TripCancellation extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'counts_towards_limit' => 'boolean',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'trip_id',
        'user_id',
        'cancelled_by',
        'reason',
        'counts_towards_limit',
        'cancelled_at',
    ];

    /**
     * Get the trip associated with the cancellation.
     */
    public function trip(): BelongsTo
    {
        return $this->belongsTo(Trip::class);
    }

    /**
     * Get the user who cancelled the trip.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include cancellations that count towards the limit.
     */
    public function scopeCountingTowardsLimit($query)
    {
        return $query->where('counts_towards_limit', true);
    }

    /**
     * Scope a query to only include cancellations within a date range.
     */
    public function scopeBetweenDates($query, $startDate, $endDate)
    {
        return $query->whereBetween('cancelled_at', [$startDate, $endDate]);
    }
}
