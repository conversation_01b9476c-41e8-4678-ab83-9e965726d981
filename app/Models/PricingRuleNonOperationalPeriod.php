<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRuleNonOperationalPeriod extends Model
{
    use HasFactory;

    protected $fillable = [
        'day_charge_id',
        'start_at',
        'end_at',
    ];

    public function dayCharge()
    {
        return $this->belongsTo(PricingRuleAdditionalDayCharge::class, 'day_charge_id');
    }
}
