<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleClassificationQualification extends Model
{
    use HasFactory;

    protected $fillable = [
        'is_covered',
        'weight_category',
        'min_year',
        'max_year',
        'brands',
        'models',
        'seat_numbers',
    ];

    protected $casts = [
        'brands' => 'array',
        'models' => 'array',
        'seat_numbers' => 'array',
        'is_covered' => 'boolean',
    ];

    public function rule()
    {
        return $this->belongsTo(VehicleClassificationRule::class, 'rule_id');
    }
}
