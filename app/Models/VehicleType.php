<?php

namespace App\Models;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VehicleType extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'base_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
        'distance_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
        'category' => VehicleTypesCategories::class,
        'weight_category' => WeightCategoryEnum::class,
    ];

    public function setBaseFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['base_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['base_fare_adjustment'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['additional_base_fare'] = 0;
        }
    }

    public function setDistanceFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['distance_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['distance_fare_adjustment'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['additional_price_per_km'] = 0;
        }
    }

    public function getBaseFareValueAttribute(): string
    {
        return match ($this->base_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->base_fare_adjustment ? "{$this->base_fare_adjustment}%" : '-',
            'fixed' => $this->additional_base_fare ? number_format($this->additional_base_fare, 2).' LYD' : '-',
            default => '-',
        };
    }

    public function getDistanceFareValueAttribute(): string
    {
        return match ($this->distance_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->distance_fare_adjustment ? "{$this->distance_fare_adjustment}%" : '-',
            'fixed' => $this->additional_price_per_km ? number_format($this->additional_price_per_km, 2).' LYD' : '-',
            default => '-',
        };
    }

    // Relationship with VehicleModel
    public function vehicleModels()
    {
        return $this->hasMany(VehicleModel::class);
    }

    // Relationship with Vehicle
    public function vehicles()
    {
        return $this->hasMany(Vehicle::class, 'vehicle_type_id');
    }

    // Relationship with RiderPreferences
    public function riderPreferences()
    {
        return $this->belongsToMany(RiderPreferences::class, 'preference_vehicle_type');
    }

    public function setNameEnAttribute($value)
    {
        $this->attributes['name_en'] = ucfirst(strtolower($value));  // Capitalizes the first letter and ensures the rest are lowercase
    }
}
