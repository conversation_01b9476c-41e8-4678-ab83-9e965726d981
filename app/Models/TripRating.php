<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TripRating extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function trip()
    {
        return $this->belongsTo(Trip::class);
    }

    public function rider()
    {
        return $this->belongsTo(Rider::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }
}
