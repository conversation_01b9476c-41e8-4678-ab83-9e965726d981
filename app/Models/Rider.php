<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Rider extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'global_status' => \App\Enums\RiderGlobalStatus::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    public function tripRatings()
    {
        return $this->hasMany(TripRating::class);
    }

    public function lastTrip()
    {
        return $this->hasOne(Trip::class)->latestOfMany();
    }

    // public static function getFirstAddresses()
    // {
    //     return Address::where('addressable_type', User::class)
    //         ->whereHas('addressable', function ($query) {
    //             $query->whereHas('rider'); // Ensure the user has a rider
    //         })
    //         ->selectRaw('MIN(created_at) as first_created, address') // Get first created
    //         ->groupBy('address')
    //         ->orderBy('first_created', 'asc')
    //         ->pluck('address', 'address') // Returns ['Sousse' => 'Sousse']
    //         ->filter(); // Remove null values
    // }
    public static function getFirstAddresses()
    {
        return Address::where('addressable_type', User::class)
            ->whereHas('addressable', function ($query) {
                $query->whereHas('rider', function ($q) {
                    $q->withTrashed(); // ✅ Include soft-deleted riders
                });
            })
            ->selectRaw('MIN(created_at) as first_created, address') // ✅ Get first created address
            ->groupBy('address')
            ->orderBy('first_created', 'asc')
            ->pluck('address', 'address') // ✅ Returns ['Sousse' => 'Sousse']
            ->filter(); // ✅ Remove null values
    }
}
