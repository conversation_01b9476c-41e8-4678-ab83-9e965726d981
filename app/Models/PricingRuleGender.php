<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRuleGender extends Model
{
    use HasFactory;

    protected $fillable = [
        'gender',
        'base_fare_adjustment_type',
        'base_fare_fixed',
        'base_fare_percentage',
        'distance_fare_adjustment_type',
        'distance_fare_fixed',
        'distance_fare_percentage',
    ];

    protected $casts = [
        'gender' => \App\Enums\GenderEnum::class,
        'base_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
        'distance_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
    ];

    public function setBaseFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['base_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['base_fare_percentage'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['base_fare_fixed'] = 0;
        }
    }

    // Mutator for distance fare adjustment type
    public function setDistanceFareAdjustmentTypeAttribute($value): void
    {
        $this->attributes['distance_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['distance_fare_percentage'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['distance_fare_fixed'] = 0;
        }
    }

    public function getBaseFareValueAttribute(): string
    {
        return match ($this->base_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->base_fare_percentage ? "{$this->base_fare_percentage}%" : '-',
            'fixed' => $this->base_fare_fixed ? number_format($this->base_fare_fixed, 2).' LYD' : '-',
            default => '-',
        };
    }

    public function getDistanceFareValueAttribute(): string
    {
        return match ($this->distance_fare_adjustment_type?->value ?? null) {
            'percentage' => $this->distance_fare_percentage ? "{$this->distance_fare_percentage}%" : '-',
            'fixed' => $this->distance_fare_fixed ? number_format($this->distance_fare_fixed, 2).' LYD' : '-',
            default => '-',
        };
    }

    // Add accessor for base_fare_adjustment to maintain compatibility
    public function getBaseFareAdjustmentAttribute()
    {
        return $this->base_fare_adjustment_type === 'fixed' ||
               ($this->base_fare_adjustment_type?->value ?? null) === 'fixed'
               ? $this->base_fare_fixed
               : $this->base_fare_percentage;
    }

    // Add accessor for distance_fare_adjustment to maintain compatibility
    public function getDistanceFareAdjustmentAttribute()
    {
        return $this->distance_fare_adjustment_type === 'fixed' ||
               ($this->distance_fare_adjustment_type?->value ?? null) === 'fixed'
               ? $this->distance_fare_fixed
               : $this->distance_fare_percentage;
    }
}
