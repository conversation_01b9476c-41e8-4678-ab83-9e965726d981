<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PricingRules extends Model
{
    use HasFactory;

    protected $fillable = [
        'global_base_price',
        'global_price_per_km',
        'time_threshold_percentage',
        'fare_adjustment_type',
        'fare_adjustment',
        'distance_adjustment_type',
        'distance_adjustment',
        'seat_capacity_base_fare_adjustment_type',
        'seat_capacity_base_fare_adjustment',
        'seat_capacity_distance_based_pricing_adjustment_type',
        'seat_capacity_distance_based_pricing_adjustment',
    ];

    public function additionalDayCharges()
    {
        return $this->hasMany(PricingRuleAdditionalDayCharge::class, 'pricing_rule_id');
    }

    /**
     * Calculate the gender-based pricing for a vehicle.
     */
    public function calculateGenderBasedPricing(Vehicle $vehicle): float
    {
        if ($vehicle->is_female) {
            return $this->global_base_price + $this->fare_adjustment;
        }

        return $this->global_base_price;
    }

    /**
     * Calculate the seat capacity-based pricing for a vehicle.
     */
    public function calculateSeatCapacityPricing(Vehicle $vehicle, string $pricing_type = 'base'): float
    {
        $adjustment = 0;

        if ($pricing_type === 'base') {
            if ($this->seat_capacity_base_fare_adjustment_type === 'percentage') {
                $adjustment = ($this->seat_capacity_base_fare_adjustment / 100) * $this->global_base_price;
            } elseif ($this->seat_capacity_base_fare_adjustment_type === 'fixed') {
                $adjustment = $this->seat_capacity_base_fare_adjustment;
            }
        }

        if ($pricing_type === 'distance') {
            if ($this->seat_capacity_distance_based_pricing_adjustment_type === 'percentage') {
                $adjustment = ($this->seat_capacity_distance_based_pricing_adjustment / 100) * $this->global_price_per_km;
            } elseif ($this->seat_capacity_distance_based_pricing_adjustment_type === 'fixed') {
                $adjustment = $this->seat_capacity_distance_based_pricing_adjustment;
            }
        }

        return $adjustment;
    }
}
