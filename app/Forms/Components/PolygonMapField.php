<?php

namespace App\Forms\Components;

use Closure;
use Filament\Forms\Components\Field;

class PolygonMapField extends Field
{
    protected string $view = 'forms.components.polygon-map-field';

    protected array|Closure|null $existingPolygons = null;

    protected float $defaultLat = 32.8872;  // Default latitude for Libya

    protected float $defaultLng = 13.1913;  // Default longitude for Libya

    protected int $defaultZoom = 12;

    protected string $drawingColor = '#dd9f2b';

    protected string $existingPolygonsColor = '#666666';

    protected float $fillOpacity = 0.35;

    // Add this method to properly handle empty states for validation
    protected function isEmptyState(mixed $state): bool
    {
        if (is_array($state)) {
            return count($state) === 0;
        }

        return parent::isEmptyState($state);
    }

    public function existingPolygons(array|Closure|null $polygons): static
    {
        $this->existingPolygons = $polygons;

        return $this;
    }

    public function defaultCenter(float $lat, float $lng): static
    {
        $this->defaultLat = $lat;
        $this->defaultLng = $lng;

        return $this;
    }

    public function defaultZoom(int $zoom): static
    {
        $this->defaultZoom = $zoom;

        return $this;
    }

    public function drawingColor(string $color): static
    {
        $this->drawingColor = $color;

        return $this;
    }

    public function existingPolygonsColor(string $color): static
    {
        $this->existingPolygonsColor = $color;

        return $this;
    }

    public function fillOpacity(float $opacity): static
    {
        $this->fillOpacity = $opacity;

        return $this;
    }

    public function getExistingPolygons(): ?array
    {
        $existingPolygons = $this->evaluate($this->existingPolygons);

        return $existingPolygons;
    }

    public function getDefaultLat(): float
    {
        return $this->defaultLat;
    }

    public function getDefaultLng(): float
    {
        return $this->defaultLng;
    }

    public function getDefaultZoom(): int
    {
        return $this->defaultZoom;
    }

    public function getDrawingColor(): string
    {
        return $this->drawingColor;
    }

    public function getExistingPolygonsColor(): string
    {
        return $this->existingPolygonsColor;
    }

    public function getFillOpacity(): float
    {
        return $this->fillOpacity;
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->rules([
            function () {
                return function (string $attribute, $value, Closure $fail) {
                    // Allow empty polygons if applicable; adjust based on requirements
                    if (empty($value) || $value === '[]') {
                        return;
                    }

                    // Decode the polygon coordinates from JSON
                    $polygon = json_decode($value, true);
                    if (! is_array($polygon) || count($polygon) < 3) {
                        $fail('The polygon must have at least 3 points to be valid.');

                        return;
                    }

                    // Get existing polygons to check against
                    $existingPolygons = $this->getExistingPolygons();
                    if ($existingPolygons) {
                        foreach ($existingPolygons as $existingPolygon) {
                            if ($this->polygonsOverlap($polygon, $existingPolygon)) {
                                $fail('The polygon overlaps with an existing area.');

                                return;
                            }
                        }
                    }
                };
            },
        ]);
    }

    /**
     * Check if a point is inside a polygon using the ray-casting algorithm.
     */
    protected function pointInPolygon(array $point, array $polygon): bool
    {
        $x = $point['lng'];
        $y = $point['lat'];
        $inside = false;
        $n = count($polygon);

        for ($i = 0, $j = $n - 1; $i < $n; $j = $i++) {
            $xi = $polygon[$i]['lng'];
            $yi = $polygon[$i]['lat'];
            $xj = $polygon[$j]['lng'];
            $yj = $polygon[$j]['lat'];

            if ((($yi > $y) !== ($yj > $y)) &&
                ($x < $xi + ($xj - $xi) * ($y - $yi) / ($yj - $yi + 1e-10))) {
                $inside = ! $inside;
            }
        }

        return $inside;
    }

    /**
     * Check if two line segments intersect.
     */
    protected function lineSegmentsIntersect(array $a1, array $a2, array $b1, array $b2): bool
    {
        $denom = ($b2['lng'] - $b1['lng']) * ($a2['lat'] - $a1['lat']) -
                 ($b2['lat'] - $b1['lat']) * ($a2['lng'] - $a1['lng']);
        if (abs($denom) < 1e-10) {
            return false; // Lines are parallel
        }

        $ua = (($b2['lat'] - $b1['lat']) * ($a1['lng'] - $b1['lng']) -
               ($b2['lng'] - $b1['lng']) * ($a1['lat'] - $b1['lat'])) / $denom;
        $ub = (($a2['lat'] - $a1['lat']) * ($a1['lng'] - $b1['lng']) -
               ($a2['lng'] - $a1['lng']) * ($a1['lat'] - $b1['lat'])) / $denom;

        return $ua >= 0 && $ua <= 1 && $ub >= 0 && $ub <= 1;
    }

    /**
     * Check if two polygons overlap.
     */
    protected function polygonsOverlap(array $poly1, array $poly2): bool
    {
        // Check if any point of poly1 is inside poly2
        foreach ($poly1 as $point) {
            if ($this->pointInPolygon($point, $poly2)) {
                return true;
            }
        }

        // Check if any point of poly2 is inside poly1
        foreach ($poly2 as $point) {
            if ($this->pointInPolygon($point, $poly1)) {
                return true;
            }
        }

        // Check for edge intersections
        $n1 = count($poly1);
        $n2 = count($poly2);
        for ($i = 0; $i < $n1; $i++) {
            $a1 = $poly1[$i];
            $a2 = $poly1[($i + 1) % $n1];
            for ($j = 0; $j < $n2; $j++) {
                $b1 = $poly2[$j];
                $b2 = $poly2[($j + 1) % $n2];
                if ($this->lineSegmentsIntersect($a1, $a2, $b1, $b2)) {
                    return true;
                }
            }
        }

        return false;
    }
}
