<?php

namespace App\Livewire\Riders;

use App\Filament\Resources\Panel\RiderResource;
use App\Filament\Resources\Panel\TripResource;
use App\Models\Trip;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class RiderTripHistory extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public $rider;

    public function query()
    {
        return Trip::query()->when($this->rider, function ($query) {
            $query->where('rider_id', $this->rider->id ?? null);
        });
    }

    public function table(Table $table)
    {
        return $table
            ->query($this->query())
            ->defaultSort('created_at', 'desc')
            ->columns([
                TextColumn::make('id')
                    ->label('Trip Id'),

                TextColumn::make('created_at')
                    ->dateTime('F j, Y')
                    ->label('Date of the ride'),

                TextColumn::make('actual_departure_time')
                    ->label('Time of pickup')
                    ->getStateUsing(fn ($record) => $record->actual_departure_time ? $record->actual_departure_time->addHour()->format('H:i:s') : null),

                TextColumn::make('actual_arrival_time')
                    ->label('Time of drop-off')
                    ->getStateUsing(fn ($record) => $record->actual_arrival_time ? $record->actual_arrival_time->addHour()->format('H:i:s') : null),

                TextColumn::make('tripLocation.departure_address')
                    ->label('Pickup location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('tripLocation.arrival_address')
                    ->label('Drop-off location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('driver.user.full_name')
                    ->label('Driver name')
                    ->searchable(),

                TextColumn::make('vehicle.license_plate_number')
                    ->label('Vehicle license plate number')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->badge(),

                TextColumn::make('status')
                    ->label('Ride Status')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->badge()
                    ->icon(fn ($record) => $record->status?->getIcon())
                    ->color(fn ($record) => $record->status?->getColor())
                    ->getStateUsing(function ($record) {
                        if (! $record->status) {
                            return 'No Status Provided';
                        }

                        if ($record->status === \App\Enums\Trips\TripStatus::canceled && $record->cancelled_by) {
                            return $record->cancelled_by === 'driver'
                                ? 'Canceled by Driver'
                                : 'Canceled by Rider';
                        }

                        return $record->status->getLabel();
                    }),

                TextColumn::make('duration')
                    ->label('Trip Duration')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->badge()
                    ->getStateUsing(function ($record) {
                        if (! $record->actual_departure_time || ! $record->actual_arrival_time) {
                            return null;
                        }

                        $departure_time = \Carbon\Carbon::parse($record->actual_departure_time);
                        $arrival_time = \Carbon\Carbon::parse($record->actual_arrival_time);
                        $duration = $departure_time->diff($arrival_time);

                        return "{$duration->h} h {$duration->i} mins";
                    }),

                TextColumn::make('fare')
                    ->getStateUsing(function ($record) {
                        if (! $record->pricing_breakdown) {
                            return null;
                        }

                        $total = json_decode($record->pricing_breakdown, true);

                        return isset($total['total']) ? $total['total'].' LYD' : null;
                    })
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label('Total Fare'),

                TextColumn::make('rider_notes')
                    ->label('Notes of trip')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->limit(25),

                TextColumn::make('women_only_service')
                    ->label('Women-Only Service')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->badge()
                    ->getStateUsing(fn ($record) => $record->user?->preferences?->driver_gender === 'female' ? 'Active' : 'Not Active')
                    ->color(fn ($record) => $record->user?->preferences?->driver_gender === 'female' ? 'success' : 'danger'),

            ])
            ->actions([
                ViewAction::make()
                    ->label('View Trip')
                    ->url(fn (Model $record): string => RiderResource::getUrl('view_trip', [
                        'record' => $this->rider->id,
                        'tripId' => $record->id,
                    ])),
                // ->url(fn (Model $record): string => TripResource::getUrl('view', ['record' => $record])),
            ]);
    }

    public function render()
    {
        return view('livewire.riders.RiderTripHistory');
    }
}
