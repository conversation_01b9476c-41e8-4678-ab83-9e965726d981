<?php

namespace App\Livewire\Drivers;

use App\Filament\Resources\Panel\DriverResource;
use App\Models\Trip;
use App\Services\Pricing\PricingResult;
use App\Traits\HasTripStatusColumn;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Livewire\Component;

class Trips extends Component implements HasForms, HasTable
{
    use HasTripStatusColumn;
    use InteractsWithForms;
    use InteractsWithTable;

    public $driver;

    public function query()
    {
        // Ensure the driver ID is available before filtering
        return Trip::query()
            ->when($this->driver, function ($query) {
                $query->where('driver_id', $this->driver['id'] ?? $this->driver);
            })
            ->orderBy('created_at', 'desc'); // Sort by creation date, newest first
    }

    public function table(Table $table)
    {
        return $table
            ->query($this->query())
            ->columns([
                TextColumn::make('id')
                    ->label('Trip Id')
                    ->searchable(),

                TextColumn::make('created_at')
                    ->label('Date of the ride')
                    ->date()
                    ->label('Date'),

                TextColumn::make('pickup_time') // Virtual column
                    ->label('Time of pickup')
                    ->getStateUsing(fn ($record) => $record->actual_departure_time ? $record->actual_departure_time->addHour()->format('H:i:s') : '-'),

                TextColumn::make('dropoff_time') // Virtual column
                    ->label('Time of drop-off')
                    ->getStateUsing(fn ($record) => $record->actual_arrival_time ? $record->actual_arrival_time->addHour()->format('H:i:s') : '-'),

                TextColumn::make('tripLocation.departure_address')
                    ->label('Pickup location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('tripLocation.arrival_address')
                    ->label('Drop-off location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('rider.user.full_name')
                    // ->url(fn ($record) => RiderResource::getUrl('view', ['record' => $record->rider]))
                    ->label('Rider')
                    ->searchable(),

                self::getTripStatusColumn('status', 'Trip Status'),

                TextColumn::make('duration')
                    ->label('Trip duration')
                    ->getStateUsing(function ($record) {
                        // Only calculate duration for completed trips with both departure and arrival times
                        if ($record->actual_departure_time && $record->actual_arrival_time) {
                            $departureTime = $record->actual_departure_time;
                            $arrivalTime = $record->actual_arrival_time;

                            // Calculate the difference in minutes
                            $durationInMinutes = $departureTime->diffInMinutes($arrivalTime);

                            // Format as hours and minutes
                            $hours = floor($durationInMinutes / 60);
                            $minutes = $durationInMinutes % 60;

                            if ($hours > 0) {
                                return $hours.'h '.$minutes.'m';
                            } else {
                                return $minutes.' minutes';
                            }
                        }

                        return '-';
                    }),

                // TODO: Add FARE column
                TextColumn::make('fare')
                    ->label('Total fare')
                    ->getStateUsing(function ($record) {
                        if (! $record->pricing_breakdown) {
                            return 'N/A';
                        }

                        $total = json_decode($record->pricing_breakdown, true);

                        // Check if total exists in the decoded JSON
                        if (! $total || ! isset($total['total'])) {
                            return 'N/A';
                        }

                        // Use PricingResult to apply rounding
                        $pricingResult = new PricingResult;
                        $pricingResult->setTotal($total['total']);
                        $pricingResult->applyRounding();

                        // Get the rounded total from the pricing result
                        $roundedTotal = $pricingResult->toArray()['total'];

                        return $roundedTotal.' LYD';
                    }),

                TextColumn::make('rider_notes')
                    ->label('Notes of trip'),

            ])
            ->actions([
                ViewAction::make()
                    ->label('View Trip')
                    ->url(fn (Model $record): string => DriverResource::getUrl('view_trip', [
                        'record' => $this->driver['id'] ?? $this->driver,
                        'tripId' => $record->id,
                    ])),
            ])
            ->defaultSort('created_at', 'desc'); // Ensure newest trips are shown first
    }

    public function render()
    {
        return view('livewire.drivers.trips');
    }
}
