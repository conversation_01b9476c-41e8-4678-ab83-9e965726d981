<?php

namespace App\Livewire\Drivers;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Filament\Tables\DriverRequest\RequestDriverVehicle;
use App\Filament\Tables\VehiclesTable;
use App\Models\Driver;
use App\Models\Vehicle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class Vehicles extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public $driver;

    public function table(Table $table)
    {
        // Fetch the driver's record
        $driverId = $this->driver['id'] ?? $this->driver;

        $driver = Driver::withTrashed()->find($driverId);

        // Return no results if the driver doesn't exist
        if (! $driver) {
            return $table->query(Vehicle::query()->whereRaw('1 = 0'));
        }

        // Build query: include soft-deleted vehicles associated with the driver
        $query = Vehicle::withTrashed()->whereHas('drivers', function ($query) use ($driverId) {
            $query->where('id', $driverId);
        });

        $hasVehicles = $query->exists();

        // Conditionally return the appropriate table
        if (! $hasVehicles || in_array($driver->global_status, [
            DriverGlobalStatus::in_progress,
            DriverGlobalStatus::pending,
            DriverGlobalStatus::deleted,
        ])) {
            $table = RequestDriverVehicle::make($table, $query);
        } else {
            $table = VehiclesTable::make($table, $query, 'drivers');

            // Add actions only if the driver is not in progress
            $table->actions([
                ViewAction::make()
                    ->url(fn (Vehicle $record): string => DriverResource::getUrl(
                        'view_vehicle',
                        [
                            'record' => $driver->id,
                            'vehicleId' => $record->id,
                        ]
                    )),
            ]);
        }

        return $table;
    }

    public function render()
    {
        return view('livewire.drivers.vehicles');
    }
}
