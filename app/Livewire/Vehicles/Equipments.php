<?php

namespace App\Livewire\Vehicles;

use App\Filament\Infolists\TripInfolist;
use App\Models\VehicleEquipment;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Infolist;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Guava\FilamentIconPicker\Tables\IconColumn;
use Livewire\Component;

class Equipments extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public $vehicle; // Store the vehicle record

    public function query()
    {
        return VehicleEquipment::query()
            ->whereHas('vehicles', function ($query) {
                $query->where('vehicles.id', $this->vehicle['id'] ?? $this->vehicle)
                      ->withTrashed(); // Include soft-deleted vehicles
            });
    }

    public function table(Table $table)
    {
        return $table
            ->query($this->query())
            ->columns([
                TextColumn::make('name_ar')->label('Equipment Name (Arabic)'),
                TextColumn::make('name_en')->label('Equipment Name (English)'),
                IconColumn::make('icon')->label('Icon'),
            ])
            // ->actions([
            //     ViewAction::make()
            //         ->modalHeading('Update Trip')
            //         ->infolist(fn (Infolist $infolist) => TripInfolist::infolist($infolist)),

            // ])
            ->paginated(false);
    }

    public function render()
    {
        return view('livewire.vehicles.equipments');
    }
}
