<?php

namespace App\Livewire\PricingRules;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Models\VehicleType;
use App\Rules\MinFareRule;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Livewire\Component;

class VehicleTypePricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public ?array $data = [];

    public VehicleType $record;

    public function table(Table $table): Table
    {
        return $table
            ->query(
                VehicleType::query()
                    ->whereNotIn('name_en', ['Default_freight_type', 'Default_passenger_type'])
            )
            ->columns([
                TextColumn::make('name_ar')
                    ->label('Vehicle Type (Arabic)'),

                TextColumn::make('base_fare_adjustment_type')
                    ->label('Base Fare Type')
                    ->badge(),
                TextColumn::make('base_fare_value')
                    ->label('Base Fare Adjustment'),
                TextColumn::make('distance_fare_adjustment_type')
                    ->label('Distance Fare Type')
                    ->badge(),
                TextColumn::make('distance_fare_value')
                    ->label('Distance Fare Adjustment'),
            ])
            ->actions([
                EditAction::make()
                    ->modalHeading('Update Vehicle Type Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Vehicle type rules updated successfully.'),
                    )
                    ->form([
                        Section::make(fn ($record) => "{$record->name_ar}")
                            ->schema([
                                ToggleButtons::make('base_fare_adjustment_type')
                                    ->label('Base Fare Type')
                                    ->inline()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->required()
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the base fare will be adjusted for this vehicle type.'),

                                TextInput::make('additional_base_fare')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('base')])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'lte' => 'The base fare must not exceed 100 LYD.',
                                    ])
                                    ->suffix('LYD')
                                    ->placeholder('e.g., 15.50')
                                    ->helperText('Enter the base fare specific to this vehicle type. Value must be non-negative.')
                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'fixed'),

                                TextInput::make('base_fare_adjustment')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'gte' => 'The base fare adjustment must be at least -50%.',
                                        'lte' => 'The base fare adjustment must not exceed 100%.',
                                    ])
                                    ->suffix('%')
                                    ->placeholder('e.g., -10.25 or 10.25')
                                    ->helperText('Enter the percentage adjustment.')
                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'percentage'),

                                ToggleButtons::make('distance_fare_adjustment_type')
                                    ->label('Distance Fare Type')
                                    ->inline()
                                    ->required()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the distance fare will be adjusted for this vehicle type.'),

                                TextInput::make('additional_price_per_km')
                                    ->label('Distance Fare Adjustment')
                                    ->suffix('LYD')
                                    ->default('0')
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('distance')])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'lte' => 'The distance fare must not exceed 100 LYD.',
                                    ])
                                    ->placeholder('e.g., 2.50')
                                    ->helperText('Enter the per-unit distance fare for this vehicle type.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'fixed'),

                                TextInput::make('distance_fare_adjustment')
                                    ->label('Distance Fare Adjustment')
                                    ->default('0')
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'gte' => 'The distance fare adjustment must be at least -50%.',
                                        'lte' => 'The distance fare adjustment must not exceed 100%.',
                                    ])
                                    ->suffix('%')
                                    ->placeholder('e.g., -10.25 or 10.25')
                                    ->helperText('Enter the percentage adjustment for the distance fare.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'percentage'),
                            ])->columns(2),
                    ]),
            ])
            // ->filters([
            //     SelectFilter::make('category')
            //         ->label('Category')
            //         ->options(
            //             collect(VehicleTypesCategories::cases())
            //                 ->mapWithKeys(fn ($category) => [$category->value => $category->getLabel()])
            //                 ->toArray()
            //         )
            //         ->native(false),
            //     Filter::make('is_covered')
            //         ->label('Is Covered')
            //         ->toggle(),
            //     SelectFilter::make('weight_category')
            //         ->label('Weight')
            //         ->options(
            //             collect(WeightCategoryEnum::cases())
            //                 ->mapWithKeys(fn ($category) => [$category->value => $category->getLabel()])
            //                 ->toArray()
            //         )
            //         ->native(false),
            // ])
            ->paginated(false)
            ->bulkActions([

            ]);
    }

    public function save(): void
    {
        $data = $this->form->getState();

        $this->record->update($data);

        Notification::make()
            ->title('Update Successful')
            ->body('Vehicle type pricing has been updated successfully.')
            ->success()
            ->send();

        $this->emit('refreshTable');
    }

    public function render()
    {
        return view('livewire.pricing-rules.vehicle-type-pricing');
    }
}
