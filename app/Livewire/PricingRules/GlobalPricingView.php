<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRules;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\FontWeight;
use Livewire\Component;

class GlobalPricingView extends Component implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    public PricingRules $record;

    public function mount(): void
    {
        // Get or create the pricing rules record
        $this->record = PricingRules::firstOrCreate(
            ['id' => 1],
            [
                'global_base_price' => 5.00,
                'global_price_per_km' => 5.00,
                'time_threshold_percentage' => 0.00,
            ]
        );
    }
    protected $listeners = ['pricing-rules-updated' => 'refreshData'];

    public function refreshData()
    {
        $this->record = PricingRules::firstOrCreate(
            ['id' => 1],
            [
                'global_base_price' => 5.00,
                'global_price_per_km' => 5.00,
                'time_threshold_percentage' => 0.00,
            ]
        );
    }

    public function globalRulesInfolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->record)
            ->schema([
                Section::make('Global Rules')
                    ->schema([
                        TextEntry::make('global_base_price')
                            ->label('Base Fare (B)')
                            ->weight(FontWeight::Bold)
                            ->color('success')
                            ->size(TextEntry\TextEntrySize::Large)
                            ->icon('heroicon-o-banknotes')
                            ->iconColor('success')
                            ->helperText('Fixed starting fee for all rides')
                            ->formatStateUsing(fn ($state) => number_format((float) $state, 2).' LYD')
                            ->columnSpan(['lg' => 1]),

                        TextEntry::make('global_price_per_km')
                            ->label('Per Kilometer Rate (D)')
                            ->weight(FontWeight::Bold)
                            ->color('primary')
                            ->size(TextEntry\TextEntrySize::Large)
                            ->icon('mdi-road-variant')
                            ->iconColor('primary')
                            ->helperText('Cost per kilometer traveled')
                            ->formatStateUsing(fn ($state) => number_format((float) $state, 2).' LYD/km')
                            ->columnSpan(['lg' => 1]),

                        TextEntry::make('time_threshold_percentage')
                            ->label('Time Threshold (T)')
                            ->weight(FontWeight::Bold)
                            ->color('warning')
                            ->size(TextEntry\TextEntrySize::Large)
                            ->iconColor('warning')
                            ->helperText('Allowed duration deviation before penalties')
                            ->formatStateUsing(fn ($state) => number_format((float) $state, 2).'%')
                            ->columnSpan(['lg' => 1]),
                    ])->columns(['lg' => 3]),
            ]);
    }
}
