<?php

namespace App\Livewire\PricingRules;

use App\Enums\GenderEnum;
use App\Models\PricingRuleGender;
use App\Rules\MinFareRule;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class GenderBasedPricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(PricingRuleGender::query())
            ->columns([

                TextColumn::make('gender')
                    ->label('Gender')
                    ->formatStateUsing(fn (GenderEnum $state) => $state === GenderEnum::female ? 'أنثى' : 'ذكر')
                    ->badge(),
                TextColumn::make('base_fare_adjustment_type')
                    ->label('Base Fare Type')
                    ->badge(),
                TextColumn::make('base_fare_value')
                    ->label('Base Fare Adjustment'),
                TextColumn::make('distance_fare_adjustment_type')
                    ->label('Distance Fare Type')
                    ->badge(),
                TextColumn::make('distance_fare_value')
                    ->label('Distance Fare Adjustment'),

            ])
            ->filters([
                // ...
            ])
            ->actions([
                EditAction::make()
                    ->modalHeading('Update Gender Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Gender pricing rules updated successfully.'),
                    )
                    ->form([
                        Section::make(fn ($record) => $record->gender === GenderEnum::female ? 'أنثى' : 'ذكر')
                            ->schema([
                                ToggleButtons::make('base_fare_adjustment_type')
                                    ->label('Base Fare Type')
                                    ->inline()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the base fare will be adjusted.'),

                                TextInput::make('base_fare_fixed')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('LYD')
                                    ->placeholder('e.g., 15.50')
                                    ->helperText('Enter the base fare for the gender configuration.')
                                    ->visible(fn (callable $get) => $get('base_fare_adjustment_type') === 'fixed')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('base')])
                                    ->validationMessages([
                                        'lte' => 'The base fare must not exceed 100 LYD.',
                                    ]),

                                TextInput::make('base_fare_percentage')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->placeholder('e.g., 10.25')
                                    ->helperText('Enter the percentage adjustment for the base fare.')
                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'percentage')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->validationMessages([
                                        'gte' => 'The base fare adjustment must be at least -50%.',
                                        'lte' => 'The base fare adjustment must not exceed 100%.',
                                    ]),

                                ToggleButtons::make('distance_fare_adjustment_type')
                                    ->label('Distance Fare Type')
                                    ->inline()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the distance fare will be adjusted.'),

                                TextInput::make('distance_fare_fixed')
                                    ->label('Distance Fare Adjustment')
                                    ->suffix('LYD')
                                    ->default('0')
                                    ->placeholder('e.g., 2.50')
                                    ->helperText('Enter the per-unit distance fare for the gender configuration.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'fixed')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('distance')])
                                    ->validationMessages([
                                        'lte' => 'The distance fare must not exceed 100 LYD.',
                                    ]),

                                TextInput::make('distance_fare_percentage')
                                    ->label('Distance Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->validationMessages([
                                        'gte' => 'The distance fare adjustment must be at least -50%.',
                                        'lte' => 'The distance fare adjustment must not exceed 100%.',
                                    ])
                                    ->placeholder('e.g., 15.75')
                                    ->helperText('Enter the percentage adjustment for the distance fare.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'percentage'),

                            ])->columns(2),

                    ]),
            ])
            ->paginated(false)
            ->bulkActions([
                // ...
            ]);
    }

    public function render()
    {
        return view('livewire.pricing-rules.seat-capacity-pricing');
    }
}
