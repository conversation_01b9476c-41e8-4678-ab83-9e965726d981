<?php

namespace App\Livewire\PricingRules;

use App\Models\VehicleEquipment;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class EquipmentBasedPricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(VehicleEquipment::query())
            ->columns([
                TextColumn::make('name_ar')
                    ->label('Equipment Name (Arabic)'),
                TextColumn::make('additional_fare')
                    ->label('Additional Fare Adjustment')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 2).' LYD' : '-'),

            ])
            ->filters([
                // ...
            ])
            ->actions([
                EditAction::make()
                    ->modalHeading('Update Equipment Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Vehicle equipment pricing rules updated successfully.'),
                    )
                    ->form([
                        Section::make(fn ($record) => $record->name_ar)

                            ->schema([
                                TextInput::make('additional_fare')
                                    ->label('Additional Fare Adjustment')
                                    ->default(0)
                                    ->suffix('LYD')
                                    ->minValue(function ($get) {
                                        $pricingRule = \App\Models\PricingRules::first(); // Fetch global pricing rule
                                        $basePrice = $pricingRule?->global_base_price ?? 0; // Get base price or default to 0

                                        return -($basePrice / 2); // Calculate minimum value
                                    })
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->validationMessages([
                                        'gte' => 'The additional fare must be at least -50 LYD.',
                                        'lte' => 'The additional fare must not exceed 100 LYD.',
                                    ])
                                    ->placeholder('Enter additional base fare'),
                            ]),

                    ]),
            ])
            ->bulkActions([
                // ...
            ]);
    }

    public function render()
    {
        return view('livewire.pricing-rules.area-specific-pricing');
    }
}
