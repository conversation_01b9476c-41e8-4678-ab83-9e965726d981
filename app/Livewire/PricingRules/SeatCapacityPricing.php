<?php

namespace App\Livewire\PricingRules;

use App\Models\PricingRuleSeatNumber;
use App\Rules\MinFareRule;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;

class SeatCapacityPricing extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(PricingRuleSeatNumber::query())
            ->columns([
                TextColumn::make('seats_number')
                    ->label('Seats Number'),

                TextColumn::make('base_fare_adjustment_type')
                    ->label('Base Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state->getLabel()),

                TextColumn::make('base_fare_value')
                    ->label('Base Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->base_fare_value;
                    }),

                TextColumn::make('distance_fare_adjustment_type')
                    ->label('Distance Fare Type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state->getLabel()),

                TextColumn::make('distance_fare_value')
                    ->label('Distance Fare Adjustment')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->distance_fare_value;
                    }),
            ])
            ->filters([
                // ...
            ])
            ->actions([
                EditAction::make()
                    ->modalHeading('Update Seat Capacity Pricing')
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title('Update Successful')
                            ->body('Seat capacity pricing rules updated successfully.'),
                    )
                    ->form([
                        Section::make(fn ($record) => "{$record->seats_number} Seats")
                            ->schema([
                                ToggleButtons::make('base_fare_adjustment_type')
                                    ->label('Base Fare Type')
                                    ->inline()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the base fare will be adjusted.'),

                                TextInput::make('base_fare')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('LYD')
                                    ->placeholder('e.g., 15.50')
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('base')])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'lte' => 'The base fare must not exceed 100 LYD.',
                                    ])
                                    ->helperText('Enter the base fare for the seat configuration.')
                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'fixed'),

                                TextInput::make('base_fare_adjustment')
                                    ->label('Base Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'gte' => 'The base fare adjustment must be at least -50%.',
                                        'lte' => 'The base fare adjustment must not exceed 100%.',
                                    ])
                                    ->placeholder('e.g., 10.25')
                                    ->helperText('Enter the percentage adjustment for the base fare.')
                                    ->visible(fn ($get) => $get('base_fare_adjustment_type') === 'percentage'),

                                ToggleButtons::make('distance_fare_adjustment_type')
                                    ->label('Distance Fare Type')
                                    ->inline()
                                    ->options([
                                        'fixed' => 'Fixed',
                                        'percentage' => 'Percentage',
                                    ])
                                    ->grouped()
                                    ->reactive()
                                    ->helperText('Choose how the distance fare will be adjusted.'),

                                TextInput::make('distance_fare')
                                    ->label('Distance Fare Adjustment')
                                    ->suffix('LYD')
                                    ->default('0')
                                    ->rules(['required', 'numeric', 'lte:100', new MinFareRule('distance')])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'lte' => 'The distance fare must not exceed 100 LYD.',
                                    ])
                                    ->placeholder('e.g., 2.50')
                                    ->helperText('Enter the per-unit distance fare for the seat configuration.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'fixed'),

                                TextInput::make('distance_fare_adjustment')
                                    ->label('Distance Fare Adjustment')
                                    ->default('0')
                                    ->suffix('%')
                                    ->rules(['required', 'numeric', 'gte:-50', 'lte:100'])
                                    ->extraInputAttributes([
                                        'inputmode' => 'decimal',
                                        'pattern' => '[0-9.-]*',
                                        'maxlength' => 6,
                                        'oninput' => "this.value = this.value.replace(/[^0-9.-]/g, '').replace(/(\..*)\./g, '$1').replace(/^(-?)(.*)$/, (match, p1, p2) => p1 + p2.replace(/-/g, ''));", // Only allow one negative sign at the start
                                    ])
                                    ->validationMessages([
                                        'gte' => 'The distance fare must be at least -50%.',
                                        'lte' => 'The distance fare must not exceed 100%.',
                                    ])
                                    ->placeholder('e.g., 15.75')
                                    ->helperText('Enter the percentage adjustment for the distance fare.')
                                    ->visible(fn ($get) => $get('distance_fare_adjustment_type') === 'percentage'),

                                Select::make('seats_number')
                                    ->label('Seats Number')
                                    ->options([
                                        2 => '2 Seats',
                                        4 => '4 Seats',
                                        6 => '6 Seats',
                                    ])
                                    ->hidden()
                                    ->required()
                                    ->helperText('Choose the number of seats for this pricing rule.'),

                            ])->columns(2),

                    ]),
            ])
            ->paginated(false)
            ->bulkActions([
                // ...
            ]);
    }

    public function render()
    {
        return view('livewire.pricing-rules.seat-capacity-pricing');
    }
}
