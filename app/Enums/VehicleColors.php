<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum VehicleColors: string implements HasLabel
{
    case white = 'white';
    case yellow = 'yellow';
    case blue = 'blue';
    case gray = 'gray';
    case black = 'black';
    case red = 'red';
    case orange = 'orange';
    case brown = 'brown';
    case beige = 'beige';
    case darkGrey = 'dark grey';
    case bronze = 'bronze';
    case lightBlue = 'light blue';
    case maroon = 'maroon';
    case navyBlue = 'navy blue';
    case pearl = 'pearl';
    case pearlWhite = 'pearl white';
    case pink = 'pink';
    case purple = 'purple';
    case silver = 'silver';
    case skyBlue = 'sky blue';
    case wine = 'wine';
    case wineRed = 'wine red';

    public function getLabel(): string
    {
        return match ($this) {
            self::white => 'White',
            self::yellow => 'Yellow',
            self::blue => 'Blue',
            self::gray => 'Gray',
            self::black => 'Black',
            self::red => 'Red',
            self::orange => 'Orange',
            self::brown => 'Brown',
            self::beige => 'Beige',
            self::darkGrey => 'Dark Grey',
            self::bronze => 'Bronze',
            self::lightBlue => 'Light Blue',
            self::maroon => 'Maroon',
            self::navyBlue => 'Navy Blue',
            self::pearl => 'Pearl',
            self::pearlWhite => 'Pearl White',
            self::pink => 'Pink',
            self::purple => 'Purple',
            self::silver => 'Silver',
            self::skyBlue => 'Sky Blue',
            self::wine => 'Wine',
            self::wineRed => 'Wine Red',
        };
    }

    public static function getValues(): array
    {
        return array_map(fn ($enum) => $enum->value, self::cases());
    }
}
