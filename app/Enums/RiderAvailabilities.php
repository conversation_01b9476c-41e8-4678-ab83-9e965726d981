<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum RiderAvailabilities: string implements HasColor, HasIcon, HasLabel
{
    case busy = 'busy';
    case online = 'online';

    public function getLabel(): string
    {
        return match ($this) {
            self::busy => 'Busy',
            self::online => 'Online',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::online => 'success',
            self::busy => 'warning',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::online => 'heroicon-o-check',
            self::busy => 'heroicon-o-minus-circle',
        };
    }
}
