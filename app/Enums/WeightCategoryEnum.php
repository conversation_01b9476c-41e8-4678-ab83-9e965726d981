<?php

namespace App\Enums;

use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum WeightCategoryEnum: string implements HasIcon, HasLabel
{
    case LessThan1000kg = 'less_than_1000kg';
    case MoreThan1000kg = 'more_than_1000kg';

    public function getLabel(): string
    {
        return match ($this) {
            self::LessThan1000kg => 'Less than 1000kg',
            self::MoreThan1000kg => 'More than 1000kg',
        };
    }

    // public function getColor(): string|array|null
    // {
    //     return match ($this) {
    //         self::LessThan1000kg => 'primary',
    //         self::MoreThan1000kg => 'primary',
    //     };
    // }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::LessThan1000kg => 'mdi-weight-kilogram',
            self::MoreThan1000kg => 'mdi-weight-kilogram',
        };
    }
}
