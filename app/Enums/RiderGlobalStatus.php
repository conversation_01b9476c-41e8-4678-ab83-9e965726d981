<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum RiderGlobalStatus: string implements HasColor, HasIcon, HasLabel
{
    case pending = 'pending';
    case active = 'active';
    case blocked = 'blocked';
    case deleted = 'deleted';

    public function getLabel(): string
    {
        return match ($this) {
            self::pending => 'Pending',
            self::active => 'Active',
            self::blocked => 'Blocked',
            self::deleted => 'Deleted',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::pending => 'gray',
            self::active => 'success',
            self::blocked => 'danger',
            self::deleted => 'warning',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::pending => 'heroicon-o-clock',
            self::active => 'heroicon-o-check',
            self::blocked => 'heroicon-o-no-symbol',
            self::deleted => 'heroicon-o-trash',
        };
    }

    public static function colors(): array
    {
        return collect(self::cases())
            ->reject(fn ($status) => $status === self::deleted)
            ->mapWithKeys(fn ($status) => [$status->value => $status->getColor()])
            ->toArray();
    }
}
