<?php

namespace App\Enums\Trips;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum CancellationStage: string implements HasColor, HasIcon, HasLabel
{
    case afterDispatch = 'afterDispatch';
    case afterAssigned = 'afterAssigned';
    case onPickup = 'onPickup';
    case onTrip = 'onTrip';

    public function getLabel(): string
    {
        return match ($this) {
            self::afterDispatch => 'After Dispatch',
            self::afterAssigned => 'After Assigned',
            self::onPickup => 'On Pickup',
            self::onTrip => 'On Trip',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::afterDispatch => 'primary',
            self::afterAssigned => 'warning',
            self::onPickup => 'info',
            self::onTrip => 'success',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::afterDispatch => 'heroicon-o-clock',
            self::afterAssigned => 'heroicon-o-check-circle',
            self::onPickup => 'heroicon-o-truck',
            self::onTrip => 'heroicon-o-map-pin',
        };
    }
}
