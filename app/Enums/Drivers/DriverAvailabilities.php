<?php

namespace App\Enums\Drivers;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum DriverAvailabilities: string implements HasColor, HasIcon, HasLabel
{
    case available = 'available';
    case busy = 'busy';
    case off_duty = 'off-duty';

    public function getLabel(): string
    {
        return match ($this) {
            self::available => 'Available',
            self::busy => 'Busy',
            self::off_duty => 'Off Duty',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::available => 'success',
            self::busy => 'warning',
            self::off_duty => 'info',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::available => 'heroicon-o-check',
            self::busy => 'heroicon-o-x-mark',
            self::off_duty => 'heroicon-o-moon',
        };
    }
}
