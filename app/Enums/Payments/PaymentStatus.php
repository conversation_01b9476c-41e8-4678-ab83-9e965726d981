<?php

namespace App\Enums\Payments;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum PaymentStatus: string implements HasColor, HasIcon, HasLabel
{
    case pending = 'pending';
    case completed = 'completed';
    case failed = 'failed';
    case refunded = 'refunded';

    public function getLabel(): string
    {
        return match ($this) {
            self::pending => 'Pending',
            self::completed => 'Completed',
            self::failed => 'Failed',
            self::refunded => 'Refunded',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::pending => 'warning',
            self::completed => 'success',
            self::failed => 'danger',
            self::refunded => 'info',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::pending => 'heroicon-o-clock',
            self::completed => 'heroicon-o-check',
            self::failed => 'heroicon-o-x-circle',
            self::refunded => 'heroicon-o-receipt-refund',
        };
    }
}
