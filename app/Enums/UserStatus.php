<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum UserStatus: string implements HasLabel
{
    case ONLINE = 'online';
    case OFFLINE = 'offline';
    case BUSY = 'busy';

    public function getLabel(): string
    {
        return match ($this) {
            self::ONLINE => 'Online',
            self::OFFLINE => 'Offline',
            self::BUSY => 'Busy',
        };
    }
}
