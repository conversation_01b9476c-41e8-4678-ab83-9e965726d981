<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum GenderEnum: string implements HasLabel
{
    case female = 'female';
    case male = 'male';

    public function getLabel(): string
    {
        return match ($this) {
            self::female => 'Female',
            self::male => 'Male',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::female => 'mdi-human-female',
            self::male => 'mdi-human-male',
        };
    }
}
