<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum VehicleTypesCategories: string implements HasLabel
{
    case Passenger = 'passenger';
    case Freight = 'freight';

    public function getLabel(): string
    {
        return match ($this) {
            self::Passenger => 'Passenger',
            self::Freight => 'Freight',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Passenger => 'mdi-car-side',
            self::Freight => 'heroicon-m-truck',
        };
    }
}
