<?php

namespace App\Enums\Vehicles;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum VehicleStatus: string implements HasColor, HasIcon, HasLabel
{
    case pending = 'pending';
    case in_progress = 'in_progress';
    case rejected = 'rejected';
    case active = 'active';
    case blocked = 'blocked';
    case deleted = 'deleted';

    public function getLabel(): string
    {
        return match ($this) {
            self::pending => 'Pending',
            self::in_progress => 'In Progress',
            self::rejected => 'Rejected',
            self::active => 'Active',
            self::blocked => 'Blocked',
            self::deleted => 'Deleted',
        };
    }

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::pending => 'gray',
            self::in_progress => 'info',
            self::rejected => 'danger',
            self::active => 'success',
            self::blocked => 'danger',
            self::deleted => 'warning',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::pending => 'heroicon-o-clock',
            self::in_progress => 'heroicon-o-arrow-right',
            self::rejected => 'heroicon-o-x-mark',
            self::active => 'heroicon-o-check',
            self::blocked => 'heroicon-o-no-symbol',
            self::deleted => 'heroicon-o-trash',
        };
    }
}
