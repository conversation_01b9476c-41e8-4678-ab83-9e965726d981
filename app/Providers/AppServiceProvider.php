<?php

namespace App\Providers;

use App\Models\Driver;
use App\Models\TripRating;
use App\Models\User;
use App\Observers\DriverObserver;
use App\Observers\RideRating;
use App\Observers\UserObserver;
use Filament\Support\Colors\Color;
use Filament\Support\Facades\FilamentColor;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Sanctum\Sanctum;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Driver::observe(DriverObserver::class);
        TripRating::observe(RideRating::class);

        FilamentColor::register([
            // 🟣 Extra custom colors used in your enum:
            'purple' => Color::hex('#A020F0'),
            'red' => Color::hex('#D04848'),
            'green' => Color::hex('#4CAF50'),
            'amber' => Color::hex('#FF9800'),
            'teal' => Color::hex('#009688'),
            'orange' => Color::hex('#FF5722'),
            'grey' => Color::hex('#9E9E9E'),

        ]);
        User::observe(UserObserver::class);

        // Register the logo-small component
        Blade::component('logo-small', \App\View\Components\LogoSmall::class);

        Gate::define('viewPulse', function (User $user) {
            return $user->hasRole('super_admin');
        });
        Gate::define('viewApiDocs', function (User $user) {
            if (! $user) {
                return false;
            }

            if ($user->type === 'admin') {
                return true;
            }

            $allowedDomains = ['@zaho.ly', '@satoripop.com', '@alsahlgroup.com'];
            foreach ($allowedDomains as $domain) {
                if (str_ends_with($user->email, $domain)) {
                    return true;
                }
            }

            return false;
        });
        $this->overrideSanctumConfigurationToSupportRefreshToken();
    }

    private function overrideSanctumConfigurationToSupportRefreshToken(): void
    {
        Sanctum::$accessTokenAuthenticationCallback = function ($accessToken, $isValid) {
            $abilities = collect($accessToken->abilities);
            if (! empty($abilities) && $abilities[0] === 'refresh_token') {
                return $accessToken->expires_at && $accessToken->expires_at->isFuture();
            }

            return $isValid;
        };

        Sanctum::$accessTokenRetrievalCallback = function ($request) {
            if (! $request->routeIs('refresh')) {
                return str_replace('Bearer ', '', $request->headers->get('Authorization'));
            }

            return $request->cookie('refreshToken') ?? '';
        };
    }
}
