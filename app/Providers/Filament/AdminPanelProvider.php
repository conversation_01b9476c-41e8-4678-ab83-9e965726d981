<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Auth\CustomLogin;
use App\Filament\Pages\Auth\PasswordReset\CustomResetPassword;
use App\Filament\Pages\Auth\RequestPasswordReset;
use App\Filament\Widgets\VehiclesRealTimeMovement;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use DiogoGPinto\AuthUIEnhancer\AuthUIEnhancerPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use Saade\FilamentLaravelLog\FilamentLaravelLogPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('')
            ->login(CustomLogin::class)
            ->passwordReset(RequestPasswordReset::class, CustomResetPassword::class)
            ->spa()
            ->spaUrlExceptions(fn (): array => [
                url('/panel/areas/*'),
                url('/panel/riders/*'),
                url('/panel/drivers/*'),
            ])
            ->globalSearch(false)
            ->colors([
                'primary' => '#f6b130',
            ])
            ->brandLogo(fn () => view('filament.logo.logo_white'))
            ->darkModeBrandLogo(fn () => view('filament.logo.logo_dark'))
            ->brandLogoHeight('3.5rem')
            ->favicon(asset('icons/logo-rakeb.svg'))
            ->globalSearchKeyBindings(['command+k', 'ctrl+k'])
            ->globalSearchFieldKeyBindingSuffix()
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->maxContentWidth(MaxWidth::Full)
            ->sidebarWidth('18rem')
            ->sidebarCollapsibleOnDesktop()
            ->databaseNotifications()
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->pages([
                // Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                VehiclesRealTimeMovement::class,
            ])
            ->broadcasting(true)
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->plugins([
                FilamentApexChartsPlugin::make(),
                FilamentLaravelLogPlugin::make()->authorize(function () {
                    return Auth::user()->hasRole('super_admin');
                }),
                FilamentShieldPlugin::make(),
                AuthUIEnhancerPlugin::make()
                    ->emptyPanelBackgroundImageUrl('https://upload.wikimedia.org/wikipedia/commons/8/8d/Tripoli_at_night.jpg'),
            ]);
    }
}
