<?php

namespace App\Support\Helpers;

use Carbon\Carbon;

class TimeHelper
{
    public static function generateTimeIntervals($start, $end, $intervalMinutes)
    {
        $times = [];
        $current = Carbon::createFromFormat('H:i', $start);
        $endTime = Carbon::createFromFormat('H:i', $end);

        while ($current <= $endTime) {
            $times[] = $current->format('H:i');
            $current->addMinutes($intervalMinutes);
        }

        return $times;
    }
}
