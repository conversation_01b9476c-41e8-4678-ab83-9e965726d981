<?php

namespace App\Events\DriverEvents;

use App\Models\Driver;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DriverLocationUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public $driver;

    public $lng;

    public $lat;

    public function __construct(Driver $driver, $lat, $lng)
    {
        $this->lng = $lng;
        $this->lat = $lat;
        $this->driver = $driver;
        Log::info('driver-location-updated event created', [
            'driver_id' => $this->driver->id,
            'driver full_name ' => $this->driver->user->name,
            'lng' => $this->lng,
            'lat' => $this->lat,
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): PrivateChannel
    {
        // here when we make a private channel laravel will broadcast on private-channel_name
        // so u need always to subscribe to private-channel_name
        return new PrivateChannel('driver-location.'.$this->driver->id);
    }

    public function broadcastWith(): array
    {
        return [
            'driver_id' => $this->driver->id,
            'longitude' => $this->lng,
            'latitude' => $this->lat,
        ];
    }

    public function broadcastAs(): string
    {
        return 'driver-location-updated';
    }
}
