<?php

namespace App\Events\DriverEvents;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SearshAvailableDrivers implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $data;

    public function __construct(array $data)
    {
        if (! isset($data['trip_id'])) {
            throw new \InvalidArgumentException('trip_id is required');
        }

        $this->data = [
            'trip_id' => (int) $data['trip_id'],
            'lng' => (float) $data['lng'],
            'lat' => (float) $data['lat'],
            'vehicle_category' => (string) $data['vehicle_category'],
            'rider_id' => (int) $data['rider_id'],
            'vehicle_type_id' => (int) $data['vehicle_type_id'],
        ];
    }

    public function broadcastOn(): Channel
    {
        return new Channel('find-driver');
    }

    public function broadcastAs()
    {
        return 'searching-drivers';
    }
}
