<?php

namespace App\Events\DriverEvents;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RideRequest implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $driverId;

    public $tripDetails;

    public $newRequest;

    public function __construct($driverId, array $tripDetails, bool $newRequest = false)
    {
        $this->driverId = $driverId;
        $this->tripDetails = $tripDetails;
        $this->newRequest = $newRequest;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('ride-request.'.$this->driverId);
    }

    public function broadcastWith(): array
    {
        // distance notes
        return [
            'rider' => [
                'id' => $this->tripDetails['rider']->id,
                'name' => $this->tripDetails['rider']->user->name,
                'last_name' => $this->tripDetails['rider']->user->name,
                'full_name' => $this->tripDetails['rider']->user->full_name,
                'gender' => $this->tripDetails['rider']->user->gender,
                'image' => $this->tripDetails['rider']->user->cover_picture
                    ? env('APP_URL', '/').'/storage/'.$this->tripDetails['rider']->user->cover_picture
                    : env('APP_URL', '/').'/images/avatar.png',
                'average_rider_rating' => $this->tripDetails['rider']->user->name,
                'phone_number' => $this->tripDetails['rider']->user->phone_number,
            ],
            'trip' => [
                'pricing' => $this->tripDetails['pricing'],
                'trip_location' => $this->tripDetails['trip_location'],
                'rider_notes' => $this->tripDetails['rider_notes'],
                'distance' => $this->tripDetails['distance'],
            ],
            'newRequest' => $this->newRequest,
        ];
    }

    public function broadcastAs()
    {
        return 'ride-request';
    }
}
