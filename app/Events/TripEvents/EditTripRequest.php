<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EditTripRequest implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $new_data;


    public function __construct(Trip $trip, array $new_data)
    {
        $this->trip = $trip;
        $this->new_data = $new_data;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastAs()
    {
        return 'edit-ride-request';
    }

    public function broadcastWith()
    {
        return [
            'new_data' => $this->new_data,
        ];
    }
}
