<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripTimeoutOccurred implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $driverId;

    public $message;

    public function __construct(Trip $trip, string $message, int $driverId)
    {
        $this->trip = $trip;
        $this->driverId = $driverId;
        $this->message = $message;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('ride-request.'.$this->driverId);
    }

    public function broadcastWith()
    {
        return [
            'trip_id' => $this->trip->id,
            'Message' => $this->message,
        ];
    }

    public function broadcastAs()
    {
        return 'trip_timeout';
    }
}
