<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CancellationLimitWarning implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $cancellationCount;

    public $remainingCancellations;

    public $warningMessage;

    public $trip;

    public $addressedUser;

    public function __construct(int $cancellationCount, int $remainingCancellations, string $warningMessage, Trip $trip, string $addressedUser)
    {
        $this->cancellationCount = $cancellationCount;
        $this->remainingCancellations = $remainingCancellations;
        $this->warningMessage = $warningMessage;
        $this->trip = $trip;
        $this->addressedUser = $addressedUser;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastWith(): array
    {
        return [
            'cancellationCount' => $this->cancellationCount,
            'remainingCancellations' => $this->remainingCancellations,
            'Message' => $this->warningMessage,
            'To' => $this->addressedUser,
        ];
    }

    public function broadcastAs(): string
    {
        return 'cancellation-warning-to-user';
    }
}
