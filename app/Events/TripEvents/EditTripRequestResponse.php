<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

// this response will be routed to the rider and the driver telling them that the ride edit request has been approved or rejected
class EditTripRequestResponse implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public Trip $trip,
        public string $response
    ) {}

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastAs()
    {
        return 'edit-ride-response';
    }

    public function broadcastWith()
    {
        return [
            'trip_id' => $this->trip->id,
            'response' => $this->response,
        ];
    }
}
