<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RequestResponse implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $response;

    public $driverId;

    public function __construct(Trip $trip, string $response, int $driverId)
    {
        $this->trip = $trip;
        $this->driverId = $driverId;
        $this->response = $response;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('ride-request.'.$this->driverId);
    }

    public function broadcastWith()
    {
        $data = [
            'driver_id' => $this->driverId,
            'trip' => [
                'id' => $this->trip->id,
                'estimated_departure_time' => $this->trip->estimated_departure_time,
                'estimated_arrival_time' => $this->trip->estimated_arrival_time,
            ],
            'response' => $this->response,
        ];
        Log::info("we'r broadcasting thoes data ", [$data]);

        return $data;
    }

    public function broadcastAs()
    {
        return 'ride-request-response';
    }
}
