<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StartTrip implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $status;

    public $message;

    public function __construct(Trip $trip, bool $status, string $message)
    {
        $this->trip = $trip;
        $this->status = $status;
        $this->message = $message;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastWith()
    {
        return [
            'trip' => $this->trip,
            'status' => $this->status,
            'Message' => $this->message,
        ];
    }

    public function broadcastAs()
    {
        return 'ride-started';
    }
}
