<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CancelRideRequest implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $cancelledBy;

    public $reason;

    public function __construct(Trip $trip, string $cancelledBy, string $reason)
    {
        $this->trip = $trip;
        $this->cancelledBy = $cancelledBy;
        $this->reason = $reason;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastWith(): array
    {
        return [
            'trip' => $this->trip,
            'cancelled_by' => $this->cancelledBy,
            'reason' => $this->reason,
        ];
    }

    public function broadcastAs(): string
    {
        return 'ride-cancelled';
    }
}
