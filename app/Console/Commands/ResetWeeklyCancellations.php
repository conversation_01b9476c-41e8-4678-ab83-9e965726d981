<?php

namespace App\Console\Commands;

use App\Models\TripCancellation;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ResetWeeklyCancellations extends Command
{
    protected $signature = 'app:reset-cancellations';

    protected $description = 'Reset weekly cancellation counts';

    public function handle(): void
    {
        $weekStart = Carbon::now()->startOfWeek(Carbon::SATURDAY);
        $weekEnd = clone $weekStart;
        $weekEnd->addDays(6)->endOfDay();

        TripCancellation::whereBetween('cancelled_at', [$weekStart, $weekEnd])
            ->where('counts_towards_limit', true)
            ->delete();

        $message = 'Successfully reset weekly cancellation counts';
        Log::info($message);
        $this->info($message);
    }
}
