<?php

namespace App\Console\Commands;

use App\Services\PricingService;
use Illuminate\Console\Command;

class TestPricingService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-pricing-service';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the refactored pricing service';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $this->info('Testing the refactored pricing service...');

            // Test data
            $googleData = ['distance_value' => 10000]; // 10 km
            $vehicleTypeIds = [1]; // Assuming vehicle type ID 1 exists
            $gender = 'male';
            $equipmentIds = [1]; // Assuming equipment ID 1 exists
            $startTime = now();
            $areaId = 1; // Assuming area ID 1 exists
            $estimatedArrivalTime = now()->addMinutes(30);
            $actualArrivalTime = now()->addMinutes(40); // 10 minutes late

            // First, test the interface and abstract class
            $this->info('Testing PricingFactorInterface and AbstractPricingFactor...');
            $interfacePath = app_path('Services/Pricing/Factors/PricingFactorInterface.php');
            $abstractPath = app_path('Services/Pricing/Factors/AbstractPricingFactor.php');

            if (file_exists($interfacePath)) {
                $this->info('PricingFactorInterface exists at: '.$interfacePath);
                $interfaceContent = file_get_contents($interfacePath);
                $this->info('Interface content length: '.strlen($interfaceContent).' bytes');
            } else {
                $this->error('PricingFactorInterface does not exist!');
            }

            if (file_exists($abstractPath)) {
                $this->info('AbstractPricingFactor exists at: '.$abstractPath);
                $abstractContent = file_get_contents($abstractPath);
                $this->info('Abstract class content length: '.strlen($abstractContent).' bytes');
            } else {
                $this->error('AbstractPricingFactor does not exist!');
            }

            // Test the GlobalPricingRules class
            $this->info('Testing GlobalPricingRules...');
            $globalRules = new \App\Services\Pricing\GlobalPricingRules;
            $this->info('Base fare: '.$globalRules->getBaseFare());
            $this->info('Distance per km: '.$globalRules->getDistancePerKm());
            $this->info('Time threshold percentage: '.$globalRules->getTimeThresholdPercentage());

            // Calculate prices
            $this->info('Calculating prices...');
            $result = PricingService::calculatePrices(
                $googleData,
                $vehicleTypeIds,
                $gender,
                $equipmentIds,
                $startTime,
                $areaId,
                $estimatedArrivalTime,
                $actualArrivalTime
            );

            // Display results
            $this->info('Pricing calculation results:');
            $this->table(
                ['Vehicle Type ID', 'Base Fare', 'Per KM', 'Distance', 'Subtotal', 'Total'],
                collect($result)->map(function ($item, $vehicleTypeId) {
                    return [
                        'Vehicle Type ID' => $vehicleTypeId,
                        'Base Fare' => $item['base_fare'] ?? 'N/A',
                        'Per KM' => $item['per_km'] ?? 'N/A',
                        'Distance' => $item['distance'] ?? 'N/A',
                        'Subtotal' => $item['subtotal'] ?? 'N/A',
                        'Total' => $item['total'] ?? 'N/A',
                    ];
                })->toArray()
            );

            $this->info('Test completed successfully!');
        } catch (\Exception $e) {
            $this->error('Error: '.$e->getMessage());
            $this->error('File: '.$e->getFile().' (Line: '.$e->getLine().')');
            $this->error('Trace:');
            $this->error($e->getTraceAsString());
        }
    }
}
