<?php

namespace App\Console\Commands;

use App\Models\Driver;
use Illuminate\Console\Command;

class SyncVehicleStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vehicles:sync-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync all vehicle statuses with their driver global statuses';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing vehicle statuses...');

        Driver::with('vehicles')->get()->each(function ($driver) {
            foreach ($driver->vehicles as $vehicle) {
                $vehicle->update([
                    'global_status' => $driver->global_status->value,
                ]);
            }
        });

        $this->info('Done!');
    }
}
