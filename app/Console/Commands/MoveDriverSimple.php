<?php

namespace App\Console\Commands;

use App\Models\Driver;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MoveDriverSimple extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'driver:move-simple {driver_id=2} {--steps=10} {--direction=horizontal}';

    /**
     * The console command description.
     */
    protected $description = 'Move driver in simple steps for testing vehicle tracking and direction';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $driverId = $this->argument('driver_id');
        $steps = $this->option('steps');
        $direction = $this->option('direction');

        $driver = Driver::find($driverId);

        if (! $driver) {
            $this->error("Driver with ID {$driverId} not found!");

            return 1;
        }

        // Get current location or set default
        $currentLocation = $this->getCurrentLocation($driver);
        $startLat = $currentLocation['lat'];
        $startLng = $currentLocation['lng'];
        $lat = $startLat;
        $lng = $startLng;

        $this->info("Moving Driver ID: {$driverId} in {$steps} steps");
        $this->info("Direction pattern: {$direction}");
        $this->info("Starting position: [{$lat}, {$lng}]");

        for ($i = 1; $i <= $steps; $i++) {
            // Calculate movement based on direction
            switch ($direction) {
                case 'horizontal':
                case 'left-right':
                    // Move east and west alternately
                    if ($i % 2 == 1) {
                        $lng += 0.0005; // Move east (~50 meters)
                        $directionText = 'east';
                    } else {
                        $lng -= 0.0005; // Move west (~50 meters)
                        $directionText = 'west';
                    }
                    break;

                case 'vertical':
                case 'up-down':
                    // Move north and south alternately
                    if ($i % 2 == 1) {
                        $lat += 0.0005; // Move north (~50 meters)
                        $directionText = 'north';
                    } else {
                        $lat -= 0.0005; // Move south (~50 meters)
                        $directionText = 'south';
                    }
                    break;

                case 'circle':
                    // Move in a small circle
                    $angle = ($i - 1) * (2 * M_PI / $steps);
                    $radius = 0.0005; // ~50 meter radius
                    $lat = $startLat + ($radius * cos($angle));
                    $lng = $startLng + ($radius * sin($angle));
                    $directionText = 'circular (angle: '.round(rad2deg($angle)).'°)';
                    break;

                default:
                    // Default: move north
                    $lat += 0.0005;
                    $directionText = 'north';
            }

            $this->updateDriverLocation($driver, $lat, $lng);
            $this->line("Step {$i}: Moved {$directionText} to [{$lat}, {$lng}]");

            // Wait 3 seconds between moves
            sleep(3);
        }

        $this->info("Movement completed! Driver moved {$steps} steps in {$direction} pattern.");

        return 0;
    }

    /**
     * Get current driver location
     */
    private function getCurrentLocation(Driver $driver): array
    {
        if ($driver->location) {
            $locationData = DB::select('
                SELECT ST_X(location::geometry) as lng, ST_Y(location::geometry) as lat
                FROM drivers
                WHERE id = ?
            ', [$driver->id]);

            if (! empty($locationData)) {
                return [
                    'lat' => (float) $locationData[0]->lat,
                    'lng' => (float) $locationData[0]->lng,
                ];
            }
        }

        // Default location (Tripoli, Libya)
        return [
            'lat' => 32.8872,
            'lng' => 13.1913,
        ];
    }

    /**
     * Update driver location in database
     */
    private function updateDriverLocation(Driver $driver, float $lat, float $lng): void
    {
        DB::update('
            UPDATE drivers 
            SET location = ST_GeomFromText(?, 4326), updated_at = NOW()
            WHERE id = ?
        ', ["POINT({$lng} {$lat})", $driver->id]);
    }
}
