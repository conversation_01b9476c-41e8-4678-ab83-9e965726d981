<?php

namespace App\Console\Commands;

use App\Models\Driver;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SimulateDriverMovement extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'driver:simulate-movement {driver_id=2} {--duration=60} {--speed=5} {--route=circle}';

    /**
     * The console command description.
     */
    protected $description = 'Simulate driver movement for testing vehicle tracking';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $driverId = $this->argument('driver_id');
        $duration = $this->option('duration'); // seconds
        $speed = $this->option('speed'); // updates per second
        $route = $this->option('route'); // circle, straight, random

        $driver = Driver::find($driverId);
        
        if (!$driver) {
            $this->error("Driver with ID {$driverId} not found!");
            return 1;
        }

        $this->info("Starting movement simulation for Driver ID: {$driverId}");
        $this->info("Duration: {$duration} seconds");
        $this->info("Update frequency: {$speed} updates/second");
        $this->info("Route pattern: {$route}");
        $this->info("Press Ctrl+C to stop...");

        // Get current location or set default (Libya coordinates)
        $currentLocation = $this->getCurrentLocation($driver);
        $startLat = $currentLocation['lat'];
        $startLng = $currentLocation['lng'];

        $this->info("Starting position: [{$startLat}, {$startLng}]");

        $totalUpdates = $duration * $speed;
        $updateInterval = 1000000 / $speed; // microseconds

        for ($i = 0; $i < $totalUpdates; $i++) {
            $progress = $i / $totalUpdates;
            $newLocation = $this->calculateNewPosition($startLat, $startLng, $progress, $route);
            
            // Update driver location in database
            $this->updateDriverLocation($driver, $newLocation['lat'], $newLocation['lng']);
            
            // Show progress
            if ($i % $speed == 0) { // Show progress every second
                $this->line("Update {$i}/{$totalUpdates} - Position: [{$newLocation['lat']}, {$newLocation['lng']}]");
            }
            
            // Sleep between updates
            usleep($updateInterval);
        }

        $this->info("Movement simulation completed!");
        return 0;
    }

    /**
     * Get current driver location
     */
    private function getCurrentLocation(Driver $driver): array
    {
        if ($driver->location) {
            $locationData = DB::select('
                SELECT ST_X(location::geometry) as lng, ST_Y(location::geometry) as lat
                FROM drivers
                WHERE id = ?
            ', [$driver->id]);

            if (!empty($locationData)) {
                return [
                    'lat' => (float) $locationData[0]->lat,
                    'lng' => (float) $locationData[0]->lng,
                ];
            }
        }

        // Default location (Tripoli, Libya)
        return [
            'lat' => 32.8872,
            'lng' => 13.1913,
        ];
    }

    /**
     * Calculate new position based on route pattern
     */
    private function calculateNewPosition(float $startLat, float $startLng, float $progress, string $route): array
    {
        switch ($route) {
            case 'circle':
                return $this->calculateCircularRoute($startLat, $startLng, $progress);
            
            case 'straight':
                return $this->calculateStraightRoute($startLat, $startLng, $progress);
            
            case 'random':
                return $this->calculateRandomRoute($startLat, $startLng, $progress);
            
            case 'city':
                return $this->calculateCityRoute($startLat, $startLng, $progress);
            
            default:
                return $this->calculateCircularRoute($startLat, $startLng, $progress);
        }
    }

    /**
     * Circular route around starting point
     */
    private function calculateCircularRoute(float $startLat, float $startLng, float $progress): array
    {
        $radius = 0.005; // ~500 meters radius
        $angle = $progress * 2 * M_PI; // Full circle
        
        $lat = $startLat + ($radius * cos($angle));
        $lng = $startLng + ($radius * sin($angle));
        
        return [
            'lat' => round($lat, 6),
            'lng' => round($lng, 6),
        ];
    }

    /**
     * Straight line route (north-south)
     */
    private function calculateStraightRoute(float $startLat, float $startLng, float $progress): array
    {
        $distance = 0.01; // ~1km total distance
        
        // Go north then return south
        $normalizedProgress = $progress * 2;
        if ($normalizedProgress > 1) {
            $normalizedProgress = 2 - $normalizedProgress; // Return journey
        }
        
        $lat = $startLat + ($distance * $normalizedProgress);
        $lng = $startLng;
        
        return [
            'lat' => round($lat, 6),
            'lng' => round($lng, 6),
        ];
    }

    /**
     * Random walk route
     */
    private function calculateRandomRoute(float $startLat, float $startLng, float $progress): array
    {
        static $currentLat = null;
        static $currentLng = null;
        
        if ($currentLat === null) {
            $currentLat = $startLat;
            $currentLng = $startLng;
        }
        
        // Random movement within bounds
        $maxStep = 0.0001; // ~10 meters max step
        $latStep = (rand(-100, 100) / 100) * $maxStep;
        $lngStep = (rand(-100, 100) / 100) * $maxStep;
        
        $currentLat += $latStep;
        $currentLng += $lngStep;
        
        // Keep within reasonable bounds of start position
        $maxDistance = 0.01; // ~1km from start
        if (abs($currentLat - $startLat) > $maxDistance) {
            $currentLat = $startLat + (($currentLat > $startLat) ? $maxDistance : -$maxDistance);
        }
        if (abs($currentLng - $startLng) > $maxDistance) {
            $currentLng = $startLng + (($currentLng > $startLng) ? $maxDistance : -$maxDistance);
        }
        
        return [
            'lat' => round($currentLat, 6),
            'lng' => round($currentLng, 6),
        ];
    }

    /**
     * City-like route with turns and stops
     */
    private function calculateCityRoute(float $startLat, float $startLng, float $progress): array
    {
        // Define a route with multiple waypoints (simulating city streets)
        $waypoints = [
            ['lat' => $startLat, 'lng' => $startLng], // Start
            ['lat' => $startLat + 0.003, 'lng' => $startLng], // North
            ['lat' => $startLat + 0.003, 'lng' => $startLng + 0.004], // East
            ['lat' => $startLat + 0.006, 'lng' => $startLng + 0.004], // North
            ['lat' => $startLat + 0.006, 'lng' => $startLng - 0.002], // West
            ['lat' => $startLat, 'lng' => $startLng - 0.002], // South
            ['lat' => $startLat, 'lng' => $startLng], // Back to start
        ];
        
        $totalSegments = count($waypoints) - 1;
        $segmentProgress = $progress * $totalSegments;
        $currentSegment = (int) floor($segmentProgress);
        $segmentRatio = $segmentProgress - $currentSegment;
        
        if ($currentSegment >= $totalSegments) {
            return $waypoints[count($waypoints) - 1];
        }
        
        $from = $waypoints[$currentSegment];
        $to = $waypoints[$currentSegment + 1];
        
        $lat = $from['lat'] + (($to['lat'] - $from['lat']) * $segmentRatio);
        $lng = $from['lng'] + (($to['lng'] - $from['lng']) * $segmentRatio);
        
        return [
            'lat' => round($lat, 6),
            'lng' => round($lng, 6),
        ];
    }

    /**
     * Update driver location in database
     */
    private function updateDriverLocation(Driver $driver, float $lat, float $lng): void
    {
        DB::update('
            UPDATE drivers 
            SET location = ST_GeomFromText(?, 4326), updated_at = NOW()
            WHERE id = ?
        ', ["POINT({$lng} {$lat})", $driver->id]);
    }
}
