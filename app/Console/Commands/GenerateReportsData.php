<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Database\Seeders\ComprehensiveReportsSeeder;

class GenerateReportsData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:generate-data 
                            {--fresh : Fresh migration and seed}
                            {--trips-only : Only generate trips data}
                            {--months=12 : Number of months to generate data for}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive fake data for reports widgets over specified time period';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Reports Data Generation...');
        $this->newLine();

        if ($this->option('fresh')) {
            $this->warn('⚠️  This will reset your database!');
            if (!$this->confirm('Are you sure you want to continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }

            $this->info('🔄 Running fresh migrations...');
            $this->call('migrate:fresh');
            $this->newLine();
        }

        if ($this->option('trips-only')) {
            $this->info('🚗 Generating trips data only...');
            $this->call('db:seed', ['--class' => 'ComprehensiveReportsSeeder']);
        } else {
            $this->info('📊 Generating comprehensive reports data...');
            $this->call('db:seed', ['--class' => 'ComprehensiveReportsSeeder']);
        }

        $this->newLine();
        $this->info('✅ Reports data generation completed!');
        $this->newLine();
        
        $this->displaySummary();
        
        return 0;
    }

    private function displaySummary()
    {
        $this->info('📈 Data Summary:');
        $this->table(
            ['Model', 'Count'],
            [
                ['Users', \App\Models\User::count()],
                ['Drivers', \App\Models\Driver::count()],
                ['Riders', \App\Models\Rider::count()],
                ['Vehicles', \App\Models\Vehicle::count()],
                ['Areas', \App\Models\Area::count()],
                ['Trips', \App\Models\Trip::count()],
                ['Completed Trips', \App\Models\Trip::where('status', 'completed')->count()],
                ['Pending Trips', \App\Models\Trip::where('status', 'pending')->count()],
                ['Active Trips', \App\Models\Trip::whereIn('status', ['assigned', 'driver_arriving', 'on_trip'])->count()],
            ]
        );

        $this->newLine();
        $this->info('🎯 You can now view the reports at: /admin/reports');
        $this->info('📊 The widgets will show comprehensive data over the past year!');
    }
}
