<?php

namespace App\Filament\Exports;

use App\Models\Rider;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class RidersReportExporter extends Exporter
{
    protected static ?string $model = Rider::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('Rider ID'),

            ExportColumn::make('user.name')
                ->label('First Name'),

            ExportColumn::make('user.last_name')
                ->label('Last Name'),

            ExportColumn::make('user.phone_number')
                ->label('Phone Number'),

            ExportColumn::make('user.email')
                ->label('Email'),

            ExportColumn::make('user.gender')
                ->label('Gender')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Not specified'),

            ExportColumn::make('global_status')
                ->label('Status')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Unknown'),

            ExportColumn::make('average_rider_rating')
                ->label('Average Rating Given')
                ->formatStateUsing(fn ($state) => $state ? number_format($state, 2).'/5' : 'No ratings'),

            ExportColumn::make('trips_count')
                ->label('Total Trips')
                ->counts('trips'),

            ExportColumn::make('completed_trips_count')
                ->label('Completed Trips')
                ->counts([
                    'trips' => fn ($query) => $query->where('status', 'completed'),
                ]),

            ExportColumn::make('canceled_trips_count')
                ->label('Canceled Trips')
                ->counts([
                    'trips' => fn ($query) => $query->where('status', 'canceled'),
                ]),

            ExportColumn::make('total_spent')
                ->label('Total Spent (LYD)')
                ->state(function (Rider $record): string {
                    $totalSpent = $record->trips()
                        ->where('status', 'completed')
                        ->whereNotNull('pricing_breakdown')
                        ->get()
                        ->sum(function ($trip) {
                            $pricing = json_decode($trip->pricing_breakdown, true);

                            return $pricing['total'] ?? 0;
                        });

                    return number_format($totalSpent, 2);
                }),

            ExportColumn::make('average_trip_cost')
                ->label('Average Trip Cost (LYD)')
                ->state(function (Rider $record): string {
                    $completedTrips = $record->trips()
                        ->where('status', 'completed')
                        ->whereNotNull('pricing_breakdown')
                        ->get();

                    if ($completedTrips->isEmpty()) {
                        return '0.00';
                    }

                    $totalSpent = $completedTrips->sum(function ($trip) {
                        $pricing = json_decode($trip->pricing_breakdown, true);

                        return $pricing['total'] ?? 0;
                    });

                    return number_format($totalSpent / $completedTrips->count(), 2);
                }),

            ExportColumn::make('first_trip_date')
                ->label('First Trip Date')
                ->state(function (Rider $record): string {
                    $firstTrip = $record->trips()->oldest()->first();

                    return $firstTrip ? $firstTrip->created_at->format('Y-m-d H:i:s') : 'No trips';
                }),

            ExportColumn::make('last_trip_date')
                ->label('Last Trip Date')
                ->state(function (Rider $record): string {
                    $lastTrip = $record->trips()->latest()->first();

                    return $lastTrip ? $lastTrip->created_at->format('Y-m-d H:i:s') : 'No trips';
                }),

            ExportColumn::make('favorite_trips_count')
                ->label('Favorite Trips')
                ->counts([
                    'trips' => fn ($query) => $query->where('is_favorite', true),
                ]),

            ExportColumn::make('trip_ratings_count')
                ->label('Ratings Given')
                ->counts('tripRatings'),

            ExportColumn::make('created_at')
                ->label('Registration Date')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('updated_at')
                ->label('Last Updated')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('user.status')
                ->label('User Status')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Unknown'),

            ExportColumn::make('most_used_departure_area')
                ->label('Most Used Departure Area')
                ->state(function (Rider $record): string {
                    $mostUsedArea = $record->trips()
                        ->whereNotNull('departure_area_id')
                        ->with('departureArea')
                        ->get()
                        ->groupBy('departure_area_id')
                        ->map(fn ($trips) => [
                            'count' => $trips->count(),
                            'area_name' => $trips->first()->departureArea?->name ?? 'Unknown',
                        ])
                        ->sortByDesc('count')
                        ->first();

                    return $mostUsedArea ? $mostUsedArea['area_name'].' ('.$mostUsedArea['count'].' trips)' : 'No area data';
                }),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your riders report export has completed and '.number_format($export->successful_rows).' '.str('row')->plural($export->successful_rows).' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to export.';
        }

        return $body;
    }

    public function getFileName(Export $export): string
    {
        return "riders-report-{$export->getKey()}.csv";
    }
}
