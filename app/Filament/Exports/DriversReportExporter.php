<?php

namespace App\Filament\Exports;

use App\Models\Driver;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class DriversReportExporter extends Exporter
{
    protected static ?string $model = Driver::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('Driver ID'),

            ExportColumn::make('user.name')
                ->label('First Name'),

            ExportColumn::make('user.last_name')
                ->label('Last Name'),

            ExportColumn::make('user.phone_number')
                ->label('Phone Number'),

            ExportColumn::make('user.email')
                ->label('Email'),

            ExportColumn::make('user.gender')
                ->label('Gender')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Not specified'),

            ExportColumn::make('id_number')
                ->label('ID Number'),

            ExportColumn::make('global_status')
                ->label('Status')
                ->formatStateUsing(fn ($state) => $state?->getLabel() ?? 'Unknown'),

            ExportColumn::make('rider_gender')
                ->label('Preferred Rider Gender'),

            ExportColumn::make('average_driver_rating')
                ->label('Average Rating')
                ->formatStateUsing(fn ($state) => $state ? number_format($state, 2).'/5' : 'No ratings'),

            ExportColumn::make('trips_count')
                ->label('Total Trips')
                ->counts('trips'),

            ExportColumn::make('completed_trips_count')
                ->label('Completed Trips')
                ->counts([
                    'trips' => fn ($query) => $query->where('status', 'completed'),
                ]),

            ExportColumn::make('vehicles_count')
                ->label('Number of Vehicles')
                ->counts('vehicles'),

            ExportColumn::make('created_at')
                ->label('Registration Date')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('updated_at')
                ->label('Last Updated')
                ->formatStateUsing(fn ($state) => $state?->format('Y-m-d H:i:s')),

            ExportColumn::make('total_earnings')
                ->label('Total Earnings (LYD)')
                ->state(function (Driver $record): string {
                    $totalEarnings = $record->trips()
                        ->where('status', 'completed')
                        ->whereNotNull('pricing_breakdown')
                        ->get()
                        ->sum(function ($trip) {
                            $pricing = json_decode($trip->pricing_breakdown, true);

                            return $pricing['total'] ?? 0;
                        });

                    return number_format($totalEarnings, 2);
                }),

            ExportColumn::make('last_trip_date')
                ->label('Last Trip Date')
                ->state(function (Driver $record): string {
                    $lastTrip = $record->trips()->latest()->first();

                    return $lastTrip ? $lastTrip->created_at->format('Y-m-d H:i:s') : 'No trips';
                }),

            ExportColumn::make('active_vehicles')
                ->label('Active Vehicles')
                ->state(function (Driver $record): string {
                    return $record->vehicles()
                        ->where('global_status', 'active')
                        ->count();
                }),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your drivers report export has completed and '.number_format($export->successful_rows).' '.str('row')->plural($export->successful_rows).' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to export.';
        }

        return $body;
    }

    public function getFileName(Export $export): string
    {
        return "drivers-report-{$export->getKey()}.csv";
    }
}
