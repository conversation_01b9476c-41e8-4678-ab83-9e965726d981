<?php

namespace App\Filament\Traits;

use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Illuminate\Database\Eloquent\Builder;

trait HasDateRangeFilter
{
    /**
     * A filter that provides a date range input.
     *
     * @param  string  $field  The name of the column to filter.
     */
    public static function dateRangeFilter(string $field = 'created_at'): Filter
    {
        return Filter::make($field)
            ->form([
                DatePicker::make("{$field}_from")
                    ->label('From')
                    ->native(false)
                    ->placeholder(fn ($state): string => 'Dec 18, '.now()->subYear()->format('Y'))
                    ->reactive()
                    ->closeOnDateSelection()
                    ->suffixAction(
                        \Filament\Forms\Components\Actions\Action::make('clear')
                            ->action(fn (\Filament\Forms\Set $set) => $set("{$field}_from", null))
                            ->tooltip('Clear From Date')
                            ->icon('heroicon-m-x-mark')
                            ->label('')
                    )
                    ->afterStateUpdated(fn ($state, callable $set) => $set("{$field}_until", null)),

                DatePicker::make("{$field}_until")
                    ->label('To')
                    ->native(false)
                    ->placeholder(fn ($state): string => now()->format('M d, Y'))
                    ->reactive()
                    ->closeOnDateSelection()
                    ->suffixAction(
                        \Filament\Forms\Components\Actions\Action::make('clear')
                            ->action(fn (\Filament\Forms\Set $set) => $set("{$field}_until", null))
                            ->tooltip('Clear To Date')
                            ->icon('heroicon-m-x-mark')
                            ->label('')
                    )
                    ->minDate(fn ($get) => $get("{$field}_from")),
            ])
            ->query(function (Builder $query, array $data) use ($field): Builder {
                return $query
                    ->when(
                        $data["{$field}_from"] ?? null,
                        fn (Builder $query, $date): Builder => $query->whereDate($field, '>=', $date),
                    )
                    ->when(
                        $data["{$field}_until"] ?? null,
                        fn (Builder $query, $date): Builder => $query->whereDate($field, '<=', $date),
                    );
            })
            ->indicateUsing(function (array $data) use ($field): array {
                $indicators = [];

                if ($data["{$field}_from"] ?? null) {
                    $indicators[] = Indicator::make('From '.Carbon::parse($data["{$field}_from"])->toFormattedDateString())
                        ->removeField("{$field}_from");
                }

                if ($data["{$field}_until"] ?? null) {
                    $indicators[] = Indicator::make('To '.Carbon::parse($data["{$field}_until"])->toFormattedDateString())
                        ->removeField("{$field}_until");
                }

                return $indicators;
            });
    }
}
