<?php

namespace App\Filament\Widgets;

use App\Models\Trip;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;
use Filament\Widgets\Concerns\InteractsWithPageFilters;

class TripsChart extends ChartWidget
{
    use InteractsWithPageFilters;

    protected static bool $isLazy = false;

    protected static ?string $pollingInterval = null;

    protected static ?string $heading = 'Trips chart in current month';

    protected function getData(): array
    {
        // Get the results from the Eloquent query
        $results = $this->getTripData();

        // Extract labels (days) and data (trip counts)
        $labels = $results->pluck('date')->toArray();
        $data = $results->pluck('trip_count')->toArray();

        return [
            'datasets' => [
                [
                    'label' => 'Trips',
                    'data' => $data,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getTripData()
    {
        //dump($this->filters);
        $startDate = isset($this->filters['startDate'])
            ? Carbon::parse($this->filters['startDate']) :
            now()->startOfMonth();

        $endDate = isset($this->filters['endDate'])
            ? Carbon::parse($this->filters['endDate'])
            : now()->toDateString();

        // Query to count trips for each day
        $trips = Trip::query()
            ->selectRaw('DATE(created_at) as date, COUNT(*) as trip_count')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->keyBy('date'); // Key the results by date for easy lookup

        // Generate the date range
        $dateRange = [];
        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
            $dateRange[] = $date->toDateString();
        }

        // Merge the date range with the trip data
        $result = [];
        foreach ($dateRange as $date) {
            $formattedDate = Carbon::parse($date)->format('d'); // Format as "01,Oct :Sun"
            $result[] = [
                'date' => $formattedDate, // Use the formatted date
                'trip_count' => $trips->has($date) ? $trips[$date]->trip_count : 0,
            ];
        }

        // Return the result as a collection
        return collect($result);
    }

    protected function getType(): string
    {
        return 'line';
    }
}
