<?php

namespace App\Filament\Widgets;

use App\Services\VehicleTrackingService;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Log;

class VehiclesRealTimeMovement extends Widget
{
    protected static ?string $heading = 'Vehicle Real-Time Tracking';

    protected static ?int $sort = 1;

    protected int|string|array $columnSpan = 'full';

    protected static string $view = 'filament.widgets.vehicles-real-time-movement';

    protected static ?string $pollingInterval = null;

    public $vehicles = [];

    public $vehiclesJson = '';

    public $lastUpdateHash = '';

    public $updateCount = 0;

    // Modal state properties
    public $showModal = false;

    public $selectedVehicle = null;

    public $selectedVehicleId = null;

    public function mount(): void
    {
        $this->loadVehicles();
    }

    public function loadVehicles(): void
    {
        try {
            $newVehicles = VehicleTrackingService::getOptimizedVehicleData();
            $newHash = md5(json_encode($newVehicles));

            // Only update if data has actually changed
            if ($this->lastUpdateHash !== $newHash) {
                $this->vehicles = $newVehicles;
                $this->vehiclesJson = json_encode($newVehicles);
                $this->lastUpdateHash = $newHash;
                $this->updateCount++;

                // Update selected vehicle data if modal is open
                $this->updateSelectedVehicleIfModalOpen($newVehicles);

                // Notify frontend of the update with explicit modal data
                $this->dispatch('vehicles-updated', [
                    'vehicles' => $newVehicles,
                    'selectedVehicle' => $this->selectedVehicle,
                    'showModal' => $this->showModal,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Vehicle tracking update failed: '.$e->getMessage());
        }
    }

    public function selectVehicle($vehicleId): void
    {
        $vehicle = collect($this->vehicles)->firstWhere('id', $vehicleId);

        if ($vehicle && $this->isValidVehicleData($vehicle)) {
            $this->selectedVehicle = $vehicle;
            $this->selectedVehicleId = $vehicleId;
            $this->showModal = true;

            // Dispatch event to update frontend immediately
            $this->dispatch('vehicle-selected', [
                'selectedVehicle' => $this->selectedVehicle,
                'showModal' => true,
            ]);
        }
    }

    public function closeModal(): void
    {
        $this->showModal = false;
        $this->selectedVehicle = null;
        $this->selectedVehicleId = null;

        // Dispatch event to update frontend
        $this->dispatch('modal-closed');
    }

    private function updateSelectedVehicleIfModalOpen(array $newVehicles): void
    {
        if ($this->showModal && $this->selectedVehicleId) {
            $updatedVehicle = collect($newVehicles)->firstWhere('id', $this->selectedVehicleId);

            if ($updatedVehicle && $this->isValidVehicleData($updatedVehicle)) {
                $this->selectedVehicle = $updatedVehicle;
            } else {
                // Vehicle no longer available or invalid, close modal
                $this->closeModal();
            }
        }
    }

    private function isValidVehicleData(array $vehicle): bool
    {
        if (! $vehicle || ! isset($vehicle['id'])) {
            return false;
        }

        // Check driver data
        $hasValidDriver = isset($vehicle['driver']) &&
                         is_array($vehicle['driver']) &&
                         ! empty($vehicle['driver']['name']) &&
                         ! empty($vehicle['driver']['phone']);

        // Check vehicle data
        $hasValidVehicle = isset($vehicle['vehicle']) &&
                          is_array($vehicle['vehicle']) &&
                          ! empty($vehicle['vehicle']['model']) &&
                          ! empty($vehicle['vehicle']['brand']);

        return $hasValidDriver && $hasValidVehicle;
    }

    public function getViewData(): array
    {
        return [
            'vehicles' => $this->vehicles,
            'vehiclesJson' => $this->vehiclesJson,
        ];
    }
}
