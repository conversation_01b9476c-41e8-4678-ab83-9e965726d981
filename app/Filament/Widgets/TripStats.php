<?php

namespace App\Filament\Widgets;

use App\Enums\Trips\TripStatus;
use App\Models\Trip;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TripStats extends BaseWidget
{
    protected static bool $isLazy = false;

    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        $totoalTripsToday = Trip::whereDate('created_at', Carbon::today())
            ->count();

        $acceptedTripsToday = Trip::where('status', TripStatus::assigned)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $canceledTripsToday = Trip::where('status', TripStatus::canceled)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $pendingTripsToday = Trip::where('status', TripStatus::pending)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $completedTripsToday = Trip::where('status', TripStatus::completed)
            ->whereDate('created_at', Carbon::today())
            ->count();

        $inProgressTripsToday = Trip::where('status', TripStatus::on_trip)
            ->whereDate('created_at', Carbon::today())
            ->count();

        // Return the stats with charts and descriptions
        return [

            Stat::make('Total Trips', $totoalTripsToday)
                ->color('success')
                ->chart([1, 2, 2, 2, 2, 2, 2, 8, 9, 10]) // Example chart data for the past 7 days
                ->icon('heroicon-o-check-circle')
                ->description('total Trips today'),

            Stat::make('Accepted Trips', $acceptedTripsToday)
                ->color(TripStatus::assigned->getColor())
                ->chart([2, 3, 5, 1, 3, 1, 4, 2]) // Example chart data for the past 7 days
                ->icon(TripStatus::assigned->getIcon())
                ->description('accepted Trips today.'),

            Stat::make('Canceled Trips', $canceledTripsToday)
                ->color(TripStatus::canceled->getColor())
                ->chart([2, 3, 3, 3, 2, 1, 5, 3]) // Example chart data for canceled rides
                ->icon(TripStatus::canceled->getIcon())
                ->description('canceled Trips today.'),

            Stat::make('Pending Trips', $pendingTripsToday)
                ->color(TripStatus::pending->getColor())
                ->chart([2, 3, 5, 7, 1, 2, 3, 1, 2, 1]) // Example chart data for pending rides
                ->icon(TripStatus::pending->getIcon())
                ->description('Trips pending today.'),

            Stat::make('Completed Trips', $completedTripsToday)
                ->color(TripStatus::completed->getColor())
                ->chart([2, 4, 5, 3, 2, 1, 1, 2, 1, 4]) // Example chart data for completed rides
                ->icon(TripStatus::completed->getIcon())
                ->description('completed Trips today.'),

            Stat::make('Trips In Progress', $inProgressTripsToday)
                ->color(TripStatus::on_trip->getColor())
                ->chart([2, 3, 6, 3, 2, 1, 43, 2]) // Example chart data for rides in progress
                ->icon(TripStatus::on_trip->getIcon())
                ->description('Trips that are currently in progress today.'),
        ];
    }

    public function getColumnSpan(): int|string
    {
        return 1; // This widget will span 2 columns
    }
}
