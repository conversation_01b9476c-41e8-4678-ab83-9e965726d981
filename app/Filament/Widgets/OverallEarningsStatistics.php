<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Payment;

/*class OverallEarningsStatistics extends BaseWidget
{        

   /* protected function getStats(): array
    {    
        $Totalrevenue=Payment::where('payment_status','completed')
        ->count();

        return [
            Stat::make('Totalrevenue', $Totalrevenue)
            ->color('success') 
            ->chart([1,2,2,2,2,2,2,8,9,10]) // Example chart data for the past 7 days
            ->icon('heroicon-o-check-circle')
            ->description('total Trips today'),
        ];
    }
        */

