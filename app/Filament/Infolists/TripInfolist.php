<?php

namespace App\Filament\Infolists;

use App\Enums\Trips\CancellationStage;
use App\Enums\Trips\TripStatus;
use App\Enums\VehicleTypesCategories;
use App\Livewire\TripRoute;
use App\Livewire\Vehicles\Equipments;
use App\Traits\HasTripStatusColumn;
use Filament\Infolists\Components;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\TextEntry\TextEntrySize;
use Filament\Infolists\Infolist;
use IbrahimBou<PERSON>oua\FilamentRatingStar\Entries\Components\RatingStar as InfolistRatingStar;
use Illuminate\Support\Str;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;

class TripInfolist
{
    use HasTripStatusColumn;

    public static function infolist(Infolist $infolist): Infolist
    {

        $infolist->record($infolist->record ?? $infolist->getRecord());

        $currentStatuses = [
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
        ];
        $NoPickUptime = [
            TripStatus::timeout->value,
            TripStatus::pending->value,
            TripStatus::dispatched->value,
            TripStatus::rejected->value,
        ];

        return $infolist
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('Trip Informations')
                            ->icon('heroicon-o-information-circle')
                            ->schema([
                                Components\Section::make()
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Components\TextEntry::make('id')
                                                    ->label('Trip ID')
                                                    ->badge()
                                                    ->size(TextEntry\TextEntrySize::Small)
                                                    ->getStateUsing(fn ($record) => $record->id ?? 'No Trip ID Provided'),

                                                self::getTripStatusEntry('status', 'Trip Status'),

                                                Components\TextEntry::make('cancellation_stage')
                                                    ->label('Cancellation stage')
                                                    ->badge()
                                                    ->getStateUsing(fn ($record) => $record->cancellation_stage ?: 'No Cancellation Stage Provided')
                                                    ->visible(fn ($record) => $record->status === TripStatus::canceled),

                                                Components\TextEntry::make('estimated_arrival_time')
                                                    ->label('ETA')
                                                    ->getStateUsing(function ($record) {
                                                        if (! $record->estimated_arrival_time) {
                                                            return 'Not available';
                                                        }

                                                        if ($record->estimated_arrival_time instanceof \DateTime) {
                                                            return $record->estimated_arrival_time->format('F j, Y g:i A');
                                                        }

                                                        return date('F j, Y g:i A', strtotime($record->estimated_arrival_time));
                                                    })
                                                    ->icon('heroicon-o-clock')
                                                    ->badge()
                                                    ->weight('medium')
                                                    ->visible(fn ($record) => in_array($record->status->value, $currentStatuses)),

                                                Components\TextEntry::make('created_at')
                                                    ->label('Date of the trip')
                                                    ->getStateUsing(function ($record) {
                                                        if (! $record->created_at) {
                                                            return 'Not available';
                                                        }
                                                        if ($record->created_at instanceof \DateTime) {
                                                            return $record->created_at->format('F j, Y');
                                                        }

                                                        return date('F j, Y', strtotime($record->created_at));
                                                    })
                                                    ->icon('heroicon-o-calendar')
                                                    ->badge()
                                                    ->color('gray')
                                                    ->weight('bold')
                                                    ->size(TextEntry\TextEntrySize::Small)
                                                    ->visible(fn ($record) => ! in_array($record->status->value, $currentStatuses)),

                                                Components\TextEntry::make('Trip duration')
                                                    ->label('Trip Duration')
                                                    ->badge()
                                                    ->icon('heroicon-o-clock')
                                                    ->getStateUsing(function ($record) {
                                                        if (! $record->actual_departure_time || ! $record->actual_arrival_time) {
                                                            return 'No Trip Duration Available';
                                                        }

                                                        $departure_time = \Carbon\Carbon::parse($record->actual_departure_time);
                                                        $arrival_time = \Carbon\Carbon::parse($record->actual_arrival_time);
                                                        $duration = $departure_time->diff($arrival_time);

                                                        return "{$duration->h} h {$duration->i} mins";
                                                    })
                                                    ->weight('bold')
                                                    ->size(TextEntry\TextEntrySize::Large)
                                                    ->visible(fn ($record) => ! in_array($record->status->value, $currentStatuses)),

                                                Components\TextEntry::make('Total fare')
                                                    ->label('Total Fare')
                                                    ->badge()
                                                    ->icon('mdi-cash-multiple')
                                                    ->getStateUsing(function ($record) {
                                                        $total = json_decode($record->pricing_breakdown, true);

                                                        return isset($total['total']) ? $total['total'].' LYD' : 'No Total Fare Provided';
                                                    })
                                                    ->weight('bold')
                                                    ->size(TextEntry\TextEntrySize::Large),

                                                Components\TextEntry::make('is_female')
                                                    ->label('Women Services')
                                                    ->badge()
                                                    ->icon('mdi-human-female')
                                                    ->getStateUsing(fn ($record) => $record->is_female ? 'Active' : 'Not Active')
                                                    ->color(fn ($record) => $record->is_female ? 'success' : 'danger'),

                                                Components\TextEntry::make('rider_notes')
                                                    ->label('Notes of trip')
                                                    ->icon('heroicon-o-pencil-square')
                                                    ->getStateUsing(fn ($record) => $record->rider_notes ?: 'No Notes Provided'),
                                            ]),

                                    ])->extraAttributes(['class' => 'border-none p-0']),

                                Components\Section::make(' Pickup and Drop-off Details')
                                    ->description('Detailed informations about pickup and drop-off')
                                    ->icon('heroicon-o-map-pin')
                                    ->schema([
                                        Components\Grid::make(2)
                                            ->schema([

                                                Components\TextEntry::make('actual_departure_time')
                                                    ->label(' Time of pickup')
                                                    ->getStateUsing(function ($record) use ($NoPickUptime) {
                                                        if (! $record->status || $record->status->value === TripStatus::canceled->value &&
                                                        $record->cancellation_stage?->value === CancellationStage::afterDispatch->value &&
                                                        $record->cancelled_by === 'rider') {
                                                            return 'Not available';
                                                        }

                                                        if (! $record->status || $record->status->value === TripStatus::canceled->value &&
                                                        $record->cancellation_stage?->value === CancellationStage::afterDispatch->value &&
                                                        $record->cancelled_by === 'rider' && ($record->contacted_drivers === null || $record->contacted_drivers->isEmpty())) {
                                                            return 'Not available';
                                                        }

                                                        if (! $record->actual_departure_time || in_array($record->status?->value, $NoPickUptime)) {
                                                            return 'Not available';
                                                        }

                                                        if ($record->actual_departure_time instanceof \DateTime) {
                                                            return $record->actual_departure_time->addHour()->format('F j, Y g:i A');
                                                        }

                                                        return date('F j, Y g:i A', strtotime($record->actual_departure_time));
                                                    })
                                                    ->icon('heroicon-o-clock')
                                                    ->badge()
                                                    ->color('gray')
                                                    ->weight('medium'),

                                                Components\TextEntry::make('estimated_arrival_time')
                                                    ->label(' Time of Drop-off')
                                                    ->getStateUsing(function ($record) {
                                                        // if ($record->status->value === TripStatus::canceled->value &&
                                                        // $record->cancellation_stage->value === CancellationStage::afterDispatch->value &&
                                                        // $record->cancelled_by === 'rider') {
                                                        //     return 'Not available';
                                                        // }

                                                        if (! $record->estimated_arrival_time) {
                                                            return 'Not available';
                                                        }

                                                        if ($record->estimated_arrival_time instanceof \DateTime) {
                                                            return $record->estimated_arrival_time->addHour()->format('F j, Y g:i A');
                                                        }

                                                        return date('F j, Y g:i A', strtotime($record->estimated_arrival_time));
                                                    })
                                                    ->icon('heroicon-o-clock')
                                                    ->badge()
                                                    ->color('gray')
                                                    ->weight('medium'),

                                                Components\TextEntry::make('tripLocation.departure_address')
                                                    ->label('Pickup Location')
                                                    ->badge()
                                                    ->icon('heroicon-o-map-pin')
                                                    ->color('primary')
                                                    ->weight('bold')
                                                    ->size(TextEntry\TextEntrySize::Large)
                                                    ->getStateUsing(fn ($record) => $record->tripLocation->departure_address ?? 'No Pickup Location Provided'),

                                                Components\TextEntry::make('tripLocation.arrival_address')
                                                    ->label('Drop-off Location')
                                                    ->badge()
                                                    ->icon('heroicon-o-map-pin')
                                                    ->color('primary')
                                                    ->weight('bold')
                                                    ->size(TextEntry\TextEntrySize::Large)
                                                    ->getStateUsing(fn ($record) => $record->tripLocation->arrival_address ?? 'No Drop-off Location Provided'),
                                            ])->columns(2),

                                    ])->extraAttributes(['class' => 'border border-gray-200 rounded-lg p-6']),

                                Components\Section::make('Ratings & Feedback')
                                    ->description('Rider and driver feedback about the trip')
                                    ->icon('heroicon-o-chat-bubble-bottom-center-text')
                                    ->schema([
                                        Grid::make(2)
                                            ->schema([
                                                Components\Group::make([

                                                    Grid::make(2)
                                                        ->schema([
                                                            Components\Group::make([

                                                                InfolistRatingStar::make('tripRatings.rider_to_driver_rating')
                                                                    ->label('Rider to Driver Rating')
                                                                    ->size('md')
                                                                    ->state(function ($record) {
                                                                        return $record->tripRatings()->first()->rider_to_driver_rating ?? null;
                                                                    }),
                                                            ]),

                                                            Components\Group::make([

                                                                InfolistRatingStar::make('tripRatings.rider_to_car_rating')
                                                                    ->label('Rider to Car Rating')
                                                                    ->state(function ($record) {

                                                                        return $record->tripRatings()->first()->rider_to_car_rating ?? null;
                                                                    })
                                                                    ->size('md'),
                                                            ]),
                                                        ]),

                                                    TextEntry::make('tripRatings.rider_review')
                                                        ->label('Rider Feedback')
                                                        ->columnSpanFull()
                                                        ->getStateUsing(fn ($record) => $record->tripRatings()->first()->rider_review ?? 'No Feedback Provided')
                                                        ->extraAttributes(['class' => 'p-4 rounded-lg ']),
                                                ])
                                                    ->columnSpan(1)
                                                    ->extraAttributes(['class' => 'border-r border-gray-200 pr-4']),

                                                Components\Group::make([

                                                    Components\Group::make([

                                                        InfolistRatingStar::make('tripRatings.driver_to_rider_rating')
                                                            ->label('Driver to Rider Rating')
                                                            ->state(function ($record) {

                                                                return $record->tripRatings()->first()->driver_to_rider_rating ?? null;
                                                            })
                                                            ->size('md'),
                                                    ]),

                                                    TextEntry::make('tripRatings.driver_review')
                                                        ->label('Driver Feedback')
                                                        ->columnSpanFull()
                                                        ->getStateUsing(fn ($record) => $record->tripRatings()->first()->driver_review ?? 'No Feedback Provided')
                                                        ->extraAttributes(['class' => 'p-4 rounded-lg ']),
                                                ])
                                                    ->columnSpan(1)
                                                    ->extraAttributes(['class' => 'pl-4']),
                                            ])
                                            ->columnSpanFull(),
                                    ])
                                    ->visible(fn ($record) => $record->status->value === TripStatus::completed->value)
                                    ->columns(2)
                                    ->extraAttributes(['class' => 'border border-gray-200 rounded-lg p-6']),
                            ]),

                        // Livewire::make(TripRoute::class, [
                        //     'departure' => [
                        //         'lat' => $infolist->record->departure_lat,
                        //         'lng' => $infolist->record->departure_lng,
                        //     ],
                        //     'arrival' => [
                        //         'lat' => $infolist->record->arrival_lat,
                        //         'lng' => $infolist->record->arrival_lng,
                        //     ],
                        // ])->columnSpanFull(),
                        Tabs\Tab::make('Driver details')
                            ->icon('heroicon-o-users')

                            ->schema([
                                Components\Section::make()
                                    ->schema([
                                        Components\TextEntry::make('driver.id')
                                            ->label('ID')
                                            ->badge()
                                            ->columnSpanFull(),
                                        // ->extraAttributes(['class' => 'text-lg font-semibold']),

                                        Components\TextEntry::make('driver.user.name')
                                            ->label('First name'),
                                        // ->extraAttributes(['class' => 'font-semibold']),

                                        Components\TextEntry::make('driver.user.last_name')
                                            ->label('Last name'),

                                        Components\TextEntry::make('driver.user.gender')
                                            ->label('Gender')
                                            ->icon('heroicon-s-user-circle')
                                            ->formatStateUsing(fn ($state) => $state->value === 'female' ? 'أنثى' : 'ذكر')
                                            ->badge(),

                                        Components\TextEntry::make('driver.user.address.address')
                                            ->label('Address')
                                            ->icon('heroicon-o-map')
                                            ->getStateUsing(function ($record) {
                                                // Check if driver exists first
                                                if (! $record->driver) {
                                                    return 'No driver assigned';
                                                }

                                                // Then check if user exists
                                                if (! $record->driver->user) {
                                                    return 'No user associated with driver';
                                                }

                                                // Then try to get address
                                                return $record->driver->user->address()->orderBy('created_at', 'asc')->first()?->address
                                                    ?: 'No address provided';
                                            })
                                            ->iconColor('primary'),

                                        PhoneEntry::make('driver.user.phone_number')
                                            ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                                            ->label('Primary Phone')
                                            ->label('Phone Number')
                                            ->iconColor('primary')
                                            ->icon('heroicon-o-device-phone-mobile')
                                            ->getStateUsing(function ($record) {
                                                // Check if driver exists
                                                if (! $record->driver) {
                                                    return 'No driver assigned';
                                                }

                                                // Check if user exists
                                                if (! $record->driver->user) {
                                                    return 'No user associated with driver';
                                                }

                                                // Return phone number or default message
                                                return $record->driver->user->phone_number
                                                    ? $record->driver->user->phone_number
                                                    : 'No phone number provided';
                                            }),
                                        // ->extraAttributes(['class' => 'text-sm text-gray-500']),

                                        InfolistRatingStar::make('driver.average_driver_rating')
                                            ->size('md')
                                            ->label('Ratings'),

                                    ])->columns(2),
                            ]),
                        Tabs\Tab::make('Rider details')
                            ->icon('mdi-hail')
                            ->schema([

                                Components\Section::make()
                                    ->schema([

                                        Components\TextEntry::make('rider.user.name')
                                            ->label('First name'),

                                        Components\TextEntry::make('rider.user.last_name')
                                            ->label('Last name'),

                                        Components\TextEntry::make('rider.user.gender')
                                            ->label('Gender')
                                            ->formatStateUsing(fn ($state) => $state->value === 'female' ? 'أنثى' : 'ذكر')
                                            ->badge()
                                            ->icon('heroicon-s-user-circle')
                                            ->extraAttributes(['class' => 'text-lg font-semibold']),

                                        Components\TextEntry::make('rider.user.address.address')
                                            ->label('Address')
                                            ->icon('heroicon-o-map')
                                            ->getStateUsing(function ($record) {
                                                if (! $record->rider?->user) {
                                                    return 'No address provided';
                                                }

                                                return $record->rider->user->address()
                                                    ->orderBy('created_at', 'asc')
                                                    ->first()?->address ?? 'No address provided';
                                            })
                                            ->iconColor('primary'),

                                        PhoneEntry::make('rider.user.phone_number')
                                            ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                                            ->label('Phone Number')
                                            ->iconColor('primary')
                                            ->icon('heroicon-o-device-phone-mobile')
                                            ->getStateUsing(function ($record) {
                                                if (! $record->rider?->user) {
                                                    return 'No phone number provided';
                                                }

                                                return $record->rider->user->phone_number ?? 'No phone number provided';
                                            }),

                                        InfolistRatingStar::make('rider.average_rider_rating')
                                            ->size('md')
                                            ->label('Ratings'),

                                    ])->columns(2),

                            ]),
                        Tabs\Tab::make('Vehicle Informations')
                            ->icon('mdi-taxi')
                            ->schema([
                                Components\Section::make()
                                    ->schema([
                                        Components\TextEntry::make('vehicle.vehicleType.category')
                                            ->label('Vehicle category')
                                            ->formatStateUsing(function ($state) {
                                                if ($state->value === 'passenger') {
                                                    return 'أشخاص';
                                                } else {
                                                    return 'بضائع';
                                                }
                                            })
                                            ->badge(),

                                        Components\TextEntry::make('vehicle.license_plate_number')
                                            ->label('License plate number')
                                            ->badge(),

                                        Components\TextEntry::make('vehicle.vehicleType.name_ar')
                                            ->label('Vehicle type (Arabic)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(fn ($record) => empty($record->vehicle->vehicleType?->name_ar) ||
                                                                in_array($record->vehicle->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type'])
                                                                    ? 'الاسم غير متوفر'
                                                                    : $record->vehicle->vehicleType->name_ar
                                            ),

                                        Components\TextEntry::make('vehicle.vehicleType.name_en')
                                            ->label('Vehicle type (English)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(fn ($record) => empty($record->vehicle->vehicleType?->name_en) ||
                                            in_array($record->vehicle->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type']) ? 'Type name not available' : $record->vehicle->vehicleType->name_en),

                                        Components\TextEntry::make('vehicle.vehicleModel.vehicleBrand.name_ar')
                                            ->label('Vehicle brand (Arabic)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->vehicleModel?->vehicleBrand?->name_ar ?: 'Brand name not specified';
                                            }),

                                        Components\TextEntry::make('vehicle.vehicleModel.vehicleBrand.name_en')
                                            ->label('Vehicle brand (English)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->vehicleModel?->vehicleBrand?->name_en ?: 'Brand name not specified';
                                            }),

                                        Components\TextEntry::make('vehicle.vehicleModel.name_ar')
                                            ->label('Vehicle model (Arabic)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->vehicleModel?->name_ar ?: 'Model name not specified';
                                            }),

                                        Components\TextEntry::make('vehicle.vehicleModel.name_en')
                                            ->label('Vehicle model (English)')
                                            ->icon('heroicon-m-truck')
                                            ->badge()
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->vehicleModel?->name_en ?: 'Model name not specified';
                                            }),

                                        Components\TextEntry::make('vehicle.seat_number')
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->seat_number ?: 'Seat number not specified';
                                            })
                                            ->label('Number of seats')
                                            ->visible(function ($record) {
                                                // Check if vehicle exists
                                                if (! $record->vehicle) {
                                                    return false;
                                                }

                                                // Check if vehicleType exists and has the correct category
                                                return $record->vehicle->vehicleType &&
                                                       $record->vehicle->vehicleType->category === VehicleTypesCategories::Passenger;
                                            }),

                                        Components\IconEntry::make('vehicle.vehicleType.is_covered')
                                            ->boolean()
                                            ->label('Type is covered')
                                            ->visible(function ($record) {
                                                // Check if vehicle exists
                                                if (! $record->vehicle) {
                                                    return false;
                                                }

                                                // Check if vehicleType exists and has the correct category
                                                return $record->vehicle->vehicleType &&
                                                       $record->vehicle->vehicleType->category === VehicleTypesCategories::Freight;
                                            }),

                                        Components\TextEntry::make('vehicle.vehicleType.weight_category')
                                            ->size(TextEntrySize::Large)
                                            ->label('Weight Category (Arabic)')
                                            ->badge()
                                            ->getStateUsing(function ($record) {
                                                // Check if vehicle exists
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                // Check if vehicleModel exists
                                                if (! $record->vehicle->vehicleModel) {
                                                    return 'Vehicle model not specified';
                                                }

                                                // Check if vehicleBrand exists
                                                if (! $record->vehicle->vehicleModel->vehicleBrand) {
                                                    return 'Vehicle brand not specified';
                                                }

                                                // Return the Arabic name or default message
                                                return $record->vehicle->vehicleModel->vehicleBrand->name_ar ?: 'Brand name not specified';
                                            })
                                            ->formatStateUsing(function ($state) {
                                                if ($state === 'less_than_1000kg') {
                                                    return 'أقل من 1000 كجم';
                                                } else {
                                                    return 'أكثر من 1000 كجم';
                                                }
                                            })
                                            ->visible(function ($record) {
                                                // Check if vehicle exists
                                                if (! $record->vehicle) {
                                                    return false;
                                                }

                                                // Check if vehicleType exists and has the correct category
                                                return $record->vehicle->vehicleType &&
                                                       $record->vehicle->vehicleType->category === VehicleTypesCategories::Freight;
                                            }),

                                        Components\TextEntry::make('vehicle.vehicleType.weight_category')
                                            ->size(TextEntrySize::Large)
                                            ->label('Weight Category (English)')
                                            ->badge()
                                            ->getStateUsing(fn ($record) => $record->vehicle->vehicleType?->weight_category ?: 'Weight category not assigned')
                                            ->visible(function ($record) {
                                                // Check if vehicle exists
                                                if (! $record->vehicle) {
                                                    return false;
                                                }

                                                // Check if vehicleType exists and has the correct category
                                                return $record->vehicle->vehicleType &&
                                                       $record->vehicle->vehicleType->category === VehicleTypesCategories::Freight;
                                            }),

                                        Components\TextEntry::make('vehicle.color')
                                            ->label('Color')
                                            ->icon('heroicon-m-swatch')
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->color ?: 'Color not specified';
                                            }),

                                        Components\TextEntry::make('vehicle.year')
                                            ->getStateUsing(function ($record) {
                                                if (! $record->vehicle) {
                                                    return 'No vehicle assigned';
                                                }

                                                return $record->vehicle->year ?: 'Year not provided';
                                            })
                                            ->icon('heroicon-m-calendar-days')
                                            ->label('Year'),

                                        InfolistRatingStar::make('vehicle.average_vehicle_rating')
                                            ->size('md')
                                            ->label('Ratings')
                                            ->state(function ($record) {
                                                if (! $record->vehicle) {
                                                    return null;
                                                }

                                                return $record->vehicle->average_vehicle_rating;
                                            }),

                                    ])->columns(2),

                                Components\Section::make('Specific Equipments')
                                    ->schema(function ($record) {
                                        return [
                                            Livewire::make(Equipments::class, ['vehicle' => $record->vehicle])->key(Str::random()),
                                        ];

                                    })->visible(function ($record) {
                                        // Check if vehicle exists
                                        if (! $record->vehicle) {
                                            return false;
                                        }

                                        // Check if vehicleType exists
                                        if (! $record->vehicle->vehicleType) {
                                            return false;
                                        }

                                        // Check if category exists and has the expected value
                                        return $record->vehicle->vehicleType->category->value === 'passenger';
                                    }),

                            ]),
                    ])->columnSpanFull(),

            ]);

    }
}
