<?php

namespace App\Filament\Pages\Auth;

use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Forms\Components\TextInput;
use Filament\Pages\Auth\Login;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;

class CustomLogin extends Login
{
    use HasCustomLayout;

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        TextInput::make('email')
                            ->label(__('filament-panels::pages/auth/login.form.email.label'))
                            ->rule([
                                'required',
                                'email',
                                'regex:/^(?!.*[._-]{2})[A-Za-z0-9]+[A-Za-z0-9._-]*[A-Za-z0-9]@([A-Za-z0-9-]+\.)+[A-Za-z]{2,6}$/',
                            ])
                            ->autocomplete()
                            ->autofocus()
                            ->extraInputAttributes(['tabindex' => 1]),
                        TextInput::make('password')
                            ->label(__('filament-panels::pages/auth/login.form.password.label'))
                            ->hint(filament()->hasPasswordReset() ? new HtmlString(Blade::render('<x-filament::link :href="filament()->getRequestPasswordResetUrl()" tabindex="3"> {{ __(\'filament-panels::pages/auth/login.actions.request_password_reset.label\') }}</x-filament::link>')) : null)
                            ->password()
                            ->rule(['required'])
                            ->revealable(filament()->arePasswordsRevealable())
                            ->autocomplete('current-password')
                            ->extraInputAttributes(['tabindex' => 2]),
                    ])
                    ->statePath('data'),
            ),
        ];
    }
}
