<?php

namespace App\Filament\Resources\Panel\PaymentResource\Pages;

use App\Filament\Exports\PaymentExporter;
use App\Filament\Resources\Panel\PaymentResource;
use Filament\Actions;
use Filament\Actions\ExportAction as ActionsExportAction;
use Filament\Actions\Exports\Enums\ExportFormat;
use Filament\Resources\Pages\ListRecords;

class ListPayments extends ListRecords
{
    protected static string $resource = PaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
            ActionsExportAction::make()
                ->exporter(PaymentExporter::class)
                ->formats([
                    ExportFormat::Xlsx,
                    ExportFormat::Csv,
                ]),
        ];
    }
}
