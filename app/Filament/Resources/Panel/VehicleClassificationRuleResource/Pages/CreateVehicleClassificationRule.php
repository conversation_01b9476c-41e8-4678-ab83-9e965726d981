<?php

namespace App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages;

use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateVehicleClassificationRule extends CreateRecord
{
    protected static string $resource = VehicleClassificationRuleResource::class;

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('New vehicle classification rule created successfully');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
