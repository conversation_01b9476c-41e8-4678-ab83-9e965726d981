<?php

namespace App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages;

use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListVehicleClassificationRules extends ListRecords
{
    protected static string $resource = VehicleClassificationRuleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
