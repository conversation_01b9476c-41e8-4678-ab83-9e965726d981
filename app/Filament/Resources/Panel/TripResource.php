<?php

namespace App\Filament\Resources\Panel;

use App\Enums\Trips\TripStatus;
use App\Filament\Infolists\TripInfolist;
use App\Filament\Resources\Panel\TripResource\Pages;
use App\Filament\Tables\TripsTable;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class TripResource extends Resource
{
    protected static ?string $model = Trip::class;

    protected static ?string $navigationIcon = 'mdi-map-marker-distance';

    protected static ?string $navigationGroup = 'Admin';

    public static function getModelLabel(): string
    {
        return __('crud.trips.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.trips.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.trips.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Group::make()->schema([
                Section::make('Rider & Driver Information')
                    ->schema([
                        Select::make('rider_id')
                            ->label('Rider')
                            ->required()
                            ->options(Rider::with('user')
                                ->whereHas('user', function ($query) {
                                    $query->where('type', 'passenger');
                                })
                                ->get()
                                ->pluck('user.full_name', 'id'))
                            ->searchable()
                            ->preload()
                            ->native(false)
                            ->helperText('Select the rider for the trip'),

                        Select::make('driver_id')
                            ->label('Driver')
                            ->required()
                            ->options(
                                Driver::with('user')
                                    ->whereHas('user', function ($query) {
                                        $query->where('type', 'driver');
                                    })
                                    ->get()
                                    ->filter(fn ($driver) => $driver->user && $driver->user->full_name)
                                    ->pluck('user.full_name', 'id')
                            )
                            ->searchable()
                            ->preload()
                            ->native(false)
                            ->helperText('Select the assigned driver'),

                        Select::make('vehicle_id')
                            ->label('Vehicle License Plate Number')
                            ->required()
                            ->relationship('vehicle', 'license_plate_number')
                            ->searchable()
                            ->preload()
                            ->native(false)
                            ->columnSpanFull()
                            ->helperText('Select the vehicle assigned for the trip'),

                    ])->columns(2),

                Section::make('Trip Details')
                    ->schema([

                        ToggleButtons::make('status')
                            ->label('Trip Status')
                            ->inline()
                            ->default('new')
                            ->required()
                            ->options(TripStatus::class)
                            ->grouped()
                            ->helperText('Update the current status of the trip'),

                        TextInput::make('distance')
                            ->label('Distance (km)')
                            ->required()
                            ->numeric()
                            ->helperText('Enter the trip distance in kilometers'),
                    ]),
            ])->columnSpanFull(),

            Section::make('Time & Location')
                ->schema([
                    Select::make('departure_area_id')
                        ->label('Departure Area')
                        ->required()
                        ->relationship('departureArea', 'name_en')
                        ->searchable()
                        ->preload()
                        ->native(false)
                        ->helperText('Select the area where the trip will start'),

                    Select::make('arrival_area_id')
                        ->label('Arrival Area')
                        ->required()
                        ->relationship('arrivalArea', 'name_en')
                        ->searchable()
                        ->preload()
                        ->native(false)
                        ->helperText('Select the area where the trip will end'),

                    TextInput::make('departure_lat')
                        ->label('Departure Latitude')
                        ->numeric()
                        ->required()
                        ->helperText('Latitude of the departure location'),

                    TextInput::make('departure_lng')
                        ->label('Departure Longitude')
                        ->required()
                        ->helperText('Longitude of the departure location'),

                    TextInput::make('arrival_lat')
                        ->label('Arrival Latitude')
                        ->required()
                        ->helperText('Latitude of the arrival location'),

                    TextInput::make('arrival_lng')
                        ->label('Arrival Longitude')
                        ->required()
                        ->helperText('Longitude of the arrival location'),

                    // Additional distance field removed as it was redundant
                    DateTimePicker::make('estimated_departure_time')
                        ->label('Estimated Departure')
                        ->rules(['date'])
                        ->nullable()
                        ->native(false)
                        ->helperText('Set the expected departure time'),

                    DateTimePicker::make('actual_departure_time')
                        ->label('Actual Departure')
                        ->rules(['date'])
                        ->nullable()
                        ->native(false)
                        ->helperText('Record the actual departure time if available'),

                    DateTimePicker::make('estimated_arrival_time')
                        ->label('Estimated Arrival')
                        ->rules(['date'])
                        ->nullable()
                        ->native(false)
                        ->helperText('Set the expected arrival time'),

                    DateTimePicker::make('actual_arrival_time')
                        ->label('Actual Arrival')
                        ->rules(['date'])
                        ->nullable()
                        ->native(false)
                        ->helperText('Record the actual arrival time if available'),

                    Textarea::make('pricing_breakdown')
                        ->label('Pricing Breakdown')
                        ->placeholder('Enter any pricing details or notes here...')
                        ->nullable()
                        ->json()
                        ->columnSpanFull()
                        ->rows(3)
                        ->helperText('Provide a breakdown of trip pricing, if necessary'),
                ])->columns(2),
        ]);
    }

    public static function table(Table $table): Table
    {
        return TripsTable::make($table);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return TripInfolist::infolist($infolist);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTrips::route('/'),
            'create' => Pages\CreateTrip::route('/create'),
            'view' => Pages\ViewTrip::route('/{record}'),
            'edit' => Pages\EditTrip::route('/{record}/edit'),
        ];
    }
}
