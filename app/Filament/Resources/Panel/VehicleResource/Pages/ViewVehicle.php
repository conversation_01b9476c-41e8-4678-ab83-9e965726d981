<?php

namespace App\Filament\Resources\Panel\VehicleResource\Pages;

use App\Enums\Vehicles\VehicleStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Filament\Resources\Panel\VehicleResource;
use App\Notifications\Firebase_notifications\VehicleApprovalNotification;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Log;

class ViewVehicle extends ViewRecord
{
    protected static string $resource = VehicleResource::class;

    public function getRelationManagers(): array
    {
        return [];
    }

    protected function getHeaderActions(): array
    {
        return [
            // Accept Vehicle Action
            Actions\Action::make('accept')
                ->label('Accept Vehicle')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->inprogress())
                ->form([
                    Select::make('vehicle_type_id')
                        ->label('Vehicle Type')
                        ->options(function () {
                            $vehicle = $this->record->load('vehicleModel.vehicleBrand', 'vehicleType');

                            // Use VehicleClassificationService to get suggested types
                            $classificationService = app(\App\Services\VehicleClassificationService::class);
                            $options = $classificationService->getSuggestedVehicleTypes($vehicle);

                            if (! empty($options)) {
                                return $options;
                            }

                            // Fallback: Use shared method for consistency
                            return $classificationService->getFormattedVehicleTypeOptions();
                        })
                        ->preload()
                        ->required()
                        ->searchable()
                        ->placeholder('Select vehicle type')
                        ->default(fn () => $this->record->vehicle_type_id)
                        ->helperText('⭐ Suggested types are shown first based on vehicle classification rules'),
                ])
                ->action(function (array $data) {
                    // Update the vehicle type if changed
                    $this->record->update([
                        'vehicle_type_id' => $data['vehicle_type_id'],
                    ]);

                    // Update vehicle status
                    $this->updateVehicleStatus(VehicleStatus::active);
                }),

            // Reject Vehicle Action
            Actions\Action::make('reject')
                ->label('Reject Vehicle')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Reject Vehicle')
                ->form([
                    Select::make('rejection_reason_columns')
                        ->multiple()
                        ->required()
                        ->options([
                            'license_plate_number' => 'License Plate Number',
                            'vehicle_brand' => 'Vehicle Brand',
                            'vehicle_model' => 'Vehicle Model',
                            'year' => 'Year',
                            'color' => 'Color',
                            'seat_number' => 'Number of Seats',
                            'is_covered' => 'Covered/Open (Freight)',
                            'weight_category' => 'Weight Category (Freight)',
                            'image' => 'Car Image',
                            'insurance' => 'Insurance Certificate',
                            'technical_inspection' => 'Technical Inspection',
                            'roaming_permit' => 'Roaming Permit',
                        ])
                        ->label('Select fields that need to be corrected'),

                    Textarea::make('rejection_reason')
                        ->label('Rejection Reason')
                        ->rows(5)
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Provide a reason for rejection...'),
                ])
                ->visible(fn () => $this->inprogress())
                ->action(fn (array $data) => $this->updateVehicleStatus(VehicleStatus::rejected, $data['rejection_reason'], $data['rejection_reason_columns'])),

            Actions\ViewAction::make()
                ->label('View Driver')
                ->url(function ($record) {
                    return $record->drivers->isNotEmpty()
                        ? DriverResource::getUrl('view', ['record' => $record->drivers->first()])
                        : null;
                })
                ->visible(fn () => $this->inprogress())
                ->tooltip(function ($record) {
                    return $record->drivers->isEmpty()
                        ? 'Assign a driver to proceed.'
                        : 'Driver pending approval. Accept or reject to update vehicle status.';
                }),
        ];
    }

    protected function inprogress(): bool
    {
        return $this->record->global_status === VehicleStatus::in_progress;
    }

    protected function updateVehicleStatus(VehicleStatus $status, ?string $reason = null, ?array $rejectionReasonColumns = null): void
    {
        $vehicle = $this->record;

        try {
            // Prepare the data to update
            $updateData = [
                'global_status' => $status->value,
                'rejection_reason' => $reason,
            ];

            // Only include rejection_reason_columns if the status is rejected
            if ($status === VehicleStatus::rejected) {
                $updateData['rejection_reason_columns'] = $rejectionReasonColumns;
                $updateData['previous_global_status'] = VehicleStatus::in_progress->value;
            } elseif ($status === VehicleStatus::active) {
                $updateData['previous_global_status'] = VehicleStatus::in_progress->value;
            }

            // Update the vehicle record
            $vehicle->update($updateData);

            // Send Firebase notification to driver
            $this->sendFirebaseNotification($vehicle, $status, $reason);

            // Send Filament notification
            $this->sendNotification($status);

            // Redirect to the list of vehicles after the action
            $this->redirect(VehicleResource::getUrl('index'));
        } catch (\Exception $e) {
            Log::error('Failed to update vehicle status', [
                'vehicle_id' => $vehicle->id,
                'error' => $e->getMessage(),
                'rejection_reason_columns' => $rejectionReasonColumns,
            ]);

            Notification::make()
                ->title('Error')
                ->body('Failed to update vehicle status. Please try again.')
                ->danger()
                ->send();
        }
    }

    protected function sendFirebaseNotification($vehicle, VehicleStatus $status, ?string $reason = null): void
    {
        // Get the driver associated with this vehicle
        $driver = $vehicle->drivers->first();

        if (! $driver || ! $driver->user) {
            Log::warning('No driver found for vehicle notification', [
                'vehicle_id' => $vehicle->id,
                'status' => $status->value,
            ]);

            return;
        }

        // Check if this is an active driver adding a new vehicle
        $isActiveDriver = $driver->global_status === \App\Enums\Drivers\DriverGlobalStatus::active;

        // Prepare notification data
        $notificationData = [
            'title' => $status === VehicleStatus::active
                ? 'تم قبول طلب المركبة'
                : 'تم رفض طلب المركبة',
            'description' => $status === VehicleStatus::active
                ? ($isActiveDriver
                    ? 'تهانينا! تم قبول طلب مركبتك الجديدة وهي الآن نشطة ومتاحة للرحلات.'
                    : 'تهانينا! تم قبول طلب مركبتك وهي الآن نشطة.')
                : "تم رفض طلب مركبتك. السبب: {$reason}",
            'vehicle_id' => $vehicle->id,
            'status' => $status->value,
        ];

        // Send the notification if the driver has FCM tokens
        if ($driver->user->tokens->isNotEmpty()) {
            try {
                $driver->user->notify(new VehicleApprovalNotification($driver->user->id, $notificationData));

                Log::info('Vehicle approval notification sent', [
                    'vehicle_id' => $vehicle->id,
                    'driver_id' => $driver->id,
                    'user_id' => $driver->user->id,
                    'status' => $status->value,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send vehicle approval notification', [
                    'vehicle_id' => $vehicle->id,
                    'driver_id' => $driver->id,
                    'error' => $e->getMessage(),
                ]);
            }
        } else {
            Log::warning('Driver has no FCM tokens for vehicle notification', [
                'vehicle_id' => $vehicle->id,
                'driver_id' => $driver->id,
                'user_id' => $driver->user->id,
            ]);
        }
    }

    protected function sendNotification(VehicleStatus $status): void
    {
        $title = match ($status) {
            VehicleStatus::active => 'Vehicle Approved',
            VehicleStatus::rejected => 'Vehicle Rejected',
            default => 'Vehicle Status Updated',
        };

        $body = match ($status) {
            VehicleStatus::active => 'The vehicle has been successfully approved and is now active.',
            VehicleStatus::rejected => 'The vehicle has been rejected. The driver will be notified.',
            default => 'The vehicle status has been updated.',
        };

        $notification = Notification::make()
            ->title($title)
            ->body($body);

        // Apply the appropriate notification type method
        if ($status === VehicleStatus::active) {
            $notification->success();
        } elseif ($status === VehicleStatus::rejected) {
            $notification->warning();
        } else {
            $notification->info();
        }

        $notification->send();
    }

    protected function resolveRecord(string|int $key): \Illuminate\Database\Eloquent\Model
    {
        return static::getModel()::withTrashed()->findOrFail($key);
    }
}
