<?php

namespace App\Filament\Resources\Panel\VehicleResource\Pages;

use App\Filament\Resources\Panel\DriverResource;
use App\Filament\Resources\Panel\VehicleResource;
use App\Notifications\Firebase_notifications\NotifyUser;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditVehicle extends EditRecord
{
    protected static string $resource = VehicleResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [Actions\DeleteAction::make()];
    // }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Update the record (this is the default behavior)
        $record->update($data);

        $driver = $record->drivers()->first(); // Get the first driver

        // If a driver exists, redirect to their edit page
        if ($driver) {
            $this->redirect(DriverResource::getUrl('edit', ['record' => $driver]));
        }

        // Fallback redirect (if no driver is associated)
        return $record;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $changes = []; // Track changes for notifications

        // Track changes in vehicle fields
        if ($this->record->license_plate_number !== ($data['license_plate_number'] ?? null)) {
            $changes[] = 'license plate number';
        }
        if ($this->record->vehicle_type_id !== ($data['vehicle_type_id'] ?? null)) {
            $changes[] = 'vehicle type';
        }
        if ($this->record->vehicle_model_id !== ($data['vehicle_model_id'] ?? null)) {
            $changes[] = 'vehicle model';
        }
        if ($this->record->year !== ($data['year'] ?? null)) {
            $changes[] = 'year';
        }
        if ($this->record->color !== ($data['color'] ?? null)) {
            $changes[] = 'color';
        }
        if ($this->record->insurance !== ($data['insurance'] ?? null)) {
            $changes[] = 'insurance';
        }
        if ($this->record->technical_inspection !== ($data['technical_inspection'] ?? null)) {
            $changes[] = 'technical inspection';
        }
        if ($this->record->roaming_permit !== ($data['roaming_permit'] ?? null)) {
            $changes[] = 'roaming permit';
        }
        if ($this->record->insurance_expiry !== ($data['insurance_expiry'] ?? null)) {
            $changes[] = 'insurance expiry';
        }
        if ($this->record->technical_inspection_expiry !== ($data['technical_inspection_expiry'] ?? null)) {
            $changes[] = 'technical inspection expiry';
        }
        if ($this->record->roaming_permit_expiry !== ($data['roaming_permit_expiry'] ?? null)) {
            $changes[] = 'roaming permit expiry';
        }

        // Send notifications based on changes
        if (! empty($changes)) {
            $this->sendNotification($changes);
        }

        return $data;
    }

    /**
     * Send notification to the driver about vehicle updates.
     */
    private function sendNotification(array $changes): void
    {
        // Get the first driver associated with the vehicle
        $driver = $this->record->drivers()->first();

        if ($driver) {
            // Get the user associated with the driver
            $user = $driver->user;

            if ($user && $user->driver) {
                try {
                    // Send notification to the rider
                    $notificationData = [
                        'title' => 'تم تعديل تفاصيل سيارتك',
                        'description' => 'تم تعديل تفاصيل سيارتك بنجاح. الرجاء التحقق من التفاصيل الجديدة.',
                    ];
                    if ($user->tokens->isNotEmpty()) {
                        $user->notifyNow(new NotifyUser($this->record, $notificationData));
                    }
                    // Generate notification data based on changes

                } catch (\Throwable $e) {
                    \Log::error('Failed to send notification to the user', [
                        'user_id' => $user->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }
    }

    /**
     * Generate notification data based on changes.
     */
    private function generateNotificationData(array $changes): array
    {
        if (count($changes) === 1) {
            $field = $changes[0];

            return [
                'title' => 'تم تعديل تفاصيل سيارتك',
                'description' => "تم تعديل $field بنجاح. الرجاء التحقق من التفاصيل الجديدة.",
            ];
        } else {
            return [
                'title' => 'تم تعديل تفاصيل سيارتك',
                'description' => 'تم تعديل عدة تفاصيل في سيارتك. الرجاء التحقق من التفاصيل الجديدة.',
            ];
        }
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Vehicle updated successfully');
    }
}
