<?php

namespace App\Filament\Resources\Panel\VehicleResource\Pages;

use App\Enums\Vehicles\VehicleStatus;
use App\Filament\Resources\Panel\VehicleResource;
use App\Models\Vehicle;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListVehicles extends ListRecords
{
    protected static string $resource = VehicleResource::class;

    //     protected function getHeaderActions(): array
    //     {
    //         return [
    // //            Actions\CreateAction::make(),
    //         ];
    //     }
    protected function resolveRecord($key): ?\Illuminate\Database\Eloquent\Model
    {
        return static::getModel()::withTrashed()->findOrFail($key);
    }

    public function getTabs(): array
    {
        $counts = Vehicle::whereIn('global_status', [
            VehicleStatus::in_progress->value,
            VehicleStatus::pending->value,
        ])->selectRaw('global_status, COUNT(*) as count')
            ->groupBy('global_status')
            ->pluck('count', 'global_status');

        return [
            'all' => Tab::make()->label('All')
                ->icon('mdi-account-multiple')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotIn('global_status', [
                    VehicleStatus::in_progress->value,
                    VehicleStatus::deleted->value,
                ])),

            'requests' => Tab::make()->label('Requests')
                ->icon('mdi-account-arrow-down')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('global_status', VehicleStatus::in_progress->value))
                ->badge(fn () => $counts[VehicleStatus::in_progress->value] ?? 0),

            'deleted' => Tab::make()
                ->label('Deleted')
                ->icon('mdi-account-multiple-remove')
                ->modifyQueryUsing(fn (Builder $query) => $query->withTrashed()
                    ->where('global_status', VehicleStatus::deleted->value)
                    ->orderByDesc('updated_at') // Sort by last updated (deleted) date
                ),
        ];
    }
}
