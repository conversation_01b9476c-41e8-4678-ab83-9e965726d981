<?php

namespace App\Filament\Resources\Panel\VehicleResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentIconPicker\Forms\IconPicker;
use Guava\FilamentIconPicker\Tables\IconColumn;

class VehicleEquipmentsRelationManager extends RelationManager
{
    protected static bool $canCreateAnother = false;

    protected static string $relationship = 'vehicleEquipments';

    // public function form(Form $form): Form
    // {
    //     return $form->schema([
    //         Forms\Components\Section::make('Equipment Details')
    //             ->description('Provide the basic details for the equipment.')
    //             ->schema([
    //                 Forms\Components\Grid::make(2)
    //                     ->schema([
    //                         Forms\Components\TextInput::make('name')
    //                             ->label('Equipment Name')
    //                             ->required()
    //                             ->columnSpanFull()
    //                             ->maxLength(255),

    //                         IconPicker::make('icon')
    //                             ->label('Icon')
    //                             ->columns([
    //                                 'default' => 1,
    //                                 'lg' => 3,
    //                                 '2xl' => 5,
    //                             ])
    //                             ->columnSpanFull()
    //                             ->required(),
    //                     ]),
    //             ]),

    //         Forms\Components\Section::make('Status')
    //             ->description('Set the current status of the equipment.')
    //             ->schema([
    //                 Forms\Components\Toggle::make('is_active')
    //                     ->label('Status')
    //                     ->rules(['boolean'])
    //                     ->required()
    //                     ->onIcon('heroicon-m-check')
    //                     ->offIcon('heroicon-m-x-mark')
    //                     ->inline()
    //                     ->default(true),
    //             ]),
    //     ]);
    // }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_en')
                    ->label('Equipment Name'),

                IconColumn::make('icon')
                    ->label('Icon'),

                Tables\Columns\BooleanColumn::make('status')
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle'),

            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->recordTitleAttribute('name_en')
                    ->attachAnother(false),
            ])
            ->actions([
                Tables\Actions\DetachAction::make(),
            ])
            ->modifyQueryUsing(fn ($query) => $query->withTrashed());
    }
}
