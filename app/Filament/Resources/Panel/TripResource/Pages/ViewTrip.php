<?php

namespace App\Filament\Resources\Panel\TripResource\Pages;

use App\Filament\Resources\Panel\TripResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTrip extends ViewRecord
{
    protected static string $resource = TripResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [Actions\EditAction::make()];
    // }

    /**
     * Get the URL to redirect to when the record is saved or deleted.
     */
    protected function getRedirectUrl(): string
    {
        // Get the active tab from the request, default to 'all' if not set
        $activeTab = request()->query('activeTab', 'all');

        // Generate the URL with the active tab as a query parameter
        return TripResource::getUrl('index')."?activeTab={$activeTab}";
    }
}
