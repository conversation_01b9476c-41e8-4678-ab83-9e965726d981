<?php

namespace App\Filament\Resources\Panel\TripResource\Pages;

use App\Enums\Trips\TripStatus;
use App\Filament\Resources\Panel\TripResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListTrips extends ListRecords
{
    protected static string $resource = TripResource::class;

    public ?string $activeTab = 'all'; // Default tab, typed as nullable string

    public function mount(): void
    {
        if (request()->has('activeTab') && in_array(request()->get('activeTab'), ['all', 'current'])) {
            $this->activeTab = request()->get('activeTab');
        }

        parent::mount();
    }

    public function updatedActiveTab(): void
    {
        // Reset the status filter to clear selected values
        if (isset($this->tableFilters['status']['status'])) {
            $this->tableFilters['status']['status'] = []; // Clear selected statuses
        }
        $this->dispatch('$refresh'); // Refresh the component to re-evaluate filter options
    }

    public function getTabs(): array
    {
        $currentStatuses = [
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
        ];

        return [
            'all' => Tab::make()
                ->label('All')
                ->icon('heroicon-o-list-bullet')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotIn('status', $currentStatuses)),

            'current' => Tab::make()
                ->label('Current')
                ->icon('heroicon-o-clock')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', $currentStatuses)),
        ];
    }

    protected function getTableRecordUrlParameters(string $operation = 'view'): array
    {
        return [
            'activeTab' => $this->activeTab,
        ];
    }
}
