<?php

namespace App\Filament\Resources\Panel\UserResource\Pages;

use App\Filament\Resources\Panel\UserResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        if (isset($data['phone_number']) && strpos($data['phone_number'], '+218') === 0) {
            $number = substr($data['phone_number'], 4); // Remove '+218'
            $data['phone_number'] = substr_replace($number, '-', 2, 0); // Insert '-' after first two digits
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if (isset($data['phone_number'])) {
            $data['phone_number'] = '+218'.preg_replace('/[^0-9]/', '', $data['phone_number']);
        }

        return $data;
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->title('User updated successfully')
            ->success();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }
}
