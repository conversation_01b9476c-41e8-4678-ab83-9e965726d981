<?php

namespace App\Filament\Resources\Panel\UserResource\Pages;

use App\Filament\Resources\Panel\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->createAnother(false)
        ];
    }
}
