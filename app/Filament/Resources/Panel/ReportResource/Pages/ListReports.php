<?php

namespace App\Filament\Resources\Panel\ReportResource\Pages;

use App\Filament\Resources\Panel\ReportResource;
use Filament\Resources\Pages\Page;

class ListReports extends Page
{
    protected static string $resource = ReportResource::class;

    protected static string $view = 'filament.resources.report-resource.pages.list-reports';

    protected static ?string $title = 'Reports Dashboard';

    protected static ?string $navigationLabel = 'Reports';

    public function getHeaderActions(): array
    {
        return [
            // Action::make('drivers_report')
            //     ->label('Drivers Report')
            //     ->icon('heroicon-o-users')
            //     ->color('primary')
            //     ->url(fn (): string => static::getResource()::getUrl('drivers')),

            // Action::make('riders_report')
            //     ->label('Riders Report')
            //     ->icon('heroicon-o-user-group')
            //     ->color('success')
            //     ->url(fn (): string => static::getResource()::getUrl('riders')),

            // Action::make('trips_report')
            //     ->label('Trips Report')
            //     ->icon('heroicon-o-map')
            //     ->color('warning')
            //     ->url(fn (): string => static::getResource()::getUrl('trips')),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Resources\Panel\ReportResource\Widgets\ReportsOverviewWidget::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            \App\Filament\Resources\Panel\ReportResource\Widgets\RevenueAnalyticsApexWidget::class,
            \App\Filament\Resources\Panel\ReportResource\Widgets\TripTimelineWidget::class,
            \App\Filament\Resources\Panel\ReportResource\Widgets\UserGrowthApexWidget::class,
            \App\Filament\Resources\Panel\ReportResource\Widgets\DriverPerformanceRadarWidget::class,
            \App\Filament\Resources\Panel\ReportResource\Widgets\AreaRevenueHeatmapWidget::class,
            \App\Filament\Resources\Panel\ReportResource\Widgets\TopPerformersWidget::class,
        ];
    }
}
