<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Driver;
use App\Models\Trip;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ReportsOverviewWidget extends BaseWidget
{
    use CalculatesRevenue;

    protected static ?string $pollingInterval = '30s';

    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        // Get all data efficiently with minimal queries
        $data = $this->getOptimizedData();

        // Calculate growth metrics
        $tripGrowth = $data['yesterdayTrips'] > 0
            ? round((($data['todayTrips'] - $data['yesterdayTrips']) / $data['yesterdayTrips']) * 100, 1)
            : ($data['todayTrips'] > 0 ? 100 : 0);

        $revenueGrowth = $data['lastWeekRevenue'] > 0
            ? round((($data['thisWeekRevenue'] - $data['lastWeekRevenue']) / $data['lastWeekRevenue']) * 100, 1)
            : ($data['thisWeekRevenue'] > 0 ? 100 : 0);

        $driverUtilization = $data['totalActiveDrivers'] > 0
            ? round(($data['driversWithTripsToday'] / $data['totalActiveDrivers']) * 100, 1)
            : 0;

        $completionRate = $data['totalTrips'] > 0
            ? round(($data['completedTrips'] / $data['totalTrips']) * 100, 1)
            : 0;

        return [
            Stat::make('Trip Growth', ($tripGrowth >= 0 ? '+' : '').$tripGrowth.'%')
                ->description("{$data['todayTrips']} today vs {$data['yesterdayTrips']} yesterday")
                ->descriptionIcon($tripGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($tripGrowth >= 0 ? 'success' : 'danger')
                ->chart($data['tripChart'])
                ->icon('heroicon-o-chart-bar-square'),

            Stat::make('Revenue Growth', ($revenueGrowth >= 0 ? '+' : '').$revenueGrowth.'%')
                ->description('This week vs last week')
                ->descriptionIcon($revenueGrowth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($revenueGrowth >= 0 ? 'success' : 'danger')
                ->chart($data['revenueChart'])
                ->icon('heroicon-o-currency-dollar'),

            Stat::make('Driver Utilization', $driverUtilization.'%')
                ->description("{$data['driversWithTripsToday']}/{$data['totalActiveDrivers']} drivers active today")
                ->descriptionIcon($driverUtilization >= 70 ? 'heroicon-m-check-circle' : 'heroicon-m-exclamation-triangle')
                ->color($driverUtilization >= 70 ? 'success' : ($driverUtilization >= 40 ? 'warning' : 'danger'))
                ->chart($data['driverChart'])
                ->icon('heroicon-o-users'),

            Stat::make('Completion Rate', $completionRate.'%')
                ->description('Trip success rate')
                ->descriptionIcon($completionRate >= 80 ? 'heroicon-m-check-circle' : 'heroicon-m-exclamation-triangle')
                ->color($completionRate >= 80 ? 'success' : ($completionRate >= 60 ? 'warning' : 'danger'))
                ->chart($data['completionChart'])
                ->icon('heroicon-o-check-circle'),
        ];
    }

    /**
     * Get all data efficiently with minimal database queries
     */
    private function getOptimizedData(): array
    {
        // Define date ranges
        $today = Carbon::today();
        $yesterday = Carbon::yesterday();
        $thisWeek = Carbon::now()->startOfWeek();
        $lastWeek = Carbon::now()->subWeek()->startOfWeek();
        $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();
        $weekAgo = Carbon::now()->subDays(6)->startOfDay();

        // Get all trips for the last week + today/yesterday (single query)
        $allTrips = Trip::whereBetween('created_at', [$weekAgo, Carbon::now()->endOfDay()])
            ->select('created_at', 'status', 'pricing_breakdown', 'driver_id')
            ->get();

        // Get all trips for week comparisons (2 additional queries)
        $thisWeekTrips = Trip::whereBetween('created_at', [$thisWeek, Carbon::now()])
            ->whereNotNull('pricing_breakdown')
            ->select('pricing_breakdown')
            ->get();

        $lastWeekTrips = Trip::whereBetween('created_at', [$lastWeek, $lastWeekEnd])
            ->whereNotNull('pricing_breakdown')
            ->select('pricing_breakdown')
            ->get();

        // Get driver data (2 queries)
        $totalActiveDrivers = Driver::where('global_status', 'active')->count();
        $allDriversWithTrips = Driver::whereHas('trips', function ($query) use ($weekAgo) {
            $query->whereBetween('created_at', [$weekAgo, Carbon::now()->endOfDay()]);
        })->with(['trips' => function ($query) use ($weekAgo) {
            $query->whereBetween('created_at', [$weekAgo, Carbon::now()->endOfDay()])
                ->select('driver_id', 'created_at');
        }])->get();

        // Get overall trip statistics (1 query)
        $overallStats = Trip::selectRaw('
            COUNT(*) as total_trips,
            COUNT(CASE WHEN status = \'completed\' THEN 1 END) as completed_trips
        ')->first();

        // Process data in memory
        $todayTrips = $allTrips->filter(fn ($trip) => $trip->created_at->isToday())->count();
        $yesterdayTrips = $allTrips->filter(fn ($trip) => $trip->created_at->isYesterday())->count();

        $driversWithTripsToday = $allDriversWithTrips->filter(function ($driver) {
            return $driver->trips->some(fn ($trip) => $trip->created_at->isToday());
        })->count();

        // Calculate revenues in memory
        $thisWeekRevenue = $thisWeekTrips->sum(function ($trip) {
            $pricing = json_decode($trip->pricing_breakdown, true);

            return $pricing['total'] ?? 0;
        });

        $lastWeekRevenue = $lastWeekTrips->sum(function ($trip) {
            $pricing = json_decode($trip->pricing_breakdown, true);

            return $pricing['total'] ?? 0;
        });

        // Generate chart data in memory
        $chartData = $this->generateChartsFromData($allTrips, $allDriversWithTrips, $totalActiveDrivers);

        return [
            'todayTrips' => $todayTrips,
            'yesterdayTrips' => $yesterdayTrips,
            'thisWeekRevenue' => $thisWeekRevenue,
            'lastWeekRevenue' => $lastWeekRevenue,
            'totalActiveDrivers' => $totalActiveDrivers,
            'driversWithTripsToday' => $driversWithTripsToday,
            'totalTrips' => $overallStats->total_trips,
            'completedTrips' => $overallStats->completed_trips,
            'tripChart' => $chartData['tripChart'],
            'revenueChart' => $chartData['revenueChart'],
            'completionChart' => $chartData['completionChart'],
            'driverChart' => $chartData['driverChart'],
        ];
    }

    /**
     * Generate all chart data from collected trips
     */
    private function generateChartsFromData($allTrips, $allDriversWithTrips, $totalActiveDrivers): array
    {
        $tripChart = [];
        $revenueChart = [];
        $completionChart = [];
        $driverChart = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);

            // Filter trips for this date
            $dayTrips = $allTrips->filter(fn ($trip) => $trip->created_at->isSameDay($date));

            // Trip count
            $tripChart[] = $dayTrips->count();

            // Revenue
            $revenue = $dayTrips->sum(function ($trip) {
                if (! $trip->pricing_breakdown) {
                    return 0;
                }
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });
            $revenueChart[] = round($revenue, 0);

            // Completion rate
            $totalDayTrips = $dayTrips->count();
            $completedDayTrips = $dayTrips->where('status', 'completed')->count();
            $completionRate = $totalDayTrips > 0 ? round(($completedDayTrips / $totalDayTrips) * 100, 0) : 0;
            $completionChart[] = $completionRate;

            // Driver utilization
            $driversWithTripsThisDay = $allDriversWithTrips->filter(function ($driver) use ($date) {
                return $driver->trips->some(fn ($trip) => $trip->created_at->isSameDay($date));
            })->count();
            $utilization = $totalActiveDrivers > 0 ? round(($driversWithTripsThisDay / $totalActiveDrivers) * 100, 0) : 0;
            $driverChart[] = $utilization;
        }

        return [
            'tripChart' => $tripChart,
            'revenueChart' => $revenueChart,
            'completionChart' => $completionChart,
            'driverChart' => $driverChart,
        ];
    }
}
