<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Driver;
use App\Models\Rider;
use Carbon\Carbon;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class UserGrowthApexWidget extends ApexChartWidget
{
    /**
     * Chart Id
     */
    protected static ?string $chartId = 'userGrowthChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'User Growth Trends';

    /**
     * Widget Subheading
     */
    protected static ?string $subheading = 'Driver and rider registration trends';

    /**
     * Chart height
     */
    protected static ?int $contentHeight = 350;

    /**
     * Widget column span
     */
    protected int|string|array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    /**
     * Polling interval
     */
    protected static ?string $pollingInterval = '120s';

    /**
     * Default filter
     */
    public ?string $filter = 'month';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        $data = $this->getChartData();

        // Add fallback data if no data is available
        if (empty($data['drivers']) || array_sum($data['drivers']) == 0) {
            $data = [
                'labels' => ['No Data'],
                'drivers' => [0],
                'riders' => [0],
                'total' => [0],
            ];
        }

        return [
            'chart' => [
                'type' => 'line',
                'height' => 350,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Drivers',
                    'data' => $data['drivers'],
                ],
                [
                    'name' => 'Riders',
                    'data' => $data['riders'],
                ],
                [
                    'name' => 'Total Users',
                    'data' => $data['total'],
                ],
            ],
            'xaxis' => [
                'categories' => $data['labels'],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                    ],
                ],
                'axisBorder' => [
                    'show' => true,
                ],
                'axisTicks' => [
                    'show' => true,
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => 'Number of Users',
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
                'min' => 0,
            ],
            'colors' => ['#f59e0b', '#3b82f6', '#8b5cf6'],
            'stroke' => [
                'width' => 3,
                'curve' => 'smooth',
            ],
            'grid' => [
                'show' => true,
                'borderColor' => '#e5e7eb',
                'strokeDashArray' => 3,
            ],
            'legend' => [
                'show' => true,
                'position' => 'top',
                'horizontalAlign' => 'right',
                'fontFamily' => 'inherit',
            ],
            'tooltip' => [
                'shared' => true,
                'intersect' => false,
                'theme' => 'dark',
            ],
            'theme' => [
                'mode' => 'dark',
            ],
            'markers' => [
                'size' => 5,
                'strokeWidth' => 2,
                'strokeColors' => '#ffffff',
                'hover' => [
                    'size' => 7,
                ],
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
        ];
    }

    /**
     * Get filters for the chart
     */
    protected function getFilters(): ?array
    {
        return [
            'week' => 'Last Week',
            'month' => 'Last Month',
            'quarter' => 'Last Quarter',
            'year' => 'Last Year',
        ];
    }

    /**
     * Get chart data based on filter
     */
    private function getChartData(): array
    {
        return match ($this->filter) {
            'week' => $this->getWeekData(),
            'month' => $this->getMonthData(),
            'quarter' => $this->getQuarterData(),
            'year' => $this->getYearData(),
            default => $this->getMonthData(),
        };
    }

    /**
     * Get last week's data (daily)
     */
    private function getWeekData(): array
    {
        // Get counts for each date we need
        $dates = [];
        for ($i = 6; $i >= 0; $i--) {
            $dates[] = Carbon::now()->subDays($i);
        }

        // Get all drivers and riders with their creation dates (2 queries total)
        $drivers = Driver::select('created_at')->get();
        $riders = Rider::select('created_at')->get();

        $driverCounts = [];
        $riderCounts = [];

        foreach ($dates as $date) {
            // Count cumulative users up to this date in memory
            $driverCounts[] = $drivers->where('created_at', '<=', $date->endOfDay())->count();
            $riderCounts[] = $riders->where('created_at', '<=', $date->endOfDay())->count();
        }

        $labels = [];
        foreach ($dates as $date) {
            $labels[] = $date->format('M j');
        }

        return [
            'labels' => $labels,
            'drivers' => $driverCounts,
            'riders' => $riderCounts,
            'total' => array_map(fn ($d, $r) => $d + $r, $driverCounts, $riderCounts),
        ];
    }

    /**
     * Get last month's data (daily)
     */
    private function getMonthData(): array
    {
        // Get dates for the last 30 days
        $dates = [];
        for ($i = 29; $i >= 0; $i--) {
            $dates[] = Carbon::now()->subDays($i);
        }

        // Get all drivers and riders with their creation dates (2 queries total)
        $drivers = Driver::select('created_at')->get();
        $riders = Rider::select('created_at')->get();

        $driverCounts = [];
        $riderCounts = [];

        foreach ($dates as $date) {
            // Count cumulative users up to this date in memory
            $driverCounts[] = $drivers->where('created_at', '<=', $date->endOfDay())->count();
            $riderCounts[] = $riders->where('created_at', '<=', $date->endOfDay())->count();
        }

        $labels = [];
        foreach ($dates as $date) {
            $labels[] = $date->format('M j');
        }

        return [
            'labels' => $labels,
            'drivers' => $driverCounts,
            'riders' => $riderCounts,
            'total' => array_map(fn ($d, $r) => $d + $r, $driverCounts, $riderCounts),
        ];
    }

    /**
     * Get last quarter's data (weekly)
     */
    private function getQuarterData(): array
    {
        // Get dates for the last 13 weeks
        $dates = [];
        for ($i = 12; $i >= 0; $i--) {
            $dates[] = Carbon::now()->subWeeks($i);
        }

        // Get all drivers and riders with their creation dates (2 queries total)
        $drivers = Driver::select('created_at')->get();
        $riders = Rider::select('created_at')->get();

        $driverCounts = [];
        $riderCounts = [];

        foreach ($dates as $date) {
            // Count cumulative users up to this date in memory
            $driverCounts[] = $drivers->where('created_at', '<=', $date->endOfDay())->count();
            $riderCounts[] = $riders->where('created_at', '<=', $date->endOfDay())->count();
        }

        $labels = [];
        foreach ($dates as $date) {
            $labels[] = $date->format('M j');
        }

        return [
            'labels' => $labels,
            'drivers' => $driverCounts,
            'riders' => $riderCounts,
            'total' => array_map(fn ($d, $r) => $d + $r, $driverCounts, $riderCounts),
        ];
    }

    /**
     * Get last year's data (monthly)
     */
    private function getYearData(): array
    {
        // Get dates for the last 12 months
        $dates = [];
        for ($i = 11; $i >= 0; $i--) {
            $dates[] = Carbon::now()->subMonths($i)->endOfMonth();
        }

        // Get all drivers and riders with their creation dates (2 queries total)
        $drivers = Driver::select('created_at')->get();
        $riders = Rider::select('created_at')->get();

        $driverCounts = [];
        $riderCounts = [];

        foreach ($dates as $date) {
            // Count cumulative users up to this date in memory
            $driverCounts[] = $drivers->where('created_at', '<=', $date->endOfDay())->count();
            $riderCounts[] = $riders->where('created_at', '<=', $date->endOfDay())->count();
        }

        $labels = [];
        foreach ($dates as $date) {
            $labels[] = $date->format('M Y');
        }

        return [
            'labels' => $labels,
            'drivers' => $driverCounts,
            'riders' => $riderCounts,
            'total' => array_map(fn ($d, $r) => $d + $r, $driverCounts, $riderCounts),
        ];
    }
}
