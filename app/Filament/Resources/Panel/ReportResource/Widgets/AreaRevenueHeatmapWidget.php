<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Area;
use App\Models\Trip;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class AreaRevenueHeatmapWidget extends ApexChartWidget
{
    use CalculatesRevenue;

    /**
     * Chart Id
     */
    protected static ?string $chartId = 'areaRevenueHeatmapChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'Area Performance Heatmap';

    /**
     * Widget Subheading
     */
    protected static ?string $subheading = 'Revenue distribution across areas and time';

    /**
     * Chart height
     */
    protected static ?int $contentHeight = 400;

    /**
     * Widget column span
     */
    protected int|string|array $columnSpan = 'full';

    /**
     * Polling interval
     */
    protected static ?string $pollingInterval = '300s';

    /**
     * Default filter
     */
    public ?string $filter = 'week';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        $data = $this->getHeatmapData();

        return [
            'chart' => [
                'type' => 'heatmap',
                'height' => 400,
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
            ],
            'series' => $data['series'],
            'xaxis' => [
                'categories' => $data['categories'],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                        'fontSize' => '12px',
                    ],
                ],
                'axisBorder' => [
                    'show' => true,
                ],
                'axisTicks' => [
                    'show' => true,
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                        'fontSize' => '12px',
                    ],
                ],
            ],
            'colors' => ['#10b981'],
            'plotOptions' => [
                'heatmap' => [
                    'shadeIntensity' => 0.5,
                    'radius' => 4,
                    'useFillColorAsStroke' => true,
                    'colorScale' => [
                        'ranges' => [
                            [
                                'from' => 0,
                                'to' => 50,
                                'name' => 'Low',
                                'color' => '#dcfce7',
                            ],
                            [
                                'from' => 51,
                                'to' => 200,
                                'name' => 'Medium',
                                'color' => '#86efac',
                            ],
                            [
                                'from' => 201,
                                'to' => 500,
                                'name' => 'High',
                                'color' => '#22c55e',
                            ],
                            [
                                'from' => 501,
                                'to' => 1000,
                                'name' => 'Very High',
                                'color' => '#16a34a',
                            ],
                            [
                                'from' => 1001,
                                'to' => 9999,
                                'name' => 'Excellent',
                                'color' => '#15803d',
                            ],
                        ],
                    ],
                ],
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
            'stroke' => [
                'width' => 1,
                'colors' => ['#ffffff'],
            ],
            'legend' => [
                'show' => true,
                'position' => 'bottom',
                'horizontalAlign' => 'center',
                'fontFamily' => 'inherit',
            ],
            'tooltip' => [
                'enabled' => true,
                'theme' => 'dark',
                'style' => [
                    'fontSize' => '12px',
                    'fontFamily' => 'inherit',
                ],
                'y' => [
                    'formatter' => 'function(val) { return val.toFixed(2) + " LYD"; }',
                ],
            ],
            'theme' => [
                'mode' => 'dark',
            ],
            'grid' => [
                'show' => false,
            ],
        ];
    }

    /**
     * Get filters for the chart
     */
    protected function getFilters(): ?array
    {
        return [
            'week' => 'Last 7 Days',
            'month' => 'Last 30 Days',
        ];
    }

    /**
     * Get heatmap data based on filter
     */
    private function getHeatmapData(): array
    {
        return match ($this->filter) {
            'week' => $this->getWeekData(),
            'month' => $this->getMonthData(),
            default => $this->getWeekData(),
        };
    }

    /**
     * Get last 7 days data
     */
    private function getWeekData(): array
    {
        $startDate = Carbon::now()->subDays(6)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        // Get areas and all trips in a single efficient query
        $areas = Area::where('is_active', true)->select('id', 'name_en')->take(10)->get();

        if ($areas->isEmpty()) {
            $categories = [];
            for ($i = 6; $i >= 0; $i--) {
                $categories[] = Carbon::now()->subDays($i)->format('M j');
            }

            return [
                'series' => [[
                    'name' => 'No Data',
                    'data' => array_map(fn ($cat) => ['x' => $cat, 'y' => 0], $categories),
                ]],
                'categories' => $categories,
            ];
        }

        // Single query to get all trips for all areas in the date range
        $allTrips = Trip::whereIn('departure_area_id', $areas->pluck('id'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->select('departure_area_id', 'created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(['departure_area_id', function ($trip) {
                return $trip->created_at->format('Y-m-d');
            }]);

        // Generate date categories
        $categories = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $categories[] = $date->format('M j');
        }

        $series = [];
        foreach ($areas as $area) {
            $data = [];
            $areaTrips = $allTrips->get($area->id, collect());

            for ($i = 6; $i >= 0; $i--) {
                $date = Carbon::now()->subDays($i);
                $dateKey = $date->format('Y-m-d');
                $dayTrips = $areaTrips->get($dateKey, collect());

                // Calculate revenue for this day in memory
                $revenue = $dayTrips->sum(function ($trip) {
                    $pricing = json_decode($trip->pricing_breakdown, true);

                    return $pricing['total'] ?? 0;
                });

                $data[] = [
                    'x' => $date->format('M j'),
                    'y' => round($revenue, 2),
                ];
            }

            $series[] = [
                'name' => $area->name_en,
                'data' => $data,
            ];
        }

        return [
            'series' => $series,
            'categories' => $categories,
        ];
    }

    /**
     * Get last 30 days data (grouped by weeks)
     */
    private function getMonthData(): array
    {
        $startDate = Carbon::now()->subWeeks(3)->startOfWeek();
        $endDate = Carbon::now()->endOfWeek();

        // Get areas and all trips in a single efficient query
        $areas = Area::where('is_active', true)->select('id', 'name_en')->take(10)->get();

        // Generate week categories
        $categories = [];
        $weekRanges = [];
        for ($i = 3; $i >= 0; $i--) {
            $weekStart = Carbon::now()->subWeeks($i)->startOfWeek();
            $weekEnd = Carbon::now()->subWeeks($i)->endOfWeek();
            $categories[] = $weekStart->format('M j').' - '.$weekEnd->format('j');
            $weekRanges[] = ['start' => $weekStart, 'end' => $weekEnd, 'key' => $weekStart->format('Y-W')];
        }

        if ($areas->isEmpty()) {
            return [
                'series' => [[
                    'name' => 'No Data',
                    'data' => array_map(fn ($cat) => ['x' => $cat, 'y' => 0], $categories),
                ]],
                'categories' => $categories,
            ];
        }

        // Single query to get all trips for all areas in the date range
        $allTrips = Trip::whereIn('departure_area_id', $areas->pluck('id'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->select('departure_area_id', 'created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(['departure_area_id', function ($trip) {
                return $trip->created_at->format('Y-W'); // Group by year-week
            }]);

        $series = [];
        foreach ($areas as $area) {
            $data = [];
            $areaTrips = $allTrips->get($area->id, collect());

            foreach ($weekRanges as $index => $week) {
                $weekTrips = $areaTrips->get($week['key'], collect());

                // Calculate revenue for this week in memory
                $revenue = $weekTrips->sum(function ($trip) {
                    $pricing = json_decode($trip->pricing_breakdown, true);

                    return $pricing['total'] ?? 0;
                });

                $data[] = [
                    'x' => $categories[$index],
                    'y' => round($revenue, 2),
                ];
            }

            $series[] = [
                'name' => $area->name_en,
                'data' => $data,
            ];
        }

        return [
            'series' => $series,
            'categories' => $categories,
        ];
    }
}
