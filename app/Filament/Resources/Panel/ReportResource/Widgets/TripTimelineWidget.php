<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Trip;
use Carbon\Carbon;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class TripTimelineWidget extends ApexChartWidget
{
    /**
     * Chart Id
     */
    protected static ?string $chartId = 'tripTimelineChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'Trip Activity Timeline';

    /**
     * Widget Subheading
     */
    protected static ?string $subheading = 'Hourly trip patterns and peak times';

    /**
     * Chart height
     */
    protected static ?int $contentHeight = 350;

    /**
     * Widget column span
     */
    protected int|string|array $columnSpan = 'full';

    /**
     * Polling interval
     */
    protected static ?string $pollingInterval = '120s';

    /**
     * Default filter
     */
    public ?string $filter = 'today';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        $data = $this->getTimelineData();

        // Add fallback data if no data is available
        if (empty($data['completed']) || array_sum($data['completed']) == 0) {
            $data = [
                'labels' => ['No Data'],
                'completed' => [0],
                'cancelled' => [0],
                'active' => [0],
            ];
        }

        return [
            'chart' => [
                'type' => 'bar',
                'height' => 350,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Completed Trips',
                    'data' => $data['completed'],
                ],
                [
                    'name' => 'Cancelled Trips',
                    'data' => $data['cancelled'],
                ],
                [
                    'name' => 'Active Trips',
                    'data' => $data['active'],
                ],
            ],
            'xaxis' => [
                'categories' => $data['labels'],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                    ],
                ],
                'axisBorder' => [
                    'show' => true,
                ],
                'axisTicks' => [
                    'show' => true,
                ],
            ],
            'yaxis' => [
                'title' => [
                    'text' => 'Number of Trips',
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                    ],
                ],
                'min' => 0,
            ],
            'colors' => ['#10b981', '#ef4444', '#f59e0b'],
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,
                    'columnWidth' => '70%',
                    'endingShape' => 'rounded',
                    'borderRadius' => 4,
                    'dataLabels' => [
                        'position' => 'top',
                    ],
                ],
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
            'stroke' => [
                'show' => true,
                'width' => 2,
                'colors' => ['transparent'],
            ],
            'grid' => [
                'show' => true,
                'borderColor' => '#e5e7eb',
                'strokeDashArray' => 3,
            ],
            'legend' => [
                'show' => true,
                'position' => 'top',
                'horizontalAlign' => 'right',
                'fontFamily' => 'inherit',
            ],
            'tooltip' => [
                'shared' => true,
                'intersect' => false,
                'theme' => 'dark',
                'y' => [
                    'formatter' => 'function(val) { return val + " trips"; }',
                ],
            ],
            'theme' => [
                'mode' => 'dark',
            ],
            'fill' => [
                'opacity' => 0.8,
            ],
        ];
    }

    /**
     * Get filters for the chart
     */
    protected function getFilters(): ?array
    {
        return [
            'today' => 'Today',
            'yesterday' => 'Yesterday',
            'week_avg' => 'Week Average',
        ];
    }

    /**
     * Get timeline data based on filter
     */
    private function getTimelineData(): array
    {
        return match ($this->filter) {
            'today' => $this->getTodayData(),
            'yesterday' => $this->getYesterdayData(),
            'week_avg' => $this->getWeekAverageData(),
            default => $this->getTodayData(),
        };
    }

    /**
     * Get today's hourly data
     */
    private function getTodayData(): array
    {
        $startTime = Carbon::today();
        $endTime = Carbon::today()->endOfDay();

        // Single query to get all trips for today
        $trips = Trip::whereBetween('created_at', [$startTime, $endTime])
            ->select('created_at', 'status')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('H');
            });

        $labels = [];
        $completedData = [];
        $cancelledData = [];
        $activeData = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $labels[] = sprintf('%02d:00', $hour);
            $hourKey = sprintf('%02d', $hour);

            $hourTrips = $trips->get($hourKey, collect());

            $completed = $hourTrips->where('status', 'completed')->count();
            $cancelled = $hourTrips->where('status', 'canceled')->count(); // Note: 'canceled' not 'cancelled'
            $active = $hourTrips->whereIn('status', ['assigned', 'on_trip', 'driver_arriving', 'driver_arrived'])->count();

            $completedData[] = $completed;
            $cancelledData[] = $cancelled;
            $activeData[] = $active;
        }

        return [
            'labels' => $labels,
            'completed' => $completedData,
            'cancelled' => $cancelledData,
            'active' => $activeData,
        ];
    }

    /**
     * Get yesterday's hourly data
     */
    private function getYesterdayData(): array
    {
        $startTime = Carbon::yesterday();
        $endTime = Carbon::yesterday()->endOfDay();

        // Single query to get all trips for yesterday
        $trips = Trip::whereBetween('created_at', [$startTime, $endTime])
            ->select('created_at', 'status')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('H');
            });

        $labels = [];
        $completedData = [];
        $cancelledData = [];
        $activeData = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $labels[] = sprintf('%02d:00', $hour);
            $hourKey = sprintf('%02d', $hour);

            $hourTrips = $trips->get($hourKey, collect());

            $completed = $hourTrips->where('status', 'completed')->count();
            $cancelled = $hourTrips->where('status', 'canceled')->count(); // Note: 'canceled' not 'cancelled'
            $active = $hourTrips->whereIn('status', ['assigned', 'on_trip', 'driver_arriving', 'driver_arrived'])->count();

            $completedData[] = $completed;
            $cancelledData[] = $cancelled;
            $activeData[] = $active;
        }

        return [
            'labels' => $labels,
            'completed' => $completedData,
            'cancelled' => $cancelledData,
            'active' => $activeData,
        ];
    }

    /**
     * Get week average hourly data
     */
    private function getWeekAverageData(): array
    {
        $startTime = Carbon::now()->subDays(6)->startOfDay();
        $endTime = Carbon::now()->endOfDay();

        // Single query to get all trips for the last 7 days
        $trips = Trip::whereBetween('created_at', [$startTime, $endTime])
            ->select('created_at', 'status')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('H');
            });

        $labels = [];
        $completedData = [];
        $cancelledData = [];
        $activeData = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $labels[] = sprintf('%02d:00', $hour);
            $hourKey = sprintf('%02d', $hour);

            $hourTrips = $trips->get($hourKey, collect());

            $completed = $hourTrips->where('status', 'completed')->count();
            $cancelled = $hourTrips->where('status', 'canceled')->count(); // Note: 'canceled' not 'cancelled'
            $active = $hourTrips->whereIn('status', ['assigned', 'on_trip', 'driver_arriving', 'driver_arrived'])->count();

            // Calculate averages over 7 days
            $completedData[] = round($completed / 7, 1);
            $cancelledData[] = round($cancelled / 7, 1);
            $activeData[] = round($active / 7, 1);
        }

        return [
            'labels' => $labels,
            'completed' => $completedData,
            'cancelled' => $cancelledData,
            'active' => $activeData,
        ];
    }
}
