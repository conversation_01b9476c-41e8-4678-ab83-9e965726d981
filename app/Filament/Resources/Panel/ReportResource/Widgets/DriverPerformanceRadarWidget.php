<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Driver;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class DriverPerformanceRadarWidget extends ApexChartWidget
{
    use CalculatesRevenue;

    /**
     * Chart Id
     */
    protected static ?string $chartId = 'driverPerformanceRadarChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'Driver Performance Metrics';

    /**
     * Widget Subheading
     */
    protected static ?string $subheading = 'Multi-dimensional performance analysis';

    /**
     * Chart height
     */
    protected static ?int $contentHeight = 400;

    /**
     * Widget column span
     */
    protected int|string|array $columnSpan = [
        'md' => 2,
        'xl' => 1,
    ];

    /**
     * Polling interval
     */
    protected static ?string $pollingInterval = '300s';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        $data = $this->getPerformanceData();

        // Add fallback data if no data is available
        if (empty($data['average']) || array_sum($data['average']) == 0) {
            $data = [
                'average' => [0, 0, 0, 0, 0, 0],
                'top' => [0, 0, 0, 0, 0, 0],
            ];
        }

        return [
            'chart' => [
                'type' => 'radar',
                'height' => 400,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Average Performance',
                    'data' => $data['average'],
                ],
                [
                    'name' => 'Top Performers',
                    'data' => $data['top'],
                ],
            ],
            'labels' => [
                'Trip Volume',
                'Revenue Generation',
                'Customer Rating',
                'Completion Rate',
                'Response Time',
                'Active Hours',
            ],
            'colors' => ['#3b82f6', '#10b981'],
            'fill' => [
                'opacity' => 0.25,
            ],
            'stroke' => [
                'show' => true,
                'width' => 2,
                'colors' => ['#3b82f6', '#10b981'],
            ],
            'markers' => [
                'size' => 4,
                'colors' => ['#3b82f6', '#10b981'],
                'strokeWidth' => 2,
                'strokeColors' => '#ffffff',
            ],
            'plotOptions' => [
                'radar' => [
                    'size' => 140,
                    'polygons' => [
                        'strokeColors' => '#e5e7eb',
                        'strokeWidth' => 1,
                        'connectorColors' => '#e5e7eb',
                        'fill' => [
                            'colors' => ['#f9fafb', '#f3f4f6'],
                        ],
                    ],
                ],
            ],
            'xaxis' => [
                'labels' => [
                    'show' => true,
                    'style' => [
                        'colors' => ['#6b7280', '#6b7280', '#6b7280', '#6b7280', '#6b7280', '#6b7280'],
                        'fontSize' => '12px',
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                    ],
                ],
            ],
            'yaxis' => [
                'show' => true,
                'min' => 0,
                'max' => 100,
                'tickAmount' => 5,
                'labels' => [
                    'style' => [
                        'colors' => '#6b7280',
                        'fontSize' => '11px',
                        'fontFamily' => 'inherit',
                    ],
                ],
            ],
            'legend' => [
                'show' => true,
                'position' => 'top',
                'horizontalAlign' => 'center',
                'fontFamily' => 'inherit',
                'fontSize' => '14px',
                'markers' => [
                    'width' => 12,
                    'height' => 12,
                    'radius' => 6,
                ],
            ],
            'tooltip' => [
                'enabled' => true,
                'theme' => 'dark',
                'style' => [
                    'fontSize' => '12px',
                    'fontFamily' => 'inherit',
                ],
                'y' => [
                    'formatter' => 'function(val) { return val.toFixed(1) + "%"; }',
                ],
            ],
            'theme' => [
                'mode' => 'dark',
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
        ];
    }

    /**
     * Get performance data for radar chart
     */
    private function getPerformanceData(): array
    {
        $startDate = Carbon::now()->subDays(30);

        // Get all drivers with trips in the last 30 days
        $drivers = Driver::with(['user', 'trips' => function ($query) use ($startDate) {
            $query->where('created_at', '>=', $startDate);
        }])
            ->whereHas('trips', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })
            ->get();

        if ($drivers->isEmpty()) {
            return [
                'average' => [0, 0, 0, 0, 0, 0],
                'top' => [0, 0, 0, 0, 0, 0],
            ];
        }

        $metrics = $drivers->map(function ($driver) {
            $trips = $driver->trips;
            $completedTrips = $trips->where('status', 'completed');

            // Calculate metrics (normalized to 0-100 scale)
            $tripVolume = min(($trips->count() / 50) * 100, 100); // Max 50 trips = 100%

            $revenue = $completedTrips->sum(function ($trip) {
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });
            $revenueScore = min(($revenue / 1000) * 100, 100); // Max 1000 LYD = 100%

            $rating = ($driver->average_driver_rating ?? 0) * 20; // 5-star to 100 scale

            $completionRate = $trips->count() > 0
                ? ($completedTrips->count() / $trips->count()) * 100
                : 0;

            // Response time (mock data - you can implement actual response time tracking)
            $responseTime = rand(70, 95); // Mock score

            // Active hours (mock data - you can implement actual active hours tracking)
            $activeHours = rand(60, 90); // Mock score

            return [
                'trip_volume' => $tripVolume,
                'revenue' => $revenueScore,
                'rating' => $rating,
                'completion_rate' => $completionRate,
                'response_time' => $responseTime,
                'active_hours' => $activeHours,
            ];
        });

        // Calculate averages
        $averageMetrics = [
            $metrics->avg('trip_volume'),
            $metrics->avg('revenue'),
            $metrics->avg('rating'),
            $metrics->avg('completion_rate'),
            $metrics->avg('response_time'),
            $metrics->avg('active_hours'),
        ];

        // Get top 10% performers
        $topPerformers = $metrics->sortByDesc(function ($metric) {
            return array_sum($metric) / count($metric);
        })->take(max(1, ceil($metrics->count() * 0.1)));

        $topMetrics = [
            $topPerformers->avg('trip_volume'),
            $topPerformers->avg('revenue'),
            $topPerformers->avg('rating'),
            $topPerformers->avg('completion_rate'),
            $topPerformers->avg('response_time'),
            $topPerformers->avg('active_hours'),
        ];

        return [
            'average' => array_map(fn ($val) => round($val, 1), $averageMetrics),
            'top' => array_map(fn ($val) => round($val, 1), $topMetrics),
        ];
    }
}
