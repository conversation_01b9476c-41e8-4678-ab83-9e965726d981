<?php

namespace App\Filament\Resources\Panel\ReportResource\Widgets;

use App\Models\Trip;
use App\Traits\CalculatesRevenue;
use Carbon\Carbon;
use Filament\Support\RawJs;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;

class RevenueAnalyticsApexWidget extends ApexChartWidget
{
    use CalculatesRevenue;

    /**
     * Chart Id
     */
    protected static ?string $chartId = 'revenueAnalyticsChart';

    /**
     * Widget Title
     */
    protected static ?string $heading = 'Revenue Analytics';

    /**
     * Widget Subheading
     */
    protected static ?string $subheading = 'Revenue and trip trends over time';

    /**
     * Chart height
     */
    protected static ?int $contentHeight = 400;

    /**
     * Widget column span
     */
    protected int|string|array $columnSpan = 'full';

    /**
     * Polling interval
     */
    protected static ?string $pollingInterval = '60s';

    /**
     * Default filter
     */
    public ?string $filter = 'week';

    /**
     * Chart options
     */
    protected function getOptions(): array
    {
        $data = $this->getChartData();

        // Add fallback data if no data is available
        if (empty($data['revenue']) || array_sum($data['revenue']) == 0) {
            $data = [
                'labels' => ['No Data'],
                'revenue' => [0],
                'trips' => [0],
            ];
        }

        return [
            'chart' => [
                'type' => 'area',
                'height' => 400,
                'toolbar' => [
                    'show' => true,
                    'tools' => [
                        'download' => true,
                        'selection' => false,
                        'zoom' => false,
                        'zoomin' => false,
                        'zoomout' => false,
                        'pan' => false,
                        'reset' => false,
                    ],
                ],
                'animations' => [
                    'enabled' => true,
                    'easing' => 'easeinout',
                    'speed' => 800,
                ],
            ],
            'series' => [
                [
                    'name' => 'Revenue (LYD)',
                    'data' => $data['revenue'],
                    'type' => 'area',
                ],
                [
                    'name' => 'Trips',
                    'data' => $data['trips'],
                    'type' => 'line',
                ],
            ],
            'xaxis' => [
                'categories' => $data['labels'],
                'labels' => [
                    'style' => [
                        'fontFamily' => 'inherit',
                        'fontWeight' => 600,
                    ],
                ],
                'axisBorder' => [
                    'show' => true,
                ],
                'axisTicks' => [
                    'show' => true,
                ],
            ],
            'yaxis' => [
                [
                    'title' => [
                        'text' => 'Revenue (LYD)',
                        'style' => [
                            'fontFamily' => 'inherit',
                        ],
                    ],
                    'labels' => [
                        'style' => [
                            'fontFamily' => 'inherit',
                        ],

                    ],
                ],
                [
                    'opposite' => true,
                    'title' => [
                        'text' => 'Number of Trips',
                        'style' => [
                            'fontFamily' => 'inherit',
                        ],
                    ],
                    'labels' => [
                        'style' => [
                            'fontFamily' => 'inherit',
                        ],
                    ],
                ],
            ],
            'colors' => ['#10b981', '#3b82f6'],
            'fill' => [
                'type' => ['gradient', 'solid'],
                'gradient' => [
                    'shade' => 'light',
                    'type' => 'vertical',
                    'shadeIntensity' => 0.25,
                    'gradientToColors' => ['#34d399'],
                    'inverseColors' => false,
                    'opacityFrom' => 0.85,
                    'opacityTo' => 0.25,
                ],
            ],
            'stroke' => [
                'width' => [0, 3],
                'curve' => 'smooth',
            ],
            'grid' => [
                'show' => true,
                'borderColor' => '#e5e7eb',
                'strokeDashArray' => 3,
            ],
            'legend' => [
                'show' => true,
                'position' => 'top',
                'horizontalAlign' => 'right',
                'fontFamily' => 'inherit',
            ],
            'tooltip' => [
                'shared' => true,
                'intersect' => false,
                'theme' => 'dark',
            ],
            'theme' => [
                'mode' => 'dark',
            ],
            'dataLabels' => [
                'enabled' => false,
            ],
        ];
    }

    /**
     * Get filters for the chart
     */
    protected function getFilters(): ?array
    {
        return [
            'today' => 'Today',
            'week' => 'Last 7 days',
            'month' => 'Last 30 days',
            'year' => 'Last 12 months',
        ];
    }

    /**
     * Get chart data based on filter
     */
    private function getChartData(): array
    {
        return match ($this->filter) {
            'today' => $this->getTodayData(),
            'week' => $this->getWeekData(),
            'month' => $this->getMonthData(),
            'year' => $this->getYearData(),
            default => $this->getWeekData(),
        };
    }

    /**
     * Get today's hourly data
     */
    private function getTodayData(): array
    {
        $startTime = Carbon::today();
        $endTime = Carbon::today()->endOfDay();

        // Single query to get all trips for today
        $trips = Trip::whereBetween('created_at', [$startTime, $endTime])
            ->select('created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('H');
            });

        $labels = [];
        $revenueData = [];
        $tripData = [];

        for ($hour = 0; $hour < 24; $hour++) {
            $labels[] = sprintf('%02d:00', $hour);
            $hourKey = sprintf('%02d', $hour);

            $hourTrips = $trips->get($hourKey, collect());

            // Calculate revenue for this hour
            $revenue = $hourTrips->sum(function ($trip) {
                if (! $trip->pricing_breakdown) {
                    return 0;
                }
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });

            $revenueData[] = round($revenue, 2);
            $tripData[] = $hourTrips->count();
        }

        return [
            'labels' => $labels,
            'revenue' => $revenueData,
            'trips' => $tripData,
        ];
    }

    /**
     * Get last 7 days data
     */
    private function getWeekData(): array
    {
        $startDate = Carbon::now()->subDays(6)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        // Single query to get all trips for the week
        $trips = Trip::whereBetween('created_at', [$startDate, $endDate])
            ->select('created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('Y-m-d');
            });

        $labels = [];
        $revenueData = [];
        $tripData = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $labels[] = $date->format('M j');

            $dayTrips = $trips->get($dateKey, collect());

            // Calculate revenue for this day
            $revenue = $dayTrips->sum(function ($trip) {
                if (! $trip->pricing_breakdown) {
                    return 0;
                }
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });

            $revenueData[] = round($revenue, 2);
            $tripData[] = $dayTrips->count();
        }

        return [
            'labels' => $labels,
            'revenue' => $revenueData,
            'trips' => $tripData,
        ];
    }

    /**
     * Get last 30 days data
     */
    private function getMonthData(): array
    {
        $startDate = Carbon::now()->subDays(29)->startOfDay();
        $endDate = Carbon::now()->endOfDay();

        // Single query to get all trips for the month
        $trips = Trip::whereBetween('created_at', [$startDate, $endDate])
            ->select('created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('Y-m-d');
            });

        $labels = [];
        $revenueData = [];
        $tripData = [];

        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dateKey = $date->format('Y-m-d');
            $labels[] = $date->format('M j');

            $dayTrips = $trips->get($dateKey, collect());

            // Calculate revenue for this day
            $revenue = $dayTrips->sum(function ($trip) {
                if (! $trip->pricing_breakdown) {
                    return 0;
                }
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });

            $revenueData[] = round($revenue, 2);
            $tripData[] = $dayTrips->count();
        }

        return [
            'labels' => $labels,
            'revenue' => $revenueData,
            'trips' => $tripData,
        ];
    }

    /**
     * Get last 12 months data
     */
    private function getYearData(): array
    {
        $startDate = Carbon::now()->subMonths(11)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // Single query to get all trips for the year
        $trips = Trip::whereBetween('created_at', [$startDate, $endDate])
            ->select('created_at', 'pricing_breakdown')
            ->get()
            ->groupBy(function ($trip) {
                return $trip->created_at->format('Y-m');
            });

        $labels = [];
        $revenueData = [];
        $tripData = [];

        for ($i = 11; $i >= 0; $i--) {
            $monthStart = Carbon::now()->subMonths($i)->startOfMonth();
            $monthKey = $monthStart->format('Y-m');
            $labels[] = $monthStart->format('M Y');

            $monthTrips = $trips->get($monthKey, collect());

            // Calculate revenue for this month
            $revenue = $monthTrips->sum(function ($trip) {
                if (! $trip->pricing_breakdown) {
                    return 0;
                }
                $pricing = json_decode($trip->pricing_breakdown, true);

                return $pricing['total'] ?? 0;
            });

            $revenueData[] = round($revenue, 2);
            $tripData[] = $monthTrips->count();
        }

        return [
            'labels' => $labels,
            'revenue' => $revenueData,
            'trips' => $tripData,
        ];
    }

    /**
     * Extra JavaScript options for better formatting
     */
    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
        {
            yaxis: [
                {
                    labels: {
                        formatter: function (val) {
                            return val.toFixed(0) + ' LYD'
                        }
                    }
                },
                {
                    labels: {
                        formatter: function (val) {
                            return val + ' trips'
                        }
                    }
                }
            ],
            tooltip: {
                y: [
                    {
                        formatter: function (val) {
                            return val.toFixed(2) + ' LYD'
                        }
                    },
                    {
                        formatter: function (val) {
                            return val + ' trips'
                        }
                    }
                ]
            }
        }
        JS);
    }
}
