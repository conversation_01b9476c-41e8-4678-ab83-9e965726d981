<?php

namespace App\Filament\Resources\Panel\VehicleBrandResource\Pages;

use App\Filament\Resources\Panel\VehicleBrandResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListVehicleBrands extends ListRecords
{
    protected static string $resource = VehicleBrandResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\CreateAction::make()
            ->createAnother(false)
            ->successNotification(
                Notification::make()
                    ->success()
                    ->title('New vehicle brand created successfully')
            ), ];
    }
}
