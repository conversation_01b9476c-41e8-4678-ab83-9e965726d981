<?php

namespace App\Filament\Resources\Panel\VehicleTypeResource\Pages;

use App\Enums\VehicleTypesCategories;
use App\Filament\Resources\Panel\VehicleTypeResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListVehicleTypes extends ListRecords
{
    protected static string $resource = VehicleTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\CreateAction::make()
            ->createAnother(false)
            ->successNotification(
                Notification::make()
                    ->success()
                    ->title('New vehicle type created successfully')
            ),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            VehicleTypesCategories::Passenger->value => Tab::make()
                ->label(VehicleTypesCategories::Passenger->getLabel())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('category', VehicleTypesCategories::Passenger->value)
                    ->orderBy('created_at', 'desc')),

            VehicleTypesCategories::Freight->value => Tab::make()
                ->label(VehicleTypesCategories::Freight->getLabel())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('category', VehicleTypesCategories::Freight->value)
                    ->orderBy('created_at', 'desc')),
        ];

        foreach (VehicleTypesCategories::cases() as $category) {
            $tabs[$category->value] = Tab::make()
                ->label($category->getLabel())
                ->icon($category->getIcon())
                ->modifyQueryUsing(fn (Builder $query) => $query->where('category', $category->value)->orderBy('created_at', 'desc'));
        }

        return $tabs;
    }
}
