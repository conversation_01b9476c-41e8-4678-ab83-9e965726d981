<?php

namespace App\Filament\Resources\Panel\VehicleTypeResource\Pages;

use App\Filament\Resources\Panel\VehicleTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewVehicleType extends ViewRecord
{
    protected static string $resource = VehicleTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\EditAction::make()];
    }
}
