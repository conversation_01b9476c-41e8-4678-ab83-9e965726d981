<?php

namespace App\Filament\Resources\Panel\RiderResource\RelationManagers;

use App\Filament\Tables\TripsTable;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;

class TripsRelationManager extends RelationManager
{
    protected static string $relationship = 'trips';

    protected static ?string $recordTitleAttribute = 'created_at';

    public function form(Form $form): Form
    {
        return $form->schema([
        ]);
    }

    public function table(Table $table): Table
    {
        return TripsTable::make($table);
    }
}
