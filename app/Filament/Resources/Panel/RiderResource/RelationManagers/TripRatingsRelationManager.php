<?php

namespace App\Filament\Resources\Panel\RiderResource\RelationManagers;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Mokhosh\FilamentRating\Components\Rating;

class TripRatingsRelationManager extends RelationManager
{
    protected static string $relationship = 'tripRatings';

    protected static ?string $recordTitleAttribute = 'trip_review';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(['default' => 1])->schema([

                Select::make('trip_id')
                    ->required()
                    ->relationship('trip', 'created_at')
                    ->searchable()
                    ->preload()
                    ->native(false),

                Rating::make('trip_rating')
                    ->required()
                    ->size('lg'),

                RichEditor::make('trip_review')
                    ->required()
                    ->string()
                    ->fileAttachmentsVisibility('public')
                    ->placeholder('Write a review about the trip'),

                Rating::make('car_rating')
                    ->required()
                    ->size('lg'),

                RichEditor::make('car_review')
                    ->required()
                    ->string()
                    ->fileAttachmentsVisibility('public')
                    ->placeholder('Write a review about the car'),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('trip.created_at')
                    ->label('Trip Date')
                    ->sortable()
                    ->since(),

                TextColumn::make('trip_rating')
                    ->label('Trip Rating')
                    ->icon('heroicon-m-star')
                    ->iconColor('primary')
                    ->iconPosition('after')
                    ->sortable(),

                TextColumn::make('trip_review')
                    ->label('Trip Review')
                    ->limit(100),

                TextColumn::make('car_rating')
                    ->label('Car Rating')
                    ->icon('heroicon-m-star')
                    ->iconColor('primary')
                    ->iconPosition('after')
                    ->sortable(),

                TextColumn::make('car_review')
                    ->label('Car Review')
                    ->limit(100),
            ])
            ->filters([])
           // ->headerActions([Tables\Actions\CreateAction::make()])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
