<?php

namespace App\Filament\Resources\Panel\RiderResource\Pages;

use App\Filament\Resources\Panel\RiderResource;
use App\Models\Address;
use App\Models\Rider;
use App\Models\User;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateRider extends CreateRecord
{
    protected static string $resource = RiderResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Create the user
        $user = User::create([
            'name' => $data['name'],
            'last_name' => $data['last_name'],
            'phone_number' => $data['phone_number'],
            'password' => bcrypt($data['password']),
            'type' => $data['type'],
        ]);

        // Create the rider
        $rider = Rider::create([
            'user_id' => $user->id,
        ]);

        // Create the address
        Address::create([
            'addressable_type' => User::class,
            'addressable_id' => $user->id,
            'address' => $data['address'] ?? null,
            'full_address' => $data['full_address'],
            'postal_address' => json_encode([
                'city' => $data['city'] ?? null,
                'state' => $data['state'] ?? null,
                'street' => $data['street'] ?? null,
            ], true),

            'latitude' => $data['latitude'] ?? 0.0,
            'longitude' => $data['longitude'] ?? 0.0,
        ]);

        return $rider;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Registered rider';
    }
}
