<?php

namespace App\Filament\Resources\Panel\RiderResource\Pages;

use App\Filament\Infolists\TripInfolist;
use App\Filament\Resources\Panel\RiderResource;
use App\Models\Trip;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;

class ViewRiderTrip extends Page
{
    protected static string $resource = RiderResource::class;

    protected static string $view = 'filament.resources.panel.rider-resource.pages.view-rider-trip';

    public ?Trip $trip = null;

    // Define the record property
    public $record;

    public function mount(int|string $record, int|string $tripId): void
    {
        $this->record = $this->resolveRecord($record);

        // Find the trip and ensure it belongs to this rider
        $this->trip = Trip::where('rider_id', $this->record->id)
            ->findOrFail($tripId);
    }

    protected function resolveRecord(string|int $key): \Illuminate\Database\Eloquent\Model
    {
        $model = static::getResource()::getModel();

        return $model::findOrFail($key);
    }

    public function tripInfolist(Infolist $infolist): Infolist
    {
        // Explicitly set the trip as the record for the infolist
        $infolist->record($this->trip);

        return TripInfolist::infolist($infolist);
    }

    public function getBreadcrumbs(): array
    {
        return [
            RiderResource::getUrl('index') => 'Riders',
            RiderResource::getUrl('view', ['record' => $this->record]) => $this->record->user->full_name,
            'Trip '.$this->trip->id,
        ];
    }
}
