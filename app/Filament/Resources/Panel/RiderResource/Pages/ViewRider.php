<?php

namespace App\Filament\Resources\Panel\RiderResource\Pages;

use App\Filament\Resources\Panel\RiderResource;
use App\Filament\Resources\Panel\RiderResource\RelationManagers;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewRider extends ViewRecord
{
    protected static string $resource = RiderResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [Actions\EditAction::make()];
    // }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['name'] = $this->record->user->full_name ?? '';
        $data['phone_number'] = $this->record->user->phone_number ?? '';

        $data['blocking_reason'] = $this->record->user->blocking_reason ?? '';

        return $data;
    }

    // public function getRelationManagers(): array
    // {
    //     return [
    //         RelationManagers\TripsRelationManager::class,
    //     ];
    // }
}
