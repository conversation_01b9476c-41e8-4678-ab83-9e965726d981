<?php

namespace App\Filament\Resources\Panel\RiderResource\Pages;

use App\Enums\RiderGlobalStatus;
use App\Filament\Resources\Panel\RiderResource;
use App\Models\Rider;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListRiders extends ListRecords
{
    protected static string $resource = RiderResource::class;

    public ?string $activeTab = 'all'; // Default tab, typed as nullable string

    public function mount(): void
    {
        if (request()->has('activeTab') && in_array(request()->get('activeTab'), ['all', 'pending', 'deleted'])) {
            $this->activeTab = request()->get('activeTab');
        }

        parent::mount();
    }

    public function updatedActiveTab(): void
    {
        // Reset the global_status filter to clear selected values
        if (isset($this->tableFilters['global_status']['global_status'])) {
            $this->tableFilters['global_status']['global_status'] = []; // Clear selected statuses
        }

        // Update the URL with the new active tab
        $this->redirectRoute('filament.admin.resources.panel.riders.index', [
            'activeTab' => $this->activeTab,
        ]);

        $this->dispatch('$refresh'); // Refresh the component to re-evaluate filter options
    }

    public function getTabs(): array
    {
        $counts = Rider::whereIn('global_status', [
            RiderGlobalStatus::pending->value,
        ])->selectRaw('global_status, COUNT(*) as count')
            ->groupBy('global_status')
            ->pluck('count', 'global_status');

        return [
            'all' => Tab::make()
                ->label('All')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotIn('global_status', [
                    RiderGlobalStatus::deleted->value,
                    RiderGlobalStatus::pending->value, // Exclude pending riders
                ]))
                ->icon('mdi-account-multiple'),
            'pending' => Tab::make()
                ->label('Pending')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('global_status', '=', RiderGlobalStatus::pending->value))
                ->badge(fn () => $counts[RiderGlobalStatus::pending->value] ?? 0)
                ->icon('mdi-account-clock'),
            'deleted' => Tab::make()
                ->label('Deleted')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('global_status', '=', RiderGlobalStatus::deleted->value))
                ->icon('mdi-account-multiple-remove'),
        ];
    }

    protected function getTableRecordUrlParameters(string $operation = 'view'): array
    {
        // The $operation parameter is part of the Filament API but not used in this implementation
        return [
            'activeTab' => $this->activeTab,
        ];
    }
}
