<?php

namespace App\Filament\Resources\Panel\RiderResource\Pages;

use App\Enums\RiderAvailabilities;
use App\Enums\RiderGlobalStatus;
use App\Filament\Resources\Panel\RiderResource;
use App\Models\TripCancellation;
use App\Notifications\Firebase_notifications\NotifyUser;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditRider extends EditRecord
{
    protected static string $resource = RiderResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [Actions\DeleteAction::make()];
    // }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $address = $this->record->user->address()->orderBy('created_at', 'asc')->first();

        $data['phone_number'] = $this->record->user->phone_number ?? '';
        $data['blocking_reason'] = $this->record->user->blocking_reason ?? '';

        if ($address) {
            $data['address'] = $address->address ?? '';  // Ensure it's a string
            $data['full_address'] = $address->full_address ?? '';  // Ensure it's a string
            $data['postal_address'] = json_decode($address->postal_address, true) ?? [];
            $data['city'] = $data['postal_address']['city'] ?? '';  // Ensure it's a string
            $data['state'] = $data['postal_address']['state'] ?? '';  // Ensure it's a string
            $data['street'] = $data['postal_address']['street'] ?? '';  // Ensure it's a string
            $data['latitude'] = $address->latitude ?? null;
            $data['longitude'] = $address->longitude ?? null;
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        if ($this->record->user) {

            if (isset($data['phone_number'])) {
                // Remove non-numeric characters except '-'
                $digitsOnly = preg_replace('/\D/', '', $data['phone_number']);

                // Ensure it follows the +218 format
                if (strlen($digitsOnly) === 9) {
                    $data['phone_number'] = '+218'.$digitsOnly;
                }
            }
            $this->record->user->phone_number = $data['phone_number'];
            if (isset($data['block_toggle']) && $data['block_toggle'] === true) {
                if ($this->record->user->status->value === RiderAvailabilities::busy->value) {
                    Notification::make()
                        ->title('Rider is busy')
                        ->body('Cannot block rider. Rider is currently on a trip.')
                        ->warning()
                        ->send();

                    $data['global_status'] = $this->record->global_status;  // Stop the process
                } elseif ($this->record->global_status !== RiderGlobalStatus::blocked->value) {
                    $this->record->previous_global_status = $this->record->global_status;
                    $data['global_status'] = RiderGlobalStatus::blocked->value;

                    $this->record->global_status = $data['global_status'];
                }

            } elseif (isset($data['global_status'])) {
                if ($data['global_status'] === false) {
                    $this->record->global_status = $this->record->previous_global_status;
                    $this->record->previous_global_status = RiderGlobalStatus::blocked->value;

                    $this->record->user->update([
                        'blocking_reason' => null,
                    ]);

                    // Clear all cancellation records for this user
                    TripCancellation::where('user_id', $this->record->user->id)->delete();

                    $Riderdata = [
                        'title' => 'تم إلغاء حظر الراكب',
                        'description' => 'تم إلغاء حظر حسابك. يمكنك الآن حجز الرحلات مجددًا',
                    ];

                    if ($this->record->user->tokens->isNotEmpty()) {
                        $this->record->user->notifyNow(new NotifyUser($this->record, $Riderdata));
                    }

                } else {
                    $this->record->global_status = RiderGlobalStatus::blocked->value;
                }
            }

            // Handle blocking reason if provided
            if (! empty($data['blocking_reason'])) {
                $this->record->user->blocking_reason = $data['blocking_reason'];
                $reason = $data['blocking_reason'];

                $Riderdata = [
                    'title' => 'تم حظر الراكب',
                    'description' => "تم حظر حسابك بسبب $reason. لن تتمكن من حجز الرحلات حتى يتم حل المشكلة",
                ];

                if ($this->record->user->tokens->isNotEmpty()) {
                    $this->record->user->notifyNow(new NotifyUser($this->record, $Riderdata));
                }
            }

            // Save the changes
            $this->record->user->save();
        }
        $address = $this->record->user->address()->orderBy('created_at', 'asc')->first();

        if (! $address) {
            // Create new address with default values to avoid constraint violations
            $address = $this->record->user->address()->create([
                'address' => $data['address'] ?? null,
                'full_address' => $data['full_address'] ?? null,
                'postal_address' => json_encode([
                    'city' => $data['city'] ?? null,
                    'state' => $data['state'] ?? null,
                    'street' => $data['street'] ?? null,
                ]),
                'latitude' => $data['latitude'] ?? 0.0,
                'longitude' => $data['longitude'] ?? 0.0,
            ]);
        } else {
            // Update existing address
            $address->update([
                'address' => $data['address'] ?? null,
                'full_address' => $data['full_address'] ?? null,
                'postal_address' => json_encode([
                    'city' => $data['city'] ?? null,
                    'state' => $data['state'] ?? null,
                    'street' => $data['street'] ?? null,
                ]),
                'latitude' => $data['latitude'] ?? $address->latitude ?? 0.0,
                'longitude' => $data['longitude'] ?? $address->longitude ?? 0.0,
            ]);
        }

        unset(
            $data['name'],
            $data['global_status'],
            $data['unblock_toggle'],
            $data['block_toggle'],
            $data['last_name'],
            $data['blocking_reason'],
            $data['type'],
            $data['phone_number'],
            $data['gender'],
            $data['password'],
            $data['address'],
            $data['full_address'],
            $data['postal_address'],
            $data['city'],
            $data['state'],
            $data['street'],
            $data['latitude'],
            $data['longitude'],
            $data['location']
        );

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }

    protected function getSavedNotification(): ?Notification
    {
        $record = $this->record;

        // Notification Data
        $data = [
            'title' => 'تم تحديث حسابك',
            'description' => 'تم تحديث معلومات حسابك بنجاح. الرجاء التحقق من التفاصيل الجديدة',
        ];

        // Send Notification
        if ($record->user && $record->user->tokens->isNotEmpty()) {
            $record->user->notifyNow(new NotifyUser($record, $data));
        }

        return Notification::make()
            ->success()
            ->title('Rider updated successfully');
    }
}
