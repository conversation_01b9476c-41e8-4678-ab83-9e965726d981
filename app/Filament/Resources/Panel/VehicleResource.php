<?php

namespace App\Filament\Resources\Panel;

use App\Enums\VehicleColors;
use App\Enums\VehicleTypesCategories;
use App\Filament\Infolists\VehicleInfolist;
use App\Filament\Resources\Panel\VehicleResource\Pages;
use App\Filament\Resources\Panel\VehicleResource\RelationManagers;
use App\Filament\Tables\VehiclesTable;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Infolists\InfoList;
use Filament\Resources\Resource;
use Filament\Tables\Table;

class VehicleResource extends Resource
{
    protected static ?string $model = Vehicle::class;

    protected static ?string $navigationIcon = 'mdi-taxi';

    protected static ?string $navigationGroup = 'Admin';

    public static function getModelLabel(): string
    {
        return __('crud.vehicles.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.vehicles.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.vehicles.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make('Vehicle Information')
                ->columns(2)
                ->schema([
                    Select::make('brand_id')
                        ->label('Vehicle Brand')
                        ->options(VehicleBrand::pluck('name_en', 'id'))
                        ->searchable()
                        ->rules([
                            'required',
                        ])
                        ->preload()
                        ->markAsRequired()
                        ->native(false)
                        ->reactive()
                        ->placeholder('Search for a brand')
                        ->helperText('Select the vehicle manufacturer')
                        ->afterStateHydrated(function (callable $set, $state, $record) {
                            if (! $state && $record?->vehicle_model_id) {
                                $brandId = VehicleModel::find($record->vehicle_model_id)?->vehicle_brand_id;
                                $set('brand_id', $brandId);
                            }
                        })
                        ->afterStateUpdated(function (callable $set) {
                            $set('vehicle_model_id', null);
                        })
                        ->dehydrated(false),

                    Select::make('vehicle_model_id')
                        ->label('Vehicle Model')
                        ->rules([
                            'required',
                        ])
                        ->relationship('vehicleModel', 'name_en')
                        ->searchable()
                        ->preload()
                        ->markAsRequired()
                        ->native(false)
                        ->reactive()
                        ->placeholder('Select brand first')
                        // Choose model from selected brand reactive
                        ->helperText('Choose model from selected brand')
                        ->options(function (callable $get) {
                            $brandId = $get('brand_id');

                            return $brandId
                                ? VehicleModel::where('vehicle_brand_id', $brandId)->pluck('name_en', 'id')
                                : [];
                        })
                        ->dehydrated(true)
                        ->disabled(fn (callable $get): bool => blank($get('brand_id'))),

                    // Select::make('vehicle_type_id')
                    //     ->label('Vehicle Type')
                    //     ->relationship('vehicleType', 'name_en')
                    //     ->preload()
                    //     ->searchable()
                    //     ->rules([
                    //         'required',
                    //     ])
                    //     ->placeholder('Select vehicle category')
                    //     ->helperText('e.g., Sedan, SUV, Truck')
                    //     ->options(function () {
                    //         // Fetch vehicle types and filter out those starting with "Default"
                    //         return \App\Models\VehicleType::query()
                    //             ->where('name_en', 'NOT LIKE', 'Default%') // Exclude options starting with "Default"
                    //             ->pluck('name_en', 'id'); // Fetch options as [id => name_en]
                    //     }),
                    Select::make('vehicle_type_id')
                        ->label('Vehicle Type')
                        ->relationship('vehicleType', 'name_en')
                        ->preload()
                        ->searchable()
                        ->markAsRequired()
                        ->required()
                        ->placeholder('Select vehicle category')
                        ->columnSpanFull()
                        ->options(function (?Vehicle $record) {
                            $currentVehicleType = $record?->vehicleType;
                            $query = VehicleType::query()
                                ->where('status', true)
                                ->where('name_en', 'NOT LIKE', 'Default%')
                                ->whereNotIn('name_en', ['Default_passenger_type', 'Default_freight_type']);

                            if ($currentVehicleType) {
                                $query->where('category', $currentVehicleType->category);
                            }

                            return $query->pluck('name_en', 'id');
                        })
                        ->reactive()
                        ->afterStateUpdated(function (callable $set, $state) {
                            $vehicleType = VehicleType::find($state);

                            // When user selects a new type, update freight_details dynamically if it's Freight.
                            if ($vehicleType && $vehicleType->category === VehicleTypesCategories::Freight) {
                                $set('freight_details', [
                                    'Weight Category' => $vehicleType->weight_category->getLabel(),
                                    'Covered Vehicle' => $vehicleType->is_covered ? 'Yes' : 'No',
                                ]);
                            } else {
                                // Clear details if it's not a Freight vehicle.
                                $set('freight_details', []);
                            }
                        })
                        ->afterStateHydrated(function (callable $set, ?Vehicle $record) {
                            // On page load, if editing an existing record, set the freight details if applicable.
                            if ($record && $record->vehicle_type_id) {
                                $vehicleType = VehicleType::find($record->vehicle_type_id);
                                if ($vehicleType && $vehicleType->category === VehicleTypesCategories::Freight) {
                                    $set('freight_details', [
                                        'Weight Category' => $vehicleType->weight_category->getLabel(),
                                        'Covered Vehicle' => $vehicleType->is_covered ? 'Yes' : 'No',
                                    ]);
                                }
                            }
                        }),

                    KeyValue::make('freight_details')
                        ->label('Freight Specifications')
                        ->columnSpanFull()
                        ->visible(fn (callable $get): bool => ! empty($get('freight_details')))
                        ->disabled()
                        ->live(),

                    KeyValue::make('freight_details')
                        ->disabled()
                        ->label('Freight Specifications')
                        ->columnSpanFull()
                        ->markAsRequired()
                        ->live()
                        ->visible(fn (callable $get): bool => (bool) $get('freight_details_visible')),

                    TextInput::make('license_plate_number')
                        ->label('License Plate Number')
                        ->rules([
                            'required',
                            'max:15',
                            'regex:/^[0-9\-]+$/', // Only numbers and hyphens
                            'not_regex:/\-\-+/',   // No consecutive hyphens
                        ])
                        ->maxLength(15)
                        ->markAsRequired()
                        ->validationMessages([
                            'regex' => 'Only numbers and hyphens (-) are allowed.',
                            'not_regex' => 'Consecutive hyphens are not allowed.',
                        ])
                        ->unique('vehicles', 'license_plate_number', ignoreRecord: true),

                    Select::make('year')
                        ->options(array_combine(
                            $years = range(now()->year, 1900),
                            $years
                        ))
                        ->searchable()
                        ->markAsRequired()
                        ->native(false)
                        ->rules([
                            'required',
                        ])
                        ->placeholder('Manufacturing year')
                        ->helperText('Search or scroll for year'),

                    Select::make('color')
                        ->label('Vehicle Color')
                        ->options(VehicleColors::class)
                        ->rules([
                            'required',
                        ])
                        ->markAsRequired()
                        ->searchable()
                        ->placeholder('Select color')
                        ->helperText('Choose from available colors'),

                    Radio::make('seat_number')
                        ->label('Seat Capacity')
                        // fix inline radio button
                        ->rules([
                            'required',
                        ])
                        ->inline()
                        ->markAsRequired()
                        ->inlineLabel(false)
                        ->options([
                            '2' => '2 Seats',
                            '4' => '4 Seats',
                            '6' => '6 Seats',
                        ]),

                    Select::make('vehicle_equipment_links')
                        ->relationship('vehicleEquipments', 'name_en')
                        ->preload()
                        ->multiple()
                        ->visible(fn (callable $get): bool => $get('vehicle_type_id')
                                ? VehicleType::find($get('vehicle_type_id'))?->category !== VehicleTypesCategories::Freight
                                : true
                        ),
                ]),

            // Section::make('Visual Identification')
            //     ->schema([
            //         FileUpload::make('image')
            //             ->label('Vehicle Image')
            //             ->image()
            //             ->directory('vehicles')
            //             ->nullable()
            //             ->avatar()
            //             ->imageEditorMode(2)
            //             ->imageResizeTargetWidth(800)
            //             ->imageResizeTargetHeight(600)
            //             ->maxSize(5120)
            //             ->validationMessages([
            //                 'max' => 'The file must not be larger than 5MB.',
            //             ])
            //             ->helperText('Recommended size: 800x600px. Formats: JPG, PNG. Max size: 5MB')
            //             ->uploadingMessage('Uploading vehicle image...')
            //             ->downloadable()
            //             ->openable(),
            //     ])
            //     ->columns(1),

            // Section::make('Additional Settings')
            //     ->schema([
            //         Toggle::make('is_female_only')
            //             ->label('Female-only Vehicle')
            //             ->default(false)
            //             ->inline(false)
            //             ->helperText('Enable if only female drivers should see this vehicle'),
            //     ]),

            Fieldset::make('')
                ->relationship('documents')
                ->schema([
                    Grid::make()
                        ->schema([
                            // Insurance Document Section
                            Section::make('Insurance')
                                ->description('Upload your vehicle insurance')
                                ->icon('heroicon-o-shield-check')
                                ->collapsible()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            FileUpload::make('insurance')
                                                ->required()
                                                ->maxSize(5120)
                                                ->directory('vehicle_documents/insurance')
                                                ->acceptedFileTypes(['image/*', 'application/pdf'])
                                                ->maxSize(5120)
                                                ->helperText('Max 5MB • PNG, JPG, SVG, PDF')
                                                ->label(__('Insurance Document'))
                                                ->downloadable()
                                                ->openable()
                                                ->image()
                                                ->previewable(true)
                                                ->removeUploadedFileButtonPosition('left')
                                                ->validationMessages([
                                                    'max' => 'The file must not be larger than 5MB.',
                                                ])
                                                ->columnSpan(2),

                                            DatePicker::make('insurance_expiry')
                                                ->label(__('Expiry Date'))
                                                ->native(false)
                                                ->minDate(now())
                                                ->displayFormat('M d, Y')
                                                ->helperText('Select expiration date')
                                                ->columnSpanfull()
                                                ->markAsRequired()
                                                ->rules([
                                                    'required',
                                                ]),
                                        ]),
                                ]),

                            // Technical Inspection Section
                            Section::make('Technical Inspection')
                                ->description('Upload your vehicle technical inspection')
                                ->icon('heroicon-o-wrench-screwdriver')
                                ->collapsible()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            FileUpload::make('technical_inspection')
                                                ->required()
                                                ->maxSize(5120)
                                                ->directory('vehicle_documents/technical_inspection')
                                                ->acceptedFileTypes(['image/*', 'application/pdf'])
                                                ->maxSize(5120)
                                                ->helperText('Max 5MB • PNG, JPG, SVG, PDF')
                                                ->label(__('Inspection Document'))
                                                ->downloadable()
                                                ->openable()
                                                ->image()
                                                ->previewable(true)
                                                ->removeUploadedFileButtonPosition('left')
                                                ->validationMessages([
                                                    'max' => 'The file must not be larger than 5MB.',
                                                ])
                                                ->columnSpan(2),

                                            DatePicker::make('technical_inspection_expiry')
                                                ->label(__('Expiry Date'))
                                                ->native(false)
                                                ->minDate(now())
                                                ->displayFormat('M d, Y')
                                                ->helperText('Select expiration date')
                                                ->columnSpanfull()
                                                ->markAsRequired()
                                                ->rules([
                                                    'required',
                                                ]),
                                        ]),
                                ]),

                            // Roaming Permit Section
                            Section::make('Roaming Permit')
                                ->description('Upload your vehicle roaming permit')
                                ->icon('heroicon-o-map')
                                ->collapsible()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            FileUpload::make('roaming_permit')
                                                ->required()
                                                ->maxSize(5120)
                                                ->directory('vehicle_documents/roaming_permit')
                                                ->acceptedFileTypes(['image/*', 'application/pdf'])
                                                ->maxSize(5120)
                                                ->helperText('Max 5MB • PNG, JPG, SVG, PDF')
                                                ->label(__('Permit Document'))
                                                ->downloadable()
                                                ->openable()
                                                ->image()
                                                ->previewable(true)
                                                ->removeUploadedFileButtonPosition('left')
                                                ->validationMessages([
                                                    'max' => 'The file must not be larger than 5MB.',
                                                ])
                                                ->columnSpan(2),

                                            DatePicker::make('roaming_permit_expiry')
                                                ->label(__('Expiry Date'))
                                                ->native(false)
                                                ->minDate(now())
                                                ->displayFormat('M d, Y')
                                                ->helperText('Select expiration date')
                                                ->columnSpanfull()
                                                ->markAsRequired()
                                                ->rules([
                                                    'required',
                                                ]),
                                        ]),
                                ]),
                        ]),
                ])
                ->columns(1),
        ]);
    }

    public static function table(Table $table): Table
    {
        return VehiclesTable::make($table);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return VehicleInfolist::infolist($infolist);
    }

    public static function getRelations(): array
    {
        return [
            // RelationManagers\VehicleEquipmentsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicles::route('/'),
            'view' => Pages\ViewVehicle::route('/{record}'),
            'edit' => Pages\EditVehicle::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return parent::getEloquentQuery()->withTrashed();
    }
}
