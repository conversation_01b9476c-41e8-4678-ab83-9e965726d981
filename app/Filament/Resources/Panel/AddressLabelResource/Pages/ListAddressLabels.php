<?php

namespace App\Filament\Resources\Panel\AddressLabelResource\Pages;

use App\Filament\Resources\Panel\AddressLabelResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAddressLabels extends ListRecords
{
    protected static string $resource = AddressLabelResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->createAnother(false),
        ];
    }
}
