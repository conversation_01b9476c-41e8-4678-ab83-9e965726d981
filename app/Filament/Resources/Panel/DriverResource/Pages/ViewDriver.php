<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Notifications\Firebase_notifications\NotifyUser; // Import the NotifyUser class
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Log;

class ViewDriver extends ViewRecord
{
    protected static string $resource = DriverResource::class;

    // Hide relation manager from the view
    public function getRelationManagers(): array
    {
        return [];
    }

    protected function getHeaderActions(): array
    {
        return [
            // Add notify now for the accept
            Actions\Action::make('accept')
                ->label('Accept Driver')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->inprogress())
                ->action(function (array $data) {
                    // Get the driver and their first vehicle
                    $driver = $this->record->load('vehicles.vehicleType');

                    // Check if the driver has vehicles
                    if ($driver->vehicles->isNotEmpty()) {
                        $vehicle = $driver->vehicles->first();

                        // Update the vehicle type
                        $vehicle->update([
                            'vehicle_type_id' => $data['vehicle_type_id'],
                        ]);
                    }

                    // Update driver status
                    $this->updateDriverStatus(DriverGlobalStatus::active);
                })
                ->form([
                    Select::make('vehicle_type_id')
                        ->label('Vehicle Type')
                        ->options(function () {
                            $driver = $this->record->load('vehicles.vehicleModel.vehicleBrand', 'vehicles.vehicleType');

                            // Initialize classification service
                            $classificationService = app(\App\Services\VehicleClassificationService::class);

                            if ($driver->vehicles->isNotEmpty()) {
                                $vehicle = $driver->vehicles->first();

                                // Use VehicleClassificationService to get suggested types
                                $options = $classificationService->getSuggestedVehicleTypes($vehicle);

                                if (! empty($options)) {
                                    return $options;
                                }
                            }

                            // Fallback: Use shared method for consistency
                            return $classificationService->getFormattedVehicleTypeOptions();
                        })
                        ->preload()
                        ->required()
                        ->searchable()
                        ->placeholder('Select vehicle type')
                        ->helperText('⭐ Suggested types are shown first based on vehicle classification rules'),
                ]),

            // Add notify now for the reject
            Actions\Action::make('reject')
                ->label('Reject Driver')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Reject Driver')
                ->form([
                    Select::make('rejection_reason_columns')
                        ->multiple()
                        ->required()
                        ->reactive()
                        ->options(function () {
                            // Get the driver record with relationships loaded
                            $driver = $this->record->loadMissing(['documents', 'vehicles.vehicleType']);

                            // Base options for driver
                            $driverOptions = [
                                'name' => 'First Name',
                                'last_name' => 'Last Name',
                                'address' => 'Address',
                                'gender' => 'Gender',
                                'id_number' => 'ID Number',
                                'document_front' => 'ID Front',
                                'document_back' => 'ID Back',
                                'license_back' => 'License Back',
                                'license_front' => 'License Front',
                                'license_expiry' => 'License Expiry',
                            ];

                            // Base options for vehicle
                            $vehicleOptions = [
                                'vehicle_photo' => 'Vehicle Photo',
                                'category' => 'Vehicle Category',
                                'license_plate_number' => 'License Plate Number',
                                'vehicle_model' => 'Vehicle Model',
                                'vehicle_brand' => 'Vehicle Brand',
                                'year' => 'Year',
                                'vehicle_color' => 'Vehicle Color',
                                'insurance' => 'Insurance',
                                'technical_inspection' => 'Technical Inspection',
                                'roaming_permit' => 'Roaming Permit',
                                'insurance_expiry' => 'Insurance Expiry',
                                'technical_inspection_expiry' => 'Technical Inspection Expiry',
                                'roaming_permit_expiry' => 'Roaming Permit Expiry',
                            ];

                            // Condition for vehicle type
                            if ($driver->vehicles->isNotEmpty() && $driver->vehicles->first()->vehicleType) {
                                $vehicleTypeCategory = $driver->vehicles->first()->vehicleType->category;

                                // Compare enum values properly
                                if ($vehicleTypeCategory->value === 'passenger') {
                                    // Remove is_covered and weight_category for passenger vehicles
                                    unset($vehicleOptions['is_covered']);
                                    unset($vehicleOptions['weight_category']);
                                    $vehicleOptions['seats_number'] = 'Seats Number'; // Add seats_number for passenger vehicles
                                } elseif ($vehicleTypeCategory->value === 'freight') {
                                    // Remove seats_number for freight vehicles
                                    unset($vehicleOptions['seats_number']);
                                    // Add is_covered and weight_category for freight vehicles
                                    $vehicleOptions['is_covered'] = 'Is Covered';
                                    $vehicleOptions['weight_category'] = 'Weight Category';
                                }
                            }

                            // Ensure all options have string values (not null)
                            $mergedOptions = array_merge($driverOptions, $vehicleOptions);

                            // Convert any null values to strings
                            foreach ($mergedOptions as $key => $value) {
                                if ($value === null) {
                                    $mergedOptions[$key] = "Option {$key}";
                                }
                            }

                            return $mergedOptions;
                        }),

                    Textarea::make('rejection_reason')
                        ->label('Rejection Reason')
                        ->rows(5)
                        ->markAsRequired()
                        ->rules(['required', 'max:60'])
                        ->maxLength(60)
                        ->placeholder('Provide a reason for rejection...'),
                ])
                ->visible(fn () => $this->inprogress())
                ->action(fn (array $data) => $this->updateDriverStatus(DriverGlobalStatus::rejected, $data['rejection_reason'], $data['rejection_reason_columns'])),
        ];
    }

    protected function inprogress(): bool
    {
        return $this->record->global_status === DriverGlobalStatus::in_progress;
    }

    protected function updateDriverStatus(DriverGlobalStatus $status, ?string $reason = null, ?array $rejectionReasonColumns = null)
    {
        $driver = $this->record;

        try {
            // Prepare the data to update
            $updateData = [
                'global_status' => $status->value,
                'rejection_reason' => $reason,
            ];

            // Only include rejection_reason_columns if the status is rejected
            // if ($status === DriverGlobalStatus::rejected) {
            //     $updateData['rejection_reason_columns'] = $rejectionReasonColumns;
            // }

            if ($status === DriverGlobalStatus::rejected) {
                $updateData['rejection_reason_columns'] = $rejectionReasonColumns;
                $updateData['previous_global_status'] = DriverGlobalStatus::in_progress->value;
            } elseif ($status === DriverGlobalStatus::active) {
                $updateData['previous_global_status'] = DriverGlobalStatus::in_progress->value;
            }
            // Update the driver record
            $driver->update($updateData);

            // Send Firebase notification to the driver
            $this->sendFirebaseNotification($driver, $status, $reason);

            // Send Filament notification
            $this->sendNotification($status);

            // Redirect to the list of drivers after the action
            $this->redirect(DriverResource::getUrl('index'));
        } catch (\Exception $e) {
            Log::error('Failed to update driver status', [
                'driver_id' => $driver->id,
                'error' => $e->getMessage(),
                'rejection_reason_columns' => $rejectionReasonColumns, // Log the data causing the error
            ]);

            Notification::make()
                ->title('Error')
                ->body('Failed to update driver status. Please try again.')
                ->color('danger')
                ->send();
        }
    }

    protected function sendFirebaseNotification($driver, DriverGlobalStatus $status, ?string $reason = null): void
    {
        // Prepare notification data
        $notificationData = [
            'title' => $status === DriverGlobalStatus::active
                ? 'تم قبول طلب السائق'
                : 'تم رفض طلب السائق',
            'description' => $status === DriverGlobalStatus::active
                ? 'تهانينا! تم قبول طلبك كسائق.'
                : "تم رفض طلبك كسائق. السبب: {$reason}",
        ];

        // Send the notification
        if ($driver->user->tokens->isNotEmpty()) {
            $driver->user->notifyNow(new NotifyUser($driver->user, $notificationData));
        }
    }

    protected function sendNotification(DriverGlobalStatus $status): void
    {
        $notification = Notification::make()
            ->title($status === DriverGlobalStatus::active ? 'Driver Accepted' : 'Driver Rejected')
            ->body($status === DriverGlobalStatus::active
                ? 'The driver has been accepted successfully.'
                : 'The driver has been rejected successfully.')
            ->icon($status === DriverGlobalStatus::active
            ? 'heroicon-o-check-circle'
            : 'heroicon-o-x-circle');

        // Conditionally set success or danger
        if ($status === DriverGlobalStatus::active) {
            $notification->success();
        } else {
            $notification->danger();
        }

        $notification->send();
    }
}
