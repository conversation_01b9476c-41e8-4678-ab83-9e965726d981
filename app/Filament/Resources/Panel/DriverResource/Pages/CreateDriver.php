<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Filament\Resources\Panel\DriverResource;
use App\Models\Address;
use App\Models\Driver;
use App\Models\User;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateDriver extends CreateRecord
{
    protected static string $resource = DriverResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // Create the user
        $user = User::create([
            'name' => $data['name'],
            'last_name' => $data['last_name'],
            'phone_number' => $data['phone_number'],
            'password' => bcrypt($data['password']),
            'type' => $data['type'],
            'gender' => $data['gender'],
        ]);

        // Create the driver
        $driver = Driver::create([
            'user_id' => $user->id,
            'license_expiry' => $data['license_expiry'],
            'national_id_number' => $data['national_id_number'],
            'id_card' => $data['id_card'] ?? null,
            'driver_license' => $data['driver_license'] ?? null,
        ]);

        $latitude = null;
        $longitude = null;

        if (! empty($data['location']) && is_array($data['location'])) {
            $latitude = $data['location']['lat'] ?? null;
            $longitude = $data['location']['lng'] ?? null;
        } elseif (! empty($data['location']) && is_string($data['location'])) {
            $location = json_decode($data['location'], true);
            if (is_array($location)) {
                $latitude = $location['lat'] ?? null;
                $longitude = $location['lng'] ?? null;
            }
        }
        // Create the address
        Address::create([
            'addressable_type' => User::class,
            'addressable_id' => $user->id,
            'address' => $data['address'] ?? null,
            'full_address' => $data['full_address'],
            'postal_address' => json_encode([
                'city' => $data['city'] ?? null,
                'state' => $data['state'] ?? null,
                'street' => $data['street'] ?? null,
            ]),
            'latitude' => $latitude ?? 0.0,
            'longitude' => $longitude ?? 0.0,
            'is_favorite' => false,
        ]);

        return $driver;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Registered driver';
    }
}
