<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Filament\Infolists\TripInfolist;
use App\Filament\Resources\Panel\DriverResource;
use App\Models\Trip;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;

class ViewDriverTrip extends Page
{
    protected static string $resource = DriverResource::class;

    protected static string $view = 'filament.resources.panel.driver-resource.pages.view-driver-trip';

    public ?Trip $trip = null;

    // Define the record property
    public $record;

    public function mount(int|string $record, int|string $tripId): void
    {
        $this->record = $this->resolveRecord($record);

        // Find the trip and ensure it belongs to this driver
        $this->trip = Trip::where('driver_id', $this->record->id)
            ->findOrFail($tripId);
    }

    protected function resolveRecord(string|int $key): \Illuminate\Database\Eloquent\Model
    {
        $model = static::getResource()::getModel();

        return $model::withTrashed()->findOrFail($key);
    }

    public function tripInfolist(Infolist $infolist): Infolist
    {
        // Explicitly set the trip as the record for the infolist
        $infolist->record($this->trip);

        return TripInfolist::infolist($infolist);
    }

    public function getBreadcrumbs(): array
    {
        return [
            DriverResource::getUrl('index') => 'Drivers',
            DriverResource::getUrl('view', ['record' => $this->record]) => $this->record->user->full_name,
            'Trip '.$this->trip->id,
        ];
    }
}
