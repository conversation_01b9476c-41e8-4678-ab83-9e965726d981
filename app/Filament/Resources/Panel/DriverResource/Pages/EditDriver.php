<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Enums\Drivers\DriverAvailabilities;
use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Models\TripCancellation;
use App\Notifications\Firebase_notifications\NotifyUser;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditDriver extends EditRecord
{
    protected static string $resource = DriverResource::class;

    // Track if driver was blocked or unblocked
    protected bool $wasBlocked = false;

    protected bool $wasUnblocked = false;

    protected string $blockingReason = '';

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $user = $this->record->user;
        $address = $user ? $user->address()->orderBy('created_at', 'asc')->first() : null;

        $data['cover_picture'] = $user->cover_picture ?? '';
        $data['type'] = $user->type ?? '';
        $data['blocking_reason'] = $user->blocking_reason ?? '';

        // Format phone number
        if (isset($user->phone_number) && strpos($user->phone_number, '+218') === 0) {
            $number = substr($user->phone_number, 4);
            $data['phone_number'] = substr_replace($number, '-', 2, 0); // Format phone number as xx-xxxxxxx
        } else {
            $data['phone_number'] = $user->phone_number ?? '';
        }

        // Format old phone number
        if (isset($user->old_phone_number) && strpos($user->old_phone_number, '+218') === 0) {
            $number = substr($user->old_phone_number, 4);
            $data['old_phone_number'] = substr_replace($number, '-', 2, 0); // Format old phone number
        } else {
            $data['old_phone_number'] = $user->old_phone_number ?? '';
        }

        // Driver-specific fields
        $data['rider_gender'] = $this->record->rider_gender ?? '';
        $data['global_status'] = $this->record->global_status ?? 'pending';
        $data['average_driver_rating'] = $this->record->average_driver_rating ?? null;

        // Address-related fields
        if ($address) {
            $data['address'] = $address->address ?? '';
            $data['full_address'] = $address->full_address ?? '';
            $data['postal_address'] = json_decode($address->postal_address, true) ?? [];
            $data['city'] = $data['postal_address']['city'] ?? '';
            $data['state'] = $data['postal_address']['state'] ?? '';
            $data['street'] = $data['postal_address']['street'] ?? '';
            $data['latitude'] = $address->latitude ?? null;
            $data['longitude'] = $address->longitude ?? null;
        }

        return $data;
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $user = $this->record->user;
        $this->wasBlocked = false;
        $this->wasUnblocked = false;

        if ($user) {
            // Format phone number
            if (isset($data['phone_number'])) {
                $digitsOnly = preg_replace('/\D/', '', $data['phone_number']);
                if (strlen($digitsOnly) === 9) {
                    $data['phone_number'] = '+218'.$digitsOnly;
                }
            }

            if (isset($data['block_toggle']) && $data['block_toggle'] === true) {
                if ($this->record->user->status->value === DriverAvailabilities::busy->value) {
                    Notification::make()
                        ->title('Driver is busy')
                        ->body('Cannot block Driver. Driver is currently on a trip.')
                        ->warning()
                        ->send();

                    $data['global_status'] = $this->record->global_status;
                } elseif ($this->record->global_status !== DriverGlobalStatus::blocked->value) {
                    $this->record->previous_global_status = $this->record->global_status;
                    $data['global_status'] = DriverGlobalStatus::blocked->value;

                    $this->record->global_status = $data['global_status'];
                }
            } elseif (isset($data['global_status'])) {
                if ($data['global_status'] === false) {
                    $this->record->global_status = $this->record->previous_global_status;
                    $this->record->previous_global_status = DriverGlobalStatus::blocked->value;
                    $user->blocking_reason = null; // Clear blocking reason when unblocking

                    // Clear all cancellation records for this user
                    TripCancellation::where('user_id', $this->record->user->id)->delete();

                    $notificationData = [
                        'title' => 'تم إلغاء حظر حسابك',
                        'description' => 'تم إلغاء حظر حسابك. يمكنك الآن قبول الرحلات وإتمامها مجددًا.',
                    ];

                    if ($this->record->user->tokens->isNotEmpty()) {
                        $this->record->user->notifyNow(new NotifyUser($this->record, $notificationData));
                    }
                } else {
                    $this->record->global_status = DriverGlobalStatus::blocked->value;
                    $this->wasBlocked = true;
                    $this->blockingReason = $data['blocking_reason'] ?? 'administrative decision';
                }
            }

            if (! empty($data['blocking_reason'])) {
                $user->blocking_reason = $data['blocking_reason'];

                $reason = $data['blocking_reason'];

                $notificationData = [
                    'title' => 'تم حظر حسابك',
                    'description' => "تم حظر حسابك بسبب $reason . لن تتمكن من قبول الرحلات حتى يتم حل المشكلة",
                ];

                if ($this->record->user->tokens->isNotEmpty()) {
                    $this->record->user->notifyNow(new NotifyUser($this->record, $notificationData));
                }
            }

            $user->update([
                'phone_number' => $data['phone_number'] ?? $user->phone_number,
                'blocking_reason' => $user->blocking_reason ?? $data['blocking_reason'] ?? $user->blocking_reason,
            ]);
        }

        // Update address fields
        $address = $this->record->user->address()->orderBy('created_at', 'asc')->first();

        if (! $address) {
            // Create new address with default values to avoid constraint violations
            $address = $this->record->user->address()->create([
                'address' => $data['address'] ?? null,
                'full_address' => $data['full_address'] ?? null,
                'postal_address' => json_encode([
                    'city' => $data['city'] ?? null,
                    'state' => $data['state'] ?? null,
                    'street' => $data['street'] ?? null,
                ]),
                'latitude' => $data['latitude'] ?? 0.0,
                'longitude' => $data['longitude'] ?? 0.0,
            ]);
        } else {
            // Update existing address
            $address->update([
                'address' => $data['address'] ?? null,
                'full_address' => $data['full_address'] ?? null,
                'postal_address' => json_encode([
                    'city' => $data['city'] ?? null,
                    'state' => $data['state'] ?? null,
                    'street' => $data['street'] ?? null,
                ]),
                'latitude' => $data['latitude'] ?? $address->latitude ?? 0.0,
                'longitude' => $data['longitude'] ?? $address->longitude ?? 0.0,
            ]);
        }

        // Clean up unnecessary data before saving
        unset(
            $data['cover_picture'],
            $data['name'],
            $data['last_name'],
            $data['phone_number'],
            $data['old_phone_number'],
            $data['unblock_toggle'],
            $data['block_toggle'],
            $data['global_status'],
            $data['type'],
            $data['gender'],
            $data['password'],
            $data['address'],
            $data['full_address'],
            $data['postal_address'],
            $data['city'],
            $data['state'],
            $data['street'],
            $data['latitude'],
            $data['longitude'],
            $data['location'],
            $data['blocking_reason']
        );

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }

    protected function getSavedNotification(): ?Notification
    {
        $record = $this->record;

        // if ($this->wasBlocked) {
        //     // Send block notification (similar to table action)
        //     $notificationData = [
        //         'title' => 'تم حظر حسابك',
        //         'description' => "تم حظر حسابك بسبب {$this->blockingReason}. لن تتمكن من قبول أو إتمام الرحلات حتى يتم حل المشكلة. يمكنك اتباع الخطوات التالية لإلغاء الحظر: [خطوات إلغاء الحظر]. أو يمكنك التواصل مع الدعم للمساعدة.",
        //     ];
        //     $record->user->notify(new NotifyUser($record, $notificationData));

        //     return Notification::make()
        //         ->success()
        //         ->title('Driver Blocked Successfully');
        // } elseif ($this->wasUnblocked) {
        //     // Send unblock notification (similar to table action)
        //     $notificationData = [
        //         'title' => 'تم إلغاء حظر حسابك',
        //         'description' => 'تم إلغاء حظر حسابك. يمكنك الآن قبول الرحلات وإتمامها مجددًا.',
        //     ];
        //     $record->user->notify(new NotifyUser($record, $notificationData));

        //     return Notification::make()
        //         ->success()
        //         ->title('Driver Unblocked Successfully');
        // } else {
        // Regular update notification for other changes
        $data = [
            'title' => 'تم تحديث حسابك',
            'description' => 'تم تحديث معلومات حسابك بنجاح. الرجاء التحقق من التفاصيل الجديدة',
        ];

        // Send Notification
        if ($record->user && $record->user->tokens->isNotEmpty()) {
            $record->user->notify(new NotifyUser($record, $data));
        }

        return Notification::make()
            ->success()
            ->title('Driver updated successfully');
        // }
    }
}
