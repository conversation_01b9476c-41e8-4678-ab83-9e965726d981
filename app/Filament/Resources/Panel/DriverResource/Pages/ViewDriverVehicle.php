<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Filament\Infolists\VehicleInfolist;
use App\Filament\Resources\Panel\DriverResource;
use App\Models\Driver;
use App\Models\Vehicle;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\ViewRecord;

class ViewDriverVehicle extends ViewRecord
{
    protected static string $resource = DriverResource::class;

    protected static string $view = 'filament.resources.panel.driver-resource.pages.view-driver-vehicle';

    public ?Vehicle $vehicle = null;

    public function mount(int|string $record): void
    {
        parent::mount($record);

        $vehicleId = request()->route('vehicleId');

        // Find the vehicle, even if soft-deleted, and ensure it's associated with the driver
        $this->vehicle = Vehicle::withTrashed()
            ->where('id', $vehicleId)
            ->whereHas('drivers', function ($query) {
                $query->where('id', $this->record->id); // no withTrashed here
            })
            ->firstOrFail();
    }

    public function getBreadcrumbs(): array
    {
        return [
            DriverResource::getUrl('index') => 'Drivers',
            DriverResource::getUrl('view', ['record' => $this->record]) => $this->record->user->full_name,
            $this->vehicle->license_plate_number,
        ];
    }

    public function vehicleInfolist(Infolist $infolist): Infolist
    {
        return VehicleInfolist::infolist($infolist)
            ->record($this->vehicle);
    }

    protected function resolveRecord(string|int $key): \Illuminate\Database\Eloquent\Model
    {
        return static::getModel()::withTrashed()->findOrFail($key);
    }

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         \Filament\Actions\Action::make('back_to_driver')
    //             ->label('Back to Driver')
    //             ->url(fn () => DriverResource::getUrl('view', ['record' => $this->record]))
    //             ->color('gray')
    //             ->icon('heroicon-o-arrow-left'),
    //     ];
    // }
}
