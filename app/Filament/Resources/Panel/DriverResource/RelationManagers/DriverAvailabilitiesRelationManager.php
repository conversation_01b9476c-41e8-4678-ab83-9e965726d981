<?php

namespace App\Filament\Resources\Panel\DriverResource\RelationManagers;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class DriverAvailabilitiesRelationManager extends RelationManager
{
    protected static string $relationship = 'driverAvailabilities';

    protected static ?string $recordTitleAttribute = 'notes';

    public function form(Form $form): Form
    {
        return $form->schema([
            Grid::make(['default' => 1])->schema([
                Select::make('status')
                    ->required()
                    ->searchable()
                    ->preload()
                    ->native(false)
                    ->options([
                        'available' => 'Available',
                        'busy' => 'Busy',
                        'off-duty' => 'Off duty',
                    ]),

                TimePicker::make('start_time')
                    ->required()
                    ->native(false),

                TimePicker::make('end_time')
                    ->required()
                    ->native(false),

                TextInput::make('notes')
                    ->nullable()
                    ->string(),
            ]),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('status'),

                TextColumn::make('start_time'),

                TextColumn::make('end_time'),

                TextColumn::make('notes'),
            ])
            ->filters([])
            //->headerActions([Tables\Actions\CreateAction::make()])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
