<?php

namespace App\Filament\Resources\Panel\DriverResource\RelationManagers;

use App\Enums\VehicleColors;
use App\Filament\Infolists\VehicleInfolist;
use App\Filament\Resources\Panel\VehicleResource;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class VehiclesRelationManager extends RelationManager
{
    protected static string $relationship = 'vehicles';

    protected static ?string $recordTitleAttribute = 'Vehicle';

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make()
                ->schema([
                    Grid::make(2)
                        ->schema([
                            // Select::make('brand_id')
                            //     ->label('Vehicle Brand')
                            //     ->options(VehicleBrand::pluck('name_en', 'id'))
                            //     ->required()
                            //     ->preload()
                            //     ->native(false)
                            //     ->reactive()
                            //     ->afterStateHydrated(function (callable $set, $state, $record) {
                            //         if (! $state && $record?->vehicle_model_id) {
                            //             $brandId = VehicleModel::find($record->vehicle_model_id)?->vehicle_brand_id;
                            //             $set('brand_id', $brandId);
                            //         }
                            //     })
                            //     ->afterStateUpdated(function (callable $set) {
                            //         $set('vehicle_model_id', null);
                            //     })
                            //     ->dehydrated(false),

                            // Select::make('vehicle_model_id')
                            //     ->label('Vehicle Model')
                            //     ->required()
                            //     ->relationship('vehicleModel', 'name_en')
                            //     ->preload()
                            //     ->native(false)
                            //     ->reactive()
                            //     ->options(function (callable $get) {
                            //         $brandId = $get('brand_id');

                            //         return $brandId
                            //             ? VehicleModel::where('vehicle_brand_id', $brandId)->pluck('name_en', 'id')
                            //             : [];
                            //     })
                            //     ->dehydrated(true),

                            // Select::make('vehicle_type_id')
                            //     ->label('Vehicle Type')
                            //     ->relationship('vehicleType', 'name_en')
                            //     ->native(false)
                            //     ->preload()
                            //     ->required(),

                            // TextInput::make('license_plate_number')
                            //     ->label(__('License Plate Number'))
                            //     ->required()
                            //     ->placeholder(__('Enter license plate number'))
                            //     ->regex('/^[0-9\-]+$/') // Allow only numbers and hyphens
                            //     ->rules(['regex:/^[0-9\-]+$/'])
                            //     ->maxLength(20),

                            // Select::make('year')
                            //     ->options(array_combine(range(now()->year, 1900), range(now()->year, 1900)))
                            //     ->native(false)
                            //     ->required(),

                            // Select::make('color')
                            //     ->label('Color')
                            //     ->options(VehicleColors::class)
                            //     ->required(),

                            // // FileUpload::make('image')
                            // //     ->label('Vehicle Image')
                            // //     ->image()
                            // //     ->directory('vehicles')
                            // //     ->nullable(),

                            // Select::make('seat_number')
                            //     ->label('Seat Number')
                            //     ->options([
                            //         '2' => '2 Seats',
                            //         '4' => '4 Seats',
                            //         '6' => '6 Seats',
                            //     ])
                            //     ->required(),
                        ]),

                    Fieldset::make('Vehicle Documents')
                        ->relationship('documents')
                        ->schema([
                            // FileUpload::make('roaming_permit')
                            //     ->directory('vehiclle_documents/roaming_permit')
                            //     ->disk('public')
                            //     ->rules(['mimes:png,jpg,pdf', 'max:5120'])
                            //     ->validationMessages([
                            //         'max' => 'The file must not be larger than 5MB.',
                            //     ])
                            //     ->previewable(true)
                            //     ->downloadable()
                            //     ->openable()
                            //     ->imageEditor()
                            //     ->helperText('Max 5MB • PNG, JPG, PDF')
                            //     ->label(__('Roaming Permit')),

                            // DatePicker::make('roaming_permit_expiry')
                            //     ->native(false)
                            //     ->nullable()
                            //     ->required()
                            //     ->label(__('Roaming Permit Expiry Date'))
                            //     ->placeholder(__('Select license expiry date'))
                            //     ->minDate(now()->addMonths(4)) // Ensure expiration is at least 4 months from now
                            //     ->rules(['after_or_equal:'.now()->addMonths(4)->toDateString()]),

                            // FileUpload::make('technical_inspection')
                            //     ->directory('vehiclle_documents/technical_inspection')
                            //     ->disk('public')
                            //     ->rules(['mimes:png,jpg,pdf', 'max:5120'])
                            //     ->validationMessages([
                            //         'max' => 'The file must not be larger than 5MB.',
                            //     ])
                            //     ->previewable(true)
                            //     ->downloadable()
                            //     ->openable()
                            //     ->imageEditor()
                            //     ->helperText('Max 5MB • PNG, JPG, PDF')
                            //     ->label(__('Technical Inspection')),
                            // DatePicker::make('technical_inspection_expiry')
                            //     ->native(false)
                            //     ->nullable()
                            //     ->required()
                            //     ->label(__('Technical Inspection Expiry Date'))
                            //     ->placeholder(__('Select Technical Inspection Expiry date'))
                            //     ->minDate(now()->addMonths(4)) // Ensure expiration is at least 4 months from now
                            //     ->rules(['after_or_equal:'.now()->addMonths(4)->toDateString()]),

                            // FileUpload::make('insurance')
                            //     ->directory('driver_documents/insurance')
                            //     ->disk('public')
                            //     ->uploadingMessage('Uploading the front side of License...')
                            //     ->rules(['mimes:png,jpg,pdf', 'max:5120'])
                            //     ->validationMessages([
                            //         'max' => 'The file must not be larger than 5MB.',
                            //     ])
                            //     ->previewable(true)
                            //     ->downloadable()
                            //     ->openable()
                            //     ->imageEditor()
                            //     ->helperText('Max 5MB • PNG, JPG, PDF')
                            //     ->label(__('Insurance')),
                            // DatePicker::make('insurance_expiry')
                            //     ->native(false)
                            //     ->nullable()
                            //     ->required()
                            //     ->label(__('Insurance Expiry Date'))
                            //     ->placeholder(__('Select Insurance Expiry Date'))
                            //     ->minDate(now()->addMonths(4)) // Ensure expiration is at least 4 months from now
                            //     ->rules(['after_or_equal:'.now()->addMonths(4)->toDateString()]),
                        ])->columns(2),
                ]),
        ]);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return VehicleInfolist::infolist($infolist);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('license_plate_number')
                    ->label('License Plate Number'),

                TextColumn::make('drivers.user.full_name')
                    ->label('Driver Name'),

                TextColumn::make('vehicleModel.vehicleBrand.name_ar')
                    ->label('Brand (Arabic)'),

                TextColumn::make('vehicleModel.name_ar')
                    ->label('Model (Arabic)'),

                TextColumn::make('vehicleType.name_ar')
                    ->label('Type (Arabic)'),

                TextColumn::make('vehicleType.category.value')
                    ->label('Category (Arabic)')
                    ->formatStateUsing(function ($state, $record) {
                        if ($state === 'passenger') {
                            return 'أشخاص';
                        } else {
                            return 'بضائع';
                        }
                    }),

            ])
            ->actions([
                ViewAction::make(),
                EditAction::make()->url(fn (Model $record): string => VehicleResource::getUrl('edit', ['record' => $record])),
            ])
            ->paginated(false)
            ->defaultSort('created_at', 'desc');
    }
}
