<?php

namespace App\Filament\Resources\Panel\DriverResource\Widgets;

use App\Models\Driver;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class NumberOfRegistration extends BaseWidget
{
    protected function getStats(): array
    {
        $totalDrivers = Driver::count();
        $newDriversToday = Driver::whereDate('created_at', today())->count();
        $activeDrivers = Driver::where('global_status', 'active')->count();
        $newDriversThisMonth = Driver::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return [
            Stat::make('Total Drivers', $totalDrivers)
                ->description('All registered drivers')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('Active Drivers', $activeDrivers)
                ->description('Currently active drivers')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('New Today', $newDriversToday)
                ->description('Registered today')
                ->descriptionIcon('heroicon-m-user-plus')
                ->color('info'),

            Stat::make('This Month', $newDriversThisMonth)
                ->description('Registered this month')
                ->descriptionIcon('heroicon-m-calendar')
                ->color('warning'),
        ];
    }
}
