<?php

namespace App\Filament\Resources\Panel\DriverResource\Widgets;

use App\Enums\Drivers\DriverAvailabilities;
use App\Models\DriverAvailability;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Log;

class DriversCurrentlyOnTrip extends ChartWidget
{
    protected static ?string $heading = 'Driver Current Status';

    protected function getData(): array
    {
        try {
            $statusCounts = [
                'available' => DriverAvailability::where('status', DriverAvailabilities::available->value)->count(),
                'busy' => DriverAvailability::where('status', DriverAvailabilities::busy->value)->count(),
                'off_duty' => DriverAvailability::where('status', DriverAvailabilities::off_duty->value)->count(),
            ];

            return [
                'datasets' => [
                    [
                        'label' => 'Current Status',
                        'data' => array_values($statusCounts),
                        'backgroundColor' => [
                            'rgba(34, 197, 94, 0.8)',  // Green for Available
                            'rgba(245, 158, 11, 0.8)', // Orange for Busy
                            'rgba(99, 102, 241, 0.8)', // Indigo for Off Duty
                        ],
                    ],
                ],
                'labels' => [
                    'Available ('.$statusCounts['available'].')',
                    'Busy ('.$statusCounts['busy'].')',
                    'Off Duty ('.$statusCounts['off_duty'].')',
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error in Driver Current Status chart:', [
                'error' => $e->getMessage(),
            ]);

            return [
                'datasets' => [
                    [
                        'label' => 'No Data',
                        'data' => [0],
                        'backgroundColor' => ['rgba(156, 163, 175, 0.5)'],
                    ],
                ],
                'labels' => ['No Data Available'],
            ];
        }
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
        ];
    }
}
