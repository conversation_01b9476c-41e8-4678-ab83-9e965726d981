<?php

namespace App\Filament\Resources\Panel\DriverResource\Widgets;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Models\Driver;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DriverStats extends BaseWidget
{
    protected static ?string $resource = \App\Filament\Resources\Panel\DriverResource::class;

    protected function getStats(): array
    {
        $statusCounts = [
            'active' => Driver::where('global_status', DriverGlobalStatus::active->value)->count(),
            'pending' => Driver::where('global_status', DriverGlobalStatus::pending->value)->count(),
            'blocked' => Driver::where('global_status', DriverGlobalStatus::blocked->value)->count(),
            'in_progress' => Driver::where('global_status', DriverGlobalStatus::in_progress->value)->count(),
        ];

        $totalDrivers = array_sum($statusCounts);

        return [
            Stat::make('Active Drivers', $statusCounts['active'])
                ->description(number_format(($statusCounts['active'] / max(1, $totalDrivers) * 100), 1).'% of total')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),

            Stat::make('Pending Drivers', $statusCounts['pending'])
                ->description(number_format(($statusCounts['pending'] / max(1, $totalDrivers) * 100), 1).'% of total')
                ->descriptionIcon('heroicon-m-clock')
                ->color('gray'),

            Stat::make('Blocked Drivers', $statusCounts['blocked'])
                ->description(number_format(($statusCounts['blocked'] / max(1, $totalDrivers) * 100), 1).'% of total')
                ->descriptionIcon('heroicon-m-no-symbol')
                ->color('danger'),

            Stat::make('In Progress', $statusCounts['in_progress'])
                ->description(number_format(($statusCounts['in_progress'] / max(1, $totalDrivers) * 100), 1).'% of total')
                ->descriptionIcon('heroicon-m-arrow-path')
                ->color('info'),
        ];
    }
}
