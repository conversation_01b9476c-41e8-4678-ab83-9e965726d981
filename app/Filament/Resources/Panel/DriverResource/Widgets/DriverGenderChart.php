<?php

namespace App\Filament\Resources\Panel\DriverResource\Widgets;

use App\Models\Driver;
use App\Enums\GenderEnum;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Log;
use Filament\Support\Facades\FilamentAsset;

class DriverGenderChart extends ChartWidget
{
    protected static ?string $heading = 'Driver Gender Distribution';

    // Use Filament's default chart widget view 

    public ?string $filter = null;

    protected function getData(): array
    {
        try {
            $maleCount = Driver::query()
                ->where('rider_gender', GenderEnum::male->value)
                ->count();

            $femaleCount = Driver::query()
                ->where('rider_gender', GenderEnum::female->value)
                ->count();

            Log::info('Chart data:', [
                'male' => $maleCount,
                'female' => $femaleCount
            ]);

            if ($maleCount === 0 && $femaleCount === 0) {
                throw new \Exception('No gender data available');
            }

            return [
                'datasets' => [
                    [
                        'label' => 'Drivers by Gender',
                        'data' => [$maleCount, $femaleCount],
                        'backgroundColor' => [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(236, 72, 153, 0.8)',
                        ],
                    ],
                ],
                'labels' => [
                    GenderEnum::male->getLabel() . " ({$maleCount})",
                    GenderEnum::female->getLabel() . " ({$femaleCount})",
                ],
            ];
        } catch (\Exception $e) {
            Log::error('Error in DriverGenderChart:', [
                'error' => $e->getMessage(),
            ]);

            return [
                'datasets' => [
                    [
                        'label' => 'No Data Available',
                        'data' => [1],
                        'backgroundColor' => ['rgba(156, 163, 175, 0.5)'],
                    ],
                ],
                'labels' => ['No Data'],
            ];
        }
    }

    // Use a doughnut chart for a modern look
    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
        ];
    }

    protected function getMaxHeight(): ?string
    {
        return '300px';
    }

    public static function canView(): bool
    {
        return true;
    }

    public function mount(): void
    {
        Log::info('Chart Widget Mounted', [
            'data' => $this->getData(),
            'options' => $this->getOptions(),
        ]);
    }
}
