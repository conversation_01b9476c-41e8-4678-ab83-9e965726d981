<?php

namespace App\Filament\Resources\Panel;

use App\Enums\Trips\TripStatus;
use App\Filament\Resources\Panel\TripRatingResource\Pages;
use App\Models\TripRating;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Infolists\Components;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use IbrahimBougaoua\FilamentRatingStar\Entries\Components\RatingStar;
use IbrahimBougaoua\FilamentRatingStar\Forms\Components\RatingStar as FormRatingStar;
use Illuminate\Database\Eloquent\Builder;

class TripRatingResource extends Resource
{
    protected static ?string $model = TripRating::class;

    protected static ?string $navigationIcon = 'mdi-star-cog-outline';

    protected static ?string $navigationGroup = 'Admin';

    public static function getModelLabel(): string
    {
        return __('crud.tripRatings.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.tripRatings.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.tripRatings.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    Grid::make(['default' => 1])
                        ->schema([
                            Section::make('Rider Trip Rating')
                                ->schema([
                                    FormRatingStar::make('rider_to_driver_rating')
                                        ->label('Rider to Driver Rating'),
                                    FormRatingStar::make('rider_to_car_rating')
                                        ->label('Rider to Car Rating'),

                                    // RichEditor::make('trip_review')
                                    // ->toolbarButtons(['redo', 'undo'])
                                    // ->required()
                                    // ->string()
                                    // ->fileAttachmentsVisibility('public')
                                    // ->placeholder('Write a review about the trip'),
                                ]),

                            // Section::make('Driver To Rider Rating')
                            //     ->schema([
                            //         FormRatingStar::make('car_rating')
                            //             ->required(),

                            //         RichEditor::make('car_review')
                            //             ->toolbarButtons(['redo', 'undo'])
                            //             ->required()
                            //             ->string()
                            //             ->fileAttachmentsVisibility('public')
                            //             ->placeholder('Write a review about the car'),
                            //     ]),
                        ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {

        $currentStatuses = [
            TripStatus::dispatched->value,
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
        ];

        return $table
            ->poll('60s')
            ->modifyQueryUsing(function (Builder $query) {
                return $query->whereHas('trip', function ($tripQuery) {
                    $tripQuery->where('status', '=', TripStatus::completed->value);
                });
            })
            ->columns([

                TextColumn::make('trip.id')
                    ->label('Trip Id')
                    ->searchable(),

                TextColumn::make('trip.created_at')
                    ->date()
                    ->label('Date'),

                TextColumn::make('trip.created_at_time')
                    ->label('Time')
                    ->getStateUsing(fn ($record) => $record->created_at->format('H:i:s')),

                TextColumn::make('trip.driver.user.full_name')
                    ->label('Driver')
                    ->searchable(),
                TextColumn::make('trip.driver.user.phone_number')
                    ->label('Driver Phone')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('trip.rider.user.full_name')
                    ->label('Rider')
                    ->searchable(),

                TextColumn::make('trip.rider.user.phone_number')
                    ->label('Rider Phone')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('trip.status')
                    ->label('Status')
                    ->badge(),

                TextColumn::make('trip.tripLocation.arrival_address')
                    ->label('Pickup location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('trip.tripLocation.departure_address')
                    ->label('Drop-off location')
                    ->default('-')
                    ->limit(15)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        if (strlen($state) <= $column->getCharacterLimit()) {
                            return null;
                        }

                        // Only render the tooltip if the column content exceeds the length limit.
                        return $state;
                    })
                    ->searchable(),

                TextColumn::make('trip.fare')
                    ->getStateUsing(function ($record) {
                        if (! $record->trip->pricing_breakdown) {
                            return 'N/A';
                        }

                        $total = json_decode($record->trip->pricing_breakdown, true);

                        // Check if total exists in the decoded JSON
                        if (! $total || ! isset($total['total'])) {
                            return 'N/A';
                        }

                        return $total['total'].' LYD';
                    })
                    ->label('Fare'),

                TextColumn::make('trip.estimated_arrival_time')
                    ->label('ETA')
                    ->visible(function ($livewire) {
                        return $livewire instanceof \App\Filament\Resources\Panel\TripResource\Pages\ListTrips
                            && $livewire->activeTab === 'current';
                    })
                    ->since()
                    ->tooltip(fn ($record) => $record->estimated_arrival_time),

            ])
            // ->filters([

            // ])
            // ->filtersTriggerAction(
            //     fn (Action $action) => $action
            //         ->slideOver()
            //         ->modalIcon('heroicon-o-funnel')
            //         ->button()
            //         ->size('md')
            //         ->icon('heroicon-o-funnel')
            //         ->label('Filter'),
            // )
            ->actions([
                // Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->defaultSort('created_at', 'asc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make('Trip Information')
                    ->schema([
                        Components\Group::make([
                            Components\TextEntry::make('trip.status')
                                ->label('Status')
                                ->badge(),

                            Components\TextEntry::make('trip.rider.user.full_name')
                                ->label('Rider'),

                            Components\TextEntry::make('trip.driver.user.full_name')
                                ->label('Driver'),
                        ])->inlineLabel(),

                        Components\Section::make('Ratings')
                            ->schema([
                                RatingStar::make('trip_rating')
                                    ->label('Trip')
                                    ->size('lg'),

                                RatingStar::make('car_rating')
                                    ->label('Car')
                                    ->size('lg'),
                            ])->columns(2),

                        Components\Section::make('Reviews')
                            ->schema([
                                Components\TextEntry::make('trip_review')
                                    ->label('Trip Review')
                                    ->columnSpan(2),

                                Components\TextEntry::make('car_review')
                                    ->label('Car Review')
                                    ->columnSpan(2),
                            ])->columns(2),
                    ]),

                Components\Section::make('Trip Details')
                    ->schema([
                        Components\TextEntry::make('trip.distance')
                            ->label('Distance')
                            ->suffix('km')
                            ->columnSpanFull(),

                        Components\Group::make([
                            Components\TextEntry::make('trip.departure_area.name')
                                ->label('Departure Area')
                                ->badge(),

                            Components\TextEntry::make('trip.arrival_area.name')
                                ->label('Arrival Area')
                                ->badge(),
                        ])->inlineLabel()->columnSpanFull(),

                        Components\Group::make([
                            Components\Section::make('Departure Times')
                                ->schema([
                                    Components\TextEntry::make('trip.estimated_departure_time')
                                        ->label('Estimated')
                                        ->dateTime()
                                        ->badge(),

                                    Components\TextEntry::make('trip.actual_departure_time')
                                        ->label('Actual')
                                        ->dateTime()
                                        ->badge(),
                                ])->columns(2),

                            Components\Section::make('Arrival Times')
                                ->schema([
                                    Components\TextEntry::make('trip.estimated_arrival_time')
                                        ->label('Estimated')
                                        ->dateTime()
                                        ->badge(),

                                    Components\TextEntry::make('trip.actual_arrival_time')
                                        ->label('Actual')
                                        ->dateTime()
                                        ->badge(),
                                ])->columns(2),
                        ])->columns(2),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTripRatings::route('/'),
            // 'create' => Pages\CreateTripRating::route('/create'),
            // 'view' => Pages\ViewTripRating::route('/{record}'),
            'edit' => Pages\EditTripRating::route('/{record}/edit'),
        ];
    }
}
