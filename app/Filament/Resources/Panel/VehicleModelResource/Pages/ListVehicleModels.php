<?php

namespace App\Filament\Resources\Panel\VehicleModelResource\Pages;

use App\Filament\Resources\Panel\VehicleModelResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListVehicleModels extends ListRecords
{
    protected static string $resource = VehicleModelResource::class;

    protected function getHeaderActions(): array
    {
        return [Actions\CreateAction::make()
            ->createAnother(false)
            ->successNotification(
                Notification::make()
                    ->success()
                    ->title('New vehicle model created successfully')
            ), ];
    }
}
