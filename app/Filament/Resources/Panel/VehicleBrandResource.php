<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\VehicleBrandResource\Pages;
use App\Filament\Resources\Panel\VehicleBrandResource\RelationManagers;
use App\Models\VehicleBrand;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;

class VehicleBrandResource extends Resource
{
    protected static ?string $model = VehicleBrand::class;

    protected static ?string $navigationIcon = 'mdi-car-search';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 2;

    // Cached regex patterns to compile them once
    private const ARABIC_NAME_REGEX = '/^(?!.*[\x{0660}-\x{0669}\x{06F0}-\x{06F9}0-9])(?!^-)(?!.*-$)[\p{Arabic}-]+( [\p{Arabic}-]+)*$/u';

    private const ENGLISH_NAME_REGEX = '/^[A-Za-z0-9ŠšÉéÈèÊêËë\-]+( [A-Za-z0-9ŠšÉéÈèÊêËë\-]+)*$/';

    public static function getModelLabel(): string
    {
        return __('crud.vehicleBrands.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.vehicleBrands.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.vehicleBrands.collectionTitle');
    }

    private static function validateField(HasForms $livewire, TextInput $component, string $regex, string $message): void
    {
        $livewire->validateOnly($component->getStatePath(), [
            $component->getStatePath() => [
                'required',
                "regex:{$regex}",
            ],
        ], [
            $component->getStatePath().'.regex' => $message,
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make()->schema([
                Grid::make(['default' => 1])->schema([
                    // Arabic Name Field
                    TextInput::make('name_ar')
                        ->rules(['required'])
                        ->markAsRequired()
                        ->string()
                        ->label('Brand Name (Arabic)')
                        ->maxLength(30)
                        ->placeholder('أدخل اسم العلامة التجارية باللغة العربية')
                        ->afterStateUpdated(fn ($set, $state) => $set('name_ar', preg_replace('/\s+/', ' ', trim($state))))
                        ->regex(self::ARABIC_NAME_REGEX)
                        ->validationMessages([
                            'regex' => 'The brand Name (Arabic) can contain only Arabic letters, hyphens (-), and spaces between words.',
                        ])
                        ->autofocus(false)
                        ->unique('vehicle_brands', 'name_ar', ignoreRecord: true)
                        ->live(debounce: 500)
                        ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ARABIC_NAME_REGEX, 'The brand Name (Arabic) can contain only Arabic letters, hyphens (-), and spaces between words.')),

                    // English Name Field
                    TextInput::make('name_en')
                        ->rules(['required'])
                        ->markAsRequired()
                        ->string()
                        ->maxLength(30)
                        ->autofocus()
                        ->label('Brand Name (English)')
                        ->placeholder('Enter the brand name in English')
                        ->regex(self::ENGLISH_NAME_REGEX)
                        ->afterStateUpdated(fn ($set, $state) => $set('name_en', preg_replace('/\s+/', ' ', trim(ucfirst($state)))))
                        ->validationMessages([
                            'regex' => 'The brand Name (English) can contain only English letters, numbers, special characters, with spaces between words.',
                        ])
                        ->unique('vehicle_brands', 'name_en', ignoreRecord: true)
                        ->live(debounce: 500)
                        ->afterStateUpdated(fn (HasForms $livewire, TextInput $component) => self::validateField($livewire, $component, self::ENGLISH_NAME_REGEX, 'The brand Name (English) can contain only English letters, numbers, special characters, with spaces between words.')),
                    Toggle::make('status')
                        ->required()
                        ->rules(['boolean'])
                        ->default(true)
                        ->onIcon('heroicon-m-check')
                        ->offIcon('heroicon-m-x-mark')
                        ->inline()
                        ->helperText(function ($record) {
                            if ($record && $record->exists) {
                                if ($record->vehicles()->exists()) {
                                    return 'This brand is associated with one or more vehicles and cannot be deactivated.';
                                }

                                if ($record->vehicleModels()->where('status', true)->exists()) {
                                    return 'This brand has active vehicle models and cannot be deactivated.';
                                }
                            }

                            return '';
                        })
                        ->dehydrated(function ($state, $record) {
                            if ($record && $record->exists) {
                                // Prevent deactivation if brand has vehicles or active vehicle models
                                if (($record->vehicles()->exists() || $record->vehicleModels()->where('status', true)->exists()) && $state === false) {
                                    return true;
                                }
                            }

                            return $state;
                        })
                        ->disabled(function ($record) {
                            // Disable toggle if the brand has associated vehicles or active vehicle models
                            return $record && $record->exists && ($record->vehicles()->exists() || $record->vehicleModels()->where('status', true)->exists());
                        }),
                ]),
            ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->columns([

                TextColumn::make('name_ar')
                    ->searchable()
                    ->label('Brand Name (Arabic)')
                    ->wrap(),
                // ->description(fn ($record) => $record->vehicleModels()->exists() ? 'هذه العلامة لديها نماذج مرتبطة بها.' : 'لا توجد نماذج مرتبطة.'),

                TextColumn::make('name_en')
                    ->searchable()
                    ->label('Brand Name (English)')
                    ->wrap(),
                // ->description(fn ($record) => $record->vehicleModels()->exists() ? 'This brand has models associated.' : 'No models associated.')

                ToggleColumn::make('status')
                    ->label('Status')
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state === false) { // If trying to deactivate the brand
                            if ($record->vehicles()->exists() || $record->vehicleModels()->where('status', true)->exists()) {
                                Notification::make()
                                    ->title('Deactivation Prevented')
                                    ->body('This brand cannot be deactivated because it is assigned to one or more active vehicle models or vehicles.')
                                    ->warning()
                                    ->send();

                                $record->status = true;
                                $record->save();

                                return;
                            }

                            // Deactivate all vehicle models linked to this brand
                            $record->vehicleModels()->update(['status' => false]);
                        } else {
                            // If activating the brand
                            // Prevent activation of the brand if it has inactive models
                            if ($record->vehicleModels()->exists() && $record->vehicleModels()->where('status', true)->doesntExist()) {
                                Notification::make()
                                    ->title('Activation Warning')
                                    ->body('The vehicle brand is activated, but its models remain inactive.')
                                    ->warning()
                                    ->send();
                            }
                        }

                        // Save the new state
                        $record->status = $state;
                        $record->save();

                        // Send notification for activation or deactivation
                        Notification::make()
                            ->title($state ? 'Activated' : 'Deactivated')
                            ->body('The vehicle brand has been '.($state ? 'activated' : 'deactivated').'.')
                            ->success()
                            ->send();
                    }),

            ])
            ->filters([
            ])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle brand updated successfully')
                        ),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle brand deleted successfully')
                        )
                        ->before(function ($record, Tables\Actions\DeleteAction $action) {
                            // Check if the VehicleBrand has any related VehicleModels or Vehicles
                            if ($record->vehicleModels()->where('status', true)->exists() || $record->vehicles()->exists()) {
                                Notification::make()
                                    ->title('Deletion Prevented')
                                    ->body('This brand cannot be deleted because it is assigned to one or more active vehicle models or vehicles.')
                                    ->warning()
                                    ->send();

                                $action->cancel();
                            }
                        }),
                ]),
            ])
            ->bulkActions([
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make('Vehicle Brand Details')
                    ->schema([
                        Components\Group::make([
                            Components\TextEntry::make('name_ar')
                                ->label('Brand Name (Arabic)')
                                ->icon('heroicon-o-truck')
                                ->iconPosition(IconPosition::Before)
                                ->badge(fn (): string => 'Arabic Name')
                                ->columnSpan(1),

                            Components\TextEntry::make('name_en')
                                ->label('Brand Name (English)')
                                ->icon('heroicon-o-truck')
                                ->iconPosition(IconPosition::Before)
                                ->badge(fn (): string => 'English Name')
                                ->columnSpan(1),

                            IconEntry::make('status')
                                ->label('Brand Status')
                                ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                ->boolean(),

                        ])->inlineLabel(),
                    ])
                    ->columnSpanFull()
                    ->description('Overview of the vehicle brand details.'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\VehicleModelsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleBrands::route('/'),
            // 'create' => Pages\CreateVehicleBrand::route('/create'),
            // 'view' => Pages\ViewVehicleBrand::route('/{record}'),
            // 'edit' => Pages\EditVehicleBrand::route('/{record}/edit'),
        ];
    }
}
