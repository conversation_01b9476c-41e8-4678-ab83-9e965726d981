<?php

namespace App\Filament\Resources\Panel\TripRatingResource\Pages;

use App\Filament\Resources\Panel\TripRatingResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditTripRating extends EditRecord
{
    protected static string $resource = TripRatingResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }

    protected function getSavedNotification(): ?Notification
    {

        return Notification::make()
            ->success()
            ->title('Trip Rating updated successfully');
    }
}
