<?php

namespace App\Filament\Resources\Panel\TripRatingResource\Pages;

use App\Filament\Resources\Panel\TripRatingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTripRatings extends ListRecords
{
    protected static string $resource = TripRatingResource::class;

    // protected function getHeaderActions(): array
    // {
    //     return [Actions\CreateAction::make()];
    // }
}
