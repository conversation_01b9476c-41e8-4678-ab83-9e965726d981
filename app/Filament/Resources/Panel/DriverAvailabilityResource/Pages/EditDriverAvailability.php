<?php

namespace App\Filament\Resources\Panel\DriverAvailabilityResource\Pages;

use App\Filament\Resources\Panel\DriverAvailabilityResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDriverAvailability extends EditRecord
{
    protected static string $resource = DriverAvailabilityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
            ];
    }
}
