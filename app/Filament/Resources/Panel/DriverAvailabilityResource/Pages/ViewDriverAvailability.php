<?php

namespace App\Filament\Resources\Panel\DriverAvailabilityResource\Pages;

use App\Filament\Resources\Panel\DriverAvailabilityResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewDriverAvailability extends ViewRecord
{
    protected static string $resource = DriverAvailabilityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
        ];
    }
}
