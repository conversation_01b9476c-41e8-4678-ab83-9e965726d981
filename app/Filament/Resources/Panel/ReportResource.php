<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\ReportResource\Pages;
use Filament\Navigation\NavigationItem;
use Filament\Resources\Resource;

class ReportResource extends Resource
{
    protected static ?string $model = null; // No model needed for reports

    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Reports';

    protected static ?string $slug = 'reports';

    protected static ?int $navigationSort = 100;

    protected static ?string $navigationGroup = 'Analytics';

    public static function getNavigationItems(): array
    {
        return [
            NavigationItem::make(static::getNavigationLabel())
                ->icon(static::getNavigationIcon())
                ->activeIcon(static::getActiveNavigationIcon())
                ->group(static::getNavigationGroup())
                ->sort(static::getNavigationSort())
                ->url(static::getUrl('index'))
                ->isActiveWhen(fn (): bool => request()->routeIs(static::getRouteBaseName().'.*')),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReports::route('/'),
            'drivers' => Pages\DriversReport::route('/drivers'),
            'riders' => Pages\RidersReport::route('/riders'),
            'trips' => Pages\TripsReport::route('/trips'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }

    public static function canDeleteAny(): bool
    {
        return false;
    }
}
