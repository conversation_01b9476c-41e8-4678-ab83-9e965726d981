<?php

namespace App\Filament\Resources\Panel\AreaResource\Pages;

use App\Filament\Resources\Panel\AreaResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewArea extends ViewRecord
{
    protected static string $resource = AreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\EditAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }
}
