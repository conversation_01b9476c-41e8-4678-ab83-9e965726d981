<?php

namespace App\Filament\Resources\Panel\AreaResource\Pages;

use App\Filament\Resources\Panel\AreaResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditArea extends EditRecord
{
    protected static string $resource = AreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Area updated successfully');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }
}
