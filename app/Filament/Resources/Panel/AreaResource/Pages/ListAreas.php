<?php

namespace App\Filament\Resources\Panel\AreaResource\Pages;

use App\Filament\Resources\Panel\AreaResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListAreas extends ListRecords
{
    protected static string $resource = AreaResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->createAnother(false)
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title('New area created successfully')
                ),
        ];
    }
}
