<?php

namespace App\Filament\Resources\Panel\VehicleEquipmentResource\Pages;

use App\Filament\Resources\Panel\VehicleEquipmentResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;

class ListVehicleEquipment extends ListRecords
{
    protected static string $resource = VehicleEquipmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->createAnother(false)
                ->successNotification(
                    Notification::make()
                        ->success()
                        ->title('New equipment created successfully')
                ),
        ];
    }
}
