<?php

namespace App\Filament\Tables\Filters;

use App\Enums\Trips\TripStatus;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;

class TripStatusTabFilter extends Filter
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->form([
            Select::make('status')
                ->label('Trip Status')
                ->options(function () {
                    $currentStatuses = [
                        TripStatus::assigned->value,
                        TripStatus::driver_arriving->value,
                        TripStatus::driver_arrived->value,
                        TripStatus::on_trip->value,
                        TripStatus::waiting_for_driver_confirmation->value,
                    ];

                    $activeTab = $this->getLivewire()->activeTab ?? 'current';

                    if ($activeTab === 'current') {
                        return collect(TripStatus::cases())
                            ->filter(fn ($status) => in_array($status->value, $currentStatuses))
                            ->mapWithKeys(fn ($status) => [$status->value => $status->getLabel()])
                            ->toArray();
                    }

                    return collect(TripStatus::cases())
                        ->filter(fn ($status) => ! in_array($status->value, $currentStatuses))
                        ->mapWithKeys(fn ($status) => [$status->value => $status->getLabel()])
                        ->toArray();
                })
                ->multiple()
                ->live() // Enable reactivity
                ->afterStateUpdated(function ($state, $livewire) {
                    // Refresh the table when the filter changes
                    $livewire->dispatch('$refresh');
                }),
        ]);

        $this->query(function (Builder $query, array $data) {
            if (! empty($data['status'])) {
                $query->whereIn('status', $data['status']);
            }
        });

        $this->indicateUsing(function (array $data): ?string {
            if (! empty($data['status'])) {
                return 'Status: '.implode(', ', array_map(fn ($value) => TripStatus::from($value)->getLabel(), $data['status']));
            }

            return null;
        });
    }
}
