<?php

namespace App\Filament\Tables\DriverRequest;

use App\Filament\Resources\Panel\DriverResource\Pages;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class RequestDriverVehicle
{
    public static function make(Table $table, ?Builder $query = null): Table
    {
        return $table
            ->paginated(false)
            ->poll('60s')
            ->query(query: $query ?? \App\Models\Vehicle::query())
            ->columns([
                TextColumn::make('license_plate_number')
                    ->label('License Plate Number'),

                TextColumn::make('drivers.user.full_name')
                    ->label('Driver Name'),

                TextColumn::make('vehicleModel.vehicleBrand.name_en')
                    ->label('Brand'),

                TextColumn::make('vehicleModel.name_en')
                    ->label('Model'),

                TextColumn::make('vehicleType.name_en')
                    ->label('Type')
                    ->getStateUsing(fn ($record) => empty($record->vehicleType?->name_en) ||
                        in_array($record->vehicleType?->name_en, ['Default_passenger_type', 'Default_freight_type'])
                            ? 'Not available'
                            : $record->vehicleType->name_en
                    ),

                TextColumn::make('vehicleType.category.value')
                    ->label('Category')
                    ->formatStateUsing(fn ($state) => $state === 'passenger' ? 'Passenger' : ($state === 'freight' ? 'Freight' : $state)),

            ])
            ->filters([]) // No filters needed for a single record
            ->actions([
                \Filament\Tables\Actions\ViewAction::make()
                    ->url(fn (Model $record): string => Pages\ViewDriverVehicle::getUrl([
                        'record' => $record->drivers->first()->id,
                        'vehicleId' => $record->id,
                    ])),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'view' => Pages\ViewDriver::route('/{record}'),
            'view_vehicle' => Pages\ViewDriverVehicle::route('/{record}/vehicle/{vehicleId}'),
        ];
    }
}
