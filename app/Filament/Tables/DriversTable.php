<?php

namespace App\Filament\Tables;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\GenderEnum;
use App\Filament\Traits\HasDateRangeFilter;
use App\Helpers\TripHelper;
use App\Models\Driver;
use App\Models\TripCancellation;
use App\Models\User;
use App\Notifications\Firebase_notifications\NotifyUser;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\ToggleButtons;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use IbrahimBougaoua\FilamentRatingStar\Columns\Components\RatingStar as ColumnRatingStar;
use IbrahimBougaoua\FilamentRatingStar\Forms\Components\RatingStar as FormRatingStar;
use Illuminate\Database\Eloquent\Builder;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class DriversTable
{
    use HasDateRangeFilter;

    public static function make(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->columns([
                // Registration Date
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),
                ImageColumn::make('user.cover_picture')
                    ->label('Profile Photo')
                    ->size(60)
                    ->circular()
                    ->defaultImageUrl(url('/images/avatar.png')),

                TextColumn::make('user.full_name')
                    ->label('Driver Name')
                    ->searchable(),

                PhoneColumn::make('user.phone_number')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->label('Phone Number')
                    ->searchable(),

                TextColumn::make('user.gender')
                    ->label('Gender')
                    ->getStateUsing(function ($record) {
                        if (! $record->user->gender) {
                            return '';
                        }

                        return $record->user->gender->value === 'male' ? 'ذكر' : 'أنثى';
                    })
                    ->badge(),

                TextColumn::make('user.address.address')
                    ->label('Address')
                    ->limit(30),
                // ->getStateUsing(function ($record) {
                //     return $record->user?->address()->orderBy('created_at', 'asc')->first()?->address;
                // }),

                ColumnRatingStar::make('average_driver_rating')
                    ->label('Ratings')
                    ->size('sm'),

                TextColumn::make('user.created_at')
                    ->label('Registration Date')
                    ->since()
                    ->dateTimeTooltip(),

                TextColumn::make('global_status')
                    ->badge()
                    ->label('Status'),

                TextColumn::make('vehicles.license_plate_number')
                    ->label('License Plate Number')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ])
            ->filters([
                self::dateRangeFilter(),

                Filter::make('Gender')
                    ->form([
                        Select::make('Gender')
                            ->native(false)
                            ->options(GenderEnum::class),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['Gender']) && $data['Gender'] == 'male') {
                            return $query->whereHas('user', function (Builder $query) use ($data) {
                                $query->where('gender', '=', $data['Gender'])
                                    ->whereNotNull('gender');
                            });
                        } elseif (isset($data['Gender']) && $data['Gender'] == 'female') {
                            return $query->whereHas('user', function (Builder $query) use ($data) {
                                $query->where('gender', '=', $data['Gender'])
                                    ->whereNotNull('gender');
                            });
                        }

                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! isset($data['Gender']) || ! $data['Gender']) {
                            return null;
                        }

                        return ucfirst(str_replace('_', ' ', $data['Gender']));
                    }),

                SelectFilter::make('global_status')
                    ->options(
                        collect(DriverGlobalStatus::cases())
                            ->reject(fn ($case) => $case === DriverGlobalStatus::deleted)
                            ->reject(fn ($case) => $case === DriverGlobalStatus::in_progress)
                            ->reject(fn ($case) => $case === DriverGlobalStatus::pending)
                            ->mapWithKeys(fn ($case) => [$case->value => $case->name])
                            ->toArray()
                    )
                    ->visible(function ($livewire) {
                        // Only show this filter in the 'all' tab
                        return $livewire->activeTab === 'all';
                    })
                    ->multiple(),

                // SelectFilter::make('user_address')
                //     ->label('Address')
                //     ->options(fn () => Driver::getFirstAddresses()) // Fetch distinct first-created addresses
                //     ->searchable()
                //     ->query(function ($query, $data) {
                //         if (! empty($data['value'])) {
                //             $addressValue = $data['value'];

                //             $query->whereExists(function ($subQuery) use ($addressValue) {
                //                 $subQuery->selectRaw(1)
                //                     ->from('users')
                //                     ->whereColumn('drivers.user_id', 'users.id')
                //                     ->whereExists(function ($addressQuery) use ($addressValue) {
                //                         $addressQuery->selectRaw(1)
                //                             ->from('addresses')
                //                             ->whereColumn('addresses.addressable_id', 'users.id')
                //                             ->where('addresses.addressable_type', User::class)
                //                             ->where('addresses.address', 'LIKE', "%{$addressValue}%")
                //                             ->orderBy('addresses.created_at', 'asc')
                //                             ->limit(1);
                //                     });
                //             });
                //         }
                //     }),
                SelectFilter::make('user_address')
                    ->label('Address')
                    ->options(fn () => Driver::getFirstAddresses()) // Fetch distinct first-created addresses
                    ->searchable()
                    ->query(function ($query, $data) {
                        if (! empty($data['value'])) {
                            $addressValue = $data['value'];

                            $query->whereExists(function ($subQuery) use ($addressValue) {
                                $subQuery->selectRaw(1)
                                    ->from('users')
                                    ->whereColumn('drivers.user_id', 'users.id')
                                    ->whereExists(function ($addressQuery) use ($addressValue) {
                                        $addressQuery->selectRaw(1)
                                            ->from('addresses')
                                            ->whereColumn('addresses.addressable_id', 'users.id')
                                            ->where('addresses.addressable_type', User::class)
                                            ->whereNotNull('addresses.address') // Ensure no null addresses
                                            ->where('addresses.address', 'ILIKE', "%{$addressValue}%") // Use ILIKE for case-insensitive search (PostgreSQL) or LIKE (MySQL)
                                            ->orderBy('addresses.created_at', 'asc')
                                            ->limit(1);
                                    });
                            });
                            $query->withTrashed();
                        }
                    }),
                Filter::make('Rating Star')
                    ->form([
                        FormRatingStar::make('average_driver_rating')
                            ->label('Ratings'),
                    ])
                    ->query(function ($query, $data) {
                        if (isset($data['average_driver_rating'])) {
                            $query->where('average_driver_rating', $data['average_driver_rating']);
                        }
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! isset($data['average_driver_rating'])) {
                            return null;
                        }

                        return 'Ratings: '.$data['average_driver_rating'];
                    }),

                // Tables\Filters\SelectFilter::make('status')
                //     ->label('Status')
                //     ->options([
                //         'full_registration' => 'Full Registration',
                //         'not_registered' => 'Not Registered',
                //     ])
                //     ->query(fn ($query, $status) => match ($status) {
                //         'full_registration' => $query->whereNotNull('user.phone_number')->whereNotNull('user.email'),
                //         'not_registered' => $query->whereNull('user.phone_number')->orWhereNull('user.email'),
                //         default => $query,
                //     }),

            ])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Driver updated successfully')
                        )
                        ->visible(fn ($record) => $record->global_status === DriverGlobalStatus::blocked || $record->global_status === DriverGlobalStatus::active),
                    Tables\Actions\ViewAction::make(),

                    Action::make('block')
                        ->visible(fn (Driver $record) => $record->global_status === DriverGlobalStatus::active) // Only show for active drivers
                        ->icon('heroicon-o-no-symbol')
                        ->requiresConfirmation()
                        ->modalHeading('Block Driver')
                        ->modalDescription('Please enter a reason for blocking this driver.')
                        ->form([
                            Textarea::make('blocking_reason')
                                ->label('Blocking Reason')
                                ->rules(['required', 'max:50'])
                                ->maxLength(50),
                        ])
                        ->action(function (array $data, Driver $record) {
                            // Check if driver has active trips
                            if (TripHelper::driverHasActiveTrips($record)) {
                                Notification::make()
                                    ->title('Blocking Prevented')
                                    ->body('This driver cannot be blocked because they have active trips in progress.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            // Save the current global_status as previous_global_status
                            $record->previous_global_status = $record->global_status;

                            // Update the driver's global_status to blocked
                            $record->update([
                                'global_status' => DriverGlobalStatus::blocked,
                                'previous_global_status' => $record->previous_global_status,
                            ]);

                            // Update the user's blocking_reason
                            $record->user->update([
                                'blocking_reason' => $data['blocking_reason'],
                            ]);

                            // Manually trigger the success notification
                            Notification::make()
                                ->success()
                                ->title('Driver Blocked Successfully')
                                ->send();
                        })
                        ->color('warning')
                        ->after(function ($record, $data) {
                            // Only execute after logic if the action was successful (no active trips)
                            if (! TripHelper::driverHasActiveTrips($record)) {
                                $reason = $data['blocking_reason'];

                                // Send notification to the driver
                                $notificationData = [
                                    'title' => 'تم حظر حسابك',
                                    'description' => "تم حظر حسابك بسبب $reason . لن تتمكن من قبول الرحلات حتى يتم حل المشكلة",
                                ];

                                if ($record->user->tokens->isNotEmpty()) {
                                    $record->user->notifyNow(new NotifyUser($record, $notificationData));
                                }
                            }
                        }),

                    Action::make('Unblock')
                        ->visible(fn (Driver $record) => $record->global_status === DriverGlobalStatus::blocked) // Only show for blocked drivers
                        ->icon('heroicon-o-no-symbol')
                        ->requiresConfirmation()
                        ->color('warning')
                        ->modalHeading('Unblock Driver')
                        ->after(function (Driver $record) {
                            // Restore the driver's status to active
                            $record->global_status = DriverGlobalStatus::active;

                            // Clear the blocking reason
                            $record->user->update([
                                'blocking_reason' => null,
                            ]);

                            // Save the updated record
                            $record->save();

                            // Send notification to the driver
                            $notificationData = [
                                'title' => 'تم إلغاء حظر حسابك',
                                'description' => 'تم إلغاء حظر حسابك. يمكنك الآن قبول الرحلات وإتمامها مجددًا.',
                            ];
                            if ($record->user->tokens->isNotEmpty()) {
                                $record->user->notifyNow(new NotifyUser($record, $notificationData));
                            }
                            // Manually trigger the success notification
                            Notification::make()
                                ->success()
                                ->title('Driver Unblocked Successfully')
                                ->send();

                            TripCancellation::where('user_id', $record->user->id)->delete();
                        }),

                    Tables\Actions\DeleteAction::make()
                        ->action(function (Driver $record) {
                            // Check if driver has active trips before deletion
                            if (TripHelper::driverHasActiveTrips($record)) {
                                Notification::make()
                                    ->title('Deletion Prevented')
                                    ->body('This driver cannot be deleted because they have active trips in progress.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            // Proceed with deletion if no active trips
                            $record->delete();
                            $record->update(['global_status' => DriverGlobalStatus::deleted->value]);

                            // Send success notification
                            Notification::make()
                                ->success()
                                ->title('Driver deleted successfully')
                                ->send();
                        })
                        ->requiresConfirmation(),

                    //                    Tables\Actions\RestoreAction::make()
                    //                        ->visible(fn ($record) => $record->trashed())
                    //                        ->form([
                    //                            Section::make([
                    //                                ToggleButtons::make('global_status')
                    //                                    ->inline()
                    //                                    ->required()
                    //                                    ->reactive()
                    //                                    ->options([
                    //                                        DriverGlobalStatus::pending->value => 'pending',
                    //                                        DriverGlobalStatus::active->value => 'active',
                    //                                    ])
                    //                                    ->icons([
                    //                                        DriverGlobalStatus::pending->value => 'heroicon-o-clock',
                    //                                        DriverGlobalStatus::active->value => 'heroicon-o-check',
                    //                                    ])
                    //                                    ->colors([
                    //                                        DriverGlobalStatus::pending->value => 'gray',
                    //                                        DriverGlobalStatus::active->value => 'success',
                    //                                    ])
                    //                                    ->helperText('Select the driver status after restoration'),
                    //                            ])->columnSpan(['lg' => 1]),
                    //                        ])
                    //                        ->successNotification(
                    //                            Notification::make()
                    //                                ->success()
                    //                                ->title('Driver restored successfully')
                    //                        )
                    //                        ->after(function ($record, $data) {
                    //                            // Get the value of 'global_status' from the form data
                    //                            $globalStatus = $data['global_status'];
                    //
                    //                            // Update the global_status of the record
                    //                            $record->global_status = $globalStatus;
                    //                            $record->save();
                    //                        }),
                ]),
            ])
            ->defaultSort('id', 'desc');
    }
}
