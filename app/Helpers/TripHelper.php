<?php

namespace App\Helpers;

use App\Enums\Trips\TripStatus;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;

class TripHelper
{
    /**
     * Get the list of active trip statuses that prevent blocking/deleting users
     */
    public static function getActiveStatuses(): array
    {
        return [
            TripStatus::assigned->value,
            TripStatus::driver_arriving->value,
            TripStatus::driver_arrived->value,
            TripStatus::on_trip->value,
            TripStatus::waiting_for_driver_confirmation->value,
        ];
    }

    /**
     * Check if driver has active trips that prevent blocking/deleting
     */
    public static function driverHasActiveTrips(Driver $driver): bool
    {
        return Trip::where('driver_id', $driver->id)
            ->whereIn('status', self::getActiveStatuses())
            ->exists();
    }

    /**
     * Check if rider has active trips that prevent blocking/deleting
     */
    public static function riderHasActiveTrips(Rider $rider): bool
    {
        return Trip::where('rider_id', $rider->id)
            ->whereIn('status', self::getActiveStatuses())
            ->exists();
    }

}
