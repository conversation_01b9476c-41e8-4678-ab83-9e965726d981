<?php

namespace App\Traits;

use App\Enums\Trips\TripStatus;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Columns\TextColumn;

trait HasTripStatusColumn
{
    public static function getTripStatusColumn(string $name = 'status', string $label = 'Status'): TextColumn
    {
        return self::configureTripStatusComponent(
            TextColumn::make($name),
            $label
        );
    }

    public static function getTripStatusEntry(string $name = 'status', string $label = 'Status'): TextEntry
    {
        return self::configureTripStatusComponent(
            TextEntry::make($name),
            $label
        );
    }

    private static function configureTripStatusComponent($component, string $label)
    {
        return $component
            ->label($label)
            ->badge()
            ->icon(fn ($record) => $record->status?->getIcon())
            ->color(fn ($record) => $record->status?->getColor())
            ->getStateUsing(self::getTripStatusState());
    }

    private static function getTripStatusState(): \Closure
    {
        return function ($record) {
            if (! $record->status) {
                return 'No Status Provided';
            }

            if ($record->status === TripStatus::canceled) {
                // Check if cancellation reason is "no_show"
                if ($record->cancellation && $record->cancellation->reason === 'no_show') {
                    return 'No Show';
                }

                // Otherwise show who canceled the trip
                if ($record->cancelled_by) {
                    return $record->cancelled_by === 'driver'
                        ? 'Canceled by Driver'
                        : 'Canceled by Rider';
                }

                return 'Canceled';
            }

            return $record->status->getLabel();
        };
    }
}
