<?php

namespace App\Traits;

use App\Models\Trip;
use Illuminate\Database\Eloquent\Builder;

trait CalculatesRevenue
{
    /**
     * Calculate total revenue from trips
     */
    public function calculateTotalRevenue(Builder $query = null): float
    {
        $query = $query ?: Trip::query();
        
        return $query
            ->whereNotNull('pricing_breakdown')
            ->get()
            ->sum(function ($trip) {
                $pricing = json_decode($trip->pricing_breakdown, true);
                return $pricing['total'] ?? 0;
            });
    }

    /**
     * Calculate revenue by trip status
     */
    public function calculateRevenueByStatus(Builder $query = null): array
    {
        $query = $query ?: Trip::query();
        
        $trips = $query
            ->whereNotNull('pricing_breakdown')
            ->get()
            ->groupBy('status');

        $revenueByStatus = [];
        
        foreach ($trips as $status => $statusTrips) {
            $revenueByStatus[$status] = $statusTrips->sum(function ($trip) {
                $pricing = json_decode($trip->pricing_breakdown, true);
                return $pricing['total'] ?? 0;
            });
        }

        return $revenueByStatus;
    }

    /**
     * Calculate average revenue per trip
     */
    public function calculateAverageRevenue(Builder $query = null): float
    {
        $query = $query ?: Trip::query();
        
        $trips = $query
            ->whereNotNull('pricing_breakdown')
            ->get();

        if ($trips->isEmpty()) {
            return 0;
        }

        $totalRevenue = $trips->sum(function ($trip) {
            $pricing = json_decode($trip->pricing_breakdown, true);
            return $pricing['total'] ?? 0;
        });

        return $totalRevenue / $trips->count();
    }

    /**
     * Get pricing breakdown for a trip
     */
    public function getTripPricing(Trip $trip): array
    {
        if (!$trip->pricing_breakdown) {
            return [
                'base_fare' => 0,
                'distance_fare' => 0,
                'surge_multiplier' => 1,
                'total' => 0,
                'currency' => 'LYD'
            ];
        }

        return json_decode($trip->pricing_breakdown, true) ?: [
            'base_fare' => 0,
            'distance_fare' => 0,
            'surge_multiplier' => 1,
            'total' => 0,
            'currency' => 'LYD'
        ];
    }
}
