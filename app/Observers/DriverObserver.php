<?php

namespace App\Observers;

use App\Models\Driver;

class DriverObserver
{
    /**
     * Handle the Driver "created" event.
     */
    public function created(Driver $driver): void
    {
        //
    }

    /**
     * Handle the Driver "updated" event.
     */
    public function updated(Driver $driver): void
    {
        // Only trigger if the status actually changed
        if ($driver->wasChanged('global_status')) {
            foreach ($driver->vehicles as $vehicle) {
                $vehicle->update([
                    'global_status' => $driver->global_status->value,
                ]);
            }
        }
    }

    /**
     * Handle the Driver "deleted" event.
     */
    public function deleted(Driver $driver): void
    {
        // When a driver is deleted, update all their vehicles' status to deleted
        foreach ($driver->vehicles as $vehicle) {
            $vehicle->update([
                'global_status' => \App\Enums\Vehicles\VehicleStatus::deleted->value,
            ]);

            // Soft delete the vehicle but don't detach from relationship
            $vehicle->delete();
        }
    }

    /**
     * Handle the Driver "restored" event.
     */
    public function restored(Driver $driver): void
    {
        // When a driver is restored, update all their vehicles' status to match the driver's global_status
        foreach ($driver->vehicles as $vehicle) {
            $vehicle->update([
                'global_status' => $driver->global_status->value,
            ]);
        }
    }

    /**
     * Handle the Driver "force deleted" event.
     */
    public function forceDeleted(Driver $driver): void
    {
        // When a driver is force deleted, update all their vehicles' status to deleted
        foreach ($driver->vehicles as $vehicle) {
            $vehicle->update([
                'global_status' => \App\Enums\Vehicles\VehicleStatus::deleted->value,
            ]);
        }
    }
}
