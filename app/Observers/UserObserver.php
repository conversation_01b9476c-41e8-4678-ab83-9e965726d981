<?php

namespace App\Observers;

use App\Models\User;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Support\Facades\Password;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        if ($user->type === 'admin') {

            $token = Password::broker()->createToken($user);

            $user->notifyNow(new ResetPasswordNotification($token));
        }
    }
}
