<?php

namespace App\Observers;

use App\Models\TripRating;
use Illuminate\Support\Facades\Log;

class RideRating
{
    /**
     * Handle the TripRating "created" event.
     */
    public function created(TripRating $tripRating): void
    {
        // Log::info('[CREATED EVENT] TripRating observer triggered', [
        //     'trip_rating_id' => $tripRating->id,
        // ]);

        $rider = $tripRating->rider;

        if ($rider) {
            $ratings = TripRating::whereHas('trip', function ($query) {
                $query->where('status', 'completed');
            })
                ->where('rider_id', $rider->id)
                ->get();
            // Log::info('[CREATED EVENT] Rider ratings count', [$ratings->count()]);
            // Calculate the new average rating
            if ($ratings->count() > 0) {
                $totalRating = $ratings->sum('driver_to_rider_rating');
                // Log::info('[CREATED EVENT] Rider total rating', [$totalRating]);

                $averageRating = $totalRating / $ratings->count();
                // Log::info('[CREATED EVENT] Rider average rating', [$averageRating]);

                $rider->update([
                    'average_rider_rating' => $averageRating,
                ]);
            }
        }

        $driver = $tripRating->driver;

        if ($driver) {
            $driverRatings = TripRating::whereHas('trip', function ($query) {
                $query->where('status', 'completed');
            })
                ->where('driver_id', $driver->id)
                ->get();
            // Log::info('[CREATED EVENT] Driver ratings count', [$driverRatings->count()]);

            if ($driverRatings->count() > 0) {
                $totalDriverRating = $driverRatings->sum('rider_to_driver_rating');
                // Log::info('[CREATED EVENT] Driver total rating', [$totalDriverRating]);

                $averageDriverRating = $totalDriverRating / $driverRatings->count();
                // Log::info('[CREATED EVENT] Driver average rating', [$averageDriverRating]);

                $driver->update([
                    'average_driver_rating' => $averageDriverRating,
                ]);
            }
        }
        $vehicle = $tripRating->trip->vehicle;
        if ($vehicle) {
            $vehicleRatings = TripRating::whereHas('trip', function ($query) {
                $query->where('status', 'completed');
            })
                ->whereHas('trip', function ($query) use ($vehicle) {
                    $query->where('vehicle_id', $vehicle->id);
                })
                ->get();
            // Log::info('[CREATED EVENT] Vehicle ratings count', [$vehicleRatings->count()]);

            if ($vehicleRatings->count() > 0) {
                $totalVehicleRating = $vehicleRatings->sum('rider_to_car_rating');
                // Log::info('[CREATED EVENT] Vehicle total rating', [$totalVehicleRating]);

                $averageVehicleRating = $totalVehicleRating / $vehicleRatings->count();
                // Log::info('[CREATED EVENT] Vehicle average rating', [$averageVehicleRating]);

                $vehicle->update([
                    'average_vehicle_rating' => $averageVehicleRating,
                ]);
            }
        }
    }

    /**
     * Handle the TripRating "updated" event.
     */
    public function updated(TripRating $tripRating): void
    {
        // Log::info('[UPDATED EVENT] TripRating observer triggered', [
        //     'trip_rating_id' => $tripRating->id,
        //     'changed_attributes' => $tripRating->getChanges(),
        // ]);

        if ($tripRating->wasChanged(['driver_to_rider_rating', 'rider_to_driver_rating', 'rider_to_car_rating'])) {

            // Recalculate rider's average rating if driver_to_rider_rating changed
            if ($tripRating->wasChanged('driver_to_rider_rating') && $tripRating->rider) {
                $rider = $tripRating->rider;
                $ratings = TripRating::whereHas('trip', function ($query) {
                    $query->where('status', 'completed');
                })
                    ->where('rider_id', $rider->id)
                    ->get();

                // Log::info('[UPDATED EVENT] Rider ratings count', [$ratings->count()]);

                if ($ratings->count() > 0) {
                    $totalRating = $ratings->sum('driver_to_rider_rating');
                    // Log::info('[UPDATED EVENT] Rider total rating', [$totalRating]);

                    $averageRating = $totalRating / $ratings->count();
                    // Log::info('[UPDATED EVENT] Rider average rating', [$averageRating]);

                    $rider->update([
                        'average_rider_rating' => $averageRating,
                    ]);
                }
            }

            // Recalculate driver's average rating if rider_to_driver_rating changed
            if ($tripRating->wasChanged('rider_to_driver_rating') && $tripRating->driver) {
                $driver = $tripRating->driver;
                $driverRatings = TripRating::whereHas('trip', function ($query) {
                    $query->where('status', 'completed');
                })
                    ->where('driver_id', $driver->id)
                    ->get();

                // Log::info('[UPDATED EVENT] Driver ratings count', [$driverRatings->count()]);

                if ($driverRatings->count() > 0) {
                    $totalDriverRating = $driverRatings->sum('rider_to_driver_rating');
                    // Log::info('[UPDATED EVENT] Driver total rating', [$totalDriverRating]);

                    $averageDriverRating = $totalDriverRating / $driverRatings->count();
                    // Log::info('[UPDATED EVENT] Driver average rating', [$averageDriverRating]);

                    $driver->update([
                        'average_driver_rating' => $averageDriverRating,
                    ]);
                }
            }

            // Recalculate vehicle's average rating if rider_to_car_rating changed
            if ($tripRating->wasChanged('rider_to_car_rating') && $tripRating->trip && $tripRating->trip->vehicle) {
                $vehicle = $tripRating->trip->vehicle;
                $vehicleRatings = TripRating::whereHas('trip', function ($query) {
                    $query->where('status', 'completed');
                })
                    ->whereHas('trip', function ($query) use ($vehicle) {
                        $query->where('vehicle_id', $vehicle->id);
                    })
                    ->get();

                // Log::info('[UPDATED EVENT] Vehicle ratings count', [$vehicleRatings->count()]);

                if ($vehicleRatings->count() > 0) {
                    $totalVehicleRating = $vehicleRatings->sum('rider_to_car_rating');
                    // Log::info('[UPDATED EVENT] Vehicle total rating', [$totalVehicleRating]);

                    $averageVehicleRating = $totalVehicleRating / $vehicleRatings->count();
                    // Log::info('[UPDATED EVENT] Vehicle average rating', [$averageVehicleRating]);

                    $vehicle->update([
                        'average_vehicle_rating' => $averageVehicleRating,
                    ]);
                }
            }
        }
    }

    /**
     * Handle the TripRating "deleted" event.
     */
    public function deleted(TripRating $tripRating): void
    {
        //
    }

    /**
     * Handle the TripRating "restored" event.
     */
    public function restored(TripRating $tripRating): void
    {
        //
    }

    /**
     * Handle the TripRating "force deleted" event.
     */
    public function forceDeleted(TripRating $tripRating): void
    {
        //
    }
}
