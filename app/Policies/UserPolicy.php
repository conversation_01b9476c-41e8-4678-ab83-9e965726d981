<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_panel::user');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $targetUser): bool
    {
        if (! $user->hasRole('admin') && ! $user->hasRole('super_admin')) {
            if ($targetUser->hasRole('super_admin')) {
                return false;
            }
        }

        // Super admins can view anyone
        if ($user->hasRole('super_admin') && $targetUser->hasRole('super_admin') && $user->id != $targetUser->id) {
            return false;
        }

        return true;
        // // Regular users can only view their own profile
        // return $user->id === $targetUser->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_panel::user');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $targetUser): bool
    {
        if ($user->hasRole('super_admin')) {
            return true;
        }

        if ($user->id === $targetUser->id) {
            return true;
        }

        if ($user->hasRole('admin') && (! $targetUser->hasRole('admin') && ! $targetUser->hasRole('super_admin'))) {
            return true;
        }

        if ($user->hasRole('Chief Human Resources Officer') &&
        ! $targetUser->hasRole('admin') &&
        ! $targetUser->hasRole('super_admin') &&
        ! $targetUser->hasRole('Chief Human Resources Officer')) {
            return true;
        }

        if ($user->id != $targetUser->id) {
            $targetUser->forgetCachedPermissions();
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $targetUser): bool
    {
        if ($user->hasRole('admin')) {
            if (
                (! $targetUser->hasRole('admin') && ! $targetUser->hasRole('super_admin') && $user->id !== $targetUser->id)
            ) {
                return true;
            }
        } elseif ($user->hasRole('super_admin')) {
            if (! $targetUser->hasRole('super_admin') && $user->id !== $targetUser->id) {
                return true;
            }
        }

        if ($user->hasRole('Chief Human Resources Officer') &&
        ! $targetUser->hasRole('admin') &&
        ! $targetUser->hasRole('super_admin') &&
        ! $targetUser->hasRole('Chief Human Resources Officer')) {
            return true;
        }

        // Default case: No permission to delete
        return false;
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_panel::user');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user): bool
    {
        return $user->can('force_delete_panel::user');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_panel::user');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user): bool
    {
        return $user->can('restore_panel::user');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_panel::user');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function replicate(User $user): bool
    {
        return $user->can('replicate_panel::user');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_panel::user');
    }
}
