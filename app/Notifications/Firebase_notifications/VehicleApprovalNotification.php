<?php

namespace App\Notifications\Firebase_notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;
use Throwable;

class VehicleApprovalNotification extends Notification implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $data;
    protected $userId;

    public $tries = 3;
    public $maxExceptions = 3;
    public $backoff = 10;

    public function __construct($user_id, $data)
    {
        $this->userId = $user_id; // Store just the user ID
        $this->data = $data;
    }

    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    protected function getUser()
    {
        return \App\Models\User::findOrFail($this->userId);
    }

    public function toFcm($notifiable): FcmMessage
    {
        try {
            $notif = new FcmMessage(notification: new FcmNotification(
                title: $this->data['title'],
                body: $this->data['description'],
            ));

            return $notif
                ->data([
                    'type' => 'vehicle_approval',
                    'vehicle_id' => $this->data['vehicle_id'] ?? null,
                    'status' => $this->data['status'] ?? null,
                    'notification_id' => uniqid('vehicle_'),
                ])
                ->custom([
                    'android' => [
                        'notification' => [
                            'color' => '#0A0A0A',
                            'channel_id' => 'vehicle_approval',
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'vehicle_approval',
                        ],
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => '10',
                            'apns-push-type' => 'alert',
                        ],
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $this->data['title'],
                                    'body' => $this->data['description'],
                                ],
                                'sound' => 'default',
                                'badge' => 1,
                            ],
                        ],
                        'fcm_options' => [
                            'analytics_label' => 'vehicle_approval',
                        ],
                    ],
                ]);
        } catch (Throwable $e) {
            Log::error('Vehicle Approval FCM Notification Creation Failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function failed(Throwable $exception)
    {
        try {
            $user = $this->getUser();
            $tokens = $user->tokens->pluck('token')->toArray();
        } catch (Throwable $e) {
            $tokens = [];
        }

        Log::error('Vehicle Approval FCM Notification Failed to Send', [
            'user_id' => $this->userId,
            'title' => $this->data['title'],
            'error' => $exception->getMessage(),
            'tokens' => $tokens,
        ]);
    }
}
