<?php

namespace App\Notifications;

use Filament\Facades\Filament;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class ResetPasswordNotification extends Notification
{
    use Queueable;

    public function __construct(private readonly string $token) {}

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $expireInMinutes = config('auth.passwords.'.config('auth.defaults.passwords').'.expire');
        $expireInDays = intdiv($expireInMinutes, 1440);

        return (new MailMessage)
            ->subject(Lang::get('Invitation to Join Our Website'))
            ->greeting(Lang::get('Hello')." {$notifiable->name},")
            ->line(Lang::get('You have been invited to join :appName.', ['appName' => config('app.name')]))
            ->action(Lang::get('Join Now'), $this->resetUrl($notifiable))
            ->line(Lang::get('This invitation link will expire in :count days.', ['count' => $expireInDays]))
            ->line(Lang::get('If you did not request to join, no further action is required.'));

    }

    protected function resetUrl(mixed $notifiable): string
    {
        return Filament::getResetPasswordUrl($this->token, $notifiable);
    }
}
