<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification as BaseNotification;

class DriverDocumentsExpiryNotification extends BaseNotification implements ShouldQueue
{
    use Queueable;

    protected string $message;

    protected string $title;

    protected string $color;

    public function __construct(string $title, string $message, string $color)
    {
        $this->message = $message;
        $this->color = $color;
        $this->title = $title;
    }

    public function via($notifiable): array
    {
        return ['database'];
    }

    public function toDatabase($notifiable): array
    {
        return [
            'title' => $this->title,
            'body' => $this->message,
            'actions' => [
                [
                    'name' => 'mark_as_read',
                    'label' => 'Mark as Read',
                    'isDisabled' => false,
                    'shouldMarkAsRead' => true,
                    'view' => 'filament-actions::button-action',
                ],
            ],
            'duration' => 'persistent',
            'icon' => 'heroicon-o-exclamation-circle',
            'iconColor' => $this->color,
            'view' => 'filament-notifications::notification',
            'viewData' => [],
            'format' => 'filament',
        ];
    }
}
