<?php

namespace App\Listeners;

use App\Http\Controllers\Api\DriversTripController;
use App\Services\EventDecoder;
use Illuminate\Support\Facades\Log;

class StartTrip
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
    {
        $data = EventDecoder::Decoder($event, 'client-start-trip');
        // we just need the trip id that's all
        if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
            Log::info('this is the data : ', [$data]);
            app(DriversTripController::class)->startTrip($data);
        }
    }
}
