<?php

namespace App\Listeners;

use App\Http\Controllers\Api\DriverController;
use App\Services\EventDecoder;

class LocationUpdated
{
    public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
    {
        $message = json_decode($event->message);

        if ($message->event == 'client-driver-location-updated') {
            $data = EventDecoder::Decoder($event, 'client-driver-location-updated');
            if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
                app(DriverController::class)->UpdateLocation($data);
            }
        }
    }
}
