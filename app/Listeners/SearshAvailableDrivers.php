<?php

namespace App\Listeners;

use App\Events\DriverEvents\SearshAvailableDrivers as SearshAvailableDriversEvent;
use App\Http\Controllers\Api\TripController;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class SearshAvailableDrivers implements ShouldQueue
{
    use InteractsWithQueue;

    // Increase timeout to match your search tiers total time (60 + 30 + 30 = 120 seconds)
    // Add buffer for processing time
    public $timeout = 180;

    // Set to a high number instead of null
    public $tries = 5;

    // Set reasonable exception limit
    public $maxExceptions = 3;

    // Keep this to prevent job release
    public $deleteWhenMissingModels = true;

    // Add retry delay
    public $backoff = [10, 30, 60];

    public function handle(SearshAvailableDriversEvent $event): void
    {
        try {
            Log::info('Handling driver search event', [
                'event_data' => $event->data,
                'attempt' => $this->attempts(),
            ]);

            if (! isset($event->data['trip_id'])) {
                throw new \InvalidArgumentException('trip_id is missing from event data');
            }

            app(TripController::class)->getAvailableDrivers($event->data, $event->data['trip_id']);
        } catch (\Exception $e) {
            Log::error('Driver search error', [
                'trip_id' => $event->data['trip_id'] ?? 'missing',
                'error' => $e->getMessage(),
                'attempt' => $this->attempts(),
            ]);

            // Throw the exception to trigger a retry if attempts remain
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Driver search job failed permanently', [
            'error' => $exception->getMessage(),
            'final_attempt' => $this->attempts(),
        ]);
    }
}
