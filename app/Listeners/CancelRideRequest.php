<?php

namespace App\Listeners;

use App\Http\Controllers\Api\RidersTripController;
use App\Services\EventDecoder;

class CancelRideRequest
{
    public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
    {
        $message = json_decode($event->message);

        $data = EventDecoder::Decoder($event, 'client-ride-cancelled');
        if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
            app(RidersTripController::class)->cancelTrip($data['trip_id'], $data['user_id'], $data['cancelled_by'], $data['reason']);
        }
    }
}
