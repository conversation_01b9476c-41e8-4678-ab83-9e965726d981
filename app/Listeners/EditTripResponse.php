<?php

namespace App\Listeners;

use App\Http\Controllers\Api\TripPriceController;
use App\Services\EventDecoder;
use App\Services\PricingService;
use Illuminate\Support\Facades\Log;

class EditTripResponse
{
    public function __construct(private PricingService $pricingService) {}

    public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
    {
        $data = EventDecoder::Decoder($event, 'client-edit-trip-response');

        if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
            try {
                // data : the driver will say accept or reject and based on that we will update the trip
                app(TripPriceController::class)->HandleEditTripResponse($data);
            } catch (\Exception $e) {
                Log::error('Failed to handle edit trip response', [
                    'error' => $e->getMessage(),
                    'data' => $data,
                ]);
            }
        }
    }
}
