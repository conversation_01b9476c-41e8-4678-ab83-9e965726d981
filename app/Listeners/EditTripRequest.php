<?php

// namespace App\Listeners;

// use App\Http\Controllers\Api\RidersTripController;
// use App\Services\EventDecoder;
// use Illuminate\Support\Facades\Log;

// class EditTripRequest
// {
//     public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
//     {
//         $data = EventDecoder::Decoder($event, 'client-edit-trip-request');

//         if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
//             try {
//                 app(RidersTripController::class)->EditTripRequest($data);
//             } catch (\Exception $e) {
//                 Log::error('Failed to initiate edit trip request', [
//                     'error' => $e->getMessage(),
//                     'data' => $data,
//                 ]);
//             }
//         }
//     }
// }
