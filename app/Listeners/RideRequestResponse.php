<?php

namespace App\Listeners;

use App\Http\Controllers\Api\DriverController;
use App\Services\EventDecoder;

class RideRequestResponse
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(\Laravel\Reverb\Events\MessageReceived $event): void
    {
        $data = EventDecoder::Decoder($event, 'client-ride-request-response');

        if ($data != ['skiping_event'] && $data != ['no_data'] && $data != ['no_event']) {
            app(DriverController::class)->RideRequestResponse($data);
        }
    }
}
