<?php

return [
    'riders' => [
        'itemTitle' => 'Rider',
        'collectionTitle' => 'Riders',
        'inputs' => [
            'user_id' => [
                'label' => 'User id',
                'placeholder' => 'User id',
            ],
        ],
        'filament' => [
            'user_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
        ],
    ],
    'deletedRiders' => [
        'itemTitle' => 'Deleted Rider',
        'collectionTitle' => 'Deleted Riders',
        'inputs' => [
            'user_id' => [
                'label' => 'Deleted Rider id',
                'placeholder' => 'Deleted Rider id',
            ],
        ],
        'filament' => [
            'user_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
        ],
    ],
    'drivers' => [
        'itemTitle' => 'Driver',
        'collectionTitle' => 'Drivers',
        'inputs' => [
            'user_id' => [
                'label' => 'User id',
                'placeholder' => 'User id',
            ],
            'license_number' => [
                'label' => 'License number',
                'placeholder' => 'License number',
            ],
            'license_expiry' => [
                'label' => 'License expiry',
                'placeholder' => 'License expiry',
            ],
            'national_id_number' => [
                'label' => 'National id number',
                'placeholder' => 'National id number',
            ],
        ],
        'filament' => [
            'user_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'license_number' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'license_expiry' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'national_id_number' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
        ],
    ],
    'driverAvailabilities' => [
        'itemTitle' => 'Driver Availability',
        'collectionTitle' => 'Driver Availabilities',
        'inputs' => [
            'driver_id' => [
                'label' => 'Driver id',
                'placeholder' => 'Driver id',
            ],
            'status' => [
                'label' => 'Status',
                'placeholder' => 'Status',
            ],
            'start_time' => [
                'label' => 'Start time',
                'placeholder' => 'Start time',
            ],
            'end_time' => [
                'label' => 'End time',
                'placeholder' => 'End time',
            ],
            'notes' => [
                'label' => 'Notes',
                'placeholder' => 'Notes',
            ],
        ],
        'filament' => [
            'driver_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'status' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'start_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'end_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'notes' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
        ],
    ],
    'vehicles' => [
        'itemTitle' => 'Vehicle',
        'collectionTitle' => 'Vehicles',
        'inputs' => [
            'vehicle_model_id' => [
                'label' => 'Vehicle model id',
                'placeholder' => 'Vehicle model id',
            ],
            'year' => [
                'label' => 'Year',
                'placeholder' => 'Year',
            ],
            'color' => [
                'label' => 'Color',
                'placeholder' => 'Color',
            ],
            'license_plate_number' => [
                'label' => 'License plate number',
                'placeholder' => 'License plate number',
            ],
            'insurance_company' => [
                'label' => 'Insurance company',
                'placeholder' => 'Insurance company',
            ],
            'insurance_policy_number' => [
                'label' => 'Insurance policy number',
                'placeholder' => 'Insurance policy number',
            ],
            'insurance_expiry_date' => [
                'label' => 'Insurance expiry date',
                'placeholder' => 'Insurance expiry date',
            ],
            'registration_expiry_date' => [
                'label' => 'Registration expiry date',
                'placeholder' => 'Registration expiry date',
            ],
        ],
        'filament' => [
            'vehicle_model_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'year' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'color' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'license_plate_number' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'insurance_company' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'insurance_policy_number' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'insurance_expiry_date' => [
                'helper_text' => '',
                'label' => '',
            ],
            'registration_expiry_date' => [
                'helper_text' => '',
                'label' => '',
            ],
        ],
    ],
    'vehicleTypes' => [
        'itemTitle' => 'Vehicle Type',
        'collectionTitle' => 'Vehicle Types',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
            'description' => [
                'label' => 'Description',
                'placeholder' => 'Description',
            ],
            'image' => [
                'label' => 'Image',
                'placeholder' => 'Image',
            ],
            'additional_base_fare' => [
                'label' => 'Additional base fare',
                'placeholder' => 'Additional base fare',
            ],
            'additional_price_per_km' => [
                'label' => 'Distance Fare Adjustment',
                'placeholder' => 'Distance Fare Adjustment',
            ],
            'distance_fare_adjustment' => [
                'label' => 'Distance Fare Adjustment',
                'placeholder' => 'Distance Fare Adjustment',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'description' => [
                'helper_text' => '',
                'label' => '',
            ],
            'image' => [
                'helper_text' => '',
                'label' => '',
            ],
            'additional_base_fare' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'additional_price_per_km' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
        ],
    ],
    'vehicleBrands' => [
        'itemTitle' => 'Vehicle Brand',
        'collectionTitle' => 'Vehicle Brands',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
        ],
    ],
    'vehicleModels' => [
        'itemTitle' => 'Vehicle Model',
        'collectionTitle' => 'Vehicle Models',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
            'vehicle_type_id' => [
                'label' => 'Vehicle type id',
                'placeholder' => 'Vehicle type id',
            ],
            'vehicle_brand_id' => [
                'label' => 'Vehicle brand id',
                'placeholder' => 'Vehicle brand id',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'vehicle_type_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'vehicle_brand_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
        ],
    ],
    'zones' => [
        'itemTitle' => 'Zone',
        'collectionTitle' => 'Zones',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
            'polygon' => [
                'label' => 'Polygon',
                'placeholder' => 'Polygon',
            ],
            'city_id' => [
                'label' => 'City id',
                'placeholder' => 'City id',
            ],
            'is_active' => [
                'label' => 'Is active',
                'placeholder' => 'Is active',
            ],
            'departure_base_fare' => [
                'label' => 'Departure base fare',
                'placeholder' => 'Departure base fare',
            ],
            'departure_price_per_km' => [
                'label' => 'Departure price per km',
                'placeholder' => 'Departure price per km',
            ],
            'departure_surge_multiplier' => [
                'label' => 'Departure surge multiplier',
                'placeholder' => 'Departure surge multiplier',
            ],
            'departure_night_fare_multiplier' => [
                'label' => 'Departure night fare multiplier',
                'placeholder' => 'Departure night fare multiplier',
            ],
            'arrival_base_fare' => [
                'label' => 'Arrival base fare',
                'placeholder' => 'Arrival base fare',
            ],
            'arrival_price_per_km' => [
                'label' => 'Arrival price per km',
                'placeholder' => 'Arrival price per km',
            ],
            'arrival_surge_multiplier' => [
                'label' => 'Arrival surge multiplier',
                'placeholder' => 'Arrival surge multiplier',
            ],
            'arrival_night_fare_multiplier' => [
                'label' => 'Arrival night fare multiplier',
                'placeholder' => 'Arrival night fare multiplier',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'polygon' => [
                'helper_text' => '',
                'label' => '',
            ],
            'city_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'is_active' => [
                'helper_text' => '',
                'label' => '',
            ],
            'departure_base_fare' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'departure_price_per_km' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'departure_surge_multiplier' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'departure_night_fare_multiplier' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_base_fare' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_price_per_km' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_surge_multiplier' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_night_fare_multiplier' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
        ],
    ],
    'cities' => [
        'itemTitle' => 'City',
        'collectionTitle' => 'Cities',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
            'is_active' => [
                'label' => 'Is active',
                'placeholder' => 'Is active',
            ],
            'night_start' => [
                'label' => 'Night start',
                'placeholder' => 'Night start',
            ],
            'night_end' => [
                'label' => 'Night end',
                'placeholder' => 'Night end',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'is_active' => [
                'helper_text' => '',
                'label' => '',
            ],
            'night_start' => [
                'helper_text' => '',
                'label' => '',
            ],
            'night_end' => [
                'helper_text' => '',
                'label' => '',
            ],
        ],
    ],
    'trips' => [
        'itemTitle' => 'Trip',
        'collectionTitle' => 'Trips',
        'inputs' => [
            'status' => [
                'label' => 'Status',
                'placeholder' => 'Status',
            ],
            'rider_id' => [
                'label' => 'Rider id',
                'placeholder' => 'Rider id',
            ],
            'driver_id' => [
                'label' => 'Driver id',
                'placeholder' => 'Driver id',
            ],
            'vehicle_id' => [
                'label' => 'Vehicle id',
                'placeholder' => 'Vehicle id',
            ],
            'departure_zone_id' => [
                'label' => 'Departure zone id',
                'placeholder' => 'Departure zone id',
            ],
            'arrival_zone_id' => [
                'label' => 'Arrival zone id',
                'placeholder' => 'Arrival zone id',
            ],
            'departure_lat' => [
                'label' => 'Departure lat',
                'placeholder' => 'Departure lat',
            ],
            'departure_lng' => [
                'label' => 'Departure lng',
                'placeholder' => 'Departure lng',
            ],
            'arrival_lat' => [
                'label' => 'Arrival lat',
                'placeholder' => 'Arrival lat',
            ],
            'arrival_lng' => [
                'label' => 'Arrival lng',
                'placeholder' => 'Arrival lng',
            ],
            'distance' => [
                'label' => 'Distance',
                'placeholder' => 'Distance',
            ],
            'estimated_departure_time' => [
                'label' => 'Estimated departure time',
                'placeholder' => 'Estimated departure time',
            ],
            'actual_departure_time' => [
                'label' => 'Actual departure time',
                'placeholder' => 'Actual departure time',
            ],
            'estimated_arrival_time' => [
                'label' => 'Estimated arrival time',
                'placeholder' => 'Estimated arrival time',
            ],
            'actual_arrival_time' => [
                'label' => 'Actual arrival time',
                'placeholder' => 'Actual arrival time',
            ],
            'pricing_breakdown' => [
                'label' => 'Pricing breakdown',
                'placeholder' => 'Pricing breakdown',
            ],
        ],
        'filament' => [
            'status' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'rider_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'driver_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'vehicle_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'departure_zone_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'arrival_zone_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'departure_lat' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'departure_lng' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_lat' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'arrival_lng' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'distance' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'estimated_departure_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'actual_departure_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'estimated_arrival_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'actual_arrival_time' => [
                'helper_text' => '',
                'label' => '',
            ],
            'pricing_breakdown' => [
                'helper_text' => '',
                'label' => '',
            ],
        ],
    ],
    'tripRatings' => [
        'itemTitle' => 'Trip Rating',
        'collectionTitle' => 'Trip Ratings',
        'inputs' => [
            'trip_id' => [
                'label' => 'Trip id',
                'placeholder' => 'Trip id',
            ],
            'rider_id' => [
                'label' => 'Rider id',
                'placeholder' => 'Rider id',
            ],
            'trip_rating' => [
                'label' => 'Trip rating',
                'placeholder' => 'Trip rating',
            ],
            'trip_review' => [
                'label' => 'Trip review',
                'placeholder' => 'Trip review',
            ],
            'car_rating' => [
                'label' => 'Car rating',
                'placeholder' => 'Car rating',
            ],
            'car_review' => [
                'label' => 'Car review',
                'placeholder' => 'Car review',
            ],
        ],
        'filament' => [
            'trip_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'rider_id' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
            'trip_rating' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'trip_review' => [
                'helper_text' => '',
                'label' => '',
            ],
            'car_rating' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'car_review' => [
                'helper_text' => '',
                'label' => '',
            ],
        ],
    ],
    'driverVehicle' => [
        'itemTitle' => 'Driver Vehicle',
        'collectionTitle' => 'Driver Vehicle',
        'inputs' => [
            'vehicle_id' => [
                'label' => 'Vehicle id',
                'placeholder' => 'Vehicle id',
            ],
            'driver_id' => [
                'label' => 'Driver id',
                'placeholder' => 'Driver id',
            ],
        ],
    ],
    'users' => [
        'itemTitle' => 'User',
        'collectionTitle' => 'Users',
        'inputs' => [
            'name' => [
                'label' => 'Name',
                'placeholder' => 'Name',
            ],
            'email' => [
                'label' => 'Email',
                'placeholder' => 'Email',
            ],
            'password' => [
                'label' => 'Password',
                'placeholder' => 'Password',
            ],
            'type' => [
                'label' => 'Type',
                'placeholder' => 'Type',
            ],
        ],
        'filament' => [
            'name' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'email' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'password' => [
                'helper_text' => '',
                'label' => '',
                'description' => '',
            ],
            'type' => [
                'helper_text' => '',
                'loading_message' => '',
                'no_result_message' => '',
                'search_message' => '',
                'label' => '',
            ],
        ],
    ],
];
