{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-shield": "^3.2", "blade-ui-kit/blade-heroicons": "^2.5", "cheesegrits/filament-google-maps": "^3.0", "dedoc/scramble": "^0.11.17", "diogogpinto/filament-auth-ui-enhancer": "^1.0", "filament/filament": "^3.2", "guava/filament-icon-picker": "^2.0", "ibrahimbougaoua/filament-rating-star": "^1.0", "ichtrojan/laravel-otp": "^2.0", "joaopaulolndev/filament-pdf-viewer": "^1.0", "kreait/laravel-firebase": "^6.0", "laravel-notification-channels/fcm": "^5.0", "laravel/framework": "^11.0", "laravel/pulse": "^1.2", "laravel/reverb": "^1.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "leandrocfe/filament-apex-charts": "^3.1", "orangehill/iseed": "^3.0", "postare/blade-mdi": "^1.1", "propaganistas/laravel-phone": "^5.3", "saade/filament-laravel-log": "^3.2", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "laravel/pint": "^1.18", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --ansi"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}