# ZTS - Ride Hailing Platform

Welcome to the **ZTS** project developed by **<PERSON><PERSON><PERSON>**. This project is a comprehensive ride-hailing platform built with Lara<PERSON> and includes real-time event broadcasting using Laravel Echo and Reverb.

## 🚀 Features

- **Real-time Trip Management**
  - Live trip tracking with WebSocket integration
  - Push notifications for riders and drivers
  - Real-time status updates and location sharing

- **Advanced Pricing Engine**
  - Dynamic pricing based on multiple factors
  - Time-of-day and day-of-week adjustments
  - Area-based pricing zones
  - Special equipment and vehicle type modifiers

- **User Management**
  - Driver verification and document management
  - Rider profiles with preferences
  - Gender-specific ride options
  - Rating and feedback system

- **Vehicle Management**
  - Multiple vehicle types and categories
  - Equipment tracking and availability
  - Vehicle capacity and accessibility features

- **Geographic Features**
  - Area-based service availability
  - Geofencing for restricted areas
  - PostGIS integration for spatial queries

- **Multi-language Support**
  - English and Arabic interfaces
  - RTL layout support
  - Localized notifications and messages

- **Admin Dashboard**
  - Comprehensive Filament admin panel
  - Real-time monitoring and analytics
  - User management and moderation tools
  - Pricing configuration interface

## 🛠️ Technology Stack

- **Backend**
  - Laravel 11.x framework
  - PHP 8.3
  - PostgreSQL with PostGIS extension
  - Redis for caching and queues

- **Frontend**
  - Blade templates with Tailwind CSS
  - Livewire for reactive components
  - Alpine.js for client-side interactivity
  - Laravel Echo for real-time updates

- **Real-time Communication**
  - Laravel Reverb WebSocket server
  - Laravel Echo client
  - Firebase Cloud Messaging for push notifications

- **Admin Interface**
  - Filament admin panel
  - Custom Filament resources and widgets
  - Role-based access control with Shield

- **Deployment**
  - Docker containerization
  - Kubernetes orchestration
  - CI/CD pipeline with Bitbucket

## 📋 Requirements

Before you begin, ensure you have the following installed on your system:

- **PHP 8.3**
- **Composer** (dependency management)
- **Node.js** (with npm or yarn for managing JavaScript packages)
- **PostgreSQL** with PostGIS extension (for geographic data)
- **Redis** (for caching and broadcasting queues)
- **Laravel Echo & Reverb** (for real-time broadcasting)
- **Nginx/Apache** (for serving the application)
- **npm** or **yarn**

## 🔧 Installation

Follow these steps to set up the project:

### 1. Clone the repository

```bash
git clone ssh://**************************:7999/zts/web.git zts
cd zts
```

### 2. Install PHP dependencies

```bash
composer install
```

### 3. Create and configure environment file

```bash
cp .env.example .env
```

- Set up your database credentials:
  ```
  DB_CONNECTION=pgsql
  DB_HOST=127.0.0.1
  DB_PORT=5432
  DB_DATABASE=zts
  DB_USERNAME=your_username
  DB_PASSWORD=your_password
  ```

- Configure Redis for caching and queue management:
  ```
  REDIS_HOST=127.0.0.1
  REDIS_PASSWORD=null
  REDIS_PORT=6379
  ```

- Set up broadcasting configuration for Reverb/Echo:
  ```
  REVERB_APP_ID=your_app_id
  REVERB_APP_KEY=your_app_key
  REVERB_APP_SECRET=your_app_secret
  REVERB_HOST=zts.test
  REVERB_PORT=8080
  REVERB_SCHEME=https
  REVERB_SERVER_HOST=127.0.0.1
  
  VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
  VITE_REVERB_HOST="${REVERB_HOST}"
  VITE_REVERB_PORT="${REVERB_PORT}"
  VITE_REVERB_SCHEME="${REVERB_SCHEME}"
  ```

- Generate an application key:

```bash
php artisan key:generate
```

### 4. Run migrations and seed data

```bash
php artisan migrate:fresh --seed
```

### 5. Install and build frontend assets

```bash
# Install dependencies
npm install

# Build for production
npm run build

# For development with hot reload
npm run dev

# Build custom JavaScript components
node bin/build.js
```

### 6. Start background services

```bash
# Start the queue worker for background jobs
php artisan queue:work

# Start the Reverb WebSocket server
php artisan reverb:start
```

## 🔄 Development Workflow

### Git Workflow

1. Create a feature branch from develop
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and commit them
   ```bash
   git add .
   git commit -m "Description of your changes"
   ```

3. Run tests and linting
   ```bash
   php artisan test
   php artisan pint
   ```

4. Push your branch and create a pull request
   ```bash
   git push -u origin feature/your-feature-name
   ```

### Filament Admin Development

The admin panel is built with Filament. To create new resources:

```bash
php artisan make:filament-resource ModelName
```
## 🔍 Troubleshooting

### Common Issues

#### Reverb Server Not Starting
- Check Redis connection
- Ensure ports are not in use by other applications
- Verify .env configuration
- Check logs: `storage/logs/laravel.log`

#### Queue Worker Not Processing Jobs
- Check Redis connection
- Run `php artisan queue:restart` to restart the queue
- Verify job classes are properly namespaced

#### Database Connection Issues
- Ensure PostgreSQL is running
- Verify PostGIS extension is installed
- Check database credentials in .env

### Performance Optimization

Optimize the application with the following commands:
```bash
php artisan optimize
php artisan route:cache
php artisan view:cache
php artisan config:cache
```

## 💰 Pricing Calculation

The system uses a sophisticated pricing model with the following formula:

```
G = B + (D × d) × (1 + Overcharge)
```

Where:
- **B** = Base fare (adjusted based on various factors)
- **D** = Distance fare per kilometer (adjusted on various factors)
- **d** = Trip distance in kilometers
- **Overcharge** = Max(0, Timediff - Threshold)
- **Timediff** = (ActualTime - ExpectedTime) / ExpectedTime

### Pricing Factors

The base fare and distance fare are adjusted based on:
- Day of week (weekday vs weekend)
- Time of day (peak hours vs off-peak)
- Geographic area (urban, suburban, rural)
- Vehicle type (economy, comfort, premium)
- Number of seats requested
- Gender preference (female driver option)
- Special equipment (child seat, wheelchair access, etc.)

## 📱 Mobile Applications

The ZTS platform includes mobile applications for both riders and drivers:

- **Rider App**: Available for iOS and Android
- **Driver App**: Available for iOS and Android

Mobile app repositories are separate from this web backend.

## 📖 API Documentation

Once the application is running, visit:
```
/docs/api
```
to access the API documentation generated by Scramble.

## 🔒 Security

- All API endpoints are secured with Laravel Sanctum
- Admin panel access is protected with Filament Shield
- CSRF protection is enabled for all web routes
- Input validation is implemented for all form submissions

## 📊 Monitoring

The application includes Laravel Pulse for monitoring:
```
/pulse
```

## 📄 License

This project is proprietary software owned by Satoripop.
